venv/
.env
mysql_data/
node_modules/
dist/
target/
# Logs
logs
*.log
npm-debug.log*

# OS generated files
.DS_Store
Thumbs.db

# IDE files
.idea/
.vscode/
*.suo
*.ntvs*
*.njsproj
*.sln

instagram/extracted_data*.json
# Environment variables
.env
.parcel-cache
__pycache__
tmp/
data.json
data1.json
sigma/node/dist
svc_update_balance/modules
svc_tx_processor/modules
svc_api_server/modules
svc_frontend/modules
svc_pump_discovery/modules
svc_pump_discovery/images
svc_bots/modules
wallet_notify/static/images
instagram/modules
create_token/static/images
create_token/videos
instagram/tmp
create_token/node/node_modules
create_token/node/package-lock.json
create_token/node/static/images
create_token/node/static/videos
create_token/node/tmp
create_token/node/.wallets.json
svc_pump_discovery/rust/helius_webhook/target
demo/node/transfer/dist/*
demo/node/transfer/dist/logs/*
demo/node/transfer/tx/*