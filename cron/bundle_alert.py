import mysql.connector
import requests

DISCORD_WEBHOOK = "https://discord.com/api/webhooks/1332021656943853649/dEHzF0MBgG74vO1SsU7625SXdEjTcC8imvxTiOoliz9dD6CpYnU-hOnMVzl_C7nXD57u"

def find_duplicate_balances():
    # Database connection details
    db_config = {
        'host': 'kroocoin.xyz',  # Replace with your host
        'user': 'pump2',       # Replace with your username
        'password': 'pump2',  # Replace with your password
        'database': 'pump'  # Replace with your database name
    }

    try:
        # Connect to the MySQL database
        conn = mysql.connector.connect(**db_config)
        cursor = conn.cursor(dictionary=True)

        # Query to find accounts updated in the last minute with duplicate balances
        query = """
        SELECT 
            master,
            ROUND(balance / 1e9, 2) as balance,
            COUNT(*) AS address_count
        FROM 
            accounts
        WHERE 
            last_balance_update BETWEEN NOW() - INTERVAL 1 MINUTE AND NOW()
        GROUP BY 
            master, balance
        HAVING 
            COUNT(*) > 1;
        """

        # Execute the query
        cursor.execute(query)
        results = cursor.fetchall()

        # Send results to Discord webhook
        if results:
            print("Duplicate balances found in the past minute:")
            for row in results:
                message = (
                    f"[A](https://kroocoin.xyz/analyzer) [R](http://kroocoin.xyz:5151)  **Master Address:** `{row['master']}` "
                    f"**Amount:** `{row['balance']} SOL` "
                    f"**Occurrences:** `{row['address_count']} times`"
                )
                print(message)
                send_to_discord(message)
        else:
            print("No duplicate balances found in the past minute.")

    except mysql.connector.Error as err:
        print(f"Error: {err}")
    finally:
        if conn.is_connected():
            cursor.close()
            conn.close()

def send_to_discord(message):
    data = {
        "content": message
    }
    response = requests.post(DISCORD_WEBHOOK, json=data)
    if response.status_code == 204:
        print("Message sent to Discord successfully.")
    else:
        print(f"Failed to send message to Discord. Status code: {response.status_code}")

if __name__ == "__main__":
    find_duplicate_balances()
