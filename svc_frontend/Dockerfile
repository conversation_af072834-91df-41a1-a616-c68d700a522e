#Build stage
FROM node:22-alpine AS build
WORKDIR /app
COPY package*.json  ./
COPY tsconfig.json ./
RUN npm install 
RUN npm install -g webpack webpack-cli ts-loader parcel rimraf typescript @types/express
COPY ./ ./
RUN npm run build
# #Production stage
# FROM node:22-alpine AS production
# WORKDIR /app
# COPY package*.json ./
# COPY tsconfig.json ./
# COPY ./ ./
# RUN npm ci --only=production
# RUN npm install -g rimraf typescript
# COPY --from=build /app/dist ./dist
# CMD ["node", "dist/index.js"] 