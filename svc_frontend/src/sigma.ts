import Graph from "graphology";
import <PERSON> from "sigma";
import forceAtlas2 from 'graphology-layout-forceatlas2';
import FA2Layout from "graphology-layout-forceatlas2/worker";
import ForceSupervisor from "graphology-layout-force/worker";
import { animateNodes } from "sigma/utils";


const apiServer = process.env.API_SERVER;


function getRandomInt(min: number, max: number): number {
    min = Math.ceil(min); // Ensure min is an integer
    max = Math.floor(max); // Ensure max is an integer
    return Math.floor(Math.random() * (max - min + 1)) + min;
}
  
// Create a graphology graph
const graph = new Graph();
let fa2Layout: any;
let renderer: any;
const label = document.getElementById("label") as HTMLElement;
const rightPanelData = document.getElementById("right-panel-data") as HTMLElement;

const fa2Button = document.getElementById("fa2") as HTMLButtonElement;
// Function to fetch and update data

let minEdgeSum=0.5;
let minNodeSum=0.3;




function fetchDataAndUpdateGraph() {
    fetch('http://'+apiServer+'/data')
    .then(response => response.json())
    .then(data => {
        // Store current positions
        // const positions = {};
        // const sizes={};
        // const balances={};
        // graph.forEachNode((node, attributes) => {
        //     positions[node] = { x: attributes.x, y: attributes.y };
        //     sizes[node] = { size: attributes.size};

           
        // });

    


        // Add new nodes and update existing ones
        data.nodes.forEach((node: { id: unknown; label: any; size: any; color: any; balance:any ; role:any}) => {
            if (!graph.hasNode(node.id)) {
                if ( isNaN(node.balance) ){
                    node.balance=1
                }
                let balSols=parseFloat(node.balance)/1e9
                let nodesize=3
                if (balSols >= 1000) {
                    nodesize = 20;
                } else if (balSols >= 500) {
                    nodesize = 18;
                } else if (balSols >= 200) {
                    nodesize = 14;
                } else if (balSols >= 100) {
                    nodesize = 10;
                } else if (balSols >= 50) {
                    nodesize = 8;
                } else if (balSols >= 20) {
                    nodesize = 6;
                } else if (balSols >= 10) {
                    nodesize = 6;
                } else if (balSols >= 5) {
                    nodesize = 5;
                } else if (balSols >= 2) {
                    nodesize = 5;
                } else if (balSols >= 1) {
                    nodesize = 4;
                } else {
                    nodesize = 3;  // Default size if balSols is less than 1
                }
                let nodeBalance=parseFloat(node.balance) > minEdgeSum ? parseFloat(node.balance).toFixed(2)  : '';
                let nodeSize=parseFloat(node.balance) > minEdgeSum  ? 5 : 1;
                let role=node.role;
                //let nodeColor=parseFloat(node.balance) > minEdgeSum  ?  '#F5F5F5' : '#444444' ;
                let nodeColor=parseFloat(node.balance) > minEdgeSum  ?  'blue' : 'green' ;

                if ( role == "mint") {
                    nodeColor="pink";
                }

                graph.addNode(node.id, {
                    label: node.label.substring(0, 5),
                    fullname: node.label,
                    balance: node.balance/1000000000,
                    type: 'circle',
                    fixed: false,
                    x: getRandomInt(1,10)*100,
                    y: getRandomInt(1,10)*100,
                    size: nodesize,
                    color: nodeColor
                });
            }
             else {

                // graph.updateNodeAttributes(node.id, (attributes) => ({
                //     ...attributes,
                //     size: 10 + getRandomInt(10,100),
                // }
            //));
            }
        });

       // Restore positions
        // graph.forEachNode((node, attributes) => {

        //         graph.setNodeAttribute(node, 'x', positions[node].x);
        //         graph.setNodeAttribute(node, 'y', positions[node].y);


            
        // });

        // Add new edges
        data.edges.forEach((edge: { source: unknown; target: unknown; size: any; color: any; amount:any; }) => {
            if (!graph.hasEdge(edge.source, edge.target)) {
                if (graph.hasNode(edge.source) && graph.hasNode(edge.target)) {

                    let edgeBalance=parseFloat(edge.amount) > minEdgeSum ? parseFloat(edge.amount).toFixed(2)  : '';
                    let edgeSize=parseFloat(edge.amount) > minEdgeSum  ? 5 : 1;
                    //let edgeColor=parseFloat(edge.amount) > minEdgeSum  ? '#444444' : '#F5F5F5';
                    let edgeColor=parseFloat(edge.amount) > minEdgeSum  ? 'black' : 'lightgray';
                    
                    graph.addEdge(edge.source, edge.target, {
                        size: edgeSize,
                        color: edgeColor,
                        label: edgeBalance
                    });
                } else {
                    console.error("Edge references non-existing node:", edge);
                }
            }
        });

        // const layout = new ForceSupervisor(graph, { isNodeFixed: (_, attr) => attr.highlighted });
        // layout.start();
  

        if (!fa2Layout) {
            const sensibleSettings = forceAtlas2.inferSettings(graph);
            sensibleSettings.slowDown=3;
            sensibleSettings.strongGravityMode=true;
            sensibleSettings.barnesHutOptimize=false;
            sensibleSettings.scalingRatio=20;
            sensibleSettings.outboundAttractionDistribution=false;
            sensibleSettings.linLogMode=false;
            fa2Layout = new FA2Layout(graph, { settings: sensibleSettings });
            fa2Layout.start();
            

        } else {
            // Stop and restart the layout to process new additions
            fa2Layout.stop();
            fa2Layout.start();
        }

        if (!renderer) {
            renderer = new Sigma(
                graph,
                document.getElementById("container") as HTMLDivElement,
                {
                    renderLabels: true,  // Ensure edges are rendered
                    renderEdgeLabels: true,
          

                }
            );
        }
        // const renderer = new Sigma(graph, document.getElementById("container") as HTMLDivElement, {
        //     // We don't have to declare edgeProgramClasses here, because we only use the default ones ("line" and "arrow")
        //     nodeProgramClasses: {
        //       image: createNodeImageProgram(),
        //       gradient: NodeGradientProgram,
        //     },
        //     renderEdgeLabels: true,
        //   });

          
        renderer.on('downNode', function(e: any) {

            let nodeData=graph.getNodeAttributes(''+e.node);
            // var prefix = e.settings('prefix') || '';
            // var aa = renderer.positions;
            // var node = e.data.node;
            // var x = e.data.node[prefix + 'x'];
            // var y = e.data.node[prefix + 'y'];
            // console.log("x:"+x+" y:"+y);
            label.style.left = e.event.x+'px'; // X-axis position
            label.style.top = e.event.y+'px'; // Y-axis position
            label.innerHTML='<b>'+nodeData.fullname+'</b><br>Amount:<i>'+ parseFloat(nodeData.balance).toFixed(2)+'</i><br><a href="https://bullx.io/terminal?chainId=**********&address='+nodeData.fullname+'">BullX</a>'
            rightPanelData.innerHTML='<b>'+nodeData.fullname+'</b><br>Amount:<i>'+ parseFloat(nodeData.balance).toFixed(2)+'</i><br><a href="https://bullx.io/terminal?chainId=**********&address='+nodeData.fullname+'">BullX</a><br>Solscan:<a href="https://solscan.io/account/'+nodeData.fullname+'">Solscan</a>'
            //renderer.refresh();
            // Now render some div based on these coordinates
          });
          renderer.on('leaveNode', function(e:any) {
            label.style.left ='-1000px'; // X-axis position
            label.style.top = '-1000px'; // Y-axis position
            //renderer.refresh();
            // Now render some div based on these coordinates
          });

          function toggleFA2Layout() {
            if (fa2Layout.isRunning()) {
              fa2Layout.stop();
              fa2Button.innerHTML = `Start layout ▶`;
            } else {
              fa2Layout.start();
              fa2Button.innerHTML = `Stop layout ⏸`;
            }
          }
          fa2Button.addEventListener("click", toggleFA2Layout);

    })
    .catch(error => {
        console.error('Error fetching data:', error);
    });
}

// Initial data fetch
fetchDataAndUpdateGraph();


// Set up periodic data fetch every 30 seconds
setInterval(fetchDataAndUpdateGraph, 30000); 
