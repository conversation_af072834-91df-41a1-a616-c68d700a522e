import express, { Express, Request, Response } from "express";
import path from "path";

const app: Express = express();

// Serve static files from the "public" directory
app.use(express.static(path.join(__dirname, '../public')));

// Default route to serve the index.html file
app.get("/", (req: Request, res: Response) => {
  res.sendFile(path.join(__dirname, '../public', 'index.html'));
});

app.listen(4000, () => {
  console.log(`App is listening on port 4000`);
});