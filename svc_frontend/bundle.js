(() => {
    const defines = {};
    const entry = [null];
    function define(name, dependencies, factory) {
        defines[name] = { dependencies, factory };
        entry[0] = name;
    }
    define("require", ["exports"], (exports) => {
        Object.defineProperty(exports, "__cjsModule", { value: true });
        Object.defineProperty(exports, "default", { value: (name) => resolve(name) });
    });
    var __importDefault = (this && this.__importDefault) || function (mod) {
        return (mod && mod.__esModule) ? mod : { "default": mod };
    };
    define("index", ["require", "exports", "express", "path"], function (require, exports, express_1, path_1) {
        "use strict";
        Object.defineProperty(exports, "__esModule", { value: true });
        express_1 = __importDefault(express_1);
        path_1 = __importDefault(path_1);
        const app = (0, express_1.default)();
        // Serve static files from the "public" directory
        app.use(express_1.default.static(path_1.default.join(__dirname, '../public')));
        // Default route to serve the index.html file
        app.get("/", (req, res) => {
            res.sendFile(path_1.default.join(__dirname, '../public', 'index.html'));
        });
        app.listen(4000, () => {
            console.log(`App is listening on port 4000`);
        });
    });
    define("sigma", ["require", "exports", "graphology", "sigma", "graphology-layout-forceatlas2", "graphology-layout-forceatlas2/worker"], function (require, exports, graphology_1, sigma_1, graphology_layout_forceatlas2_1, worker_1) {
        "use strict";
        Object.defineProperty(exports, "__esModule", { value: true });
        graphology_1 = __importDefault(graphology_1);
        sigma_1 = __importDefault(sigma_1);
        graphology_layout_forceatlas2_1 = __importDefault(graphology_layout_forceatlas2_1);
        worker_1 = __importDefault(worker_1);
        function getRandomInt(min, max) {
            min = Math.ceil(min); // Ensure min is an integer
            max = Math.floor(max); // Ensure max is an integer
            return Math.floor(Math.random() * (max - min + 1)) + min;
        }
        // Create a graphology graph
        const graph = new graphology_1.default();
        let fa2Layout;
        let renderer;
        const label = document.getElementById("label");
        const rightPanelData = document.getElementById("right-panel-data");
        const fa2Button = document.getElementById("fa2");
        // Function to fetch and update data
        let minEdgeSum = 0.5;
        let minNodeSum = 0.3;
        function fetchDataAndUpdateGraph() {
            fetch('http://109.199.121.189:2222/data')
                .then(response => response.json())
                .then(data => {
                // Store current positions
                // const positions = {};
                // const sizes={};
                // const balances={};
                // graph.forEachNode((node, attributes) => {
                //     positions[node] = { x: attributes.x, y: attributes.y };
                //     sizes[node] = { size: attributes.size};
                // });
                // Add new nodes and update existing ones
                data.nodes.forEach((node) => {
                    if (!graph.hasNode(node.id)) {
                        if (isNaN(node.balance)) {
                            node.balance = 1;
                        }
                        let balSols = parseFloat(node.balance) / 1e9;
                        let nodesize = 3;
                        if (balSols >= 7) {
                            nodesize = 20;
                        }
                        if (balSols < 7 && balSols >= 1) {
                            nodesize = 8;
                        }
                        if (balSols < 1) {
                            nodesize = 5;
                        }
                        let nodeBalance = parseFloat(node.balance) > minEdgeSum ? parseFloat(node.balance).toFixed(2) : '';
                        let nodeSize = parseFloat(node.balance) > minEdgeSum ? 5 : 1;
                        let role = node.role;
                        //let nodeColor=parseFloat(node.balance) > minEdgeSum  ?  '#F5F5F5' : '#444444' ;
                        let nodeColor = parseFloat(node.balance) > minEdgeSum ? 'blue' : 'green';
                        if (role == "mint") {
                            nodeColor = "pink";
                        }
                        graph.addNode(node.id, {
                            label: node.label.substring(0, 5),
                            fullname: node.label,
                            balance: node.balance / 1000000000,
                            type: 'circle',
                            fixed: false,
                            x: getRandomInt(1, 10) * 100,
                            y: getRandomInt(1, 10) * 100,
                            size: nodesize,
                            color: nodeColor
                        });
                    }
                    else {
                        // graph.updateNodeAttributes(node.id, (attributes) => ({
                        //     ...attributes,
                        //     size: 10 + getRandomInt(10,100),
                        // }
                        //));
                    }
                });
                // Restore positions
                // graph.forEachNode((node, attributes) => {
                //         graph.setNodeAttribute(node, 'x', positions[node].x);
                //         graph.setNodeAttribute(node, 'y', positions[node].y);
                // });
                // Add new edges
                data.edges.forEach((edge) => {
                    if (!graph.hasEdge(edge.source, edge.target)) {
                        if (graph.hasNode(edge.source) && graph.hasNode(edge.target)) {
                            let edgeBalance = parseFloat(edge.amount) > minEdgeSum ? parseFloat(edge.amount).toFixed(2) : '';
                            let edgeSize = parseFloat(edge.amount) > minEdgeSum ? 5 : 1;
                            //let edgeColor=parseFloat(edge.amount) > minEdgeSum  ? '#444444' : '#F5F5F5';
                            let edgeColor = parseFloat(edge.amount) > minEdgeSum ? 'black' : 'lightgray';
                            graph.addEdge(edge.source, edge.target, {
                                size: edgeSize,
                                color: edgeColor,
                                label: edgeBalance
                            });
                        }
                        else {
                            console.error("Edge references non-existing node:", edge);
                        }
                    }
                });
                // const layout = new ForceSupervisor(graph, { isNodeFixed: (_, attr) => attr.highlighted });
                // layout.start();
                if (!fa2Layout) {
                    const sensibleSettings = graphology_layout_forceatlas2_1.default.inferSettings(graph);
                    sensibleSettings.slowDown = 3;
                    sensibleSettings.strongGravityMode = true;
                    sensibleSettings.barnesHutOptimize = false;
                    sensibleSettings.scalingRatio = 20;
                    sensibleSettings.outboundAttractionDistribution = false;
                    sensibleSettings.linLogMode = false;
                    fa2Layout = new worker_1.default(graph, { settings: sensibleSettings });
                    fa2Layout.start();
                }
                else {
                    // Stop and restart the layout to process new additions
                    fa2Layout.stop();
                    fa2Layout.start();
                }
                if (!renderer) {
                    renderer = new sigma_1.default(graph, document.getElementById("container"), {
                        renderLabels: true, // Ensure edges are rendered
                        renderEdgeLabels: true,
                    });
                }
                // const renderer = new Sigma(graph, document.getElementById("container") as HTMLDivElement, {
                //     // We don't have to declare edgeProgramClasses here, because we only use the default ones ("line" and "arrow")
                //     nodeProgramClasses: {
                //       image: createNodeImageProgram(),
                //       gradient: NodeGradientProgram,
                //     },
                //     renderEdgeLabels: true,
                //   });
                renderer.on('downNode', function (e) {
                    let nodeData = graph.getNodeAttributes('' + e.node);
                    // var prefix = e.settings('prefix') || '';
                    // var aa = renderer.positions;
                    // var node = e.data.node;
                    // var x = e.data.node[prefix + 'x'];
                    // var y = e.data.node[prefix + 'y'];
                    // console.log("x:"+x+" y:"+y);
                    label.style.left = e.event.x + 'px'; // X-axis position
                    label.style.top = e.event.y + 'px'; // Y-axis position
                    label.innerHTML = '<b>' + nodeData.fullname + '</b><br>Amount:<i>' + parseFloat(nodeData.balance).toFixed(2) + '</i><br><a href="https://bullx.io/terminal?chainId=1399811149&address=' + nodeData.fullname + '">BullX</a>';
                    rightPanelData.innerHTML = '<b>' + nodeData.fullname + '</b><br>Amount:<i>' + parseFloat(nodeData.balance).toFixed(2) + '</i><br><a href="https://bullx.io/terminal?chainId=1399811149&address=' + nodeData.fullname + '">BullX</a>';
                    //renderer.refresh();
                    // Now render some div based on these coordinates
                });
                renderer.on('leaveNode', function (e) {
                    label.style.left = '-1000px'; // X-axis position
                    label.style.top = '-1000px'; // Y-axis position
                    //renderer.refresh();
                    // Now render some div based on these coordinates
                });
                function toggleFA2Layout() {
                    if (fa2Layout.isRunning()) {
                        fa2Layout.stop();
                        fa2Button.innerHTML = `Start layout ▶`;
                    }
                    else {
                        fa2Layout.start();
                        fa2Button.innerHTML = `Stop layout ⏸`;
                    }
                }
                fa2Button.addEventListener("click", toggleFA2Layout);
            })
                .catch(error => {
                console.error('Error fetching data:', error);
            });
        }
        // Initial data fetch
        fetchDataAndUpdateGraph();
        // Set up periodic data fetch every 30 seconds
        setInterval(fetchDataAndUpdateGraph, 1000);
    });
    
    'marker:resolver';

    function get_define(name) {
        if (defines[name]) {
            return defines[name];
        }
        else if (defines[name + '/index']) {
            return defines[name + '/index'];
        }
        else {
            const dependencies = ['exports'];
            const factory = (exports) => {
                try {
                    Object.defineProperty(exports, "__cjsModule", { value: true });
                    Object.defineProperty(exports, "default", { value: require(name) });
                }
                catch (_a) {
                    throw Error(['module "', name, '" not found.'].join(''));
                }
            };
            return { dependencies, factory };
        }
    }
    const instances = {};
    function resolve(name) {
        if (instances[name]) {
            return instances[name];
        }
        if (name === 'exports') {
            return {};
        }
        const define = get_define(name);
        if (typeof define.factory !== 'function') {
            return define.factory;
        }
        instances[name] = {};
        const dependencies = define.dependencies.map(name => resolve(name));
        define.factory(...dependencies);
        const exports = dependencies[define.dependencies.indexOf('exports')];
        instances[name] = (exports['__cjsModule']) ? exports.default : exports;
        return instances[name];
    }
    if (entry[0] !== null) {
        return resolve(entry[0]);
    }
})();