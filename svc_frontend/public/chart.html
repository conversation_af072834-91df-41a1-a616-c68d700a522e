<!DOCTYPE html>
<html>
<head>
    <title>TradingView Chart with Live Data</title>
    <style>
        html, body {
            height: 100%;
            margin: 0;
            display: flex;
            flex-direction: column;
        }
        #chart {
            flex-grow: 1;
        }
        #tooltip {
            position: absolute;
            display: none;
            padding: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            border-radius: 5px;
            pointer-events: none;
            z-index: 1000;
        }
    </style>
    <script src="https://unpkg.com/lightweight-charts@4.2.0/dist/lightweight-charts.standalone.production.js"></script>
</head>
<body>
    <div id="chart"></div>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const chart = LightweightCharts.createChart(document.getElementById('chart'), {
                width: window.innerWidth,
                height: window.innerHeight,
                backgroundColor: '#000000',
                layout: {
                    background:{
                            color: '#000000'
                    },
                    backgroundColor: '#000000',
                    textColor: '#444',
                },
                grid: {
                    vertLines: {
                        color: '#222',
                    },
                    horzLines: {
                        color: '#222',
                    },
                },
                rightPriceScale: {
                    borderColor: '#555',
                },
                timeScale: {
                    borderColor: '#555',
                    timeVisible: true,
                    secondsVisible: true,
                },mode:1,

                priceFormat: {
                        formatter: price => {
                            if (price >= 1000000) {
                                return (price / 1000000).toFixed(1) + 'M';
                            } else if (price >= 1000) {
                                return (price / 1000).toFixed(1) + 'K';
                            }
                            return price.toFixed(2);
                        }
                    },



            });

            chart.applyOptions({
                localization: {
                    priceFormatter: (p) => {
                        if (p >= 1000000) {
                            return (p / 1000000).toFixed(1) + 'M';
                        } else if (p >= 1000) {
                            return (p / 1000).toFixed(1) + 'K';
                        }
                        return p.toFixed(6);
                    },
                },
            });


            const candlestickSeries = chart.addCandlestickSeries({ upColor: '#26a69a', downColor: '#ef5350', borderVisible: false, wickUpColor: '#26a69a', wickDownColor: '#ef5350' });


            // const lineSeries = chart.addLineSeries({
            //     priceFormat: {
            //         type: 'price',
            //         precision: 0, // Number of decimal places
            //         minMove: 0.00000000001, // Smallest step for the price values
            //     },
            //     scaleMargins: {
            //         top: 0.1,
            //         bottom: 0.4,
            //     }
            // });

            const volumeSeries = chart.addHistogramSeries({
                color: '#26a69a',
                priceFormat: {
                    type: 'volume',
                },
                priceScaleId: '',
                scaleMargins: {
                    top: 0.9,
                    bottom: 0,
                },
            });

            volumeSeries.priceScale().applyOptions({
                scaleMargins: {
                    top: 0.9,
                    bottom: 0,
                },
            });

            // const priceData = [];
            const volumeData = [];
            const candleStickData = [];

            // lineSeries.setData(priceData);
            volumeSeries.setData(volumeData);
            candlestickSeries.setData(candleStickData);

            const socket = new WebSocket('ws://pump1:6789/AVevYR4Ban5oG5AMzJsRVEZsdgw5aQeDp8gM8qtRpump');

            socket.onmessage = function (event) {
                try {
                    const data = JSON.parse(event.data);
                    if (Array.isArray(data)) {
                        data.forEach(tradeData => {
                            const time = tradeData.timestamp + tradeData.milliseconds / 1000;  // Combine seconds and milliseconds
                            const price = tradeData.price;
                            const volume = tradeData.volume;
                            const high = tradeData.high;
                            const low = tradeData.low;
                            const open = tradeData.open;
                            const close = tradeData.close;
                            const color = tradeData.color;


                            // const priceUpdate = { time: time, value: price };
                            const volumeUpdate = { time: time, value: parseFloat(volume) };
                            const candlestickUpdate = { color: color, time: time, open: open, high: high, low: low, close: close };
                           
                            // priceData.push(priceUpdate);
                            volumeData.push(volumeUpdate);
                            candleStickData.push(candlestickUpdate);
                        });
                        // lineSeries.setData(priceData);
                        volumeSeries.setData(volumeData);
                        candlestickSeries.setData(candleStickData);

                        //chart.timeScale().fitContent();
                        console.log("set data");
                        console.log(data)
                    } else {
                        // Handle new data
                        const time = data.timestamp + data.milliseconds / 1000;  // Combine seconds and milliseconds
                        const price = data.price;
                        const volume = data.volume;
                        const high = data.high;
                        const low = data.low;
                        const open = data.open;
                        const close = data.close;
                        const color = data.color;


                        // const priceUpdate = { time: time, value: price };
                        const volumeUpdate = { time: time, value: volume };
                        const candlestickUpdate = { color: color, time: time, open: open, high: high, low: low, close: close };
                           

                        // priceData.push(priceUpdate);
                        volumeData.push(volumeUpdate);
                        candleStickData.push(candlestickUpdate);

                        // lineSeries.update(priceUpdate);
                        volumeSeries.update(volumeUpdate);
                        candlestickSeries.update(candlestickUpdate);
                        //chart.timeScale().fitContent();
                        console.log("update data");
                        console.log(data)
                    }
                } catch (error) {
                    console.error('Error processing message:', error);
                }
            };

            socket.onopen = function () {
                console.log('Connected to WebSocket server');
            };

            socket.onclose = function (event) {
                console.log('Disconnected from WebSocket server', event);
                // Optionally, try to reconnect
                setTimeout(() => {
                    location.reload();
                }, 1000);
            };

            socket.onerror = function (error) {
                console.error('WebSocket error:', error);
            };

            window.addEventListener('resize', function() {
                chart.resize(window.innerWidth, window.innerHeight);
            });
        });
    </script>
</body>
</html>
