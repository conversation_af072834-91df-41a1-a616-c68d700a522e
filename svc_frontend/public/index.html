<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Sigma example template</title>
  </head>
  <body>
    <style>
      html,
      body,
      #container {
        width: 100%;
        height: 100%;
        margin: 0;
        padding: 0;
        overflow: hidden;
        z-index:1000;
        background: #fefefe;
      }
      .right-side-div {
            position: fixed;
            right: 0;
            bottom: 0;
            width: 100%;
            height: 150px;
            background-color: rgba(206, 206, 206, 0.8);
            font-size: x-small
            color: white;
            padding: 5px;
            box-sizing: border-box;
            z-index: 1000; /* Ensures it overlaps all other content */
        }

        #label {
            position: absolute; /* or relative, fixed, etc., based on your needs */
            background-color: rgb(221, 221, 221);
            padding: 3px;
            border: 1px solid rgb(228, 228, 228);
            border-radius: 5px; /* This makes the borders rounded */

        }

        #fa2 {
            position: absolute; /* or relative, fixed, etc., based on your needs */
            background-color: rgb(203, 203, 203);
            padding: 5px;
            border: 1px solid rgb(122, 122, 122);
            top:5px;
            left:5px;
            border-radius: 5px; /* This makes the borders rounded */

        }
    </style>
    
    <div id="container"></div>
    <div id="label">test<br>aa</div>
    <button id="fa2">render on/off</button>
    <div class="right-side-div">
        <h2>Properties</h2>
        <div id="right-panel-data">No object selected</div>
        some data
    </div>
<script type="module" src="../src/sigma.ts"></script>
  </body>
</html>   

