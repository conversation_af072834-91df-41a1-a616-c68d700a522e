{"type": "commonjs", "dependencies": {"@coral-xyz/anchor": "^0.30.1", "@fastify/formbody": "^8.0.2", "@fastify/static": "^8.1.0", "@metaplex-foundation/js": "^0.20.1", "@metaplex-foundation/mpl-token-metadata": "^3.4.0", "@project-serum/anchor": "^0.26.0", "@raydium-io/raydium-sdk": "^1.3.1-beta.58", "@shyft-to/solana-transaction-parser": "^1.1.17", "@solana/spl-token": "^0.4.11", "@solana/spl-token-registry": "^0.2.4574", "@solana/web3.js": "^1.98.0", "@triton-one/yellowstone-grpc": "^4.0.0", "@types/node": "^22.12.0", "alchemy-sdk": "^3.5.4", "amqplib": "^0.10.5", "axios": "^1.7.9", "base-64": "^1.0.0", "bcrypt": "^5.1.1", "bs58": "^6.0.0", "deep-parse-json": "^2.0.0", "dotenv": "^16.4.7", "ejs": "^3.1.10", "express": "^4.21.2", "express-session": "^1.18.1", "fastify": "^5.2.1", "https-proxy-agent": "^7.0.6", "ioredis": "^5.5.0", "lodash": "^4.17.21", "mysql2": "^3.12.0", "mysql2-promise": "^0.1.4", "node-fetch": "^2.7.0", "performance": "^1.4.0", "pm2": "^6.0.5", "prettier": "^3.5.3", "prettier-eslint": "^16.3.0", "punycode": "^2.3.1", "socks-proxy-agent": "^8.0.5", "typescript": "^5.7.3", "uuid": "^11.1.0", "why-is-node-running": "^3.2.2", "winston": "^3.17.0", "ws": "^8.18.0"}, "scripts": {"build": "tsc", "start": "node dist/index.js", "accounts-new": "node dist/services/run-accounts-new.js", "balance-checker": "node dist/services/balance-checker.js", "test-balance-checker": "node dist/services/test-balance-checker.js"}, "devDependencies": {"@eslint/js": "^9.22.0", "@swc/core": "^1.11.11", "@swc/helpers": "^0.5.15", "@types/amqplib": "^0.10.6", "@types/bcrypt": "^5.0.2", "@types/express": "^5.0.0", "@types/express-session": "^1.18.1", "@types/lodash": "^4.17.16", "@types/node-fetch": "^2.6.12", "eslint": "^9.22.0", "globals": "^16.0.0", "ts-node": "^10.9.2", "typescript-eslint": "^8.26.1"}, "bin": {"sol": "./dist/src/cli/sol.js"}}