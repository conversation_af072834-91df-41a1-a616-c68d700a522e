import { Router } from 'express';
const router = Router();
import { query } from '../db';

// ...existing code...

router.post('/dashboard/add-app-config', async (req, res) => {
    try {
        const { enabled, is_simulation, app, max_time_sec, min_time_sec, max_profit_sol, min_profit_sol, max_user_buy_sol, max_mc_pct, notes, address } = req.body;
        await query('INSERT INTO app_config (enabled, is_simulation, app, max_time_sec, min_time_sec, max_profit_sol, min_profit_sol, max_user_buy_sol, max_mc_pct, notes, address) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)', 
            [enabled, is_simulation, app, max_time_sec, min_time_sec, max_profit_sol, min_profit_sol, max_user_buy_sol, max_mc_pct, notes, address]);
        res.status(200).send('Config added successfully');
    } catch (error) {
        console.error(error);
        res.status(500).send('Failed to add config');
    }
});

// ...existing code...

export default router;
