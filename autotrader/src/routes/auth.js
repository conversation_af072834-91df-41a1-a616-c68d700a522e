import { Router } from 'express';
const router = Router();
import { query } from '../db';

// ...existing code...

router.post('/login', async (req, res) => {
    try {
        const { username, password } = req.body;
        const user = await query('SELECT * FROM users WHERE username = ? AND password = ?', [username, password]);
        if (user.length > 0) {
            req.session.user = user[0];
            res.redirect('/dashboard');
        } else {
            res.status(401).send('Invalid credentials');
        }
    } catch (error) {
        console.error(error);
        res.status(500).send('Login failed');
    }
});

// ...existing code...

export default router;
