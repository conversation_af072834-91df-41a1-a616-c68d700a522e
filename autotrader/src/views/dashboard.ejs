<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <style>
        body {
            background-color: #121212;
            color: #ffffff;
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: 250px;
            background-color: #1a1a1a;
            padding: 20px;
            position: fixed;
            height: 100vh;
            border-right: 1px solid #333;
        }

        .sidebar .nav-link {
            color: #ffffff;
            padding: 10px 15px;
            margin-bottom: 5px;
            border-radius: 5px;
            transition: background-color 0.3s;
        }

        .sidebar .nav-link:hover {
            background-color: #333;
        }

        .sidebar .nav-link.active {
            background-color: #2d3748;
        }

        .main-content {
            margin-left: 250px;
            padding: 20px;
            flex-grow: 1;
            min-height: 100vh;
            width: calc(100% - 250px);
        }

        .table {
            color: #ffffff;
            background-color: #1a1a1a;
        }

        .table thead th {
            background-color: #2d3748;
            border-color: #444;
        }

        .table td, .table th {
            border-color: #444;
        }

        .editable {
            background-color: #2d3748;
            border: 1px solid #444;
            padding: 5px;
            border-radius: 3px;
        }

        .editable:focus {
            outline: none;
            background-color: #3d4758;
            border-color: #666;
        }

        .btn-primary {
            background-color: #4a5568;
            border-color: #4a5568;
        }

        .btn-primary:hover {
            background-color: #2d3748;
            border-color: #2d3748;
        }

        .btn-success {
            background-color: #48bb78;
            border-color: #48bb78;
        }

        .btn-success:hover {
            background-color: #38a169;
            border-color: #38a169;
        }

        .dashboard-header {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #1a1a1a;
            border-radius: 5px;
        }

        .dashboard-section {
            background-color: #1a1a1a;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }

        .table tr.bought-status {
            background-color: rgba(40, 167, 69, 0.2) !important; /* Green with transparency */
        }
        .table tr.bought-status:hover {
            background-color: rgba(40, 167, 69, 0.3) !important;
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <h3 class="mb-4">Dashboard</h3>
        <div class="nav flex-column nav-pills">
            <a class="nav-link active" href="#latest-rugger-mints" data-toggle="pill">Latest Rugger Mints</a>
            <a class="nav-link" href="#app-config" data-toggle="pill">App Config</a>
        </div>
    </div>

    <div class="main-content">
        <div class="tab-content">
            <!-- Latest Rugger Mints tab (now first and active) -->
            <div class="tab-pane fade show active" id="latest-rugger-mints">
                <div class="dashboard-header d-flex justify-content-between align-items-center">
                    <h2>Latest Rugger Mints</h2>
                    <button class="btn btn-primary refresh-rugger-mints">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                </div>
                <div class="dashboard-section">
                    <table class="table table-dark table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>App</th>
                          
                                <th>Mint</th>
                                <th>Created At</th>
                                <th>Trade Status</th>
                                <th>Trade</th>
                                <th>Buy SOL Amount</th>
                                <th>Buy Token Amount</th>
                                <th>Sell SOL Amount</th>
                                <th>Sell Token Amount</th>
                                <th>Buy MC</th>
                                <th>Sell MC</th>
                                <th>Max MC</th>
                                <th>PNL(%)</th>
                                <th>PNL $</th>
                               
                            </tr>
                        </thead>
                        <tbody>
                            <% latestRuggerMints.forEach(mint => { %>
                                <tr class="<%= mint.trade_status === 'bought' ? 'bought-status' : '' %>">
                                    <td><%= mint.id %></td>
                                    <td><%= mint.app %></td>
                      
                                    <td>[<a href="https://neo.bullx.io/terminal?chainId=1399811149&address=<%= mint.mint %>">BullX</a>][<a href="http://kroocoin.xyz:7017/data?mint=<%= mint.mint %>">Analyzer</a>]</td>
                                    <td><%= new Date(mint.created_at).toLocaleString('en-GB').replace(',', '').replace(/\//g, '/') %></td>
                                    <td><%= mint.trade_status %></td>
                                    <td><%= mint.trade %></td>
                                    <td><%= mint.buy_sol_amount %></td>
                                    <td><%= mint.buy_token_amount %></td>
                                    <td><%= mint.sell_sol_amount %></td>
                                    <td><%= mint.sell_token_amount %></td>

                                    <td><%= mint.buy_mc %></td>
                                    <td><%= mint.sell_mc %></td>
                                    <td><%= mint.max_mc %></td>
                                    <td><%= ((mint.sell_mc - mint.buy_mc) / mint.buy_mc * 100).toFixed(2) %>%</td>
                                    <td><%= ((mint.sell_mc - mint.buy_mc) / mint.buy_mc * mint.buy_sol_amount * configs[0].sol_price).toFixed(4) %></td>
                                    
                                </tr>
                            <% }) %>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- App Config tab (now second) -->
            <div class="tab-pane fade" id="app-config">
                <div class="dashboard-header">
                    <h2>App Configuration</h2>
                </div>
                <div class="dashboard-section">
                    <table class="table table-dark table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Enabled</th>
                                <th>Is Simulation</th>
                                <th>App</th>
                                <th>Buy Sol</th>
                                <th>Max Time Sec</th>
                                <th>Min Time Sec</th>
                                <th>Max Profit Sol</th>
                                <th>Min Profit Sol</th>
                                <th>Max User Buy Sol</th>
                                <th>Max MC Pct</th>
                                <th>Notes</th>
                                <th>Address</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <% appConfig.forEach(config => { %>
                                <tr data-id="<%= config.id %>">
                                    <td><%= config.id %></td>
                                    <td contenteditable="false" class="editable" data-field="enabled"><%= config.enabled %></td>
                                    <td contenteditable="false" class="editable" data-field="is_simulation"><%= config.is_simulation %></td>
                                    <td contenteditable="false" class="editable" data-field="app"><%= config.app %></td>
                                    <td contenteditable="false" class="editable" data-field="buy_sol"><%= config.buy_sol %></td>
                                    <td contenteditable="false" class="editable" data-field="max_time_sec"><%= config.max_time_sec %></td>
                                    <td contenteditable="false" class="editable" data-field="min_time_sec"><%= config.min_time_sec %></td>
                                    <td contenteditable="false" class="editable" data-field="max_profit_sol"><%= config.max_profit_sol %></td>
                                    <td contenteditable="false" class="editable" data-field="min_profit_sol"><%= config.min_profit_sol %></td>
                                    <td contenteditable="false" class="editable" data-field="max_user_buy_sol"><%= config.max_user_buy_sol %></td>
                                    <td contenteditable="false" class="editable" data-field="max_mc_pct"><%= config.max_mc_pct %></td>
                                    <td contenteditable="false" class="editable" data-field="notes"><%= config.notes %></td>
                                    <td contenteditable="false" class="editable" data-field="address"><%= config.address %></td>
                                    <td>
                                        <button class="btn btn-primary btn-sm edit-btn">Edit</button>
                                        <button class="btn btn-success btn-sm save-btn" style="display: none;">Save</button>
                                    </td>
                                </tr>
                            <% }) %>
                            
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.4/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    
    <script>
        document.querySelectorAll('.edit-btn').forEach(button => {
            button.addEventListener('click', function() {
                const row = this.closest('tr');
                row.querySelectorAll('.editable').forEach(cell => {
                    cell.contentEditable = true;
                });
                row.querySelector('.edit-btn').style.display = 'none';
                row.querySelector('.save-btn').style.display = 'inline-block';
            });
        });

        document.querySelectorAll('.save-btn').forEach(button => {
            button.addEventListener('click', async function() {
                const row = this.closest('tr');
                const id = row.getAttribute('data-id');
                const data = {};
                row.querySelectorAll('.editable').forEach(cell => {
                    data[cell.getAttribute('data-field')] = cell.innerText;
                    cell.contentEditable = false;
                });
                const response = await fetch('/dashboard/update-app-config', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ id, data })
                });
                if (response.ok) {
                    location.reload();
                } else {
                    alert('Failed to update config');
                }
            });
        });

        document.querySelector('.add-btn').addEventListener('click', async function() {
            const row = document.getElementById('new-config-row');
            const data = {};
            row.querySelectorAll('.editable').forEach(cell => {
                data[cell.getAttribute('data-field')] = cell.innerText;
            });
            const response = await fetch('/dashboard/add-app-config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            });
            if (response.ok) {
                location.reload();
            } else {
                alert('Failed to add config');
            }
        });

        // Improved refresh functionality
        document.querySelector('.refresh-rugger-mints').addEventListener('click', async function() {
            const button = this;
            const originalText = button.innerHTML;
            
            try {
                button.disabled = true;
                button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Refreshing...';
                
                const response = await fetch('/latest-rugger-mints', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const result = await response.json();
                
                if (!result.success) {
                    throw new Error(result.error || 'Unknown error occurred');
                }
                
                const tbody = document.querySelector('#latest-rugger-mints table tbody');
                tbody.innerHTML = '';
                
                result.latestRuggerMints.forEach(mint => {
                    const tr = document.createElement('tr');
                    if (mint.trade_status === 'bought') {
                        tr.classList.add('bought-status');
                    }
                    const solPrice = result.configs[0].sol_price;
                    const pnlPercent = ((mint.sell_mc - mint.buy_mc) / mint.buy_mc * 100).toFixed(2);
                    const pnlDollars = ((mint.sell_mc - mint.buy_mc) / mint.buy_mc * mint.buy_sol_amount * solPrice).toFixed(4);
                    
                    tr.innerHTML = `
                        <td>${mint.id}</td>
                        <td>${mint.app}</td>
                        <td>[<a href="https://neo.bullx.io/terminal?chainId=1399811149&address=${mint.mint}">BullX</a>][<a href="http://kroocoin.xyz:7017/data?mint=${mint.mint}">Analyzer</a>]</td>
                        <td>${new Date(mint.created_at).toLocaleString('en-GB').replace(',', '').replace(/\//g, '/')}</td>
                        <td>${mint.trade_status}</td>
                        <td>${mint.trade}</td>
                        <td>${mint.buy_sol_amount}</td>
                        <td>${mint.buy_token_amount}</td>
                        <td>${mint.sell_sol_amount}</td>
                        <td>${mint.sell_token_amount}</td>
                        <td>${mint.buy_mc}</td>
                        <td>${mint.sell_mc}</td>
                        <td>${pnlPercent}%</td>
                        <td>${pnlDollars}</td>
                    `;
                    tbody.appendChild(tr);
                });
            } catch (error) {
                console.error('Error refreshing data:', error);
                alert(`Failed to refresh data: ${error.message}`);
            } finally {
                button.disabled = false;
                button.innerHTML = originalText;
            }
        });
    </script>
</body>
</html>
