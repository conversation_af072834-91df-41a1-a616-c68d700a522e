import { Connection, VersionedTransactionResponse } from '@solana/web3.js';
import bs58 from 'bs58';
import { BorshCoder } from '@coral-xyz/anchor';
import axios from 'axios';
import { updateSolPrice } from '../common/db';
// eslint-disable-next-line @typescript-eslint/no-require-imports
const idl = require("./idl-new.json");
import { addRedisString } from './redis';
import { Redis } from 'ioredis';
import { REDIS_URL, TRADERS_PROGRAMS } from './config';
import { ruleIsSignedByAuto1Owner } from './pump-rules';

type TradeEvent = {
  mint: string;
  solAmount: number;
  tokenAmount: number;
  isBuy: boolean;
  user: string;
  timestamp: number;
  virtualSolReserves: number;
  virtualTokenReserves: number;
  realSolReserves: number;
  realTokenReserves: number;
  signature?: string;
  signer?: string;
  block_id?: number;
  app?: string;
};

const skipTrades = false;



type SwapInfo = {
  solAmount: number;
  tokenAmount: number;
  tradeType: string;
  mint: string;
  owner: string;
  timestamp?: number;
  isDev?: boolean;
  isBuy?: boolean;

};


// MySQL database credentials
// const MYSQL_SERVER = 'kroocoin.xyz';
// const MYSQL_PORT = 3306;
// const MYSQL_USER = 'pump2';
// const MYSQL_PASSWORD = 'pump2';
// const MYSQL_DB = 'pump';
const solFallBackPrice = 124;
let solPrice = solFallBackPrice;

const rpcUrl: string = 'https://solana-mainnet.g.alchemy.com/v2/********************************';

// let accountAddress: string = '';
// const PUMP_PROGRAM_ID = '6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P';
// const EVENT_AUTHORITY = 'Ce6TQqeHC9p8KetsN6JsjHK7UTZk7nasjjnr7XxXp9F1';  // use to get the event data
// const MASTER_ACCOUNT = 'Gh9J24j688AfsoiZQJiUGYtxb8RwhcZPJsQtiAJtLFAQ' ;


const connection = new Connection(rpcUrl, 'confirmed');



export async function processPumpTransaction(signature: string, retries: number = 3): Promise<SwapInfo | null> {
  try {
    if (!signature) {
      console.error('Invalid signature:', signature);
      return null;
    }

    // get if its signed by OWNER

    // Get transaction details with maxSupportedTransactionVersion parameter
    const transaction = await connection.getTransaction(signature, {
      maxSupportedTransactionVersion: 0,
    });

    if (transaction) {
      // Skip transactions that are in an error state
      if (transaction.meta && transaction.meta.err) {
        return null;
      }

      // Get transaction slot id


      try {
        const accountKeys = transaction.transaction.message.staticAccountKeys.map(key => key.toBase58());

        const signer = accountKeys[0] || '';


        const innerInstructions = transaction.meta?.innerInstructions || [];
        for (const innerInstruction of innerInstructions) {
          for (const instruction of innerInstruction.instructions) {


            const coder = new BorshCoder(idl as any);
            const data = instruction.data;
            const args = coder.events.decode(Buffer.from(bs58.decode(data)).slice(8).toString('base64'));
            if (args) {
              if (args.name === "TradeEvent") {
                if (skipTrades) {
                  continue;
                }
                const data1 = decodeTradeEventValues(bs58.decode(data));

                if (data1) {
                  const signedByAuto1Owner = await ruleIsSignedByAuto1Owner(signer);
                  if (signedByAuto1Owner && signer != data1.user) {
                    const redis = new Redis({ host: REDIS_URL, port: 6379, password: "pump2pump" });
                    await addRedisString(redis, data1.user, JSON.stringify({ addressType: "regular", address: data1.user, app: "auto1" }));
                    redis.disconnect();

                  }

                  const tradeType = data1.isBuy ? 'buy' : 'sell';

                  return {
                    tradeType: tradeType,
                    solAmount: data1.solAmount,
                    tokenAmount: data1.tokenAmount,
                    mint: data1.mint,
                    owner: data1.user,
                  };
                  //console.log('Trade:', tmpTrade);

                }


              }
            }
          }
        }
      } catch (err) {
        console.error(`Error processing inner instructions for transaction ${signature}:`, err);
        if (retries > 0) {
          await new Promise(resolve => setTimeout(resolve, 100));
          await processPumpTransaction(signature, retries - 1);
        }
        return null;
      }
    } else {
      if (retries > 0) {
        await processPumpTransaction(signature, retries - 1);
      }
    }
  } catch (err) {
    console.error(`Error processing transaction ${signature}:`, err);
    if (retries > 0) {
      await processPumpTransaction(signature, retries - 1);
    }
  }
  return null;
}
export async function decodePumpFunTransactionForTraining(transaction: VersionedTransactionResponse): Promise<SwapInfo | undefined> {
  let isDev = true;
  const accountKeys = transaction.transaction.message.staticAccountKeys.map(key => key.toBase58());


  const innerInstructions = transaction.meta?.innerInstructions || [];

  // Check regular instructions

  isDev = !accountKeys.some(acc => TRADERS_PROGRAMS.includes(acc));



  for (const innerInstruction of innerInstructions) {
    for (const instruction of innerInstruction.instructions) {


      const coder = new BorshCoder(idl as any);
      const data = instruction.data;
      const args = coder.events.decode(Buffer.from(bs58.decode(data)).slice(8).toString('base64'));
      if (args) {
        if (args.name === "TradeEvent") {
          if (skipTrades) {
            continue;
          }
          const data1 = decodeTradeEventValues(bs58.decode(data));

          if (data1) {
      

            const tradeType = data1.isBuy ? 'buy' : 'sell';
            console.log(tradeType, data1.solAmount, data1.tokenAmount, data1.mint, data1.user);
            return {
              timestamp: data1.timestamp,
              tradeType: tradeType,
              solAmount: data1.solAmount / 1e9,
              tokenAmount: data1.tokenAmount / 1e6,
              mint: data1.mint,
              owner: data1.user,
              isBuy: data1.isBuy,
              isDev: isDev,
            };

          }


        }
      }
    }
  }

}

export async function processPumpTransactionForTraining(signature: string, retries: number = 3): Promise<SwapInfo | null> {


  try {
    if (!signature) {
      console.error('Invalid signature:', signature);
      return null;

    }
  
    // get if its signed by OWNER

    // Get transaction details with maxSupportedTransactionVersion parameter
    const transaction = await connection.getTransaction(signature, {
      maxSupportedTransactionVersion: 0,
    });

    if (transaction) {
      // Skip transactions that are in an error state
      if (transaction.meta && transaction.meta.err) {
        return null;
      }

      // Get transaction slot id


      try {
        const txout = await decodePumpFunTransactionForTraining(transaction);
        return txout ?? null;

      } catch (err) {
        console.error(`Error processing inner instructions for transaction ${signature}:`, err);
        if (retries > 0) {
          await new Promise(resolve => setTimeout(resolve, 100));
          await processPumpTransactionForTraining(signature, retries - 1);
        }
        return null;
      }
    } else {
      if (retries > 0) {
        await processPumpTransactionForTraining(signature, retries - 1);
      }
    }
  } catch (err) {
    console.error(`Error processing transaction ${signature}:`, err);
    if (retries > 0) {
      await processPumpTransactionForTraining(signature, retries - 1);
    }
  }
  return null;
}



function decodeTradeEventValues(data: Uint8Array): TradeEvent | null {
  const buffer = Buffer.from(data).slice(8); // Remove first 8 bytes for the event CPI

  const mint = bs58.encode(buffer.slice(8, 40));
  const solAmount = buffer.readBigUInt64LE(40);
  const tokenAmount = buffer.readBigUInt64LE(48);
  const isBuy = Boolean(buffer[56]);
  const user = bs58.encode(buffer.slice(57, 89));
  const timestamp = buffer.readBigInt64LE(89);
  const virtualSolReserves = buffer.readBigUInt64LE(97);
  const virtualTokenReserves = buffer.readBigUInt64LE(105);
  const realSolReserves = buffer.readBigUInt64LE(113);
  const realTokenReserves = buffer.readBigUInt64LE(121);

  return {
    mint,
    solAmount: Number(solAmount),
    tokenAmount: Number(tokenAmount),
    isBuy,
    user,
    timestamp: Number(timestamp),
    virtualSolReserves: Number(virtualSolReserves),
    virtualTokenReserves: Number(virtualTokenReserves),
    realSolReserves: Number(realSolReserves),
    realTokenReserves: Number(realTokenReserves),
  };
}



export async function getSolPriceFromPump(): Promise<number> {
  try {
    const response = await axios.get('https://frontend-api-v3.pump.fun/sol-price');
    return response.data.solPrice;
  } catch (error) {
    console.error('Error fetching SOL price:', error);
    return 200; // Default to 1 if there's an error
  }
}

export async function updateDBSolPrice() {
  try {
    solPrice = await getSolPriceFromPump();
    if (solPrice > 0) {
      const redis = new Redis({ host: REDIS_URL, port: 6379, password: "pump2pump" });
      await addRedisString(redis, 'solPrice', solPrice.toString());
      redis.quit();

      await updateSolPrice(solPrice);
    }
    console.log('SOL price:', solPrice);
  } catch (error) {
    console.error('Error updating SOL price:', error);
  }
}



