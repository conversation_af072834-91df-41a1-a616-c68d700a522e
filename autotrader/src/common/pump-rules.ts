import { getRedisString } from "./redis";
import { Redis } from "ioredis";
import { REDIS_URL } from "./config";

export async function ruleIsSignedByAuto1Owner(address: string) {
    const redis = new Redis({
        host: REDIS_URL,
        port: 6379,
        password: "pump2pump"
    });

    const auto1Owner = await getRedisString(redis, "auto1");
    redis.disconnect();
    if (address == auto1Owner) {
        return true;
    }else {
        return false;
    }

}

