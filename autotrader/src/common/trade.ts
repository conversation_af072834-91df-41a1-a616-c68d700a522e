import {
  Keypair,
  PublicKey,
  SystemProgram,
  Transaction,
  TransactionInstruction,
  Connection,
} from "@solana/web3.js";
import bs58 from "bs58";
import { createAssociatedTokenAccountInstruction, getAssociatedTokenAddress ,ASSOCIATED_TOKEN_PROGRAM_ID} from '@solana/spl-token';
import getTokenData from "./coindata"; // Assuming getTokenData is in test.ts
import { GLOBAL, FEE_RECIPIENT,FEE_RECIPIENT2, TOKEN_PROGRAM_ID, RENT, PUMP_FUN_ACCOUNT, PUMP_FUN_PROGRAM, SYSTEM_PROGRAM_ID,ASSOC_TOKEN_ACC_PROG } from "./solana";
const LAMPORTS_PER_SOL = Number(1_000_000_000);



export async function getBuyInstruction(connection: Connection, privateKey: string, tokenAddress: string, amount: number, slippage: number = 0.25, txBuilder: Transaction) {
  const coinData = await getTokenData(tokenAddress);
  if (!coinData) {
    console.error('Failed to retrieve coin data...');
    return;
  }

  const walletPrivateKey = Keypair.fromSecretKey(bs58.decode(privateKey));
  const owner = walletPrivateKey.publicKey;
  const token = new PublicKey(tokenAddress);

  const tokenAccountAddress = await getAssociatedTokenAddress(token, owner, false);

  const tokenAccountInfo = await connection.getAccountInfo(tokenAccountAddress);

  let tokenAccount: PublicKey;
  if (!tokenAccountInfo) {
    txBuilder.add(
      createAssociatedTokenAccountInstruction(walletPrivateKey.publicKey, tokenAccountAddress, walletPrivateKey.publicKey, token)
    );
    tokenAccount = tokenAccountAddress;
  } else {
    tokenAccount = tokenAccountAddress;
  }

  const solInLamports = amount * LAMPORTS_PER_SOL;
  const tokenOut = Math.floor((solInLamports * coinData['virtual_token_reserves']) / coinData['virtual_sol_reserves']);

  const amountWithSlippage = amount * (1 + slippage);
  const maxSolCost = Math.floor(amountWithSlippage * LAMPORTS_PER_SOL);
  const ASSOCIATED_USER = tokenAccount;
  const USER = owner;
  const BONDING_CURVE = new PublicKey(coinData['bonding_curve']);
  const ASSOCIATED_BONDING_CURVE = new PublicKey(coinData['associated_bonding_curve']);

  const keys = [
    { pubkey: GLOBAL, isSigner: false, isWritable: false },
    { pubkey: FEE_RECIPIENT, isSigner: false, isWritable: true },
    { pubkey: token, isSigner: false, isWritable: false },
    { pubkey: BONDING_CURVE, isSigner: false, isWritable: true },
    { pubkey: ASSOCIATED_BONDING_CURVE, isSigner: false, isWritable: true },
    { pubkey: ASSOCIATED_USER, isSigner: false, isWritable: true },
    { pubkey: USER, isSigner: false, isWritable: true },
    { pubkey: SystemProgram.programId, isSigner: false, isWritable: false },
    { pubkey: TOKEN_PROGRAM_ID, isSigner: false, isWritable: false },
    { pubkey: RENT, isSigner: false, isWritable: false },
    { pubkey: PUMP_FUN_ACCOUNT, isSigner: false, isWritable: false },
    { pubkey: PUMP_FUN_PROGRAM, isSigner: false, isWritable: false }
  ];
  const data = Buffer.concat([bufferFromUInt64('16927863322537952870'), bufferFromUInt64(tokenOut), bufferFromUInt64(maxSolCost)]);

  const instruction = new TransactionInstruction({
    keys: keys,
    programId: PUMP_FUN_PROGRAM,
    data: data
  });

  return { instruction: instruction, tokenAmount: tokenOut };
}

export async function getSellInstruction(connection: Connection, privateKey: string, tokenAddress: string, tokenBalance: number, slippage: number = 0.25) {
  const coinData = await getTokenData(tokenAddress);
  if (!coinData) {
    console.error('Failed to retrieve coin data...');
    return;
  }

  const payer = Keypair.fromSecretKey(bs58.decode(privateKey));
  const owner = payer.publicKey;
  const mint = new PublicKey(tokenAddress);
  const txBuilder = new Transaction();

  const tokenAccountAddress = await getAssociatedTokenAddress(mint, owner, false);

  const tokenAccountInfo = await connection.getAccountInfo(tokenAccountAddress);

  let tokenAccount: PublicKey;
  if (!tokenAccountInfo) {
    txBuilder.add(createAssociatedTokenAccountInstruction(payer.publicKey, tokenAccountAddress, payer.publicKey, mint));
    tokenAccount = tokenAccountAddress;
  } else {
    tokenAccount = tokenAccountAddress;
  }

  const minSolOutput = Math.floor((tokenBalance * (1 - slippage) * coinData['virtual_sol_reserves']) / coinData['virtual_token_reserves']);

  const keys = [
    { pubkey: GLOBAL, isSigner: false, isWritable: false },
    { pubkey: FEE_RECIPIENT, isSigner: false, isWritable: true },
    { pubkey: mint, isSigner: false, isWritable: false },
    { pubkey: new PublicKey(coinData['bonding_curve']), isSigner: false, isWritable: true },
    { pubkey: new PublicKey(coinData['associated_bonding_curve']), isSigner: false, isWritable: true },
    { pubkey: tokenAccount, isSigner: false, isWritable: true },
    { pubkey: owner, isSigner: false, isWritable: true },
    { pubkey: SYSTEM_PROGRAM_ID, isSigner: false, isWritable: false },
    { pubkey: ASSOC_TOKEN_ACC_PROG, isSigner: false, isWritable: false },
    { pubkey: TOKEN_PROGRAM_ID, isSigner: false, isWritable: false },
    { pubkey: PUMP_FUN_ACCOUNT, isSigner: false, isWritable: false },
    { pubkey: PUMP_FUN_PROGRAM, isSigner: false, isWritable: false }
  ];

  const data = Buffer.concat([bufferFromUInt64('12502976635542562355'), bufferFromUInt64(tokenBalance), bufferFromUInt64(minSolOutput)]);

  const sellInstruction = new TransactionInstruction({
    keys: keys,
    programId: PUMP_FUN_PROGRAM,
    data: data
  });

  // Add the sell instruction to the transaction
  txBuilder.add(sellInstruction);

  // Create and add the close token account instruction
  const closeTokenAccountInstruction = createCloseTokenAccountInstruction(tokenAccount, owner, owner);
  txBuilder.add(closeTokenAccountInstruction);

  return txBuilder;
}

// Helper function to create a close token account instruction
function createCloseTokenAccountInstruction(
  tokenAccount: PublicKey,
  destination: PublicKey,
  owner: PublicKey
): TransactionInstruction {
  return new TransactionInstruction({
    keys: [
      { pubkey: tokenAccount, isSigner: false, isWritable: true },
      { pubkey: destination, isSigner: false, isWritable: true },
      { pubkey: owner, isSigner: true, isWritable: false },
    ],
    programId: TOKEN_PROGRAM_ID,
    data: Buffer.from([9]), // 9 is the instruction index for CloseAccount in the token program
  });
}

export function bufferFromUInt64(value: number | string) {
  const buffer = Buffer.alloc(8);
  buffer.writeBigUInt64LE(BigInt(value));
  return buffer;
}




//////////////////////   NO RPC tx ///////////////
 function getBondingCurveAddresses(mintPublicKey: string,owner:PublicKey,devPubKey:PublicKey): { 
  bondingCurve: PublicKey, 
  associatedBondingCurve: PublicKey,
  vault: PublicKey 
} {
  // Convert the base58 string to PublicKey
  const mintPubkey = new PublicKey(mintPublicKey);
  
  // Derive bonding curve address
  const [bondingCurve] = PublicKey.findProgramAddressSync(
    [
      Buffer.from("bonding-curve"),
      mintPubkey.toBuffer()
    ],
    PUMP_FUN_PROGRAM
  );
  
  // Derive associated bonding curve address
  const [associatedBondingCurve] = PublicKey.findProgramAddressSync(
    [
      bondingCurve.toBuffer(),
      new PublicKey("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA").toBuffer(),
      mintPubkey.toBuffer()
    ],
    new PublicKey("ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL")
  );

  // Derive creator vault address (for creator fees)
  // As per documentation: https://github.com/pump-fun/pump-public-docs/blob/main/docs/PUMP_CREATOR_FEE_README.md
  //const creatorVault: PublicKey ;

    const [vault] = PublicKey.findProgramAddressSync(
      [
        Buffer.from("creator-vault"),
        devPubKey.toBuffer()
      ],
      PUMP_FUN_PROGRAM
    );


  
  return {
    bondingCurve,
    associatedBondingCurve,
    vault
  };
}



export async function getBuyInstructionNoRPC( privateKey: string, tokenAddress: string, amount: number,virtual_sol_reserves:number,virtual_token_reserves:number,walletPrivateKey: Keypair,devPubKey: string,buyMore=0) {
  const txBuilder = new Transaction();
  // eslint-disable-next-line @typescript-eslint/no-unused-vars



  
  //const walletPrivateKey = Keypair.fromSecretKey(bs58.decode(privateKey));
  const owner = walletPrivateKey.publicKey;
  const token = new PublicKey(tokenAddress);

  const { bondingCurve, associatedBondingCurve , vault } = getBondingCurveAddresses(tokenAddress,owner,new PublicKey(devPubKey));
  console.log('boding assoc mint:',bondingCurve,associatedBondingCurve,tokenAddress);

  const tokenAccountAddress = await getAssociatedTokenAddress(token, owner, false);

  if (buyMore==0) {
  txBuilder.add(
     createAssociatedTokenAccountInstruction(walletPrivateKey.publicKey, tokenAccountAddress, walletPrivateKey.publicKey, token)
   );
  }

  const tokenAccount = tokenAccountAddress;


  const solInLamports = amount * LAMPORTS_PER_SOL;
  const tokenOut = Math.floor((solInLamports * virtual_token_reserves) / virtual_sol_reserves);
  //const tokenOut = 100000 * 1000000;

  
  const amountWithSlippage = amount * 2;
  const maxSolCost = Math.floor(amountWithSlippage * LAMPORTS_PER_SOL);
  const ASSOCIATED_USER = tokenAccount;
  const USER = owner;
  const BONDING_CURVE = new PublicKey(bondingCurve);
  const ASSOCIATED_BONDING_CURVE = new PublicKey(associatedBondingCurve);

  const keys = [
    { pubkey: GLOBAL, isSigner: false, isWritable: false },
    { pubkey: FEE_RECIPIENT2, isSigner: false, isWritable: true },
    { pubkey: token, isSigner: false, isWritable: false },
    { pubkey: BONDING_CURVE, isSigner: false, isWritable: true },
    { pubkey: ASSOCIATED_BONDING_CURVE, isSigner: false, isWritable: true },
    { pubkey: ASSOCIATED_USER, isSigner: false, isWritable: true },
    { pubkey: USER, isSigner: false, isWritable: true },
    { pubkey: SystemProgram.programId, isSigner: false, isWritable: false },
    { pubkey: TOKEN_PROGRAM_ID, isSigner: false, isWritable: false },
    { pubkey: vault, isSigner: false, isWritable: true },
    { pubkey: PUMP_FUN_ACCOUNT, isSigner: false, isWritable: false },
    { pubkey: PUMP_FUN_PROGRAM, isSigner: false, isWritable: false }
  ];
  const data = Buffer.concat([bufferFromUInt64('16927863322537952870'), bufferFromUInt64(tokenOut), bufferFromUInt64(maxSolCost)]);

  const instruction = new TransactionInstruction({
    keys: keys,
    programId: PUMP_FUN_PROGRAM,
    data: data
  });
  txBuilder.add(instruction);

  return txBuilder ;
}

export function getAssociatedTokenAddressSync(
  mint: PublicKey,
  owner: PublicKey,
  allowOwnerOffCurve = false,
  programId = TOKEN_PROGRAM_ID,
  associatedTokenProgramId = ASSOCIATED_TOKEN_PROGRAM_ID,
): PublicKey {
  if (!allowOwnerOffCurve && !PublicKey.isOnCurve(owner.toBuffer())) {
    throw new Error('TokenOwnerOffCurveError');
  }

  const [address] = PublicKey.findProgramAddressSync(
    [owner.toBuffer(), programId.toBuffer(), mint.toBuffer()],
    associatedTokenProgramId
  );

  return address;
}

export async function getSellInstructionNoRPC( privateKey: string, tokenAddress: string, tokenBalance: number,payer: Keypair,devPubKey: string) {
  const startTime = performance.now();
  
  //const payer = Keypair.fromSecretKey(bs58.decode(privateKey));
  const owner = payer.publicKey;
  const mint = new PublicKey(tokenAddress);
  const txBuilder = new Transaction();

  const { bondingCurve, associatedBondingCurve,vault } = getBondingCurveAddresses(tokenAddress,payer.publicKey, new PublicKey(devPubKey));

  const minSolOutput = 0;
 
  const tokenAccountAddress = getAssociatedTokenAddressSync(mint, owner, false);
  
  //const tokenAccountAddress = await getAssociatedTokenAddress(mint, owner, false);

  const tokenAccount = tokenAccountAddress;
 console.log("create vault:",vault);
  const keys = [
    { pubkey: GLOBAL, isSigner: false, isWritable: false },
    { pubkey: FEE_RECIPIENT2, isSigner: false, isWritable: true },
    { pubkey: mint, isSigner: false, isWritable: false },
    { pubkey: bondingCurve, isSigner: false, isWritable: true },
    { pubkey: associatedBondingCurve, isSigner: false, isWritable: true },
    { pubkey: tokenAccount, isSigner: false, isWritable: true },
    { pubkey: owner, isSigner: false, isWritable: true },
    { pubkey: SYSTEM_PROGRAM_ID, isSigner: false, isWritable: false },
    { pubkey: vault, isSigner: false, isWritable: true },
    { pubkey: TOKEN_PROGRAM_ID, isSigner: false, isWritable: false },
    { pubkey: PUMP_FUN_ACCOUNT, isSigner: false, isWritable: false },
    { pubkey: PUMP_FUN_PROGRAM, isSigner: false, isWritable: false }
  ];

  const data = Buffer.concat([bufferFromUInt64('12502976635542562355'), bufferFromUInt64(tokenBalance), bufferFromUInt64(minSolOutput)]);

  const sellInstruction = new TransactionInstruction({
    keys: keys,
    programId: PUMP_FUN_PROGRAM,
    data: data
  });

  // Add the sell instruction to the transaction
  txBuilder.add(sellInstruction);

  // Create and add the close token account instruction
  
  // Enable if speed is the same 
  //const closeTokenAccountInstruction = createCloseTokenAccountInstruction(tokenAccount, owner, owner);
  //txBuilder.add(closeTokenAccountInstruction);

  const endTime = performance.now();
  console.log(`getSellInstructionNoRPC execution time: ${endTime - startTime} ms`);
  
  return txBuilder;
}

