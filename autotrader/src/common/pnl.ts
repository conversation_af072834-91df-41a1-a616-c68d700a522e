
import { batchUpdateTraderStats } from "./db";

// Interface to define a single trade
interface Trade {
  mint: string;           // Token address
  solAmount: number;      // Amount of SOL in the trade
  tokenAmount: number;    // Amount of tokens in the trade
  timestamp: number;      // When the trade happened
  price: number;          // Price at which the trade happened
  isBuy: boolean;         // If true, user bought tokens; if false, user sold tokens
  signature?: string;     // Transaction signature
}

// Interface to define token-specific PNL
interface TokenPnl {
  mint: string;           // Token address
  symbol: string;         // Token symbol, if available
  totalBuys: number;      // Total buys in SOL
  totalSells: number;     // Total sells in SOL
  totalBuyTxs: number;    // Number of buy transactions
  totalSellTxs: number;   // Number of sell transactions
  tokensBought: number;   // Total tokens bought
  tokensSold: number;     // Total tokens sold
  tokensHeld: number;     // Current balance of tokens
  realizedPnl: number;    // Realized PNL in SOL
  unrealizedPnl: number;  // Unrealized PNL in SOL based on current price
  lastPrice: number;      // Last known price of the token
  firstTradeAt: number;   // Timestamp of first trade
  lastTradeAt: number;    // Timestamp of last trade
  avgBuyAge: number;      // Average age of token at buy time (in seconds)
  trades: Trade[];        // All trades for this token
}

// Interface to define a trader's overall performance
interface TraderPnl {
  address: string;             // Trader's wallet address
  totalTrades: number;         // Total number of trades
  totalTokens: number;         // Total number of tokens traded
  totalBuys: number;           // Total SOL spent on buys
  totalSells: number;          // Total SOL received from sells
  realizedPnl: number;         // Total realized PNL across all tokens
  unrealizedPnl: number;       // Total unrealized PNL across all tokens
  tokensPnl: Map<string, TokenPnl>; // PNL for each token
  // Metrics
  winningTokens: number;       // Number of tokens with positive PNL
  losingTokens: number;        // Number of tokens with negative PNL
  successRate: number;         // Winning tokens / Total tokens
  roi: number;                 // (realizedPnl + unrealizedPnl) / totalBuys
  averageHoldTime: number;     // Average holding time in seconds
  avgBuyAge: number;           // Average age of tokens at buy time (in seconds)
  isProfitableTrader: boolean; // Is the trader profitable overall
  minTrades: number;           // Minimum number of trades threshold
  lastUpdateTime: number;      // Last time this trader's PNL was updated
}

// Class to manage and calculate PNL for all traders
export class PnlTracker {
  private traders: Map<string, TraderPnl> = new Map();
  private tokenSymbols: Map<string, string> = new Map(); // Map mint address to symbol
  private minTradesThreshold: number = 5;  // Minimum trades to consider metrics reliable
  private successRateThreshold: number = 60; // Success rate threshold (percentage)
  private roiThreshold: number = 30;      // ROI threshold (percentage)

  constructor(minTradesThreshold: number = 5, successRateThreshold: number = 60, roiThreshold: number = 30) {
    this.minTradesThreshold = minTradesThreshold;
    this.successRateThreshold = successRateThreshold;
    this.roiThreshold = roiThreshold;
  }

  // Set a token's symbol (useful for reporting)
  public setTokenSymbol(mint: string, symbol: string): void {
    this.tokenSymbols.set(mint, symbol);
  }

  // Process a new trade event
  public processTrade(
    user: string,
    mint: string,
    isBuy: boolean,
    solAmount: number,
    tokenAmount: number,
    price: number,
    timestamp: number = Date.now(),
    age: number,
    signature?: string
  ): void {
    // Create a new trade
    const trade: Trade = {
      mint,
      solAmount,
      tokenAmount,
      timestamp,
      price,
      isBuy,
      signature
    };

    // Get or create trader PNL data
    if (!this.traders.has(user)) {
      this.traders.set(user, this.createTraderPnl(user));
    }
    const traderPnl = this.traders.get(user)!;

    // Get or create token PNL data
    if (!traderPnl.tokensPnl.has(mint)) {
      traderPnl.tokensPnl.set(mint, this.createTokenPnl(mint));
    }
    const tokenPnl = traderPnl.tokensPnl.get(mint)!;

    // Update token PNL with the new trade
    this.updateTokenPnl(tokenPnl, trade, age);

    // Update trader overall PNL
    this.updateTraderPnl(traderPnl);
  }

  // Create a new token PNL object
  private createTokenPnl(mint: string): TokenPnl {
    return {
      mint,
      symbol: this.tokenSymbols.get(mint) || mint.slice(0, 6) + "...",
      totalBuys: 0,
      totalSells: 0,
      totalBuyTxs: 0,
      totalSellTxs: 0,
      tokensBought: 0,
      tokensSold: 0,
      tokensHeld: 0,
      realizedPnl: 0,
      unrealizedPnl: 0,
      lastPrice: 0,
      firstTradeAt: 0,
      lastTradeAt: 0,
      avgBuyAge: 0,
      trades: []
    };
  }

  // Create a new trader PNL object
  private createTraderPnl(address: string): TraderPnl {
    return {
      address,
      totalTrades: 0,
      totalTokens: 0,
      totalBuys: 0,
      totalSells: 0,
      realizedPnl: 0,
      unrealizedPnl: 0,
      tokensPnl: new Map(),
      winningTokens: 0,
      losingTokens: 0,
      successRate: 0,
      roi: 0,
      averageHoldTime: 0,
      avgBuyAge: 0,
      isProfitableTrader: false,
      minTrades: this.minTradesThreshold,
      lastUpdateTime: Date.now()
    };
  }

  // Update token PNL with a new trade
  private updateTokenPnl(tokenPnl: TokenPnl, trade: Trade, age: number): void {
    // Add to trades history
    tokenPnl.trades.push(trade);
    
    // Update timestamp info
    if (tokenPnl.firstTradeAt === 0 || trade.timestamp < tokenPnl.firstTradeAt) {
      tokenPnl.firstTradeAt = trade.timestamp;
    }
    if (trade.timestamp > tokenPnl.lastTradeAt) {
      tokenPnl.lastTradeAt = trade.timestamp;
    }

    // Update trade counters
    if (trade.isBuy) {
      tokenPnl.totalBuys += trade.solAmount;
      tokenPnl.totalBuyTxs++;
      tokenPnl.tokensBought += trade.tokenAmount;
      tokenPnl.tokensHeld += trade.tokenAmount;
      
      // Update average buy age
      // Calculate weighted average: (currentAvg * currentCount + newValue) / (currentCount + 1)
      if (tokenPnl.totalBuyTxs > 1) {
        tokenPnl.avgBuyAge = ((tokenPnl.avgBuyAge * (tokenPnl.totalBuyTxs - 1)) + age) / tokenPnl.totalBuyTxs;
      } else {
        tokenPnl.avgBuyAge = age;
      }
    } else {
      tokenPnl.totalSells += trade.solAmount;
      tokenPnl.totalSellTxs++;
      tokenPnl.tokensSold += trade.tokenAmount;
      tokenPnl.tokensHeld -= trade.tokenAmount;
      
      // Calculate realized PNL for this sell
      // Average cost basis for sold tokens
      const avgCostPerToken = tokenPnl.totalBuys / tokenPnl.tokensBought;
      const costBasisForSoldTokens = avgCostPerToken * trade.tokenAmount;
      const realizedPnlForTrade = trade.solAmount - costBasisForSoldTokens;
      
      tokenPnl.realizedPnl += realizedPnlForTrade;
    }

    // Update last price
    tokenPnl.lastPrice = trade.price;
    
    // Calculate unrealized PNL
    if (tokenPnl.tokensHeld > 0 && tokenPnl.tokensBought > 0) {
      const avgCostPerToken = tokenPnl.totalBuys / tokenPnl.tokensBought;
      const currentValue = tokenPnl.tokensHeld * tokenPnl.lastPrice;
      const costBasis = tokenPnl.tokensHeld * avgCostPerToken;
      tokenPnl.unrealizedPnl = currentValue - costBasis;
    } else {
      tokenPnl.unrealizedPnl = 0;
    }
  }

  // Update trader's overall PNL
  private updateTraderPnl(trader: TraderPnl): void {
    trader.totalTokens = trader.tokensPnl.size;
    trader.totalTrades = 0;
    trader.totalBuys = 0;
    trader.totalSells = 0;
    trader.realizedPnl = 0;
    trader.unrealizedPnl = 0;
    trader.winningTokens = 0;
    trader.losingTokens = 0;
    let totalHoldTime = 0;
    let holdTimeCount = 0;
    let totalBuyAge = 0;
    let buyAgeCount = 0;

    // First calculate PNL for each token individually
    const tokenPnlResults = [];
    
    // Recalculate totals from token PNLs
    for (const [mint, tokenPnl] of trader.tokensPnl) {
      trader.totalTrades += tokenPnl.totalBuyTxs + tokenPnl.totalSellTxs;
      trader.totalBuys += tokenPnl.totalBuys;
      trader.totalSells += tokenPnl.totalSells;
      
      // Store individual token PNL results
      const tokenTotalPnl = tokenPnl.realizedPnl + tokenPnl.unrealizedPnl;
      tokenPnlResults.push({
        mint,
        symbol: tokenPnl.symbol,
        realizedPnl: tokenPnl.realizedPnl,
        unrealizedPnl: tokenPnl.unrealizedPnl,
        totalPnl: tokenTotalPnl,
        isProfitable: tokenTotalPnl > 0,
        avgBuyAge: tokenPnl.avgBuyAge
      });
      
      // Add to total PNL
      trader.realizedPnl += tokenPnl.realizedPnl;
      trader.unrealizedPnl += tokenPnl.unrealizedPnl;
      
      // Calculate if token is profitable
      if (tokenTotalPnl > 0) {
        trader.winningTokens++;
      } else if (tokenTotalPnl < 0) {
        trader.losingTokens++;
      }
      
      // Calculate hold time for sells
      const sellTrades = tokenPnl.trades.filter(t => !t.isBuy);
      for (const sellTrade of sellTrades) {
        // Find the most recent buy before this sell
        const buyTrades = tokenPnl.trades.filter(t => 
          t.isBuy && t.timestamp < sellTrade.timestamp
        );
        
        if (buyTrades.length > 0) {
          // Get the most recent buy
          const latestBuy = buyTrades.reduce((latest, trade) => 
            trade.timestamp > latest.timestamp ? trade : latest
          , buyTrades[0]);
          
          // Calculate hold time
          const holdTime = sellTrade.timestamp - latestBuy.timestamp;
          totalHoldTime += holdTime;
          holdTimeCount++;
        }
      }
      
      // Add to average buy age calculation if token has buy transactions
      if (tokenPnl.totalBuyTxs > 0) {
        totalBuyAge += tokenPnl.avgBuyAge * tokenPnl.totalBuyTxs;
        buyAgeCount += tokenPnl.totalBuyTxs;
      }
    }
    
    // Calculate metrics
    trader.successRate = trader.totalTokens > 0 
      ? (trader.winningTokens / trader.totalTokens) * 100 
      : 0;
    
    trader.roi = trader.totalBuys > 0 
      ? ((trader.realizedPnl + trader.unrealizedPnl) / trader.totalBuys) * 100 
      : 0;
    
    trader.averageHoldTime = holdTimeCount > 0 
      ? totalHoldTime / holdTimeCount 
      : 0;
    
    trader.avgBuyAge = buyAgeCount > 0
      ? totalBuyAge / buyAgeCount
      : 0;
    
    trader.isProfitableTrader = 
      trader.totalTrades >= this.minTradesThreshold && 
      trader.successRate >= this.successRateThreshold && 
      trader.roi >= this.roiThreshold;
    
    trader.lastUpdateTime = Date.now();
  }

  // Get all profitable traders that meet the thresholds
  public getProfitableTraders(): TraderPnl[] {
    const profitableTraders: TraderPnl[] = [];
    
    for (const [_, trader] of this.traders) {
      if (trader.isProfitableTrader) {
        profitableTraders.push(trader);
      }
    }
    
    // Sort by ROI (highest first)
    profitableTraders.sort((a, b) => b.roi - a.roi);
    
    return profitableTraders;
  }

  // Get a trader's PNL by address
  public getTraderPnl(address: string): TraderPnl | undefined {
    return this.traders.get(address);
  }

  // Get all traders
  public getAllTraders(): TraderPnl[] {
    return Array.from(this.traders.values());
  }

  // Print a trader's PNL details
  public printTraderPnl(address: string): void {
    const trader = this.traders.get(address);
    if (!trader) {
      console.log(`Trader ${address} not found`);
      return;
    }
    
    console.log(`\n===== Trader ${address.slice(0, 8)}... PNL =====`);
    console.log(`Total trades: ${trader.totalTrades}`);
    console.log(`Total unique tokens: ${trader.totalTokens}`);
    console.log(`Total spent on buys: ${(trader.totalBuys / 1e9).toFixed(4)} SOL`);
    console.log(`Total received from sells: ${(trader.totalSells / 1e9).toFixed(4)} SOL`);
    console.log(`Realized PNL: ${(trader.realizedPnl / 1e9).toFixed(4)} SOL`);
    console.log(`Unrealized PNL: ${(trader.unrealizedPnl / 1e9).toFixed(4)} SOL`);
    console.log(`Total PNL: ${((trader.realizedPnl + trader.unrealizedPnl) / 1e9).toFixed(4)} SOL`);
    console.log(`Success rate: ${trader.successRate.toFixed(2)}%`);
    console.log(`ROI: ${trader.roi.toFixed(2)}%`);
    console.log(`Average hold time: ${(trader.averageHoldTime / 1000 / 60).toFixed(2)} minutes`);
    console.log(`Average buy age: ${(trader.avgBuyAge / 60).toFixed(2)} minutes`);
    console.log(`Profitable trader: ${trader.isProfitableTrader ? 'YES' : 'NO'}`);
    
    console.log(`\n--- Token PNLs ---`);
    for (const [mint, tokenPnl] of trader.tokensPnl) {
      const symbol = tokenPnl.symbol;
      const totalPnl = (tokenPnl.realizedPnl + tokenPnl.unrealizedPnl) / 1e9;
      console.log(`${symbol}: ${totalPnl.toFixed(4)} SOL (${tokenPnl.totalBuyTxs + tokenPnl.totalSellTxs} trades, avg buy age: ${(tokenPnl.avgBuyAge / 60).toFixed(2)} min)`);
    }
  }

  // Print all profitable traders
  public async printProfitableTraders(): Promise<void> {
    const profitableTraders = this.getProfitableTraders();
    
    console.log(`\n===== Profitable Traders (${profitableTraders.length}) =====`);
    console.log(`Min trades: ${this.minTradesThreshold}, Success rate threshold: ${this.successRateThreshold}%, ROI threshold: ${this.roiThreshold}%`);
    
    // Prepare data for batch update
    const tradersStats = profitableTraders.map(trader => ({
      address: trader.address,
      totalTrades: trader.totalTrades,
      totalTokens: trader.totalTokens,
      totalBuys: parseFloat((trader.totalBuys / 1e9).toFixed(4)),
      totalSells: parseFloat((trader.totalSells / 1e9).toFixed(4)),
      realizedPnl: parseFloat((trader.realizedPnl / 1e9).toFixed(4)),
      unrealizedPnl: parseFloat((trader.unrealizedPnl / 1e9).toFixed(4)),
      totalPnl: parseFloat(((trader.realizedPnl + trader.unrealizedPnl) / 1e9).toFixed(4)),
      successRate: parseFloat(trader.successRate.toFixed(2)),
      roi: parseFloat(trader.roi.toFixed(2)),
      averageHoldTime: parseFloat((trader.averageHoldTime / 1000 ).toFixed(2)),
      avgBuyAge: parseFloat((trader.avgBuyAge / 60).toFixed(2)), // Convert to minutes
      isProfitableTrader: trader.isProfitableTrader 
    }));

    // Update database
    await batchUpdateTraderStats(tradersStats, 20);
    
    for (const trader of profitableTraders) {
      console.log(`\n https://gmgn.ai/sol/address/${trader.address} | Trades: ${trader.totalTrades} | Success: ${trader.successRate.toFixed(2)}% | ROI: ${trader.roi.toFixed(2)}% | PNL: ${((trader.realizedPnl + trader.unrealizedPnl) / 1e9).toFixed(4)} SOL | Avg Buy Age: ${(trader.avgBuyAge / 60).toFixed(2)} min`);
    }
  }

  // Set tracking thresholds
  public setThresholds(minTrades: number, successRate: number, roi: number): void {
    this.minTradesThreshold = minTrades;
    this.successRateThreshold = successRate;
    this.roiThreshold = roi;
    
    // Update all traders with new thresholds
    for (const [_, trader] of this.traders) {
      trader.minTrades = minTrades;
      this.updateTraderPnl(trader);
    }
  }
}

// Export a singleton instance to be used throughout the application
export const pnlTracker = new PnlTracker(5, 60, 30);
