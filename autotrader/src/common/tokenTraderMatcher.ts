/**
 * Simple functions to track users trading the same tokens from a stream
 * and get results whenever needed
 */

// Types
type UserAddress = string;
type TokenAddress = string;

// Main class to track trades and find matches
class TokenTracker {
  // Data structures for quick lookups
  private userToTokens: Map<UserAddress, Set<TokenAddress>> = new Map();
  private tokenToUsers: Map<TokenAddress, Set<UserAddress>> = new Map();
  private minCommonTokens: number;
  
  constructor(minCommonTokens: number = 3) {
    this.minCommonTokens = minCommonTokens;
  }
  
  /**
   * Add a single trade to the dataset
   * @param tokenAddress The token being traded
   * @param userAddress The user making the trade
   */
  addTradeData(tokenAddress: TokenAddress, userAddress: UserAddress): void {
    // Add to user-to-tokens mapping
    if (!this.userToTokens.has(userAddress)) {
      this.userToTokens.set(userAddress, new Set());
    }
    this.userToTokens.get(userAddress)!.add(tokenAddress);
    
    // Add to token-to-users mapping
    if (!this.tokenToUsers.has(tokenAddress)) {
      this.tokenToUsers.set(tokenAddress, new Set());
    }
    this.tokenToUsers.get(tokenAddress)!.add(userAddress);
    
    // Optionally, you could trigger a check for matches here
    // this.findMatches();
  }
  
  /**
   * Get all users who have traded at least minCommonTokens in common
   * @returns Array of matches with user pairs and their common tokens
   */
  getResults(): MatchResult[] {
    const results: MatchResult[] = [];
    const processedPairs = new Set<string>();
    
    // Get all users
    const users = Array.from(this.userToTokens.keys());
    
    // Compare each pair of users
    for (let i = 0; i < users.length; i++) {
      const user1 = users[i];
      const tokens1 = this.userToTokens.get(user1)!;
      
      for (let j = i + 1; j < users.length; j++) {
        const user2 = users[j];
        const tokens2 = this.userToTokens.get(user2)!;
        
        // Create a unique pair key to avoid duplication
        const pairKey = [user1, user2].sort().join(':');
        if (processedPairs.has(pairKey)) continue;
        processedPairs.add(pairKey);
        
        // Find common tokens
        const commonTokens = new Set<TokenAddress>();
        for (const token of tokens1) {
          if (tokens2.has(token)) {
            commonTokens.add(token);
          }
        }
        
        // If they have enough tokens in common, add to results
        if (commonTokens.size >= this.minCommonTokens) {
          results.push({
            users: [user1, user2],
            commonTokens: Array.from(commonTokens),
            commonTokenCount: commonTokens.size
          });
        }
      }
    }
    
    // Sort results by number of common tokens (most common first)
    results.sort((a, b) => b.commonTokenCount - a.commonTokenCount);
    
    return results;
  }
  
  /**
   * Get matches for a specific user
   * @param userAddress The user to find matches for
   * @returns Array of matches with other users
   */
  getResultsForUser(userAddress: UserAddress): MatchResult[] {
    const results: MatchResult[] = [];
    
    // If user doesn't exist, return empty array
    if (!this.userToTokens.has(userAddress)) {
      return results;
    }
    
    const userTokens = this.userToTokens.get(userAddress)!;
    const allUsers = Array.from(this.userToTokens.keys());
    
    // Compare with each other user
    for (const otherUser of allUsers) {
      // Skip comparing with self
      if (otherUser === userAddress) continue;
      
      const otherTokens = this.userToTokens.get(otherUser)!;
      
      // Find common tokens
      const commonTokens = new Set<TokenAddress>();
      for (const token of userTokens) {
        if (otherTokens.has(token)) {
          commonTokens.add(token);
        }
      }
      
      // If they have enough tokens in common, add to results
      if (commonTokens.size >= this.minCommonTokens) {
        results.push({
          users: [userAddress, otherUser],
          commonTokens: Array.from(commonTokens),
          commonTokenCount: commonTokens.size
        });
      }
    }
    
    // Sort results by number of common tokens (most common first)
    results.sort((a, b) => b.commonTokenCount - a.commonTokenCount);
    
    return results;
  }
  
  /**
   * Print results to console
   */
  printResults(): void {
    const results = this.getResults();
    console.log(`Found ${results.length} matches with at least ${this.minCommonTokens} common tokens:`);
    
    results.forEach((match, index) => {
      console.log(`\nMatch #${index + 1}:`);
      console.log(`Users: ${match.users[0]} and ${match.users[1]}`);
      console.log(`Common tokens (${match.commonTokenCount}): ${match.commonTokens.join(', ')}`);
    });
  }
  
  /**
   * Get statistics about the current dataset
   */
  getStats(): {
    userCount: number;
    tokenCount: number;
    tradeCount: number;
  } {
    // Calculate total trades (sum of all user-token pairs)
    let tradeCount = 0;
    for (const tokens of this.userToTokens.values()) {
      tradeCount += tokens.size;
    }
    
    return {
      userCount: this.userToTokens.size,
      tokenCount: this.tokenToUsers.size,
      tradeCount
    };
  }
  
  /**
   * Clear all data
   */
  reset(): void {
    this.userToTokens.clear();
    this.tokenToUsers.clear();
  }
}

// Type definition for match results
type MatchResult = {
  users: UserAddress[];
  commonTokens: TokenAddress[];
  commonTokenCount: number;
};

/**
 * Example usage
 */

// Create the tracker
const tracker = new TokenTracker(2); // Match when users have at least 2 tokens in common

// Function to add trade data
function addTradeData(tokenAddress: string, userAddress: string): void {
  tracker.addTradeData(tokenAddress, userAddress);
  console.log(`Added trade: User ${userAddress} trading token ${tokenAddress}`);
}

// Function to get and print results
function getResults(): MatchResult[] {
  const results = tracker.getResults();
  console.log(`Found ${results.length} matches:`);
  
  results.forEach((match, index) => {
    console.log(`\nMatch #${index + 1}:`);
    console.log(`Users: ${match.users[0]} and ${match.users[1]}`);
    console.log(`Common tokens (${match.commonTokenCount}): ${match.commonTokens.join(', ')}`);
  });
  
  return results;
}

// Export the functions and class
export {
  addTradeData,
  getResults,
  TokenTracker
};