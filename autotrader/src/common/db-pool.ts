import mysql from 'mysql2/promise';
import { MYSQL_CONFIG } from './config';

// Create a connection pool that can be reused across the application
let pool: mysql.Pool | null = null;

export function getDbPool(): mysql.Pool {
  if (!pool) {
    pool = mysql.createPool({
      ...MYSQL_CONFIG,
      waitForConnections: true,
      connectionLimit: 10,
      queueLimit: 0,
      enableKeepAlive: true,
      keepAliveInitialDelay: 0
    });
    
    // Add event listeners for better error handling
    pool.on('connection', () => {
      console.log('New connection established with the database');
    });
    
    // Handle connection errors by testing a connection
    pool.getConnection()
      .then((connection) => {
        connection.release();
      })
      .catch((err) => {
        console.error('Database connection error:', err);
        if (err.code === 'PROTOCOL_CONNECTION_LOST') {
          console.log('Database connection lost. Recreating pool...');
          pool = null;
        }
      });
  }
  
  return pool;
}

export async function closeDbPool(): Promise<void> {
  if (pool) {
    await pool.end();
    pool = null;
  }
}

// Helper function to execute queries with the pool
export async function executeQuery<T>(
  query: string, 
  params: any[] = []
): Promise<T> {
  const dbPool = getDbPool();
  try {
    const [results] = await dbPool.execute(query, params);
    return results as T;
  } catch (error) {
    console.error('Database query error:', error);
    throw error;
  }
}
