import {
  Connection,
  SystemProgram,
  Transaction,
  sendAndConfirmTransaction,
  Keypair,
  ComputeBudgetProgram,
  PublicKey,
  SendTransactionError,
  sendAndConfirmRawTransaction,
  ParsedTransactionWithMeta,
  VersionedTransactionResponse,
  ConfirmedSignatureInfo,

} from "@solana/web3.js";

import {
  createCloseAccountInstruction,
  getAccount,

} from '@solana/spl-token';


import bs58 from "bs58";
import fs from "fs";
// Assuming getTokenData is in test.ts
import { getAssociatedTokenAddress } from '@solana/spl-token';
import { getBuyInstruction, getSellInstruction } from "./trade"; // Import the buy and sell functions

import { TokenInfo } from '@solana/spl-token-registry';
import { getSellInstructionNoRPC } from "../common/trade";
import {
  VersionedTransaction,
  TransactionSignature,
} from '@solana/web3.js';

import { RPC_URL, RPC_URL_CS, PRIVATE_KEY } from "./config";
import axios from 'axios';
import { Redis } from 'ioredis';
import { addRedisString, getRedisClient } from './redis';

import { JITO_MAINNET_TIP_ACCOUNTS } from "./config";
import { processPumpTransaction,processPumpTransactionForTraining , decodePumpFunTransactionForTraining } from "../common/pump";

const LAMPORTS_PER_SOL = Number(1_000_000_000);
export const GLOBAL = new PublicKey('4wTV1YmiEkRvAtNtsSGPtUrqRYQMe5SKy2uB4Jjaxnjf');
export const FEE_RECIPIENT = new PublicKey('CebN5WGQ4jvEPvsVU4EoHEpgzq1VV7AbicfhtW4xC9iM');
export const FEE_RECIPIENT2 = new PublicKey('7VtfL8fvgNfhz17qKRMjzQEXgbdpnHHHQRh54R9jP2RJ');
export const TOKEN_PROGRAM_ID = new PublicKey('TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA');
export const ASSOC_TOKEN_ACC_PROG = new PublicKey('ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL');
export const RENT = new PublicKey('SysvarRent111111111111111111111111111111111');
export const PUMP_FUN_PROGRAM = new PublicKey('6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P');
export const PUMP_FUN_ACCOUNT = new PublicKey('Ce6TQqeHC9p8KetsN6JsjHK7UTZk7nasjjnr7XxXp9F1');
const TOKEN_METADATA_PROGRAM_ID = new PublicKey("metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s");
export const SYSTEM_PROGRAM_ID = SystemProgram.programId;

const ignoreMints = ["So11111111111111111111111111111111111111112", "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v", "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB"];
import dotenv from "dotenv";
dotenv.config();

const verifyRetryPause = 500;
const numOfRetries = 30;

const HeliusURL = "https://mainnet.helius-rpc.com/?api-key=cf3aa81f-7796-401b-a170-5567272f5f65";
const connection = new Connection(HeliusURL);
const connection2 = new Connection(HeliusURL);


function decodeString(buffer: Buffer, offset: number, length: number): string {
    return buffer.slice(offset, offset + length).toString('utf8').replace(/\0/g, '');
}

export async function getTokenMetadata(mintAddress: string) {
    const mintPubkey = new PublicKey(mintAddress);

    const metadata_seeds = [
        Buffer.from('metadata'),
        TOKEN_METADATA_PROGRAM_ID.toBuffer(),
        mintPubkey.toBuffer(),
    ];
    const [metadata_pda] = PublicKey.findProgramAddressSync(metadata_seeds, TOKEN_METADATA_PROGRAM_ID);

    
    console.log(metadata_pda.toBase58());
    const accountInfo = await connection.getAccountInfo(metadata_pda);
    if (!accountInfo || !accountInfo.data) {
        throw new Error("No metadata account found for this mint.");
    }
    const buf = Buffer.from(accountInfo.data);

    // Offsets are per https://github.com/metaplex-foundation/mpl-token-metadata/blob/master/programs/token-metadata/program/src/state/metadata.rs
    let offset = 1; // key
    offset += 32; // update_authority
    offset += 32; // mint
    offset += 4;  // name string length prefix (always 4, name is fixed 32 bytes)
    const name = decodeString(buf, offset, 32);
    offset += 32;
    offset += 4; // symbol length prefix (always 4, symbol is fixed 10 bytes)
    const symbol = decodeString(buf, offset, 10);
    offset += 10;
    offset += 4; // uri length prefix (always 4, uri is fixed 200 bytes)
    const uri = decodeString(buf, offset, 200);

    return { name, symbol, uri };
}


export async function createAccount() {
  const space = 0; // any extra space in the account
  const rentLamports = await connection.getMinimumBalanceForRentExemption(space);
  console.log("Minimum balance for rent exception:", rentLamports);

  const privateKey = bs58.decode(PRIVATE_KEY);
  const signer = Keypair.fromSecretKey(privateKey);

  const balance = await connection.getBalance(signer.publicKey);
  console.log("Signer balance:", balance / LAMPORTS_PER_SOL, "SOL");

  if (balance < rentLamports) {
    throw new Error("Insufficient funds in the signer account.");
  }

  const newAccountKeypair = Keypair.generate();
  console.log("New account address:", newAccountKeypair.publicKey.toBase58());

  const newAccountPrivateKey = bs58.encode(newAccountKeypair.secretKey);
  fs.writeFileSync(`account-${newAccountKeypair.publicKey.toBase58()}.txt`, newAccountPrivateKey);

  const { blockhash } = await connection.getRecentBlockhash();

  const transaction = new Transaction().add(
    SystemProgram.createAccount({
      fromPubkey: signer.publicKey,
      newAccountPubkey: newAccountKeypair.publicKey,
      lamports: rentLamports,
      space: space,
      programId: SystemProgram.programId,
    })
  );

  transaction.feePayer = signer.publicKey;
  transaction.recentBlockhash = blockhash;

  transaction.sign(signer, newAccountKeypair);

  try {
    const signature = await sendAndConfirmTransaction(connection, transaction, [signer, newAccountKeypair], { preflightCommitment: "processed" });
    console.log("Signature:", signature);
  } catch (error) {
    if (error instanceof SendTransactionError) {
      console.error("Failed to send transaction:", error.message);
      console.error("Logs:", error.logs);
    } else {
      console.error("Failed to send transaction:", error);
    }
  }
}




export async function closeTokenAccount(mintAddress: string) {
  const mint = new PublicKey(mintAddress);

  // 1. Get Associated Token Account (ATA)
  const wallet = Keypair.fromSecretKey(bs58.decode(PRIVATE_KEY));
  const ata = await getAssociatedTokenAddress(mint, wallet.publicKey);

  try {
    const tokenAccountInfo = await getAccount(connection, ata);

    // 2. If there's a balance, you need to handle it before closing
    if (tokenAccountInfo.amount > BigInt(0)) {
      console.log(`Account has ${tokenAccountInfo.amount} tokens remaining`);
      // You might want to add code here to transfer tokens elsewhere
    }

    // 3. Create close instruction using createCloseAccountInstruction
    const closeInstruction = createCloseAccountInstruction(
      ata,               // Account to close
      wallet.publicKey,  // Destination for rent SOL
      wallet.publicKey   // Owner of the account
    );

    // 4. Send and confirm the transaction
    const transaction = new Transaction().add(closeInstruction);
    const signature = await sendAndConfirmTransaction(
      connection,
      transaction,
      [wallet]
    );

    console.log('Token account closed:', signature);
    return signature;
  } catch (err: any) {
    console.error('Error:', err.message);
    throw err;
  }
}


// export async function closeAccount(accountName: string) {
//   const privateKey =  bs58.decode(PRIVATE_KEY) ;
//   const signer = Keypair.fromSecretKey(privateKey);

//   const accountPublicKey = new PublicKey(accountName);

//   const accountPrivateKey = bs58.decode(fs.readFileSync(`account-${accountName}.txt`, 'utf8'));
//   const accountKeypair = Keypair.fromSecretKey(accountPrivateKey);

//   const transaction = new Transaction().add(
//     SystemProgram.transfer({
//       fromPubkey: accountPublicKey,
//       toPubkey: signer.publicKey,
//       lamports: await connection.getBalance(accountPublicKey),
//     })
//   );

//   transaction.feePayer = signer.publicKey;
//   const { blockhash } = await connection.getRecentBlockhash();
//   transaction.recentBlockhash = blockhash;

//   transaction.sign(signer, accountKeypair);

//   try {
//     const signature = await sendAndConfirmTransaction(connection, transaction, [signer, accountKeypair], { preflightCommitment: "processed" });
//     console.log("Account closed. Signature:", signature);
//   } catch (error) {
//     if (error instanceof SendTransactionError) {
//       console.error("Failed to send transaction:", error.message);
//       console.error("Logs:", error.logs);
//     } else {
//       console.error("Failed to send transaction:", error);
//     }
//   }
// }

export async function transferSOL(toAccount: string, amount: string, priorityLevel: PriorityLevel) {
  const fromKeypair = Keypair.fromSecretKey(bs58.decode(PRIVATE_KEY));
  const toPubkey = new PublicKey(toAccount);

  const lamports = parseFloat(amount) * LAMPORTS_PER_SOL;

  const transaction = new Transaction();
  const transferIx = SystemProgram.transfer({
    fromPubkey: fromKeypair.publicKey,
    toPubkey,
    lamports,
  });
  transaction.add(transferIx);

  let feeEstimate: PriorityFeeEstimateResult = { priorityFeeEstimate: 0 };
  if (priorityLevel !== "NONE") {
    feeEstimate = await getPriorityFeeEstimate(priorityLevel, transaction, fromKeypair);
    const computePriceIx = ComputeBudgetProgram.setComputeUnitPrice({
      microLamports: feeEstimate.priorityFeeEstimate,
    });
    transaction.add(computePriceIx);
  }

  transaction.feePayer = fromKeypair.publicKey;
  transaction.recentBlockhash = (await connection.getLatestBlockhash()).blockhash;
  transaction.sign(fromKeypair);

  try {
    const txid = await sendAndConfirmTransaction(connection, transaction, [fromKeypair], { commitment: "processed", preflightCommitment: "processed" });
    console.log(`Transaction sent successfully with signature ${txid}`);
  } catch (error) {
    if (error instanceof SendTransactionError) {
      console.error("Failed to send transaction:", error.message);
      console.error("Logs:", error.logs);
    } else {
      console.error("Failed to send transaction:", error);
    }
  }
}

export async function buyToken(tokenCA: string, amount: string, slippage: string, priorityLevel: PriorityLevel) {
  console.log("Buying", amount, "tokens");
  const privateKey = bs58.decode(PRIVATE_KEY);
  const signer = Keypair.fromSecretKey(privateKey);

  const transaction = new Transaction();
  const buyInstructionResult = await getBuyInstruction(connection2, bs58.encode(privateKey), tokenCA, parseFloat(amount), parseFloat(slippage), transaction);

  if (!buyInstructionResult) {
    console.error('Failed to get buy instruction');
    return;
  }

  const { instruction } = buyInstructionResult;

  transaction.add(instruction);

  let feeEstimate: PriorityFeeEstimateResult = { priorityFeeEstimate: 0 };
  if (priorityLevel !== "NONE") {
    feeEstimate = await getPriorityFeeEstimate(priorityLevel, transaction, signer);
    const computePriceIx = ComputeBudgetProgram.setComputeUnitPrice({
      microLamports: feeEstimate.priorityFeeEstimate,
    });
    transaction.add(computePriceIx);
  }

  transaction.feePayer = signer.publicKey;
  transaction.recentBlockhash = (await connection.getLatestBlockhash()).blockhash;
  transaction.sign(signer);

  try {
    //const txid = await sendAndConfirmTransaction(connection, transaction, [signer], { skipPreflight: true,  commitment: "processed", preflightCommitment: "processed" });
    const rawTransaction = transaction.serialize();
    const txid = await sendAndConfirmRawTransaction(connection, rawTransaction, { skipPreflight: true, commitment: "processed", preflightCommitment: "processed" });
    console.log(`Transaction sent successfully with signature ${txid}`);
    return txid;
  } catch (error) {
    if (error instanceof SendTransactionError) {
      console.error("Failed to send transaction:", error.message);
      console.error("Logs:", error.logs);
      return "";
    } else {
      console.error("Failed to send transaction:", error);
      return "";
    }
  }
}

export async function getTokenBalance(tokenCA: string) {
  const privateKey = bs58.decode(PRIVATE_KEY);
  const signer = Keypair.fromSecretKey(privateKey);
  const tokenAccountAddress = await getAssociatedTokenAddress(new PublicKey(tokenCA), signer.publicKey, false);
  let tokenAccountBalance;
  try {
    tokenAccountBalance = await connection2.getTokenAccountBalance(tokenAccountAddress);
  } catch (error) {
    console.error('Failed to get token account balance:', error);
    return null;
  }

  const tokenBalance = parseInt(tokenAccountBalance.value.amount);
  console.log("Token balance", tokenBalance, " mint:", tokenCA);
  return tokenBalance;
}



export async function sellTokens(tokenCA: string, amount: string, slippage: string, priorityLevel: PriorityLevel) {
  const privateKey = process.env.PRIVATE_KEY ? bs58.decode(process.env.PRIVATE_KEY) : new Uint8Array();
  const signer = Keypair.fromSecretKey(privateKey);

  const tokenBalance = parseFloat(amount);

  const transaction = new Transaction();
  const sellInstruction = await getSellInstruction(connection, bs58.encode(privateKey), tokenCA, tokenBalance, parseFloat(slippage));

  if (!sellInstruction) {
    console.error('Failed to get sell instruction');
    return;
  }

  transaction.add(sellInstruction);

  let feeEstimate: PriorityFeeEstimateResult = { priorityFeeEstimate: 0 };
  if (priorityLevel !== "NONE") {
    feeEstimate = await getPriorityFeeEstimate(priorityLevel, transaction, signer);
    const computePriceIx = ComputeBudgetProgram.setComputeUnitPrice({
      microLamports: feeEstimate.priorityFeeEstimate,
    });
    transaction.add(computePriceIx);
  }

  transaction.feePayer = signer.publicKey;
  transaction.recentBlockhash = (await connection.getLatestBlockhash()).blockhash;
  transaction.sign(signer);

  try {
    //const txid = await sendAndConfirmTransaction(connection, transaction, [signer], {skipPreflight: true, commitment: "processed", preflightCommitment: "processed" });

    const rawTransaction = transaction.serialize();
    const txid = await sendAndConfirmRawTransaction(connection, rawTransaction, { skipPreflight: true, commitment: "processed", preflightCommitment: "processed" });

    console.log(`Transaction sent successfully with signature ${txid}`);
    console.log(`Verifying transaction: ${txid}`);
    let retries = numOfRetries;
    while (retries > 0) {
      const verified = await verifyTransaction(txid);
      if (verified) {
        console.log(`Sold ${amount} tokens`);
        break;
      } else {
        console.log(`Verification failed, retrying... (${5 - retries + 1}/5)`);
        retries--;
        await new Promise(resolve => setTimeout(resolve, verifyRetryPause));
      }
    }
  } catch (error) {
    if (error instanceof SendTransactionError) {
      console.error("Failed to send transaction:", error.message);
      console.error("Logs:", error.logs);
    } else {
      console.error("Failed to send transaction:", error);
    }
  }
}

export async function sellTokensJito(tokenCA: string, amount: string, latestBlockHash: string,devPubKey: string) {
  const jitoTipAccount = new PublicKey('DttWaMuVvTiduZRnguLF7jNxTgiMBZ1hyAumKUiL2KRL');
  const jitoEndpoint = 'https://mainnet.block-engine.jito.wtf/api/v1/transactions';

  const privateKey = process.env.PRIVATE_KEY ? bs58.decode(process.env.PRIVATE_KEY) : new Uint8Array();
  const signer = Keypair.fromSecretKey(privateKey);

  const tokenBalance = parseFloat(amount);
  const tipLamports = 1_000;

  const transaction = new Transaction();
  const sellInstruction = await getSellInstructionNoRPC(bs58.encode(privateKey), tokenCA, tokenBalance,signer,devPubKey);

  transaction.add(
    SystemProgram.transfer({
      fromPubkey: signer.publicKey,
      toPubkey: jitoTipAccount,
      lamports: tipLamports,
    })
  );




  if (!sellInstruction) {
    console.error('Failed to get sell instruction');
    return;
  }

  transaction.add(sellInstruction);


  transaction.feePayer = signer.publicKey;
  transaction.recentBlockhash = latestBlockHash;
  transaction.sign(signer);

  const serializedTransaction = bs58.encode(transaction.serialize());

  try {
    const response = await axios.post(jitoEndpoint, {
      jsonrpc: '2.0',
      id: 1,
      method: 'sendTransaction',
      params: [serializedTransaction],
    });
    console.log('Transaction Signature:', response.data.result);
    return response.data.result;
  } catch (error) {
    if (axios.isAxiosError(error)) {
      console.error('Error sending transaction:', error.response?.data || error.message);
    } else {
      console.error('Error sending transaction:', String(error));
    }
  }

  // try {
  //   const rawTransaction = transaction.serialize();
  //   const txid = await sendAndConfirmRawTransaction(connection, rawTransaction, { skipPreflight: true,  commitment: "processed", preflightCommitment: "processed" });

  //   console.log(`Transaction sent successfully with signature ${txid}`);
  //   console.log(`Verifying transaction: ${txid}`);
  //   let retries = numOfRetries;
  //   while (retries > 0) {
  //     const verified = await verifyTransaction(txid);
  //     if (verified) {
  //       console.log(`Sold ${amount} tokens`);
  //       break;
  //     } else {
  //       console.log(`Verification failed, retrying... (${5 - retries + 1}/5)`);
  //       retries--;
  //       await new Promise(resolve => setTimeout(resolve, verifyRetryPause));
  //     }
  //   }
  // } catch (error) {
  //   if (error instanceof SendTransactionError) {
  //     console.error("Failed to send transaction:", error.message);
  //     console.error("Logs:", error.logs);
  //   } else {
  //     console.error("Failed to send transaction:", error);
  //   }
  // }
}




export async function sellTokenPercentage(tokenCA: string, percentage: string, slippage: string, priorityLevel: PriorityLevel) {
  const privateKey = process.env.PRIVATE_KEY ? bs58.decode(process.env.PRIVATE_KEY) : new Uint8Array();
  const signer = Keypair.fromSecretKey(privateKey);

  const tokenAccountAddress = await getAssociatedTokenAddress(new PublicKey(tokenCA), signer.publicKey, false);
  const tokenAccountBalance = await connection.getTokenAccountBalance(tokenAccountAddress);

  if (!tokenAccountBalance) {
    console.error('Token account not found');
    return;
  }

  const tokenBalance = parseInt(tokenAccountBalance.value.amount) * (parseFloat(percentage) / 100);
  if (tokenBalance === 0) {
    console.error('Token balance is 0');
    return;
  }
  console.log("selling", tokenBalance, "tokens");
  const transaction = new Transaction();
  const sellInstruction = await getSellInstruction(connection, bs58.encode(privateKey), tokenCA, tokenBalance, parseFloat(slippage));

  if (!sellInstruction) {
    console.error('Failed to get sell instruction');
    return;
  }

  transaction.add(sellInstruction);

  let feeEstimate: PriorityFeeEstimateResult = { priorityFeeEstimate: 0 };
  if (priorityLevel !== "NONE") {
    feeEstimate = await getPriorityFeeEstimate(priorityLevel, transaction, signer);
    const computePriceIx = ComputeBudgetProgram.setComputeUnitPrice({
      microLamports: feeEstimate.priorityFeeEstimate,
    });
    transaction.add(computePriceIx);
  }

  transaction.feePayer = signer.publicKey;
  transaction.recentBlockhash = (await connection.getLatestBlockhash()).blockhash;
  transaction.sign(signer);

  try {
    const txid = await sendAndConfirmTransaction(connection, transaction, [signer], { skipPreflight: true, commitment: "processed", preflightCommitment: "processed" });
    console.log(`Transaction sent successfully with signature ${txid}`);
    return txid;
  } catch (error) {
    if (error instanceof SendTransactionError) {
      console.error("Failed to send transaction:", error.message);
      console.error("Logs:", error.logs);
    } else {
      console.error("Failed to send transaction:", error);
    }
  }
}

export async function verifyTransaction(signature: string) {
  let transaction;
  try {
    transaction = await connection.getTransaction(signature, { maxSupportedTransactionVersion: 0 });
  } catch (error) {
    console.error('Error fetching transaction:', error);
    return false;
  }
  if (!transaction) {
    console.error('Transaction not found');
    return false;
  }
  if (!transaction.meta?.err) {
    return true;
  }
  return false;
}



type PriorityLevel = "Min" | "Low" | "Medium" | "High" | "VeryHigh" | "UnsafeMax" | "NONE";

interface PriorityFeeEstimateResult {
  priorityFeeEstimate: number;
}

async function getPriorityFeeEstimate(priorityLevel: PriorityLevel, transaction: Transaction, fromKeypair: Keypair): Promise<PriorityFeeEstimateResult> {
  transaction.feePayer = fromKeypair.publicKey;
  transaction.recentBlockhash = (await connection.getLatestBlockhash()).blockhash;

  const response = await fetch(HeliusURL, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({
      jsonrpc: "2.0",
      id: "1",
      method: "getPriorityFeeEstimate",
      params: [
        {
          transaction: bs58.encode(transaction.serialize({ requireAllSignatures: false })),
          options: { priorityLevel: priorityLevel },
        },
      ],
    }),
  });
  const data = await response.json();
  console.log("Fee in function for", priorityLevel, ":", data.result.priorityFeeEstimate);
  return data.result;
}

export async function getKeyPairFromPrivateKey(key: string) {
  return Keypair.fromSecretKey(new Uint8Array(bs58.decode(key)));
}

export function bufferFromUInt64(value: number | string) {
  const buffer = Buffer.alloc(8);
  buffer.writeBigUInt64LE(BigInt(value));
  return buffer;
}


//////////////////////


interface QuoteResponse {
  data: {
    inputMint: string;
    outputMint: string;
    amount: number;
    slippageBps: number;
    otherAmountThreshold?: string;
    swapMode?: string;
    routes?: any[];
  }
  swapTransaction: string;
}

interface SwapResponse {
  swapTransaction: string;
}



// Create keypair from private key
const keypair = Keypair.fromSecretKey(bs58.decode(PRIVATE_KEY));


// eslint-disable-next-line @typescript-eslint/no-unused-vars
async function swap(inputMint: string, outputMint: string, amount: number, slippage: number): Promise<void> {
  try {
    console.log("Swapping SOL to USDC...");
    console.log(`Wallet: ${keypair.publicKey.toString()}`);

    // Step 1: Get quote for swapping SOL to USDC
    const quoteResponse = await axios.get<QuoteResponse>('https://quote-api.jup.ag/v6/quote', {
      params: {
        inputMint: inputMint, // SOL mint address
        outputMint: outputMint, // USDC mint address
        amount: amount, // 0.01 SOL in lamports
        slippageBps: slippage // 0.5% slippage
      }
    });
    console.log("Quote received:", quoteResponse.data);

    // Step 2: Get swap transaction with our priority fee
    const swapResponse = await axios.post<SwapResponse>('https://quote-api.jup.ag/v6/swap', {
      quoteResponse: quoteResponse.data,
      userPublicKey: keypair.publicKey.toString(),
      wrapAndUnwrapSol: true,
      dynamicComputeUnitLimit: true, // Adding priority fee
      //prioritizationFeeLamports: "auto",
      prioritizationFeeLamports: {
        autoMultiplier: 2,
      },
    }, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    // Step 3: Deserialize the transaction
    const swapTransactionBuf = Buffer.from(swapResponse.data.swapTransaction, 'base64');
    const transaction = VersionedTransaction.deserialize(swapTransactionBuf);

    // Step 4: Sign the transaction
    transaction.sign([keypair]);

    // Step 5: Get the latest blockhash for confirmation
    //const bhInfo = await connection.getLatestBlockhashAndContext('processed'); // Using processed instead of finalized

    // Step 6: Simulate the transaction before sending
    //   console.log("Simulating transaction...");
    //   const simulation = await connection.simulateTransaction(transaction, { commitment: 'processed' });
    //   if (simulation.value.err) {
    //     throw new Error(`Simulation failed: ${JSON.stringify(simulation.value.err)}`);
    //   }
    //   console.log("Simulation successful");

    // Step 7: Send the transaction
    console.log("Sending transaction...");
    const signature: TransactionSignature = await connection.sendTransaction(transaction, {
      skipPreflight: true,
      preflightCommitment: 'processed'
    });
    console.log(`Transaction sent with signature: ${signature}`);
    console.log(`View transaction: https://solscan.io/tx/${signature}`);

    // Step 8: Just check transaction status without waiting for finalization
    console.log("Transaction sent! Not waiting for full confirmation.");
    console.log("Checking initial status...");

    // We'll check the status once but not wait for full confirmation

    try {
      const status = await connection.getSignatureStatus(signature, { searchTransactionHistory: true });
      console.log("Transaction status:", status.value?.confirmationStatus || "unknown");
      if (status.value?.err) {
        console.error("Transaction error:", status.value.err);
      } else {
        console.log("Transaction appears to be successful!");
      }
    } catch (err) {
      console.log("Error checking status:", err);
    }


    console.log("You can continue with other operations while the transaction settles...");

  } catch (error) {
    console.error('Error during swap:', error);
  }
}


///////////// swaps 
const RAYDIUM_SWAP_PROGRAM_ID = '675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8';

interface Token {
  symbol: string;
  decimals: number;
  address: string;
}

// interface SwapInfo {
//   fromToken: Token;
//   toToken: Token;
//   fromAmount: number;
//   toAmount: number;
// }


type SwapInfo = {
  solAmount: number;
  tokenAmount: number;
  tradeType: string;
  mint: string;
  owner: string;
};


class SolanaSwapParser {
  private connection: Connection;
  private tokenMap: Map<string, TokenInfo> = new Map();

  constructor(rpcEndpoint: string) {
    this.connection = new Connection(rpcEndpoint, 'confirmed');
  }


  // Get token info by address
  private getTokenInfo(address: string): Token {
    const tokenInfo = this.tokenMap.get(address);

    if (tokenInfo) {
      return {
        symbol: tokenInfo.symbol,
        decimals: tokenInfo.decimals,
        address
      };
    }

    // Default for unknown tokens
    return {
      symbol: address.slice(0, 4) + '...' + address.slice(-4),
      decimals: 9,
      address
    };
  }



  // Parse transaction
  async parseTransaction(redis: Redis, txSignature: string): Promise<SwapInfo | null> {
    try {
      // Fetch transaction data
      const txInfo = await this.connection.getParsedTransaction(txSignature, {
        maxSupportedTransactionVersion: 0,
      });



      if (!txInfo) {
        return null;
      }

      // Check if it's a Raydium swap


      if (this.isPumpSwap(txInfo)) {
        const pump = await processPumpTransaction(txSignature);
        return pump;
        //return null;
      }

      if (!this.isRaydiumSwap(txInfo)) {
        return null;
      }



      // Extract swap information
      const swapInfo = await this.extractSwapInfo(txInfo);
      if (!swapInfo) {
        return null;
      }
      return swapInfo;

      // const ownerAddress = txInfo.transaction.message.accountKeys[0].pubkey.toBase58();
      // const isMember = await checkRedisKey(redis, ownerAddress);
      // const  blockid = txInfo.slot;
      // if (!isMember) {

      //   return JSON.stringify({
      //     trade: swapInfo.tradeType,
      //     solAmount: (swapInfo.solAmount/1e9).toFixed(6),
      //     tokenAmount: (swapInfo.tokenAmount/1e9).toFixed(0),
      //     owner: ownerAddress,
      //     blockid: blockid,
      //     tx: txSignature
      //     });


      // }else
      // {
      //   return null;
      // }

    } catch (error) {
      if (error instanceof Error) {
        console.error('Error parsing transaction:', error);
        return null;
      } else {
        return null;
      }
    }
  }

  // Check if transaction is a Raydium swap
  private isRaydiumSwap(txInfo: ParsedTransactionWithMeta): boolean {
    const accountKeys = txInfo.transaction.message.accountKeys;
    return accountKeys.some(id =>
      id.pubkey.toBase58() === RAYDIUM_SWAP_PROGRAM_ID || id.pubkey === PUMP_FUN_PROGRAM
    );
    //return accountKeys.some(id => id.pubkey.toBase58() === RAYDIUM_SWAP_PROGRAM_ID);
  }

  private isPumpSwap(txInfo: ParsedTransactionWithMeta): boolean {
    const accountKeys = txInfo.transaction.message.accountKeys;
    //console.log("accountKeys",accountKeys);
    return accountKeys.some(id =>
      id.pubkey.toBase58() === PUMP_FUN_PROGRAM.toBase58()
    );
    //return accountKeys.some(id => id.pubkey.toBase58() === RAYDIUM_SWAP_PROGRAM_ID);
  }


  // Extract swap details from transaction
  private async extractSwapInfo(txInfo: ParsedTransactionWithMeta): Promise<SwapInfo | null> {



    const owner = txInfo.transaction.message.accountKeys[0].pubkey.toBase58();
    try {
      // Look for token balance changes to determine swap details
      if (!txInfo.meta) {
        return null;
      }
      const preTokenBalances = txInfo.meta.preTokenBalances || [];
      const postTokenBalances = txInfo.meta.postTokenBalances || [];

      let tokenCA = null;
      if (preTokenBalances) {
        for (const preTokenBalance of preTokenBalances) {
          if (!ignoreMints.includes(preTokenBalance.mint)) {
            tokenCA = preTokenBalance.mint;
            break;
          }
        }
      }
      if (!tokenCA) {
        if (postTokenBalances) {
          for (const postTokenBalance of postTokenBalances) {
            if (!ignoreMints.includes(postTokenBalance.mint)) {
              tokenCA = postTokenBalance.mint;
              break;
            }
          }
        }
      }

      if (!tokenCA) {
        return null;
      }



      let fromToken: Token | null = null;
      let toToken: Token | null = null;
      let fromAmount = 0;
      let toAmount = 0;
      let solAmount = 0;
      let tokenAmount = 0;
      let tradeType = null;


      const pre = preTokenBalances.find(p =>
        p.mint === tokenCA && p.owner === owner
      );

      const post = postTokenBalances.find(p =>
        p.mint === tokenCA && p.owner === owner
      );


      if (pre && post) {
        const preAmount = Number(pre.uiTokenAmount.amount);
        const postAmount = Number(post.uiTokenAmount.amount);
        const diff = preAmount - postAmount;

        if (diff > 0) {
          fromToken = this.getTokenInfo(tokenCA);
          fromAmount = diff;
          tradeType = 'sell';
          tokenAmount = diff;
        } else {
          tradeType = 'buy';
          fromAmount = diff;
          tokenAmount = diff;
        }
      } else {
        if (post) {
          tradeType = 'buy';
          tokenAmount = Number(post.uiTokenAmount.amount);
        }
        if (pre) {
          tradeType = 'sell';
          tokenAmount = Number(pre.uiTokenAmount.amount);
        }
      }



      if (!fromToken || !toToken) {
        const preSOL = txInfo.meta.preBalances;
        const postSOL = txInfo.meta.postBalances;

        // Compare SOL balances to detect SOL swaps
        for (let i = 0; i < preSOL.length; i++) {


          // If SOL decreased significantly (accounting for fees)

          if (preSOL[i] - postSOL[i] > 10000) {
            fromToken = {
              symbol: 'SOL',
              decimals: 9,
              address: 'SOL'
            };
            fromAmount = preSOL[i] - postSOL[i];

          }
          // If SOL increased
          else if (postSOL[i] - preSOL[i] > 0) {
            toToken = {
              symbol: 'SOL',
              decimals: 9,
              address: 'SOL'
            };
            toAmount = postSOL[i] - preSOL[i];

          }
        }
      }
      if (tradeType === 'buy') {
        solAmount = toAmount;
      } else {
        solAmount = fromAmount;
      }

      //console.log("solAmount",solAmount,"tokenAmount",tokenAmount,'tradeType',tradeType);



      if (solAmount && tokenAmount && tradeType) {
        return {
          solAmount,
          tokenAmount,
          tradeType,
          mint: tokenCA,
          owner: owner
        };
      }


      return null;
    } catch (error) {
      console.error('Error extracting swap info:', error);
      return null;
    }
  }


}


export async function processSwapTransaction(redis: Redis, tx: string) {

  const rpcEndpoint = RPC_URL
  const parser = new SolanaSwapParser(rpcEndpoint);

  const result = await parser.parseTransaction(redis, tx);



  return result;
}

export async function getTransactionsForTraining(accountAddress: string, limit?: number | 100, before?: string | undefined, after?: string | undefined): Promise<VersionedTransactionResponse[]> {
  const connection = new Connection(RPC_URL);
  const publicKey = new PublicKey(accountAddress);
  let allSignatures: ConfirmedSignatureInfo[] = [];
  let currentBefore = before;
  const transactions = [];
  while (true) {
    const signatures = await connection.getSignaturesForAddress(publicKey, { 
      before: currentBefore, 
      limit: 1000 
    });
    
    if (signatures.length === 0) {
      break; // No more signatures to fetch
    }
    
    allSignatures = allSignatures.concat(signatures);
    currentBefore = signatures[signatures.length - 1].signature;
    
    console.log("Fetched signatures:", allSignatures.length);
    
    // Optional: Add a limit check if you want to cap the total number
    if (limit && allSignatures.length >= limit) {
      allSignatures = allSignatures.slice(0, limit);
      break;
    }
  }

  console.log("Total signatures:", allSignatures.length);

  const validSignatures = [];
  for (const signature of allSignatures) {
    if (after && signature.signature === after) {
      break;
    }
    if (signature.err === null) {
      validSignatures.push(signature.signature);
    }
  }


  const batchSize = 1000;
  for (let i = 0; i < validSignatures.length; i += batchSize) {
    const batch = validSignatures.slice(i, i + batchSize);
    const batchTransactions = await connection.getTransactions(batch, {
      maxSupportedTransactionVersion: 0,
    });
    console.log("Batch transactions:", batchTransactions.length);
    for (const tx of batchTransactions) {
      if (tx) {
        const decodedTx = await decodePumpFunTransactionForTraining(tx);
        if (decodedTx) {

          transactions.push(decodedTx);
        }
      }
    }
  }


  const jsonData = JSON.stringify(transactions);
  fs.writeFileSync('data.json', jsonData);
  return [];
}

/////////////// get transacstions
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export async function getTransactions(accountAddress: string, limit?: number | 100, before?: string | undefined, after?: string | undefined): Promise<VersionedTransactionResponse[]> {
  const connection = new Connection(RPC_URL_CS);
  const publicKey = new PublicKey(accountAddress);
  const signatures = await connection.getSignaturesForAddress(publicKey, { before, limit: 50 });
  console.log("Signatures:", signatures.length);
  const validSignatures = [];
  for (const signature of signatures) {
    if (after && signature.signature === after) {
      break;
    }
    if (signature.err === null) {
      validSignatures.push(signature.signature);
    }

  }
  const transactions = await connection.getTransactions(validSignatures, {
    maxSupportedTransactionVersion: 0,
  });
  if (transactions) {
    const txs = transactions.filter((tx): tx is VersionedTransactionResponse => tx !== null);
    return txs;
    
  }
  return [];
}


export async function getTransaction(signature: string): Promise<VersionedTransactionResponse | null> {
  const connection = new Connection(RPC_URL_CS);

  const transaction = await connection.getTransaction(signature, {
    maxSupportedTransactionVersion: 0,
  });
  return transaction;

}

export async function processTransactionStoreToRedisAboveOneSol(transaction: VersionedTransactionResponse, app?: string | "temp") {


  if (transaction && transaction?.meta?.postTokenBalances?.length == 0 && transaction?.meta?.preTokenBalances?.length == 0 && transaction?.meta?.postBalances) {

    const accountKeys = transaction.transaction.message.staticAccountKeys.map(key => key.toBase58());
    const keys = accountKeys.map(key => key);
    const postBalances = transaction?.meta?.postBalances;
    //console.log('Transaction:',postBalances);
    //console.log('Transaction:',keys);
    const balanceCounts = postBalances.reduce<{ [key: number]: number }>((acc, balance) => {
      if (balance > **********) {
        acc[balance] = (acc[balance] || 0) + 1;
      }
      return acc;
    }, {});

    const accountsWithBalance = Object.keys(balanceCounts).filter((balance: string) => balanceCounts[Number(balance)] > 1);
    const accounts = accountsWithBalance.map(balance => {
      const indexes = [];
      let idx = postBalances.indexOf(Number(balance));
      while (idx != -1) {
        indexes.push(idx);
        idx = postBalances.indexOf(Number(balance), idx + 1);
      }
      return indexes.map(index => keys[index]);

    });

    if (accounts.length > 0) {
      const redis = getRedisClient();
      try {
        for (const account of accounts[0]) {
          const ownerAddress = transaction.transaction.message.getAccountKeys().staticAccountKeys[0].toBase58();
          await addRedisString(redis, account, JSON.stringify({ addressType: "regular", address: ownerAddress, app: app }));   // TODO: update to JSON
        }
        redis.disconnect();
      } catch (error) {
        console.error('Error adding accounts to Redis:', error);
      }
    }


  }


}

export async function extractMintFromJsonTx(tx: any) {
  const ignoreMints = ["So11111111111111111111111111111111111111112", "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v", "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB"];
  const preTokenBalances = tx?.event?.transaction[0]?.meta[0]?.pre_token_balances || undefined;
  const postTokenBalances = tx?.event?.transaction[0]?.meta[0]?.post_token_balances || undefined;

  if (preTokenBalances) {
    for (const preTokenBalance of preTokenBalances) {
      if (!ignoreMints.includes(preTokenBalance.mint)) {
        return preTokenBalance.mint;
      }
    }
  }
  if (postTokenBalances) {
    for (const postTokenBalance of postTokenBalances) {
      if (!ignoreMints.includes(postTokenBalance.mint)) {
        return postTokenBalance.mint;
      }
    }
  }
  return null;
}


export async function checkTransactionStatus(txId: string): Promise<boolean> {
  const connection = new Connection(RPC_URL, 'processed');
  const maxAttempts = 30; // Maximum number of attempts (30 seconds)
  let attempts = 0;

  while (attempts < maxAttempts) {
    try {
      const status = await connection.getSignatureStatus(txId, { searchTransactionHistory: true });

      if (!status || !status.value) {
        console.log(`Transaction still processing (attempt ${attempts + 1}/${maxAttempts})...`);
        attempts++;

        if (attempts >= maxAttempts) {
          console.log('Max attempts reached. Transaction status uncertain.');
          return false;
        }

        // Wait 1 second before the next attempt
        await new Promise(resolve => setTimeout(resolve, 1000));
        continue;
      }

      if (status.value.err) {
        console.error('Transaction failed:', status.value.err);
        return false;
      }

      console.log('Transaction succeeded!');
      return true;
    } catch (error) {
      console.error(`Error checking transaction: ${(error as Error).message}`);
      return false;
    }
  }

  return false;
}


export async function extractMintTransactions(accountAddress: string, lamports?: number | 5000, limit?: number | 100, before?: string | undefined, after?: string | undefined) {
  const connection = new Connection(RPC_URL_CS);
  const publicKey = new PublicKey(accountAddress);
  const batchSize = 100;
  const parallelBatchSize = 30;
  let lastSignature: string | undefined = before;
  let devAccounts = new Set();

  async function getTransactionWithRetry(signature: string, maxRetries = 5): Promise<ParsedTransactionWithMeta | null> {
    let retryCount = 0;
    const baseDelay = 500;

    while (retryCount < maxRetries) {
      try {
        const parsedTransaction = await connection.getParsedTransaction(signature, {
          maxSupportedTransactionVersion: 0
        });
        return parsedTransaction;
      } catch (error: any) {
        // Silently retry on 429 errors
        if (error?.message?.includes('429') || error?.response?.status === 429) {
          retryCount++;
          if (retryCount === maxRetries) return null;
          await new Promise(resolve => setTimeout(resolve, baseDelay * Math.pow(2, retryCount)));
          continue;
        }
        return null;
      }
    }
    return null;
  }

  process.stderr.write = () => true; // Suppress stderr output

  while (true) {
    try {
      const signatures = await connection.getSignaturesForAddress(publicKey, { before: lastSignature, limit: batchSize });
      if (signatures.length === 0) break;


      for (let i = 0; i < signatures.length; i += parallelBatchSize) {
        const batch = signatures.slice(i, i + parallelBatchSize);

        const promises = batch.map(async (sigInfo) => {
          if (sigInfo.err) return null;

          const parsedTransaction = await getTransactionWithRetry(sigInfo.signature);
          if (!parsedTransaction) return null;

          const owner = parsedTransaction.transaction.message.accountKeys[0].pubkey.toString();


          const instruction = parsedTransaction.transaction.message.instructions[0];
          if ('parsed' in instruction && instruction.parsed.type === 'transfer') {
            const { destination, lamports, source } = instruction.parsed.info;
            if (JITO_MAINNET_TIP_ACCOUNTS.includes(destination) && lamports === lamports) {
              //process.stdout.write(`${source}\n`);
              if ('parsed' in parsedTransaction.transaction.message.instructions[1] &&
                parsedTransaction.transaction.message.instructions[1].parsed.type == "createIdempotent") {
                devAccounts.add(source);
                //if source not in devAccounts print

              }
              return null;
            }
          }

          if (!devAccounts.has(owner)) {
            console.log(owner, sigInfo.signature);
            console.log(parsedTransaction.transaction.message.instructions);


          }

          return parsedTransaction;
        });

        await Promise.all(promises);
        await new Promise(resolve => setTimeout(resolve, 200));
      }

      lastSignature = signatures[signatures.length - 1].signature;

    } catch (error) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
}

export async function followAccountInline(master:string, account: string, currentDepth: number = 0, maxDepth: number = 20, retries: number = 0): Promise<void> {

  if (account === master) {
    console.log(`Master account reached. exiting`);
    return;
  }

  if ( JITO_MAINNET_TIP_ACCOUNTS.includes(account)) {
    console.log(`Jito tip account reached. Final account: ${account}`);
    return;
  }
  // Immediately return if max depth is reached
  const maxSignatureRetries = 30;
  console.log("retries:", retries);
  if (retries >= maxSignatureRetries) {
    console.log(`Max depth ${maxDepth} reached. Final account: ${account}`);
    const redis = getRedisClient();
    await redis.set(account, JSON.stringify({
      addressType: "minter",
      address: account,
      app: "auto7"
    }));
    redis.disconnect()
    return;

  }

  // if (currentDepth >= maxDepth) {
  //   console.log(`Max depth ${maxDepth} reached. Final account: ${account}`);
  //   const redis = getRedisClient();
  //   await redis.set(account, JSON.stringify({
  //     addressType: "minter",
  //     address: account,
  //     app: "auto7"
  //   }));
  //   redis.disconnect()
  //   return;
  // }

  const connection = new Connection(RPC_URL);
  const publicKey = new PublicKey(account);
  const MIN_SOL = 4 * 1e9; // 4 SOL in lamports
  const MAX_SOL = 7 * 1e9; // 7 SOL in lamports

  async function getTransactionWithRetry(signature: string, maxRetries = 5): Promise<ParsedTransactionWithMeta | null> {
    let retryCount = 0;
    const baseDelay = 500;

    while (retryCount < maxRetries) {
      try {
        return await connection.getParsedTransaction(signature, {
          maxSupportedTransactionVersion: 0
        });
      } catch (error: any) {
        if (error?.message?.includes('429') || error?.response?.status === 429) {
          retryCount++;
          if (retryCount === maxRetries) return null;
          await new Promise(resolve => setTimeout(resolve, baseDelay * Math.pow(2, retryCount)));
          continue;
        }
        return null;
      }
    }
    return null;
  }

  try {
    const signatures = await connection.getSignaturesForAddress(publicKey, { limit: 100 });

    // Process transactions sequentially to maintain better control
    for (const sigInfo of signatures) {
      if (sigInfo.err) continue;

      const parsedTx = await getTransactionWithRetry(sigInfo.signature);
      if (!parsedTx) continue;

      for (const instruction of parsedTx.transaction.message.instructions) {
        if ('parsed' in instruction && instruction.parsed.type === 'transfer') {
          const { destination, lamports } = instruction.parsed.info;

          if (lamports >= MIN_SOL && lamports <= MAX_SOL && destination !== account) {
            console.log(`Depth ${currentDepth}: Found transfer of ${lamports / 1e9} SOL from ${account} to ${destination}`);



            // Follow the new account
            await followAccountInline(
              master,
              destination,
              currentDepth + 1,
              maxDepth,
              0
            );
            return;
            // Break after first valid transfer to avoid multiple branches
            break;
          }


        }
      }
      //   if (!found){ 
      // await new Promise(resolve => setTimeout(resolve, 1000));
      // await followAccountInline(
      //   master,
      //   account,
      //   currentDepth,
      //   maxDepth,
      //   retries + 1
      // );
      // }

      // Add small delay between transactions
      
      //throw error to enforce retrie
      throw new Error('Enforcing retry');

    }

  } catch (error) {
    if (retries < maxSignatureRetries) {
      await new Promise(resolve => setTimeout(resolve, 1000));
      await followAccountInline(master,account, currentDepth, maxDepth, retries + 1);
    }
  }
}
