//const api_key = "PaLGQLrg9BMnCLln8Orqsjvi8j7qXP9o"; 


import { ALCHEMY_NOTIFY_TOKEN,ALCHEMY_NOTIFY_ID } from "./config";

export async function createWebhook(api_key:string, app_id: string, address: string, name: string): Promise<Response> {
    const response = await fetch("https://dashboard.alchemy.com/api/create-webhook", {
        headers: {
            "accept": "application/json",
            "accept-language": "en-GB,en;q=0.9",
            //"Authorization": "Bearer " + api_key,
            "X-Alchemy-Token": api_key,
            "content-type": "application/json",
            "Referer": "https://dashboard.alchemy.com/webhooks/create",
            "Referrer-Policy": "same-origin"
        },
        body: JSON.stringify({
            network: "SOLANA_MAINNET",
            app_id: app_id,
            name: name,
            webhook_type: "ADDRESS_ACTIVITY",
            webhook_url: "http://kroocoin.xyz:7012/webhook",
            addresses: [address]
        }),
        method: "POST"
    });

    return response;
}

export async function addAddressToWebhook( address: string): Promise<Response> {

    const response = await fetch("https://dashboard.alchemy.com/api/update-webhook-addresses", {
        headers: {
            "accept": "application/json",
            "accept-language": "en-GB,en;q=0.9",
            "X-Alchemy-Token": ALCHEMY_NOTIFY_TOKEN,
            "content-type": "application/json",
            "Referer": "https://dashboard.alchemy.com/api/update-webhook-addresses",
            "Referrer-Policy": "same-origin"
        },
        body: JSON.stringify({
            addresses_to_add: [address],
            addresses_to_remove: [],
            webhook_id: ALCHEMY_NOTIFY_ID
        }),
        method: "PATCH"
    });

    return response;
}

export async function removeAddressFromWebhook( address: string): Promise<Response> {
    const response = await fetch("https://dashboard.alchemy.com/api/update-webhook-addresses", {
        headers: {
            "accept": "application/json",
            "accept-language": "en-GB,en;q=0.9",
            "X-Alchemy-Token": ALCHEMY_NOTIFY_TOKEN,
            "content-type": "application/json",
            "Referer": "https://dashboard.alchemy.com/api/update-webhook-addresses",
            "Referrer-Policy": "same-origin"
        },
        body: JSON.stringify({
            addresses_to_add: [],
            addresses_to_remove: [address],
            webhook_id: ALCHEMY_NOTIFY_ID
        }),
        method: "PATCH"
    });

    return response;
}
