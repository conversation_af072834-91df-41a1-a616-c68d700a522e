import mysql from "mysql2/promise";
import { MYSQL_CONFIG } from "./config";

type TradeEvent = {
  mint: string;
  solAmount: number;
  tokenAmount: number;
  isBuy: boolean;
  user: string;
  timestamp: number;
  virtualSolReserves: number;
  virtualTokenReserves: number;
  realSolReserves: number;
  realTokenReserves: number;
  signature?: string;
  signer?: string;
  block_id?: number;
  app?: string;
};

export interface MintTrend {
  mint: string;
  owner: string;
  symbol: string;
  created_at: number;
  vol10sec: number;
  vol10sec_trades: number;
  vol10sec_users: number;
  vol10sec_buys: number;
  vol10sec_sells: number;
  vol1min: number;
  vol1min_trades: number;
  vol1min_users: number;
  vol1min_buys: number;
  vol1min_sells: number;
  vol5min: number;
  vol5min_trades: number;
  vol5min_users: number;
  vol5min_buys: number;
  vol5min_sells: number;
  vol15min: number;
  vol15min_trades: number;
  vol15min_users: number;
  vol15min_buys: number;
  vol15min_sells: number;
  vol_all: number;
  price: number;
  market_cap: number;
  max_market_cap: number;
}

export async function getConnection() {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  return connection;
}


export async function getSolPrice() {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  const [results]: any = await connection.execute('SELECT sol_price FROM configs  LIMIT 1');
  await connection.end();
  return results[0].sol_price;
}

export async function updateWebsocket(mint: string, status: number) {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  await connection.execute('UPDATE rugger_mints SET websocket = ? WHERE mint = ?', [status, mint]);
  await connection.end();
}

export async function updateSimulated(mint: string, is_simulation: number) {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  await connection.execute('UPDATE rugger_mints SET is_simulation = ? WHERE mint = ?', [is_simulation, mint]);
  await connection.end();
}


export async function updateSolPrice(solPrice: number) {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  await connection.execute('UPDATE configs SET sol_price = ? WHERE id = 1', [solPrice]);
  await connection.end();
}

export async function getActiveWebsockets() {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  const [results]: any = await connection.execute('SELECT mint FROM rugger_mints WHERE websocket = 1 ');
  await connection.end();
  return results.map((row: any) => row.mint);
}

export async function getRuggerAccounts() {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  const [results]: any = await connection.execute('SELECT address FROM accounts WHERE app = "auto1"  ');
  await connection.end();
  return results.map((row: any) => row.address);
}

export async function getAppConfig(app: string) {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  const [results]: any = await connection.execute('SELECT  enabled	,is_simulation,	buy_sol	,max_time_sec	,min_time_sec,	max_profit_sol,	min_profit_sol,	max_user_buy_sol,	max_mc_pct  FROM app_config WHERE app = ? LIMIT 1', [app]);
  await connection.end();
  return results.map((row: any) => ({ enabled: row.enabled, is_simulation: row.is_simulation, buy_sol: row.buy_sol, max_time_sec: row.max_time_sec, min_time_sec: row.min_time_sec, max_profit_sol: row.max_profit_sol, min_profit_sol: row.min_profit_sol, max_user_buy_sol: row.max_user_buy_sol, max_mc_pct: row.max_mc_pct }));
}

export async function getAppConfigAll() {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  const [results]: any = await connection.execute('SELECT  enabled	,app,is_simulation,	buy_sol	,max_time_sec	,min_time_sec,	max_profit_sol,	min_profit_sol,	max_user_buy_sol,	max_mc_pct  FROM app_config WHERE enabled = 1 ');
  await connection.end();
  return results;
}

export async function getAllRuggerAccounts() {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  const [results]: any = await connection.execute('SELECT address, app FROM accounts WHERE app LIKE "auto%"');
  await connection.end();
  return results.map((row: any) => ({ address: row.address, app: row.app }));
}

export type AddressApp = { address: string; app: string, balance: number, last_sig: string };

export async function getEnabledAccounts(app: string) {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  const [results]: any = await connection.execute('SELECT address, app,balance, last_sig FROM accounts WHERE app like ? and enabled = 1 and status = "update" ', [app]);
  await connection.end();
  return results.map((row: AddressApp) => ({ address: row.address, app: row.app, balance: row.balance, last_sig: row.last_sig }));
}

export async function getEnabledApps() {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  const [results]: any = await connection.execute('SELECT app, app,balance, last_sig FROM accounts WHERE app like ? and enabled = 1 and status = "update" ');
  await connection.end();
  return results.map((row: AddressApp) => ({ address: row.address, app: row.app, balance: row.balance, last_sig: row.last_sig }));
}



export async function getEnabledAccountsForGRPC() {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  const [results]: any = await connection.execute('SELECT address FROM accounts WHERE app LIKE "auto%" and enabled = 1 ');
  await connection.end();
  return results.map((row: any) => row.address);
}


export async function getAccountApp(address: string) {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  const [results]: any = await connection.execute('SELECT app FROM accounts WHERE address= ? LIMIT 1', [address]);
  await connection.end();
  return results[0].app;
}

export async function disableAccount(address: string) {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  await connection.execute('UPDATE accounts SET enabled = 0 WHERE address = ?', [address]);
  await connection.end();
}


export async function getMintStatus(mint: string) {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  const [results]: any = await connection.execute('SELECT trade_status FROM rugger_mints WHERE mint= ? LIMIT 1', [mint]);
  await connection.end();
  return results[0].trade_status;
}

export async function getAppStatus(app: string) {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  const [results]: any = await connection.execute('SELECT enabled FROM app_config WHERE app = ? LIMIT 1', [app]);
  await connection.end();
  if (results.length === 0) {
    return 0;
  }
  return results[0].enabled;
}

export async function setBuyCouner(mint: string, buy_counter: number) {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  await connection.execute('UPDATE rugger_mints SET buy_counter = ? WHERE mint = ?', [buy_counter, mint]);
  await connection.end();
}

export async function updateAccountStatus(address: string, status: string) {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  await connection.execute('UPDATE accounts SET status = ? WHERE address = ?', [status, address]);
  await connection.end();
}

export async function updateAccountSignature(address: string, last_sig: string) {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  await connection.execute('UPDATE accounts SET last_sig = ?, last_balance_update = ? WHERE address = ?', [last_sig, new Date(), address]);
  await connection.end();
}


// set treade status
export async function setMintStatus(mint: string, status: string) {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  await connection.execute('UPDATE rugger_mints SET trade_status = ? WHERE mint = ?', [status, mint]);
  await connection.end();
}

export async function updatMintTradeDataBuy(mint: string, status: string, buy_sol_amount: number, buy_token_amount: number, tx: string, buy_mc: number, is_simulation: number) {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  await connection.execute('UPDATE rugger_mints SET trade_status = ?,buy_sol_amount = ?, buy_token_amount = ?,buy_tx = ?,buy_mc = ?,is_simulation = ?  WHERE mint = ?', [status, buy_sol_amount, buy_token_amount, tx, buy_mc, is_simulation, mint]);
  await connection.end();
}

export async function updatMintTradeDataSell(mint: string, status: string, sell_sol_amount: number, sell_token_amount: number, tx: string, sell_mc: number, is_simulation: number) {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  await connection.execute('UPDATE rugger_mints SET trade_status = ?,sell_sol_amount = ?, sell_token_amount = ?,sell_tx = ?, sell_mc = ?,is_simulation = ?  WHERE mint = ?', [status, sell_sol_amount, sell_token_amount, tx, sell_mc, is_simulation, mint]);
  await connection.end();
}

export async function updatMintBuyTokens(mint: string, buy_token_amount: number) {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  await connection.execute('UPDATE rugger_mints SET buy_token_amount = ?  WHERE mint = ?', [buy_token_amount, mint]);
  await connection.end();
}

export async function getMintBuyTokens(mint: string) {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  const [results]: any = await connection.execute('SELECT buy_token_amount FROM rugger_mints WHERE mint = ? LIMIT 1', [mint]);
  await connection.end();
  if (results.length === 0) {
    return 0;
  }
  return results[0].buy_token_amount;
}
export async function updateMaxMC(mint: string, max_mc: number) {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  await connection.execute('UPDATE rugger_mints SET max_mc = ?  WHERE mint = ?', [max_mc, mint]);
  await connection.end();
}

export async function updateLastSoldTime(mint: string) {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  await connection.execute('UPDATE rugger_mints SET last_sold_time = ?  WHERE mint = ?', [new Date(), mint]);
  await connection.end();
}


export async function updatMintBuyTx(mint: string, buy_tx: string) {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  await connection.execute('UPDATE rugger_mints SET buy_tx = ?  WHERE mint = ?', [buy_tx, mint]);
  await connection.end();
}


export async function getAccountAuto2() {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  const [results]: any = await connection.execute('SELECT address FROM accounts WHERE app = "auto2" order by disovery_account_ts desc limit 1  ');
  await connection.end();
  return results[0].address;
}

export async function getAccountAuto3() {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  const [results]: any = await connection.execute('SELECT address FROM accounts WHERE app = "auto3" order by disovery_account_ts desc limit 1  ');
  await connection.end();
  return results[0].address;
}

export async function getTimeSinceLastSold(mint: string) {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  const [results]: any = await connection.execute('SELECT last_sold_time FROM rugger_mints WHERE mint = ? limit 1 ', [mint]);
  await connection.end();
  const last_sold_time = results[0].last_sold_time;
  const now = new Date();
  const lastSoldTime = new Date(last_sold_time);
  const timeSinceLastSold = Math.floor((now.getTime() - lastSoldTime.getTime()) / 1000);
  return timeSinceLastSold;
}


export async function addMint(owner: string, mint: string, appType: string) {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  await connection.execute('INSERT INTO rugger_mints (rugger, mint, buy_sol_amount, buy_token_amount, sell_sol_amount, sell_token_amount, buy_tx, sell_tx, buy_mc, sell_mc, created_at, app) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)', [owner, mint, 0, 0, 0, 0, 0, 0, 0, 0, new Date(), appType]);
  await connection.end();
}

export async function getMint(mint: string) {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  const [results]: any = await connection.execute('SELECT mint FROM rugger_mints where mint = ? limit 1', [mint]);
  await connection.end();
  if (results.length === 0) {
    return null;
  }
  return results[0].mint;
}


export async function addAppAccountToDB(address: string, sol_amount: number, app: string, sig: string) {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  await connection.execute('INSERT INTO accounts (master, address, balance, status, scan, scanned, distance, app, disovery_account_ts, last_sig) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE disovery_account_ts = VALUES(disovery_account_ts)', [address, address, 99999, "new", 1, 0, 10, app, new Date(), sig]);

  await connection.end();
}


export async function addAppAccountToDBWithName(address: string, sol_amount: number, app: string, sig: string, name: string) {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  await connection.execute('INSERT INTO accounts (master, address, balance, status, scan, scanned, distance, app, disovery_account_ts, last_sig,name,enabled) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?,?,?) ON DUPLICATE KEY UPDATE disovery_account_ts = VALUES(disovery_account_ts)', [address, address, 99999, "new", 1, 0, 10, app, new Date(), sig, name, 1]);

  await connection.end();
}

export async function updateAddressName(address: string, name: string) {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  await connection.execute('UPDATE accounts SET name = ?  WHERE address = ?', [name, address]);
  await connection.end();
}

export async function getMintTradeData(mint: string) {
  const connection = await mysql.createConnection(MYSQL_CONFIG);

  const query = `
    SELECT 
        t.mint,
        rm.trade_status,
        rm.buy_token_amount,
        SUM(CASE WHEN t.trade = 'buy' THEN t.sol_amount/1e9 ELSE 0 END) AS total_buys,
        SUM(CASE WHEN t.trade = 'sell' THEN t.sol_amount/1e9 ELSE 0 END) AS total_sells,
        (SELECT market_cup 
         FROM token_tx 
         WHERE token_tx.mint = t.mint 
         ORDER BY block_time DESC 
         LIMIT 1) AS last_market_cup,
        MAX(t.block_time) - MIN(t.block_time) AS time_diff_seconds,
        COUNT(*) AS count,
        SUM(CASE WHEN t.trade = 'buy' THEN t.sol_amount/1e9 ELSE 0 END) -  
        SUM(CASE WHEN t.trade = 'sell' THEN t.sol_amount/1e9 ELSE 0 END) AS cumulative_diff,
        rm.buy_tx,
        rm.sell_tx,
        rm.app,
        MAX(t.market_cup) AS max_market_cup,
        rm.buy_counter,
        rm.is_simulation,
        (SELECT market_cup 
         FROM token_tx 
         WHERE token_tx.mint = t.mint 
         ORDER BY block_time ASC 
         LIMIT 1) AS min_market_cup
    FROM 
        token_tx t
    JOIN 
        rugger_mints rm ON t.mint = rm.mint
    WHERE  
         t.mint = ?
    GROUP BY 
        t.mint, rm.trade_status 
    LIMIT 1
  `;

  try {
    const [rows]: [any[], any] = await connection.execute(query, [mint]);

    await connection.end();

    // If no data is found, return null
    if (rows.length === 0) {
      return null;
    }

    // Convert result row into an object
    return rows[0];
  } catch (error) {
    console.error("Database query failed:", error);
    await connection.end();
    throw error; // Rethrow error for better error handling
  }
}




export async function getMintTradeDataMonitor(mint: string) {
  const connection = await mysql.createConnection(MYSQL_CONFIG);

  const query = `
        SELECT 
          account,
          sol_amount,
          trade,
          market_cup,
          block_time,
          -- Cumulative sum of buys
          SUM(CASE WHEN trade = 'buy' THEN sol_amount ELSE 0 END) 
              OVER (ORDER BY block_time ASC, account ASC ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW) AS buy_sum,
          -- Cumulative sum of sells
          SUM(CASE WHEN trade = 'sell' THEN sol_amount ELSE 0 END) 
              OVER (ORDER BY block_time ASC, account ASC ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW) AS sell_sum,
          -- Difference between buy_sum and sell_sum
          SUM(CASE 
                  WHEN trade = 'buy' THEN sol_amount 
                  WHEN trade = 'sell' THEN -sol_amount 
                  ELSE 0 
              END) 
              OVER (ORDER BY block_time ASC, account ASC ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW) AS trade_diff
      FROM 
          token_tx t
      WHERE  
          t.signer != 'Gh9J24j688AfsoiZQJiUGYtxb8RwhcZPJsQtiAJtLFAQ'
          AND t.mint = ?
      ORDER BY block_time desc, buy_sum desc;

  `;

  try {
    const [rows]: [any[], any] = await connection.execute(query, [mint]);

    await connection.end();

    // If no data is found, return null
    if (rows.length === 0) {
      return null;
    }

    // Convert result row into an object
    return rows;
  } catch (error) {
    console.error("Database query failed:", error);
    await connection.end();
    throw error; // Rethrow error for better error handling
  }
}

export async function saveTransactionsToDatabase(trades: TradeEvent[], solPrice: number) {

  const connection = await mysql.createConnection(MYSQL_CONFIG);
  try {
    const query = `
      INSERT INTO token_tx (mint, account, sol_amount, token_amount, trade, price, market_cup, block_time, block_id, signature, signer,app)
      VALUES ?
    `;
    // before inserting check if there is no duplicates remove them
    const uniqueTrades = trades.filter((trade, index, self) =>
      index === self.findIndex((t) => (
        t.user === trade.user && t.signature === trade.signature && t.isBuy === trade.isBuy
      ))
    );

    const values = uniqueTrades.map(trade => {
      let price = trade.solAmount / trade.tokenAmount;
      let marketCap = price * 1e6 * solPrice;

      // Ensure price and marketCap are valid numbers
      if (!isFinite(price)) {
        price = 0;
      }
      if (!isFinite(marketCap)) {
        marketCap = 0;
      }

      const tradeType = trade.isBuy ? 'buy' : 'sell';

      return [
        trade.mint,
        trade.user,
        trade.solAmount.toString(),
        trade.tokenAmount.toString(),
        tradeType,
        price,
        marketCap,
        trade.timestamp,
        trade.block_id, // Use block_id instead of timestamp
        trade.signature,
        trade.signer,
        trade.app
      ];
    });

    await connection.query(query, [values]);
    //console.log('Transactions saved to database:', values.length);
  } catch (err) {

    console.error('Error saving transactions to database:', err, trades);
  } finally {
    await connection.end();
  }
}

export async function addMintTrend(mint: string, mintTrendData: {
  owner: string;
  symbol: string;
  created_at?: number;
  vol10sec: number;
  vol10secTrades: number;
  vol10secUsers: number;
  vol10secBuys: number;
  vol10secSells: number;
  vol1min: number;
  vol1minTrades: number;
  vol1minUsers: number;
  vol1minBuys: number;
  vol1minSells: number;
  vol5min: number;
  vol5minTrades: number;
  vol5minUsers: number;
  vol5minBuys: number;
  vol5minSells: number;
  vol15min: number;
  vol15minTrades: number;
  vol15minUsers: number;
  vol15minBuys: number;
  vol15minSells: number;
  volAll: number;
  price: number;
  marketCap: number;
  maxMarketCap: number;
}): Promise<void> {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  try {
    await connection.execute(
      `INSERT INTO mint_trends (
        mint, owner, symbol, created_at, 
        vol10sec, vol10sec_trades, vol10sec_users, vol10sec_buys, vol10sec_sells,
        vol1min, vol1min_trades, vol1min_users, vol1min_buys, vol1min_sells,
        vol5min, vol5min_trades, vol5min_users, vol5min_buys, vol5min_sells,
        vol15min, vol15min_trades, vol15min_users, vol15min_buys, vol15min_sells,
        vol_all, price, market_cap, max_market_cap
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      ON DUPLICATE KEY UPDATE
        owner = VALUES(owner),
        symbol = VALUES(symbol),
        vol10sec = VALUES(vol10sec),
        vol10sec_trades = VALUES(vol10sec_trades),
        vol10sec_users = VALUES(vol10sec_users),
        vol10sec_buys = VALUES(vol10sec_buys),
        vol10sec_sells = VALUES(vol10sec_sells),
        vol1min = VALUES(vol1min),
        vol1min_trades = VALUES(vol1min_trades),
        vol1min_users = VALUES(vol1min_users),
        vol1min_buys = VALUES(vol1min_buys),
        vol1min_sells = VALUES(vol1min_sells),
        vol5min = VALUES(vol5min),
        vol5min_trades = VALUES(vol5min_trades),
        vol5min_users = VALUES(vol5min_users),
        vol5min_buys = VALUES(vol5min_buys),
        vol5min_sells = VALUES(vol5min_sells),
        vol15min = VALUES(vol15min),
        vol15min_trades = VALUES(vol15min_trades),
        vol15min_users = VALUES(vol15min_users),
        vol15min_buys = VALUES(vol15min_buys),
        vol15min_sells = VALUES(vol15min_sells),
        vol_all = VALUES(vol_all),
        price = VALUES(price),
        market_cap = VALUES(market_cap),
        max_market_cap = VALUES(max_market_cap)`,
      [
        mint,
        mintTrendData.owner,
        mintTrendData.symbol,
        mintTrendData.created_at || Math.floor(Date.now() / 1000),
        mintTrendData.vol10sec,
        mintTrendData.vol10secTrades,
        mintTrendData.vol10secUsers,
        mintTrendData.vol10secBuys,
        mintTrendData.vol10secSells,
        mintTrendData.vol1min,
        mintTrendData.vol1minTrades,
        mintTrendData.vol1minUsers,
        mintTrendData.vol1minBuys,
        mintTrendData.vol1minSells,
        mintTrendData.vol5min,
        mintTrendData.vol5minTrades,
        mintTrendData.vol5minUsers,
        mintTrendData.vol5minBuys,
        mintTrendData.vol5minSells,
        mintTrendData.vol15min,
        mintTrendData.vol15minTrades,
        mintTrendData.vol15minUsers,
        mintTrendData.vol15minBuys,
        mintTrendData.vol15minSells,
        mintTrendData.volAll,
        mintTrendData.price,
        mintTrendData.marketCap,
        mintTrendData.maxMarketCap
      ]
    );
  } finally {
    await connection.end();
  }
}

/**
 * Add a trade to the mint_trends_trades table
 * @param tradeData Trade data to be added
 */
export async function addMintTrendsTrade(tradeData: {
  mint: string;
  address: string;
  buyDelay: number;
  isBuy: boolean;
  solAmount: number;
  tokenAmount: number;
  price: number;
  marketCup: number;
  bot?: string;
  maxMarketCup?: number;
}): Promise<void> {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  try {
    // Convert boolean to string for MySQL
    const isBuyString = tradeData.isBuy ? 'buy' : 'sell';

    await connection.execute(
      `INSERT INTO mint_trends_trades (
        mint, address, buyDelay, isBuy, solAmount, tokenAmount, 
        price, marketCup, bot, maxMarketCup,added
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?,?)`,
      [
        tradeData.mint,
        tradeData.address,
        tradeData.buyDelay,
        isBuyString,
        tradeData.solAmount,
        tradeData.tokenAmount,
        tradeData.price,
        tradeData.marketCup,
        tradeData.bot || null,
        tradeData.maxMarketCup || null,
        Math.floor(Date.now() / 1000)
      ]
    );
  } catch (error) {
    console.error("Error adding mint trends trade:", error);
    throw error;
  } finally {
    await connection.end();
  }
}


export async function getAddressesFromMintTrendsTrades(): Promise<string[]> {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  try {
    const [results]: any = await connection.execute('SELECT DISTINCT address FROM mint_trends_trades');
    return results.map((row: any) => row.address);
  } finally {
    await connection.end();
  }
}

export async function updateTraderStats(
  address: string,
  stats: {
    totalTrades: number;
    totalTokens: number;
    totalBuys: number;
    totalSells: number;
    realizedPnl: number;
    unrealizedPnl: number;
    successRate: number;
    roi: number;
    averageHoldTime: number;
    avgBuyAge?: number; // New field for average buy age
    isProfitableTrader: boolean;
  }
): Promise<void> {
  const connection = await getConnection();
  try {
    await connection.execute(
      `INSERT INTO traders_stats (
        address, total_trades, total_tokens, total_buys, total_sells,
        realized_pnl, unrealized_pnl, total_pnl, success_rate, roi,
        average_hold_time, avg_buy_age, is_profitable_trader
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      ON DUPLICATE KEY UPDATE
        total_trades = VALUES(total_trades),
        total_tokens = VALUES(total_tokens),
        total_buys = VALUES(total_buys),
        total_sells = VALUES(total_sells),
        realized_pnl = VALUES(realized_pnl),
        unrealized_pnl = VALUES(unrealized_pnl),
        total_pnl = VALUES(total_pnl),
        success_rate = VALUES(success_rate),
        roi = VALUES(roi),
        average_hold_time = VALUES(average_hold_time),
        avg_buy_age = VALUES(avg_buy_age),
        is_profitable_trader = VALUES(is_profitable_trader)`,
      [
        address,
        stats.totalTrades,
        stats.totalTokens,
        stats.totalBuys,
        stats.totalSells,
        stats.realizedPnl,
        stats.unrealizedPnl,
        stats.realizedPnl + stats.unrealizedPnl, // total_pnl
        stats.successRate,
        stats.roi,
        stats.averageHoldTime,
        stats.avgBuyAge || 0, // Default to 0 if not provided
        stats.isProfitableTrader ? 1 : 0
      ]
    );
  } finally {
    await connection.end();
  }
}

/**
 * Updates multiple trader statistics in a single batch operation with improved error handling
 * @param tradersStats Array of trader statistics to update
 * @param batchSize Size of each batch (default: 50)
 */
export async function batchUpdateTraderStats(
  tradersStats: Array<{
    address: string;
    totalTrades: number;
    totalTokens: number;
    totalBuys: number;
    totalSells: number;
    realizedPnl: number;
    unrealizedPnl: number;
    successRate: number;
    roi: number;
    averageHoldTime: number;
    avgBuyAge?: number; // New field for average buy age
    isProfitableTrader: boolean;
  }>,
  batchSize: number = 50
): Promise<void> {
  if (tradersStats.length === 0) return;

  // Process in smaller batches to avoid out-of-range errors
  for (let i = 0; i < tradersStats.length; i += batchSize) {
    const batchStats = tradersStats.slice(i, i + batchSize);
    console.log(`Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(tradersStats.length / batchSize)}, size: ${batchStats.length}`);

    const connection = await getConnection();
    try {
      const values = batchStats.map(stats => [
        stats.address,
        stats.totalTrades,
        stats.totalTokens,
        stats.totalBuys,
        stats.totalSells,
        stats.realizedPnl,
        stats.unrealizedPnl,
        stats.realizedPnl + stats.unrealizedPnl, // total_pnl
        stats.successRate,
        stats.roi,
        stats.averageHoldTime,
        stats.avgBuyAge || 0, // Default to 0 if not provided
        stats.isProfitableTrader ? 1 : 0
      ]);

      await connection.query(
        `INSERT INTO traders_stats (
          address, total_trades, total_tokens, total_buys, total_sells,
          realized_pnl, unrealized_pnl, total_pnl, success_rate, roi,
          average_hold_time, avg_buy_age, is_profitable_trader
        ) VALUES ?
        ON DUPLICATE KEY UPDATE
          total_trades = VALUES(total_trades),
          total_tokens = VALUES(total_tokens),
          total_buys = VALUES(total_buys),
          total_sells = VALUES(total_sells),
          realized_pnl = VALUES(realized_pnl),
          unrealized_pnl = VALUES(unrealized_pnl),
          total_pnl = VALUES(total_pnl),
          success_rate = VALUES(success_rate),
          roi = VALUES(roi),
          average_hold_time = VALUES(average_hold_time),
          avg_buy_age = VALUES(avg_buy_age),
          is_profitable_trader = VALUES(is_profitable_trader)`,
        [values]
      );

      console.log(`Successfully updated batch ${Math.floor(i / batchSize) + 1}`);
    } catch (error) {
      console.error(`Error updating batch ${Math.floor(i / batchSize) + 1}:`, error);

      // If batch fails, try updating one by one
      if (batchStats.length > 1) {
        console.log('Attempting to update records individually...');
        for (const stat of batchStats) {
          try {
            await updateTraderStats(
              stat.address,
              stat
            );
          } catch (individualError) {
            console.error(`Failed to update individual record for ${stat.address}:`, individualError);
          }
        }
      }
    } finally {
      await connection.end();
    }

    // Add a small delay between batches to reduce database load
    if (i + batchSize < tradersStats.length) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }
}


export type WampToken = {
  ca: string;
  symbol: string;
  name: string;
  description?: string;
  website?: string;
  twitter?: string;
  tg?: string;
  created?: Date;
  max_mc?: number;
  user_trades?: number;
  dev_trades?: number;
  volume?: number;
  kols?: number;
};

/**
 * Add a new token to the wamp_tokens table
 */
export async function addWampToken(token: WampToken): Promise<void> {
  const connection = await getConnection();
  try {
    // Truncate website URL to maximum 250 characters if it exists
    const truncatedWebsite = token.website ? token.website.substring(0, 250) : null;
    const truncatedTwitter = token.website ? token.website.substring(0, 250) : null;

    await connection.execute(
      `INSERT INTO wamp_tokens (
        ca, symbol, name, description, website, twitter, tg, created, max_mc, user_trades, dev_trades, volume
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      ON DUPLICATE KEY UPDATE
        symbol = VALUES(symbol),
        name = VALUES(name),
        description = VALUES(description),
        website = VALUES(website),
        twitter = VALUES(twitter),
        tg = VALUES(tg),
        created = VALUES(created),
        max_mc = VALUES(max_mc),
        user_trades = VALUES(user_trades),
        dev_trades = VALUES(dev_trades),
        volume = VALUES(volume)`,
      [
        token.ca,
        token.symbol,
        token.name,
        token.description || null,
        truncatedWebsite,
        truncatedTwitter,
        token.tg || null,
        token.created ? (typeof token.created === "string" ? token.created : token.created.toISOString().slice(0, 19).replace("T", " ")) : null,
        token.max_mc ?? null,
        token.user_trades ?? null,
        token.dev_trades ?? null,
        token.volume ?? null
      ]
    );
  } finally {
    await connection.end();
  }
}

/**
 * Update an existing token in the wamp_tokens table by contract address
 */
export async function updateWampToken(ca: string, updates: Partial<WampToken>): Promise<void> {
  const connection = await getConnection();
  try {
    const fields: string[] = [];
    const values: any[] = [];
    if (updates.kols !== undefined) { fields.push("kols = ?"); values.push(updates.kols); }
    if (updates.symbol !== undefined) { fields.push("symbol = ?"); values.push(updates.symbol); }
    if (updates.name !== undefined) { fields.push("name = ?"); values.push(updates.name); }
    if (updates.description !== undefined) { fields.push("description = ?"); values.push(updates.description); }
    if (updates.website !== undefined) { fields.push("website = ?"); values.push(updates.website); }
    if (updates.twitter !== undefined) { fields.push("twitter = ?"); values.push(updates.twitter); }
    if (updates.tg !== undefined) { fields.push("tg = ?"); values.push(updates.tg); }
    if (updates.created !== undefined) {
      fields.push("created = ?");
      values.push(typeof updates.created === "string" ? updates.created : updates.created?.toISOString().slice(0, 19).replace("T", " "));
    }
    if (updates.max_mc !== undefined) { fields.push("max_mc = ?"); values.push(updates.max_mc); }
    if (updates.user_trades !== undefined) { fields.push("user_trades = ?"); values.push(updates.user_trades); }
    if (updates.dev_trades !== undefined) { fields.push("dev_trades = ?"); values.push(updates.dev_trades); }
    if (updates.volume !== undefined) { fields.push("volume = ?"); values.push(updates.volume); }

    if (fields.length === 0) return;

    values.push(ca);

    await connection.execute(
      `UPDATE wamp_tokens SET ${fields.join(", ")} WHERE ca = ?`,
      values
    );
  } finally {
    await connection.end();
  }
}


export async function dev_getAccountsToFollow() {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  const [results]: any = await connection.execute(`
SELECT address, MIN(dev_entity_id) AS dev_entity_id, MIN(scan) AS scan
FROM dev_wallets
WHERE scan = 1 
and 
 drained = 0
  AND (
    (withdraw_at = 1 AND created_at > UNIX_TIMESTAMP() - 60 )
    OR
    (withdraw_at = 1 AND created_at <= UNIX_TIMESTAMP() - 60 AND (scanned_at < UNIX_TIMESTAMP() - 60))
    OR
    (withdraw_at > 1 AND withdraw_at > UNIX_TIMESTAMP() - 60 )
    OR
    (withdraw_at > 1 AND withdraw_at <= UNIX_TIMESTAMP() - 60 AND (scanned_at < UNIX_TIMESTAMP() - 60))
  )
GROUP BY address;
  `);

  if (results.length > 0) {
    const addresses = results.map((row: any) => row.address);
    const timestamp = Math.floor(Date.now() / 1000);
    const placeholders = addresses.map(() => '?').join(',');
    await connection.execute(
      `UPDATE dev_wallets SET scanned_at = ? WHERE address IN (${placeholders})`,
      [timestamp, ...addresses]
    );
  }
  await connection.end();
  return results.map((row: any) => ({ address: row.address, dev_entity_id: row.dev_entity_id, scan: row.scan }));
}

export async function dev_addWalletAndLink(address: string, parent_wallet: string, dev_entity_id: number, blockTime: number, balance : number): Promise<void> {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  await connection.execute('INSERT INTO dev_wallets (address,parent_wallet,dev_entity_id,scan,created_at,withdraw_at,scanned_at,last_tx_date,balance,drained) VALUES (?, ?, ?, ?, ?,?,?,?, ?,?) ON DUPLICATE KEY UPDATE dev_entity_id = VALUES(dev_entity_id), scan = VALUES(scan)', [address, parent_wallet, dev_entity_id, 1, Math.floor(Date.now() / 1000), 1, 1, blockTime, balance,0]);
  await connection.execute('update  dev_wallets set scan = 0, drained =1 where address = ? ', [parent_wallet,]);

  await connection.end();
}


export async function update_dev_entity_max_pct() {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  await connection.execute(`
   WITH RECURSIVE thresholds AS (
    SELECT 10 AS threshold
    UNION ALL
    SELECT threshold + 10 FROM thresholds WHERE threshold + 10 <= 2000
),
dev_trades AS (
  SELECT
    dev_entity_id,
    ROUND(
      CASE 
        WHEN start_mcap IS NULL OR start_mcap = 0 THEN NULL
        ELSE (ath_mcap - start_mcap) / start_mcap * 100
      END, 2
    ) AS pct_increase
  FROM dev_token_creations
  WHERE is_running = 0
),
devs AS (
  SELECT dev_entity_id
  FROM dev_token_creations
  WHERE is_running = 0
  GROUP BY dev_entity_id
  HAVING COUNT(*) > 3
),
profit_table AS (
  SELECT
    d.dev_entity_id,
    t.threshold,
    COUNT(*) AS num_trades,
    SUM(CASE WHEN d.pct_increase >= t.threshold THEN 1 ELSE 0 END) AS num_wins,
    SUM(CASE WHEN d.pct_increase <  t.threshold THEN 1 ELSE 0 END) AS num_losses,
    (SUM(CASE WHEN d.pct_increase >= t.threshold THEN 1 ELSE 0 END) * (t.threshold / 100.0)
     - SUM(CASE WHEN d.pct_increase <  t.threshold THEN 1 ELSE 0 END)
    ) AS profit_usd
  FROM devs
  JOIN dev_trades d ON d.dev_entity_id = devs.dev_entity_id
  CROSS JOIN thresholds t
  GROUP BY d.dev_entity_id, t.threshold
),
ranked AS (
  SELECT *,
    ROW_NUMBER() OVER (PARTITION BY dev_entity_id ORDER BY profit_usd DESC) AS rn
  FROM profit_table
)
UPDATE dev_entities e
JOIN (
    SELECT 
        dev_entity_id, 
        threshold AS max_pct,
        num_trades,
        num_wins,
        num_losses,
        profit_usd,
        (profit_usd / num_trades) * 100 AS profit_pct
    FROM ranked
    WHERE rn = 1
) best
ON e.id = best.dev_entity_id
SET 
    e.max_pct = best.max_pct,
    e.num_trades = best.num_trades,
    e.num_wins = best.num_wins,
    e.num_losses = best.num_losses,
    e.profit_pct = best.profit_pct,
    e.profit_usd = best.profit_usd;

`);
  await connection.end();
}

export async function get_dev_config(dev_entity_id: number) {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  const [results]: any = await connection.execute('SELECT enabled, max_pct,max_sol FROM  dev_entities where id = ?', [dev_entity_id]);
  await connection.end();
  return results.map((row: any) => ({ enabled: row.enabled, max_pct: row.max_pct, max_sol: row.max_sol }));
}

export async function updateDevWalletTxInfo(address: string, last_tx_date: number, drained: number, balance: number) {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  await connection.execute(
    `UPDATE dev_wallets
     SET  drained = ?, balance = ?
     WHERE address = ?`,
    [ drained, balance, address]
  );
  await connection.end();
}

export async function updateDevWalletBalance(address: string, balance: number) {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  await connection.execute(
    `UPDATE dev_wallets
     SET balance = ?
     WHERE address = ?`,
    [balance, address]
  );
  await connection.end();
}
export async function get_wallets() {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  const [results]: any = await connection.execute('SELECT address FROM  dev_wallets');
  await connection.end();
  return results.map((row: any) => ({ address: row.address }));
}

export async function getAllDevWallets() {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  const [results]: any = await connection.execute('SELECT address, dev_entity_id FROM dev_wallets');
  await connection.end();
  return results.map((row: any) => ({ address: row.address, dev_entity_id: row.dev_entity_id }));
}