import dotenv from "dotenv";

dotenv.config();

export const MASTER_ACCOUNT =  "Gh9J24j688AfsoiZQJiUGYtxb8RwhcZPJsQtiAJtLFAQ";

export const APP = "auto1";
export const PRIVATE_KEY = process.env.PRIVATE_KEY || "";
export const WSS_URL = process.env.WSS_URL || "";
//export const RPC_URL = process.env.RPC_URL || "";
//export const RPC_URL = "https://solana-mainnet.core.chainstack.com/59d8fa6af4368b9e6e8f8a68f06517c3";
export const RPC_URL = "https://solana-mainnet.g.alchemy.com/v2/********************************";
export const MYSQL_CONFIG = {
  host: process.env.MYSQL_HOST || "",
  user: process.env.MYSQL_USER || "",
  password: process.env.MYSQL_PASSWORD || "",
  database: process.env.MYSQL_DATABASE || "",
};

export const REDIS_URL = process.env.REDIS_URL || "kroocoin.xyz";
export const REDIS_PORT = parseInt(process.env.REDIS_PORT || '6379' );
export const REDIS_PASSWORD = process.env.REDIS_PASSWORD || "";

export const RABBITMQ_SERVER = process.env.RABBITMQ_HOSTNAME || '';
export const RABBITMQ_QUEUE = process.env.RABBITMQ_QUEUE || 'snipes';
export const RABBITMQ_PORT = parseInt(process.env.RABBITMQ_PORT || '5672');
export const RABBITMQ_USERNAME = process.env.RABBITMQ_USERNAME || '';
export const RABBITMQ_PASSWORD = process.env.RABBITMQ_PASSWORD || '';


export const ignoreAccounts = [
  "MWinVXj3HscKfwcdrJetvSgNFmEYPqC1NDxDd7vZqPd",
  "7SEFNefGeEuDLNzSzdQJfCC7oey1k8GcXQP8K78BMCaZ",
  "DPzd4nhKeMzwDqMKJdMkeDSswHHCVezKpcDC7tWhwPfF",
  "5YY4uFaKyGBZ6o7MTHDkCkv5wxnzUjmXAJczdR1CdMHk",
]


export const PUMPFUN_DISCORD_WEBHOOK = "https://discord.com/api/webhooks/1339217907380523040/tdXh3hfzR-AJNgGNYupg31BKirR1KoDV8mj937I7DAk1OqqWlNpinLl2O0TfawVVyG9K";

export const ALCHEMY_NOTIFY_TOKEN = process.env.ALCHEMY_NOTIFY_TOKEN || "";
export const ALCHEMY_NOTIFY_ID = "wh_u8ul0w64j1o7sswy";
export const MINT_PROCESSOR_DEBUG : number = 0;

export const RPC_URL_SYNDICA = "https://solana-mainnet.api.syndica.io/api-key/21KG2L6E3kGURokSr4VwfrtYMbqnqVNYKJKCBTQv2jsiLs97iW8TQv98fcz5kvVDhS3MjVmGt91jZ3UGQpGD7zaPCuxpwgCJbek"
export const RPC_URL_CS="https://nd-656-324-033.p2pify.com/7026d0c4e4356e4f9fc0a1170759e31f"

export const JITO_MAINNET_TIP_ACCOUNTS : string[]  = [
  "96gYZGLnJYVFmbjzopPSU6QiEV5fGqZNyN9nmNhvrZU5",
  "HFqU5x63VTqvQss8hp11i4wVV8bD44PvwucfZ2bU7gRe",
  "Cw8CFyM9FkoMi7K7Crf6HNQqf4uEMzpKw6QNghXLvLkY",
  "ADaUMid9yfUytqMBgopwjb2DTLSokTSzL1zt6iGPaS49",
  "DfXygSm4jCyNCybVYYK6DwvWqjKee8pbDmJGcLWNDXjh",
  "ADuUkR4vqLUMWXxW9gh6D6L8pMSawimctcNZ5pGwDcEt",
  "DttWaMuVvTiduZRnguLF7jNxTgiMBZ1hyAumKUiL2KRL",
  "3AVi9Tg9Uo68tJfuvoKvqKNWKkC5wPdSSdeBnizKZ6jT",
] as const;

export const TRADERS_PROGRAMS: string[] = [
  "BSfD6SHZigAfDWSjzD5Q41jw8LmKwtmjskPH9XW1mrRW", // photon
  "AxiomQpD1TrYEHNYLts8h3ko1NHdtxfgNgHryj2hJJx4",// Axiom
  "Axiom3a2w1UbMt2SMgqSvRiuJFTPusDhwKamNgPTeNQ9", // Axiom
  "b1oomGGqPKGD6errbyfbVMBuzSC8WtAAYo8MwNafWW1",
  "JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4",
  "BANANAjs7FJiPQqJTGFzkZJndT9o7UmKiYYGaJz6frGu",
  "BANANAtm3yvgJ6Km4iv3wtxCNkhFvXEWggASPk9pEYrz",
  "BSwp6bEBihVLdqJRKGgzjcGLHkcTuzmSo1TQkHepzH8p",
  "MaestroAAe9ge5HTc64VbBQZ6fP77pwvrhM8i1XWSAx",
  "AFW9KCZtmtMWuhuLkF5mLY9wsk7SZrpZmuKijzcQ51Ni", // bullx
  "troY36YiPGqMyAYCNbEqYCdN2tb91Zf7bHcQt7KUi61", //Trojan
  "BB5dnY55FXS1e1NXqZDwCzgdYJdMCj3B92PU6Q5Fb6DT", //GMGN vault fee
  "CxvksNjwhdHDLr3qbCXNKVdeYACW8cs93vFqLqtgyFE5", // bonkbot
  "G9PhF9C9H83mAjjkdJz4MDqkufiTPMJkx7TnKE1kFyCp", //pepeboost
  "ZG98FUCjb8mJ824Gbs6RsgVmr1FhXb2oNiJHa2dwmPd", //bonk
  "AVUCZyuT35YSuj4RH7fwiyPu82Djn2Hfg7y2ND2XcnZH", //photon
  "HEPL5rTb6n1Ax6jt9z2XMPFJcDe9bSWvWQpsK7AMcbZg", // soltrading bot
  "F4hJ3Ee3c5UuaorKAMfELBjYCjiiLH75haZTKqTywRP3", //bullx
  "CBaseg7Sqjdkt6oaNqKU6iHLj9tvcD2pxddeP9ct14Xi", // cbase
  "nozievPk7HyK1Rqy1MPJwVQ7qQg2QoJGyP71oeDwbsu", // mevxio
  "LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo", // meteora
  "NoVA1TmDUqksaj2hB1nayFkPysjJbFiU76dT4qPw2wm",
]

export const tradeAccounts: { [key: string]: string } = {
  "account1": "pubkey",
  "account2": "pubkey",
  "account3": "pubkey",
};


/**
 * 1. for tradeBoost
 * buy with account
 * in parallel = grpc get every trade made by account save to database.  
 * 
 */

export const tradeBoost: { [key: string]: { account: string, trade: string; amount: number; range?: number,delay?:number }[] } = {
  "program1": [{
    account: "account1",
    trade: "buy",
    amount: 0.1,
    range:20,  // percentage around amount
    delay: 0.1, // seconds
  },
  {
    account: "account2",
    trade: "buy",
    amount: 0.1,
    range:20,  // percentage around amount
    delay: 0, // seconds
  }
  ,
  {
    account: "account3",
    trade: "buy",
    amount: 0.1,
    range:20,  // percentage around amount
    delay: 0.3, // seconds
  }
  ,
  {
    account: "account1",
    trade: "sell",
    amount: 100
  }
  ,
  {
    account: "account2",
    trade: "sell",
    amount: 100
  }
  ,
  {
    account: "account3",
    trade: "sell",
    amount: 100
  }
]
};