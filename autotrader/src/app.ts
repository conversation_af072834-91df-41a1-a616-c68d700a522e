import express from 'express';
import path from 'path';
import session from 'express-session';
import dashboardRoutes from './routes/dashboardRoutes';
import authRoutes from './routes/authRoutes';

declare module 'express-session' {
    interface SessionData {
        user: { [key: string]: any };
    }
}

const app = express();

app.set('views', path.join(__dirname, 'views'));
app.set('view engine', 'ejs');

app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(session({
    secret: 'your_secret_key',
    resave: false,
    saveUninitialized: true,
}));

app.use('/', authRoutes);
app.use('/dashboard', (req, res, next) => {
    if (req.session.user) {
        next();
    } else {
        res.redirect('/login');
    }
}, dashboardRoutes);

app.get('/', (req, res) => {
    res.redirect('/dashboard');
});

app.get('/login', (req, res) => {
    res.render('login');
});

app.listen(3003, () => {
    console.log('Server is running on port 3003');
});
