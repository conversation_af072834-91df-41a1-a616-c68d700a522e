
import { getTransactions,getTransaction,extractMintTransactions,followAccountInline,getTransactionsForTraining } from "../common/solana";
import dotenv from 'dotenv';
import { 
    REDIS_URL
} from '../common/config'; // Corrected import path
import { Redis } from 'ioredis';
import { Command } from 'commander';


const program = new Command();

dotenv.config();

let redis: Redis;

program
  .command('follow-accounts')
  .description('get-token-accounts')
  .option('-m, --master <masteraccount>', 'Token contract address')
  .option('-a, --account <account>', 'Token contract address')
  .option('-d, --depth <Depth>', 'Depth')
  .action(async (options) => {
    console.log("Scan token...", options.token);
    if (options.account) {
        await followAccountInline(options.master,options.account,1,options.depth,0 );

    }
  });


program
  .command('get-token-accounts')
  .description('get-token-accounts')
  .option('-t, --token <tokenCA>', 'Token contract address')
  .option('-l, --lamports <lamports>', 'lamports')
  .action(async (options) => {
    console.log("Scan token...", options.token);
    if (options.token) {
        await extractMintTransactions(options.token,options.lamports);
    }
  });

  program
  .command('scan-token')
  .description('Scan token')
  .option('-t, --token <tokenCA>', 'Token contract address')
   .option('-s, --start <start>', 'Start signature ')
   .option('-e, --end <end>', 'End signature')
  .action(async (options) => {
    console.log("Scan token...", options.token);
    if (options.token) {
        redis = new Redis({host: REDIS_URL, port: 6379, password: "pump2pump"});
        await getTransactions(options.token);
        console.log("closing redis...");
        await redis.quit();
        process.exit(0); // Ensure the process exits
    }
  });
  program
  .command('scan-token-train')
  .description('Scan token')
  .option('-t, --token <tokenCA>', 'Token contract address')
   .option('-s, --start <start>', 'Start signature ')
   .option('-e, --end <end>', 'End signature')
  .action(async (options) => {
    console.log("Scan token...", options.token);
    if (options.token) {
        redis = new Redis({host: REDIS_URL, port: 6379, password: "pump2pump"});
        await getTransactionsForTraining(options.token);
        console.log("closing redis...");
        await redis.quit();
        process.exit(0); // Ensure the process exits
    }
  });
program
    .command('scan-address')
    .description('Scan address')
    .option('-a, --address <address>', 'Token contract address')
     .option('-l, --limit <amountOfSigs>', 'Amount of signatures to scan', parseInt, 50)
     .option('-b, --before <txs before sig>', 'Show older then', undefined)
     .option('-n, --after <txs after sig>', 'Show newer then', undefined)
     .option('-m, --minsol <MinimutOutgoingSol>', 'Minimum Outoing Sol', parseFloat, 0) 
    .action(async (options) => {
        console.log("Scan address...", options.address);
        if (options.address) {
                const txs = await getTransactions(options.address, options.limit, options.before, options.after);
                console.log(txs.length);
                process.exit(0); // Ensure the process exits
        }
    });

  program
  .command('tx')
  .description('Check transaction')
  .option('-t, --transaction <transaction>', 'Token contract address')
  .action(async (options) => {
    console.log("Scan tx...", options.transaction);
    if (options.transaction) {
        redis = new Redis({host: REDIS_URL, port: 6379, password: "pump2pump"});
        const tx = await getTransaction(options.transaction);
        console.log(tx);
        console.log("closing redis...");
        await redis.quit();
        process.exit(0); // Ensure the process exits
    }
  });




program.parse(process.argv);
