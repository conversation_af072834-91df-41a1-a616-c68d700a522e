import { MYSQL_CONFIG } from '../common/config';
import mysql from 'mysql2/promise';

export async function update_dev_entity_max_pct() {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  await connection.execute(`
WITH RECURSIVE thresholds AS (
    SELECT 10 AS threshold
    UNION ALL
    SELECT threshold + 10 FROM thresholds WHERE threshold + 10 <= 2000
),
dev_trades AS (
    SELECT *
    FROM (
        SELECT
            dev_entity_id,
            ROUND(
                CASE
                    WHEN start_mcap IS NULL OR start_mcap = 0 THEN NULL
                    ELSE (ath_mcap - start_mcap) / start_mcap * 100
                END, 2
            ) AS pct_increase
        FROM dev_token_creations
        WHERE is_running = 0
        AND base_mint = 'So11111111111111111111111111111111111111112'
    ) t
    WHERE pct_increase >= 0
),
devs AS (
    SELECT dev_entity_id
    FROM dev_trades
    GROUP BY dev_entity_id
    HAVING COUNT(*) > 3
),
profit_table AS (
    SELECT
        d.dev_entity_id,
        t.threshold,
        COUNT(*) AS num_trades,
        SUM(CASE WHEN d.pct_increase >= t.threshold THEN 1 ELSE 0 END) AS num_wins,
        SUM(CASE WHEN d.pct_increase < t.threshold THEN 1 ELSE 0 END) AS num_losses,
        (SUM(CASE WHEN d.pct_increase >= t.threshold THEN 1 ELSE 0 END) * (t.threshold / 100.0)
         - SUM(CASE WHEN d.pct_increase < t.threshold THEN 1 ELSE 0 END)
        ) AS profit_usd
    FROM devs
    JOIN dev_trades d ON d.dev_entity_id = devs.dev_entity_id
    CROSS JOIN thresholds t
    GROUP BY d.dev_entity_id, t.threshold
),
ranked AS (
    SELECT *,
        ROW_NUMBER() OVER (PARTITION BY dev_entity_id ORDER BY profit_usd DESC) AS rn
    FROM profit_table
),
best_thresholds AS (
    SELECT
        dev_entity_id,
        threshold AS max_pct,
        num_trades,
        num_wins,
        num_losses,
        profit_usd,
        ROUND((profit_usd / num_trades) * 100, 2) AS profit_pct
    FROM ranked
    WHERE rn = 1
    AND profit_usd > 0
)
UPDATE dev_entities e
JOIN best_thresholds b ON e.id = b.dev_entity_id
SET
    e.max_pct = b.max_pct,
    e.num_trades = b.num_trades,
    e.num_wins = b.num_wins,
    e.num_losses = b.num_losses,
    e.profit_pct = b.profit_pct,
    e.profit_usd = b.profit_usd,
    e.enabled = 1;
  `);
  await connection.end();
}
