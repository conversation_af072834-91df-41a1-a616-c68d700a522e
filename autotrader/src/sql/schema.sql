-- Adminer 4.8.1 MySQL 8.0.41-0ubuntu0.24.04.1 dump

SET NAMES utf8;
SET time_zone = '+00:00';
SET foreign_key_checks = 0;

SET NAMES utf8mb4;

CREATE TABLE `accounts` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `master` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '',
  `address` varchar(255) DEFAULT NULL,
  `balance` bigint unsigned DEFAULT NULL,
  `age` bigint unsigned DEFAULT NULL,
  `balance_ui` float unsigned DEFAULT NULL,
  `account_type` varchar(255) DEFAULT NULL,
  `owner` varchar(255) DEFAULT NULL,
  `disovery_account_ts` timestamp NULL DEFAULT NULL,
  `first_tx_ts` timestamp NULL DEFAULT NULL,
  `last_tx_ts` timestamp NULL DEFAULT NULL,
  `last_balance_update` timestamp NULL DEFAULT NULL,
  `last_sig` varchar(255) DEFAULT NULL,
  `status` varchar(50) DEFAULT NULL,
  `role` varchar(100) DEFAULT NULL,
  `scan` decimal(10,0) NOT NULL DEFAULT '1',
  `scanned` decimal(10,0) DEFAULT NULL,
  `mint_path` decimal(10,0) DEFAULT NULL,
  `name` varchar(100) DEFAULT NULL,
  `distance` smallint DEFAULT NULL,
  `notify` smallint DEFAULT '0',
  `trader` smallint DEFAULT NULL,
  `scan_prior` smallint DEFAULT '100',
  `enabled` smallint DEFAULT NULL,
  `description` varchar(200) DEFAULT NULL,
  `url` varchar(200) DEFAULT NULL,
  `helius_api` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `app` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `id` (`id`),
  UNIQUE KEY `address` (`address`),
  KEY `address_index` (`address`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


CREATE TABLE `app_config` (
  `id` int NOT NULL AUTO_INCREMENT,
  `enabled` int NOT NULL,
  `is_simulation` int NOT NULL,
  `app` varchar(20) NOT NULL,
  `max_time_sec` int DEFAULT NULL,
  `min_time_sec` int DEFAULT NULL,
  `max_profit_sol` float DEFAULT NULL,
  `min_profit_sol` float DEFAULT NULL,
  `max_user_buy_sol` float DEFAULT NULL,
  `max_mc_pct` int DEFAULT NULL,
  `notes` varchar(200) DEFAULT NULL,
  `address` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


CREATE TABLE `configs` (
  `id` int NOT NULL AUTO_INCREMENT,
  `sol_price` float NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


CREATE TABLE `rugger_mints` (
  `id` int NOT NULL AUTO_INCREMENT,
  `app` varchar(20) DEFAULT NULL,
  `rugger` varchar(100) NOT NULL,
  `mint` varchar(100) NOT NULL,
  `created_at` timestamp NOT NULL,
  `trade_status` varchar(20) DEFAULT NULL,
  `trade` varchar(10) DEFAULT NULL,
  `buy_sol_amount` float DEFAULT NULL,
  `buy_token_amount` bigint unsigned DEFAULT NULL,
  `sell_sol_amount` bigint unsigned DEFAULT NULL,
  `sell_token_amount` bigint unsigned DEFAULT NULL,
  `buy_tx` varchar(200) DEFAULT NULL,
  `sell_tx` varchar(200) DEFAULT NULL,
  `buy_mc` float DEFAULT NULL,
  `sell_mc` float DEFAULT NULL,
  `trade_timestamp` float DEFAULT NULL,
  `price` float DEFAULT NULL,
  `mc` float DEFAULT NULL,
  `last_sig` varchar(200) DEFAULT NULL,
  `max_mc` float DEFAULT NULL,
  `last_sold_time` timestamp NULL DEFAULT NULL,
  `buy_counter` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `mint` (`mint`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



CREATE TABLE `token_tx` (
  `id` int NOT NULL AUTO_INCREMENT,
  `mint` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `account` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `signer` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `sol_amount` varchar(100) DEFAULT NULL,
  `token_amount` varchar(45) DEFAULT NULL,
  `trade` varchar(10) DEFAULT NULL,
  `price` decimal(10,10) DEFAULT NULL,
  `market_cup` int DEFAULT '0',
  `block_time` int NOT NULL,
  `block_id` int NOT NULL,
  `signature` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `app` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `account_sol_trade_signature` (`account`,`sol_amount`,`trade`,`signature`),
  KEY `mint` (`mint`),
  KEY `account` (`account`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


CREATE TABLE `users` (
  `id` int NOT NULL AUTO_INCREMENT,
  `username` varchar(20) NOT NULL,
  `password` varchar(20) NOT NULL,
  `role` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- 2025-03-12 07:32:32