import { Request, Response } from 'express';
import * as authService from '../services/authService';

export const login = async (req: Request, res: Response) => {
    try {
        const { username, password } = req.body;
        console.log('Login attempt:', { username, password });
        const user = await authService.authenticateUser(username, password);
        console.log('User found:', user);
        if (user) {
            req.session.user = user;
            console.log('Login successful');
            res.redirect('/dashboard');
        } else {
            console.log('Invalid username or password');
            res.status(401).json({ error: 'Invalid username or password' });
        }
    } catch (error) {
        console.error('Error during login:', error);
        res.status(500).json({ error: 'Internal Server Error' });
    }
};

export const logout = (req: Request, res: Response) => {
    req.session.destroy(() => {
        console.log('User logged out');
        res.redirect('/login');
    });
};
