import { Request, Response } from 'express';
import * as dashboardService from '../services/dashboardService';

export const getDashboardData = async (req: Request, res: Response) => {
    try {
        // Get rugger mints first since it's the primary view now
        const [latestRuggerMints, appConfig, configs] = await Promise.all([
            dashboardService.getLatestRuggerMints(),
            dashboardService.getAppConfig(),
            dashboardService.getConfigs()
        ]);

        res.render('dashboard', { 
            latestRuggerMints, 
            appConfig,
            configs
        });
    } catch (error) {
        console.error('Error fetching dashboard data:', error);
        res.status(500).json({ error: 'Internal Server Error' });
    }
};

export const updateAppConfig = async (req: Request, res: Response) => {
    try {
        const { id, data } = req.body;
        await dashboardService.updateAppConfig(id, data);
        res.json({ message: 'App config updated successfully' });
    } catch (error) {
        console.error('Error updating app config:', error);
        res.status(500).json({ error: 'Internal Server Error' });
    }
};

export const refreshMints = async (req: Request, res: Response) => {
    try {
        const [ruggerMints, configs] = await Promise.all([
            dashboardService.getLatestRuggerMints(),
            dashboardService.getConfigs()
        ]);

        return res.json({
            success: true,
            latestRuggerMints: ruggerMints,
            configs: configs
        });
    } catch (error) {
        console.error('Error refreshing mints:', error);
        res.status(500).json({ 
            success: false,
            error: 'Failed to refresh mints',
            details: error instanceof Error ? error.message : 'Unknown error'
        });
    }
};

export const updateConfig = async (req: Request, res: Response) => {
    try {
        const { id, data } = req.body;
        await dashboardService.updateConfig(id, data);
        res.json({ message: 'Config updated successfully' });
    } catch (error) {
        console.error('Error updating config:', error);
        res.status(500).json({ error: 'Internal Server Error' });
    }
};
