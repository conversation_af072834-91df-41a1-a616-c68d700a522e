import { addAppConfig } from '../services/dashboardService';
import { Request, Response } from 'express';

// ...existing code...

type AppConfig = object

export const createAppConfig = async (req: Request, res: Response): Promise<void> => {
    try {
        const data: AppConfig = req.body;
        await addAppConfig(data);
        res.status(201).send({ message: 'App config added successfully' });
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (error) {
        res.status(500).send({ error: 'Failed to add app config' });
    }
};

// ...existing code...
