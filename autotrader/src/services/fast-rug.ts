/* eslint-disable @typescript-eslint/no-unused-vars */
import Client, {
    CommitmentLevel,
    SubscribeRequest,
    SubscribeUpdate,
    SubscribeUpdateTransaction,
} from "@triton-one/yellowstone-grpc";
//import { sendToDiscord } from "../common/discord";
import dotenv from "dotenv";
import { ClientDuplexStream } from "@grpc/grpc-js";
import { PublicKey } from "@solana/web3.js";
import bs58 from "bs58";
import { getRedisString, getRedisClient } from "../common/redis";
import { redisAddress } from "../common/types";
import { getAppStatus, saveTransactionsToDatabase, updatMintTradeDataBuy, updatMintTradeDataSell, getAppConfigAll } from "../common/db";
import { getDbPool } from "../common/db-pool";
import { RPC_URL_CS, RPC_URL_SYNDICA } from "../common/config";
import { JITO_MAINNET_TIP_ACCOUNTS } from "../common/config";
import { BorshCoder } from "@coral-xyz/anchor";
import { ruleIsSignedByAuto1Owner } from "../common/pump-rules";
// eslint-disable-next-line @typescript-eslint/no-require-imports
const idl = require("../common/idl-new-2025.json");
const coder = new BorshCoder(idl as any);
import { Connection, clusterApiUrl } from "@solana/web3.js";
import https from "https";
import fetch from "node-fetch";
import { httpsAgent } from '../common/agent';
import { startSessionWarmup } from './mints-grpc-connwarmer';
import { Keypair, Transaction, VersionedTransaction, TransactionInstruction, SystemProgram, ComputeBudgetProgram } from '@solana/web3.js';
import { getSellInstructionNoRPC, getBuyInstructionNoRPC } from "../common/trade";
import { getWallets, Wallet } from "../common/wallets";

dotenv.config();
let tradeCounter = 0;
const sellDevProfit = 0.6;
const maxTotalSolInToken = 20;
let devSold = false;
let tradeMeasure = 0.0;
let devMint = "";
let appConfigs: ConfigByApp;
let latestBlockhash: string | null = null;
let lastBlockhashTime: number = 0;
const connection = new Connection(RPC_URL_SYNDICA, 'processed');
const mintTracker: { [key: string]: MintStats } = {};
const ENDPOINT = "http://grpc.solanavibestation.com:10000";
const PUMP_FUN_PROGRAM_ID = "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P";
// Use the shared Redis client instead of creating a new connection
const redis = getRedisClient();
const PUMP_FUN_CREATE_IX_DISCRIMINATOR = Buffer.from([24, 30, 200, 40, 5, 28, 7, 119,]);
const TRADING_ACCOUNT = "8cELLU91DQh5gAFBZooFPVaXAggXLX3AxAVwRUhNTZuE";
const DEV_MINT_ACCOUNT = "8cELLU91DQh5gAFBZooFPVaXAggXLX3AxAVwRUhNTZuE";
const DEV_TRADE_ACCOUNTS = [
    "68s9x6ng4T5jeKe2PSZMRNESm2LTLxiDSAU5UkJPg1je",
    "BVUS8oDZnq3RcqoZtKumX1GCGxjbrmujNAKF4qVDRapb",
    "********************************************",
    "8cELLU91DQh5gAFBZooFPVaXAggXLX3AxAVwRUhNTZuE",
];

const DEV_TRADE_ACCOUNTS_NO_DEV = [
    "68s9x6ng4T5jeKe2PSZMRNESm2LTLxiDSAU5UkJPg1je",
    "BVUS8oDZnq3RcqoZtKumX1GCGxjbrmujNAKF4qVDRapb",
    "********************************************"
];
const wallets_json = getWallets();
const wallets: { [key: string]: Keypair } = {};
for (const w of wallets_json) {
    wallets[w.pubkey] = Keypair.fromSecretKey(bs58.decode(w.privatekey));
}

const tradeMatrix = [
    { "wallet": "BVUS8oDZnq3RcqoZtKumX1GCGxjbrmujNAKF4qVDRapb", "amount": 1 },
    { "wallet": "********************************************", "amount": 0.15 }
];
console.log(tradeMatrix[0]["wallet"]);
const devProfit: { [mint: string]: number } = {}; // Example: { "So11111111111111111111111111111111111111112": 2.5 }
const totalSolInToken: { [mint: string]: number } = {}; // Example: { "So11111111111111111111111111111111111111112": 2.5 }
const mintBuyEnabled: { [mint: string]: boolean } = {}; // Example: { "So11111111111111111111111111111111111111112": 2.5 }

const mintPositions: { [mint: string]: { [account: string]: number } } = {};

const COMMITMENT = CommitmentLevel.PROCESSED;
const FILTER_CONFIG = {
    programIds: [PUMP_FUN_PROGRAM_ID, TRADING_ACCOUNT],
    instructionDiscriminators: [PUMP_FUN_CREATE_IX_DISCRIMINATOR],
};

const devAccounts = new Set();
const jitoEndpoint = 'https://mainnet.block-engine.jito.wtf/api/v1/transactions';

const ACCOUNTS_TO_INCLUDE = [
    {
        name: "mint",
        index: 0,
    },
];

const agent = new https.Agent({
    keepAlive: true,
    keepAliveMsecs: 60000 // Set keepAlive timeout to 1 minute (60000 ms)
});

interface FormattedTransactionData {
    signature: string;
    slot: string;
    [accountName: string]: string;
}

type MintStats = {
    // Creation data
    created_at: number;  // Unix timestamp
    name: string;
    symbol: string;
    owner: string;
    signature: string;

    // Trading stats
    sum_buys: number;
    sum_sells: number;
    dev_profit: number;
    tokenAmount?: number;  // Added tokenAmount property
    solAmount?: number;  // Added solAmount property
    buyPrice?: number;  // Added buyPrice property

    // Market data
    app: string;  // Will be updated during trades
    mcap_current: number;
    mcap_max: number;
};

type RedisAddress = {
    addressType: string;
    address: string;
    app: string;
};

// Use the shared database pool instead of creating a new one
const dbPool = getDbPool();

interface CompiledInstruction {
    programIdIndex: number;
    accounts: Uint8Array;
    data: Uint8Array;
}

interface Message {
    header: MessageHeader | undefined;
    accountKeys: Uint8Array[];
    recentBlockhash: Uint8Array;
    instructions: CompiledInstruction[];
    versioned: boolean;
    addressTableLookups: MessageAddressTableLookup[];
}

interface MessageHeader {
    numRequiredSignatures: number;
    numReadonlySignedAccounts: number;
    numReadonlyUnsignedAccounts: number;
}

interface MessageAddressTableLookup {
    accountKey: Uint8Array;
    writableIndexes: Uint8Array;
    readonlyIndexes: Uint8Array;
}
type TradeEvent = {
    mint: string;
    solAmount: number;
    tokenAmount: number;
    isBuy: boolean;
    user: string;
    timestamp: number;
    virtualSolReserves: number;
    virtualTokenReserves: number;
    realSolReserves: number;
    realTokenReserves: number;
    signature?: string;
    signer?: string;
    block_id?: number;
    app?: string;
};

type AutoConfig = {
    enabled: number;
    app: string;
    is_simulation: number;
    buy_sol: number;
    max_time_sec: number;
    min_time_sec: number;
    max_profit_sol: number;
    min_profit_sol: number;
    max_user_buy_sol: number;
    max_mc_pct: number;
};

type ConfigByApp = {
    [key: string]: Omit<AutoConfig, 'app'>;
};


function getLatestBlockhash(): string {
    if (!latestBlockhash) {
        return "test";
    }
    return latestBlockhash;
}

function eventDecode(data: string): any {
    try {
        const instruction = coder.events.decode(
            Buffer.from(bs58.decode(data)).slice(8).toString("base64")
        );
        return instruction;
    } catch (error) {
        console.error("Event decode error:", error);
        // Return null instead of throwing
        return null;
    }
}

async function main(): Promise<void> {
    //startBlockhashUpdateProcess();
    console.log(wallets);





    startSessionWarmup();
    // appConfigs = convertConfigs(await getAppConfigAll());
    // console.log("App Configs:", appConfigs);

    while (true) {
        try {
            const stream = await reconnectStream();
            await handleStreamEvents(stream);
        } catch (error) {
            console.error("Main loop error:", error);
            console.log("Retrying in 5 seconds...");
            await new Promise(resolve => setTimeout(resolve, 5000));
        }
    }
}

function createSubscribeRequest(): SubscribeRequest {
    return {
        accounts: {},
        slots: {},
        transactions: {
            pumpFun: {
                accountInclude: FILTER_CONFIG.programIds,
                accountExclude: [],
                accountRequired: [],// Add this to request metadata
            },
        },
        transactionsStatus: {},
        entry: {},
        blocks: {},
        blocksMeta: { blockmetadata: {} },
        commitment: COMMITMENT,
        accountsDataSlice: [],
        ping: undefined,
    };
}

function sendSubscribeRequest(
    stream: ClientDuplexStream<SubscribeRequest, SubscribeUpdate>,
    request: SubscribeRequest
): Promise<void> {
    return new Promise<void>((resolve, reject) => {
        stream.write(request, (err: Error | null) => {
            if (err) {
                reject(err);
            } else {
                resolve();
            }
        });
    });
}

function handleStreamEvents(
    stream: ClientDuplexStream<SubscribeRequest, SubscribeUpdate>
): Promise<void> {
    return new Promise<void>((resolve, reject) => {
        stream.on("data", async (data) => {
            try {
                await handleData(data);
            } catch (error) {
                console.error("Error processing data:", error);
                // Don't let decoding errors crash the stream
                if (error instanceof Error && (error.message.includes("Invalid bool") || error.message.includes("decode"))) {
                    console.log("Recoverable decoding error - continuing...");
                    return;
                }
            }
        });

        stream.on("error", async (error: Error) => {
            console.error("Stream error:", error);
            stream.end();

            // Attempt to reconnect
            console.log("Attempting to reconnect in 5 seconds...");
            setTimeout(async () => {
                try {
                    const client = new Client(ENDPOINT, undefined, {});
                    const newStream = await client.subscribe();
                    const request = createSubscribeRequest();
                    await sendSubscribeRequest(newStream, request);
                    console.log("Successfully reconnected to gRPC stream");
                    await handleStreamEvents(newStream);
                } catch (reconnectError) {
                    console.error("Reconnection failed:", reconnectError);
                    // Try again after delay
                    setTimeout(() => handleStreamEvents(stream), 5000);
                }
            }, 5000);
        });

        stream.on("end", () => {
            console.log("Stream ended - attempting to reconnect...");
            setTimeout(() => handleStreamEvents(stream), 5000);
        });
    });
}

function handleData(data: SubscribeUpdate): void {
    try {
        const transaction = data.transaction?.transaction;
        const message = transaction?.transaction?.message;

        if (data.blockMeta?.blockhash) {
            latestBlockhash = data.blockMeta.blockhash;
            lastBlockhashTime = Date.now();
            //console.log(`Updated blockhash: ${latestBlockhash}`);
            //console.log("get lbh:",getLatestBlockhash());
        }

        if (!transaction || !message) {
            return;
        }

        const formattedSignature = convertSignature(transaction.signature);
        formatData(message, formattedSignature.base58, data);
    } catch (error) {
        // Log the error but don't throw
        console.error("Error in handleData:", error);
        if (error instanceof Error && error.message.includes("Invalid bool")) {
            console.log("Skipping transaction due to bool decoding error");
            return;
        }
        throw error; // Rethrow other errors
    }
}


function convertSignature(signature: Uint8Array): { base58: string } {
    return { base58: bs58.encode(Buffer.from(signature)) };
}

async function formatData(
    message: Message,
    signature: string,
    data: SubscribeUpdate
): Promise<FormattedTransactionData | undefined> {
    const slot = data.transaction?.slot;
    const blockHash = data.blockMeta?.blockhash;
    const accountKeys = message.accountKeys;
    const owner = new PublicKey(accountKeys[0]).toBase58();

    const account = await getRedisString(redis, owner);
    let tradeData: TradeEvent | null = null;


    let appType = "";
    let addressType = "";


    //let  foundCreateEvent = false;
    data.transaction?.transaction?.meta?.innerInstructions?.forEach((innerInstruction) => {
        innerInstruction.instructions.forEach((instruction) => {
            const decodedEvent = eventDecode(bs58.encode(instruction.data));

            if (decodedEvent?.name === "TradeEvent") {
                tradeData = decodeTradeEventValues(instruction.data);
                if (tradeData) {
                    processTradeEvent(tradeData, message, data);
                }

            }

            if (decodedEvent?.name === "CreateEvent") {
                const createData = decodeCreateEventValues(instruction.data);



                if (createData) {
                    processCreateEvent(createData, message, data);

                }

                ;
            }
        });
    });



    try {
        if (account) {
            const redisAccount: redisAddress = JSON.parse(account);
            appType = redisAccount.app;
            addressType = redisAccount.addressType;

            if (appType != "" && addressType == "minter") {
                //updateDBSolPrice();
                if (!(await getAppStatus(appType))) {
                    return;
                }

            }
        } else {
            return;
        }
    } catch (error) {
        console.error("Failed to retrieve Rugger accounts:", error);
        return;
    }

    return;
}

function decodeTradeEventValues(data: Uint8Array): TradeEvent | null {
    const buffer = Buffer.from(data).slice(8); // Remove first 8 bytes for the event CPI

    const mint = bs58.encode(buffer.slice(8, 40));
    const solAmount = buffer.readBigUInt64LE(40);
    const tokenAmount = buffer.readBigUInt64LE(48);
    // Convert to boolean properly - any non-zero value is true
    const isBuy = buffer[56] !== 0;
    const user = bs58.encode(buffer.slice(57, 89));
    const timestamp = buffer.readBigInt64LE(89);
    const virtualSolReserves = buffer.readBigUInt64LE(97);
    const virtualTokenReserves = buffer.readBigUInt64LE(105);
    const realSolReserves = buffer.readBigUInt64LE(113);
    const realTokenReserves = buffer.readBigUInt64LE(121);

    return {
        mint,
        solAmount: Number(solAmount),
        tokenAmount: Number(tokenAmount),
        isBuy,
        user,
        timestamp: Number(timestamp),
        virtualSolReserves: Number(virtualSolReserves),
        virtualTokenReserves: Number(virtualTokenReserves),
        realSolReserves: Number(realSolReserves),
        realTokenReserves: Number(realTokenReserves),
    };
}

function decodeCreateEventValues(data: Uint8Array): { name: string, symbol: string, mint: string } | null {
    try {
        const buffer = Buffer.from(data);
        //console.log("Raw buffer:", buffer.toString('hex'));

        // Skip the first 16 bytes (8 bytes discriminator + 8 bytes of additional data)
        const dataBuffer = buffer.slice(16);

        // First 4 bytes are name length (u32 LE)
        const nameLength = dataBuffer.readUInt32LE(0);
        if (nameLength > 100 || nameLength < 0) {
            return null;
        }

        // Read name
        const name = dataBuffer.slice(4, 4 + nameLength).toString('utf8');

        // Read symbol length
        const symbolLength = dataBuffer.readUInt32LE(4 + nameLength);
        if (symbolLength > 20 || symbolLength < 0) {
            return null;
        }

        // Read symbol
        const symbol = dataBuffer.slice(4 + nameLength + 4, 4 + nameLength + 4 + symbolLength).toString('utf8');

        // Read URI length
        const uriStart = 4 + nameLength + 4 + symbolLength;
        const uriLength = dataBuffer.readUInt32LE(uriStart);

        // Skip URI and read mint (last 32 bytes)
        const mintStart = uriStart + 4 + uriLength;
        const mint = bs58.encode(dataBuffer.slice(mintStart, mintStart + 32));

        return { name, symbol, mint };
    } catch (error) {
        console.error('Failed to decode CreateEvent:', error);
        return null;
    }
}

function resetTrade() {
    devMint = "";
    // Clear all tracked mint positions and dev profits
    for (const mint in mintPositions) {
        delete mintPositions[mint];
    }
    for (const mint in devProfit) {
        delete devProfit[mint];
    }
    devSold = false;

}
async function processCreateEvent(createData: { name: string, symbol: string, mint: string }, message: Message, data: SubscribeUpdate): Promise<void> {
    try {
        const blockHash = data.blockMeta?.blockhash;
        const sig = data.transaction?.transaction?.signature || [];
        const sigtxt = sig instanceof Uint8Array ? convertSignature(sig) : { base58: "invalid signature" };
        const owner = new PublicKey(message.accountKeys[0]).toString();

        if (owner != DEV_MINT_ACCOUNT) {
            return;
        }

        //resetTrade();
        devMint = createData.mint;
        mintPositions[createData.mint] = {};
        devProfit[createData.mint] = 0;
        totalSolInToken[createData.mint] = 0;
        mintBuyEnabled[createData.mint] = true;
        devSold = false;


        let tradeData: TradeEvent | null = null;
        data.transaction?.transaction?.meta?.innerInstructions?.forEach((innerInstruction) => {
            innerInstruction.instructions.forEach((instruction) => {
                const decodedEvent = eventDecode(bs58.encode(instruction.data));
                if (decodedEvent?.name === "TradeEvent") {
                   
                    tradeData = decodeTradeEventValues(instruction.data);

                    if (tradeData) {
                        console.log(`============= CA Detected: ${tradeData?.mint} ${tradeData.tokenAmount} ====================== `);
                   
                        mintPositions[createData.mint][owner] = tradeData.tokenAmount;
                        totalSolInToken[createData.mint] = tradeData.solAmount / 1e9;

                        const blockHash = getLatestBlockhash();
                        const trader_pub_wallet = tradeMatrix[0]["wallet"];
                        const trader_buy_amount = tradeMatrix[0]["amount"];



                        //buyTokens(tradeData.mint, `${trader_buy_amount}`, blockHash, tradeData.virtualSolReserves, tradeData.virtualTokenReserves, wallets[trader_pub_wallet]?.secretKey);
                        //tradeCounter++;




                    }

                }

            });
        });




        // need get tokens boyght



    } catch (error) {
        console.error('Failed to process CreateEvent:', error);
    }
}


async function processTradeEvent(tradeData: TradeEvent, message: Message, data: SubscribeUpdate): Promise<void> {
    try {
        const {
            user,
            mint,
            isBuy,
            solAmount,
            tokenAmount,
            virtualSolReserves,
            virtualTokenReserves
        } = tradeData;

        // Use the cached blockhash instead of relying on blockMeta
        const blockHash = getLatestBlockhash();
        const sig = data.transaction?.transaction?.signature;
        const sigtxt = sig instanceof Uint8Array ? convertSignature(sig) : { base58: "invalid signature" };

        ////console.log(JSON.stringify(data));

        // if (mint == "********************************************" && isBuy && user == DEV_MINT_ACCOUNT) {
        //     console.log("bought:",tokenAmount);
        //     mintPositions[mint] = {};
        //     mintPositions[mint][DEV_MINT_ACCOUNT]=tokenAmount;
        //     sellTokens(mint, `${tokenAmount}`, blockHash, wallets[DEV_MINT_ACCOUNT]?.secretKey);
        //     return;
        // }






        // if (devSold) {
        //     return;
        // }

        // if (user == DEV_MINT_ACCOUNT && !isBuy) {
        //     console.log("dev mint account:", user);
        //     devSold = true;
        //     return;
        // }


        // if (mintPositions[mint]) {
        //     if (!mintPositions[mint][DEV_MINT_ACCOUNT]) {
        //         console.log("user:", user, " no dev tokenamount");
        //         return;
        //     }
        // } else {
        //     // console.log("no mintpositions for mint:",mint);
        //     return;
        // }

        let holders = 0;
        for (const pubkey in mintPositions[mint]) {
            const tokenAmount = mintPositions[mint][pubkey];
            if (tokenAmount > 0) { holders++; }
        }
        //exit no holders
        if (holders == 0) { return; }




        //console.log("Dev tokens:",mintPositions[mint][DEV_MINT_ACCOUNT]);


        if (!DEV_TRADE_ACCOUNTS.includes(user)) {
            if (isBuy) {
                devProfit[mint] += solAmount / 1e9;
            } else {
                devProfit[mint] -= solAmount / 1e9;
            }
        } else {
            if (!mintPositions[mint][user]) { mintPositions[mint][user] = 0; }
            if (user != DEV_MINT_ACCOUNT) {
                mintPositions[mint][user] += tokenAmount;
                console.log(mintPositions);
            }

        }

        if (isBuy) {
            totalSolInToken[mint] += solAmount / 1e9;
        } else {
            totalSolInToken[mint] -= solAmount / 1e9;
        }





        console.log("Dev profit/Total sol:", devProfit[mint], totalSolInToken[mint]);


        //if (devProfit[mint] > sellDevProfit || DEV_TRADE_ACCOUNTS_NO_DEV.includes(user)) {
        //  if (devProfit[mint] > sellDevProfit || totalSolInToken[mint] > maxTotalSolInToken) {
        if (devProfit[mint] > sellDevProfit ) {
            console.log(mintPositions[mint]);
            console.log(`Dev profit above ${sellDevProfit} (${devProfit[mint]}) SOL for ${mint}. Initiating sell...`);

            // Iterate over all positions for this mint and sell each
            sellAll(mint, blockHash);
            mintBuyEnabled[mint] = false;

        } else {

            // if ( mintBuyEnabled[mint]){
            //     tradeCounter++;
            //     if (tradeCounter % 10 == 0) {
            //         walletIndex
            //     }
            //     const blockHash = getLatestBlockhash();
            //     const trader_pub_wallet= tradeMatrix[0]["wallet"] ;
            //     const trader_buy_amount= tradeMatrix[0]["amount"] ;
            //     buyTokens(tradeData.mint, `${trader_buy_amount}`, blockHash, tradeData.virtualSolReserves, tradeData.virtualTokenReserves, wallets[trader_pub_wallet]?.secretKey);
            // }


            const blockHash = getLatestBlockhash();
            const trader_pub_wallet = tradeMatrix[0]["wallet"];
            const trader_buy_amount = tradeMatrix[0]["amount"];
            //if ( user == trader_pub_wallet && mintPositions[mint][trader_pub_wallet] > 0 && tradeCounter < 4) {


            // if (tradeCounter < 4) {

            //     if (mintPositions[mint][trader_pub_wallet] > 0) {
            //         buyTokens(tradeData.mint, `${trader_buy_amount}`, blockHash, tradeData.virtualSolReserves, tradeData.virtualTokenReserves, wallets[trader_pub_wallet]?.secretKey, 1);
            //         tradeCounter++;
            //     } else {
            //         buyTokens(tradeData.mint, `${trader_buy_amount}`, blockHash, tradeData.virtualSolReserves, tradeData.virtualTokenReserves, wallets[trader_pub_wallet]?.secretKey, 0);
            //         tradeCounter++;
            //     }
            // }

        }
        // Check if dev account has tokens then sell 
        if (mintPositions[mint][DEV_MINT_ACCOUNT] > 0) {
            console.log(`Dev account has tokens. Selling...`);
            sellAllUser(mint, blockHash, DEV_MINT_ACCOUNT);
        }


    } catch (error) {
        console.error('Error processing trade event:', error);
    }
}

// Setup graceful shutdown to close connections
process.on('SIGINT', async () => {
    console.log('Received SIGINT. Closing connections...');
    try {
        // Close Redis connection
        await redis.quit();
        console.log('Redis connection closed.');

        // Close database pool
        await dbPool.end();
        console.log('Database pool closed.');

        process.exit(0);
    } catch (error) {
        console.error('Error during shutdown:', error);
        process.exit(1);
    }
});

process.on('SIGTERM', async () => {
    console.log('Received SIGTERM. Closing connections...');
    try {
        // Close Redis connection
        await redis.quit();
        console.log('Redis connection closed.');

        // Close database pool
        await dbPool.end();
        console.log('Database pool closed.');

        process.exit(0);
    } catch (error) {
        console.error('Error during shutdown:', error);
        process.exit(1);
    }
});

main().catch((err) => {
    console.error("Unhandled error in main:", err);
    process.exit(1);
});

export { getLatestBlockhash };

function sellAll(mint: string, blockHash: string) {

    for (const pubkey in mintPositions[mint]) {
        const tokenAmount = mintPositions[mint][pubkey];
        const wallet = wallets[pubkey]?.secretKey;
        if (wallet && tokenAmount > 0) {
            console.log(`Selling ${tokenAmount} tokens for pubkey ${pubkey}`);
            //await sellTokensJito(mint, `${tokenAmount}`, blockHash, wallet);
            sellTokens(mint, `${tokenAmount}`, blockHash, wallet, DEV_MINT_ACCOUNT);

        } else {
            console.log(`No wallet found or zero token amount for pubkey ${pubkey}`);
        }
    }
}

function sellAllUser(mint: string, blockHash: string, user: string) {

    for (const pubkey in mintPositions[mint]) {
        if (pubkey == user) {
            const tokenAmount = mintPositions[mint][pubkey];
            const wallet = wallets[pubkey]?.secretKey;
            if (wallet && tokenAmount > 0) {
                console.log(`Selling ${tokenAmount} tokens for pubkey ${pubkey}`);
                //await sellTokensJito(mint, `${tokenAmount}`, blockHash, wallet);
                sellTokens(mint, `${tokenAmount}`, blockHash, wallet, DEV_MINT_ACCOUNT);

            } else {
                console.log(`No wallet found or zero token amount for pubkey ${pubkey}`);
            }
        }
    }
}


// Add reconnection helper
async function reconnectStream(): Promise<ClientDuplexStream<SubscribeRequest, SubscribeUpdate>> {
    try {
        const client = new Client(ENDPOINT, undefined, {});
        const stream = await client.subscribe();
        const request = createSubscribeRequest();
        await sendSubscribeRequest(stream, request);
        console.log("Successfully created new gRPC stream connection");
        return stream;
    } catch (error) {
        console.error("Error creating new stream:", error);
        throw error;
    }
}

export async function sellTokensJito(tokenCA: string, amount: string, latestBlockHash: string, wallet: Uint8Array, devPubKey: string) {
    tradeMeasure = Date.now();
    const jitoTipAccount = new PublicKey('DttWaMuVvTiduZRnguLF7jNxTgiMBZ1hyAumKUiL2KRL');
    const jitoEndpoint = 'https://mainnet.block-engine.jito.wtf/api/v1/transactions';
    let privateKey: Uint8Array;

    if (!wallet) {
        privateKey = process.env.PRIVATE_KEY ? bs58.decode(process.env.PRIVATE_KEY) : new Uint8Array();
    } else {
        privateKey = wallet;
    }
    //const privateKey = process.env.PRIVATE_KEY ? bs58.decode(process.env.PRIVATE_KEY) : new Uint8Array();

    const signer = Keypair.fromSecretKey(privateKey);

    const tokenBalance = parseFloat(amount);
    const tipLamports = 1_000;

    const transaction = new Transaction();
    const sellInstruction = await getSellInstructionNoRPC(bs58.encode(privateKey), tokenCA, tokenBalance, signer, devPubKey);

    transaction.add(
        SystemProgram.transfer({
            fromPubkey: signer.publicKey,
            toPubkey: jitoTipAccount,
            lamports: tipLamports,
        })
    );

    if (!sellInstruction) {
        console.log('Request preparation: Failed to get sell instruction');
        return;
    }

    transaction.add(sellInstruction);
    transaction.feePayer = signer.publicKey;
    transaction.recentBlockhash = latestBlockHash;
    transaction.sign(signer);

    const serializedTransaction = bs58.encode(transaction.serialize());
    console.log(`Request preparation: Transaction serialized successfully (${serializedTransaction.length} bytes)`);

    try {
        // The connection is already warmed up here, should be much faster
        console.log(`Sending request to ${jitoEndpoint}...`);
        const startTime = Date.now();

        const response = await fetch(jitoEndpoint, {
            method: 'POST',
            body: JSON.stringify({
                jsonrpc: '2.0',
                id: 1,
                method: 'sendTransaction',
                params: [serializedTransaction]
            }),
            headers: { 'Content-Type': 'application/json' },
            agent: httpsAgent,
        });

        const responseTime = Date.now() - startTime;
        const responseText = await response.text();

        console.log(`Response received in ${responseTime}ms`);
        console.log(`Response status: ${response.status}`);
        console.log(`Response body: ${responseText}`);

        return response.status === 200 ? JSON.parse(responseText).result : response;
    } catch (error) {
        console.log('Request failed. Network or server error.');
        return error;
    }
}

export async function sellTokens(tokenCA: string, amount: string, latestBlockHash: string, wallet: Uint8Array, devPubKey: string) {

    const alchemyEndpoint = "https://solana-mainnet.g.alchemy.com/v2/********************************";


    let privateKey: Uint8Array;

    if (!wallet) {
        privateKey = process.env.PRIVATE_KEY ? bs58.decode(process.env.PRIVATE_KEY) : new Uint8Array();
    } else {
        privateKey = wallet;
    }


    const signer = Keypair.fromSecretKey(privateKey);

    const tokenBalance = parseFloat(amount);
    const transaction = new Transaction();
    const sellInstruction = await getSellInstructionNoRPC(bs58.encode(privateKey), tokenCA, tokenBalance, signer, devPubKey);



    const priorityFee = ComputeBudgetProgram.setComputeUnitPrice({
        microLamports: 500_000 // Adjust this value based on network conditions
    });
    transaction.add(priorityFee);


    if (!sellInstruction) {
        console.log('Request preparation: Failed to get sell instruction');
        return;
    }

    transaction.add(sellInstruction);
    transaction.feePayer = signer.publicKey;
    transaction.recentBlockhash = latestBlockHash;
    transaction.sign(signer);


    const serializedTransaction = bs58.encode(transaction.serialize());
    console.log(`Request preparation: Transaction serialized successfully (${serializedTransaction.length} bytes)`);

    try {

        console.log(`Sending request to ${alchemyEndpoint}...`);


        const SOLANA_CONNECTION = new Connection(alchemyEndpoint, { commitment: "confirmed" });
        const tx = await SOLANA_CONNECTION.sendRawTransaction(bs58.decode(serializedTransaction), { maxRetries: 5, skipPreflight: true });
        console.log('https://solscan.io/tx/' + tx);

    } catch (error) {
        console.log('Request failed. Network or server error.');
        return error;
    }
}



export async function buyTokens(tokenCA: string, amount: string, latestBlockHash: string, virtual_sol_reserves: number, virtual_token_reserves: number, wallet: Uint8Array, buyMore = 0) {

    const alchemyEndpoint = "https://solana-mainnet.g.alchemy.com/v2/********************************";


    let privateKey: Uint8Array;

    if (!wallet) {
        privateKey = process.env.PRIVATE_KEY ? bs58.decode(process.env.PRIVATE_KEY) : new Uint8Array();
    } else {
        privateKey = wallet;
    }


    const signer = Keypair.fromSecretKey(privateKey);

    const solAmount = parseFloat(amount);

    const transaction = new Transaction();
    const buyInstruction = await getBuyInstructionNoRPC(bs58.encode(privateKey), tokenCA, solAmount, virtual_sol_reserves, virtual_token_reserves, signer, DEV_MINT_ACCOUNT, buyMore);

    const priorityFee = ComputeBudgetProgram.setComputeUnitPrice({
        microLamports: 500_000 // Adjust this value based on network conditions
    });
    transaction.add(priorityFee);


    if (!buyInstruction) {
        console.log('Request preparation: Failed to get sell instruction');
        return;
    }

    transaction.add(buyInstruction);
    transaction.feePayer = signer.publicKey;
    transaction.recentBlockhash = latestBlockHash;
    transaction.sign(signer);


    const serializedTransaction = bs58.encode(transaction.serialize());
    console.log(`Request preparation: Transaction serialized successfully (${serializedTransaction.length} bytes)`);

    try {

        console.log(`Sending request to ${jitoEndpoint}...`);


        const SOLANA_CONNECTION = new Connection(alchemyEndpoint, { commitment: "confirmed" });
        const tx = await SOLANA_CONNECTION.sendRawTransaction(bs58.decode(serializedTransaction), { maxRetries: 15, skipPreflight: true });
        console.log('https://solscan.io/tx/' + tx);

    } catch (error) {
        console.log('Request failed. Network or server error.');
        return error;
    }
}

export async function buyTokensJito(tokenCA: string, amount: string, latestBlockHash: string, virtual_sol_reserves: number, virtual_token_reserves: number, wallet?: Uint8Array) {
    const jitoTipAccount = new PublicKey('DttWaMuVvTiduZRnguLF7jNxTgiMBZ1hyAumKUiL2KRL');
    const jitoEndpoint = 'https://mainnet.block-engine.jito.wtf/api/v1/transactions';

    let privateKey: Uint8Array;

    if (!wallet) {
        privateKey = process.env.PRIVATE_KEY ? bs58.decode(process.env.PRIVATE_KEY) : new Uint8Array();
    } else {
        privateKey = wallet;
    }


    //const privateKey = process.env.PRIVATE_KEY ? bs58.decode(process.env.PRIVATE_KEY) : new Uint8Array();
    const signer = Keypair.fromSecretKey(privateKey);

    const solAmount = parseFloat(amount);
    const tipLamports = 1_000;

    const transaction = new Transaction();
    const buyInstruction = await getBuyInstructionNoRPC(bs58.encode(privateKey), tokenCA, solAmount, virtual_sol_reserves, virtual_token_reserves, signer, DEV_MINT_ACCOUNT);

    transaction.add(
        SystemProgram.transfer({
            fromPubkey: signer.publicKey,
            toPubkey: jitoTipAccount,
            lamports: tipLamports,
        })
    );

    if (!buyInstruction) {
        console.log('Request preparation: Failed to get sell instruction');
        return;
    }

    transaction.add(buyInstruction);
    transaction.feePayer = signer.publicKey;
    transaction.recentBlockhash = latestBlockHash;
    transaction.sign(signer);

    const serializedTransaction = bs58.encode(transaction.serialize());
    console.log(`Request preparation: Transaction serialized successfully (${serializedTransaction.length} bytes)`);

    try {
        // The connection is already warmed up here, should be much faster
        console.log(`Sending request to ${jitoEndpoint}...`);
        const startTime = Date.now();

        const response = await fetch(jitoEndpoint, {
            method: 'POST',
            body: JSON.stringify({
                jsonrpc: '2.0',
                id: 1,
                method: 'sendTransaction',
                params: [serializedTransaction]
            }),
            headers: { 'Content-Type': 'application/json' },
            agent: httpsAgent,
        });

        const responseTime = Date.now() - startTime;
        const responseText = await response.text();

        console.log(`Response received in ${responseTime}ms`);
        console.log(`Response status: ${response.status}`);
        console.log(`Response body: ${responseText}`);

        return response.status === 200 ? JSON.parse(responseText).result : response;
    } catch (error) {
        console.log('Request failed. Network or server error.');
        return error;
    }
}