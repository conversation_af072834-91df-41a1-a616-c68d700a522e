import { Connection, PublicKey, ParsedInstruction } from '@solana/web3.js';
import dotenv from 'dotenv';
import { addRedisString, getRedisClient } from '../common/redis';
import { dev_getAccountsToFollow, dev_addWalletAndLink, get_dev_config, updateDevWalletBalance, getAllDevWallets } from '../common/db';
dotenv.config();

const SYBNDICA_RPC_URL = "https://solana-mainnet.api.syndica.io/api-key/21KG2L6E3kGURokSr4VwfrtYMbqnqVNYKJKCBTQv2jsiLs97iW8TQv98fcz5kvVDhS3MjVmGt91jZ3UGQpGD7zaPCuxpwgCJbek";
//const ALCHEMY_RPC_URL = "https://solana-mainnet.g.alchemy.com/v2/********************************";
//const SYBNDICA_RPC_URL=ALCHEMY_RPC_URL;

const connection = new Connection(SYBNDICA_RPC_URL, 'confirmed');
const redis = getRedisClient();

type AccountToScan = { address: string; dev_entity_id: number; scan?: number };

function isSystemAccount(address: string): boolean {
  const systemAccounts = [
    '********************************',
    'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA',
    'ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL',
    'So********************************111111112',
    'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
    'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB',
  ];
  return systemAccounts.includes(address) || address.length < 32;
}

async function getDestinationAccount(accountAddress: string, dev_entity_id: number) {
  const limit = 30;
  const publicKey = new PublicKey(accountAddress);
  const before = undefined;
  const signatures = await connection.getSignaturesForAddress(publicKey, { before, limit });

  type Transfer = {
    recipient: string;
    amount: number;
    signature: string;
    blockTime: number | null;
  };
  const significantTransfers: Transfer[] = [];

  for (const signature of signatures) {
    const transaction = await connection.getParsedTransaction(signature.signature, {
      maxSupportedTransactionVersion: 0,
    });

    if (!transaction || !transaction.transaction.message.instructions) continue;
    if (transaction.meta && transaction.meta.err) continue;

    const instructions = transaction.transaction.message.instructions as ParsedInstruction[];

    for (const ix of instructions) {
      // Only system program, type transfer, from our account, and amount > 1 SOL
      if (
        ix.program === 'system' &&
        ix.parsed?.type === 'transfer' &&
        ix.parsed.info?.source === accountAddress
      ) {
        const lamports = Number(ix.parsed.info.lamports);
        if (lamports > 1_000_000_000) {
          const recipient = ix.parsed.info.destination;
          if (!isSystemAccount(recipient)) {
            significantTransfers.push({
              recipient,
              amount: lamports,
              signature: signature.signature,
              blockTime: signature.blockTime ?? 0,
            });
          }
        }
      }
    }

    // Also check for accounts that are completely drained (original logic)
    // if (transaction.meta?.postBalances && transaction.meta?.postBalances.length > 0 && transaction.meta?.postBalances[0] === 0) {
    //   const accountKeys = transaction.transaction.message.accountKeys.map(key => key.pubkey.toBase58());
    //   if (accountKeys[1] && !isSystemAccount(accountKeys[1])) {
    //     await dev_addWalletAndLink(accountKeys[1], accountAddress, dev_entity_id,signature.blockTime ?? 0,transaction.meta?.postBalances[1]/1e9);
    //     const config = await get_dev_config(dev_entity_id);
    //     let enabled = 0, max_pct = 1, max_sol = 0.007;
    //     if (config.length > 0) {
    //       enabled = config[0].enabled;
    //       max_pct = config[0].max_pct;
    //       max_sol = config[0].max_sol;
    //     }
    //     await addRedisString(redis, accountKeys[1], JSON.stringify({
    //       dev_entity_id: dev_entity_id,
    //       parent_address: accountAddress,
    //       config: { enabled, max_hold: 200, max_sol, max_pct }
    //     }));
    //     return [[accountKeys[1], transaction.meta?.postBalances[1]]];
    //   }
    // }
  }

  // Sort by blockTime (most recent first)
  significantTransfers.sort((a, b) => (b.blockTime || 0) - (a.blockTime || 0));
  const transfersToFollow = significantTransfers.slice(0, 1); // only the last one
console.log(transfersToFollow)
  for (const transfer of transfersToFollow) {
    console.log(
      `DevID: ${dev_entity_id}, add wallet: ${transfer.recipient} Following LAST significant instruction transfer of ${
        transfer.amount / 1_000_000_000
      } SOL to ${transfer.recipient}`
    );
    await dev_addWalletAndLink(transfer.recipient, accountAddress, dev_entity_id,transfer.blockTime ?? 0,transfer.amount/1e9);

    const config = await get_dev_config(dev_entity_id);
    let enabled = 0, max_pct = 1, max_sol = 0.007;
    if (config.length > 0) {
      enabled = config[0].enabled;
      max_pct = config[0].max_pct;
      max_sol = config[0].max_sol;
    }
    await addRedisString(redis, transfer.recipient, JSON.stringify({
      dev_entity_id: dev_entity_id,
      parent_address: accountAddress,
      score: Math.floor(transfer.amount / 1_000_000_000),
      config: { enabled, max_hold: 200, max_sol, max_pct },
      transfer_info: {
        amount: transfer.amount,
        signature: transfer.signature,
        blockTime: transfer.blockTime,
        isLastTransfer: true
      }
    }));
  }
  if (transfersToFollow.length > 0) {
    return transfersToFollow.map(t => [t.recipient, t.amount]);
  }
  return null;
}

async function getMultipleAccountBalances(addresses: AccountToScan[]) {
  try {
    const publicKeys = addresses.map(addr => new PublicKey(addr.address));
    const accounts = await connection.getMultipleParsedAccounts(publicKeys);

    const result = new Map<string, { lamports: number; dev_entity_id: number }>(
      addresses.map((addr, index) => [
        addr.address,
        {
          lamports: accounts.value[index] === null ? 0 : accounts.value[index].lamports,
          dev_entity_id: addr.dev_entity_id
        }
      ])
    );

    // Store balance data for every account
    for (const [address, info] of result.entries()) {
      const balanceInSol = info.lamports / 1_000_000_000;

      // Update balance in database for every account
      try {
        await updateDevWalletBalance(address, balanceInSol);
        console.log(`Updated balance for ${address}: ${balanceInSol} SOL`);
      } catch (error) {
        console.error(`Error updating balance for ${address}:`, error);
      }

      // Process low balance accounts for destination tracking
      if (info.lamports < 100_000_000) {
        console.log(
          `Entity id: ${info.dev_entity_id} Processing very low balance account: ${address}, balance: ${balanceInSol} SOL`
        );
        await getDestinationAccount(address, info.dev_entity_id);
      }
    }
  } catch (error) {
    console.error('Error fetching multiple account balances:', error);
    return new Map();
  }
}

async function followAccount() {
  const accounts = await dev_getAccountsToFollow() as AccountToScan[];
  await getMultipleAccountBalances(accounts);
}

(async () => {
  console.log('Starting Solana money tracking service...');
  console.log('Monitoring threshold: 0.01 SOL (10M lamports)');
  console.log('Transfer threshold: 1 SOL (per instruction)');
  setInterval(async () => {
    try {
      await followAccount();
    } catch (error) {
      console.error('Error occurred:', error);
    }
  }, 5000);
})();
