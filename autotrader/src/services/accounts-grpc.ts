import Client, {
    CommitmentLevel,
    SubscribeRequest,
    SubscribeUpdate,
    SubscribeUpdateTransaction,
  } from "@triton-one/yellowstone-grpc";
  import { ClientDuplexStream } from '@grpc/grpc-js';
  import { PublicKey } from '@solana/web3.js';
  import amqp from 'amqplib/callback_api';
  import bs58 from 'bs58';
  import dotenv from 'dotenv';
  import { Redis } from 'ioredis';
  import { REDIS_URL } from "../common/config";
  import { getEnabledAccountsForGRPC } from '../common/db';
  import { applyStrategies } from './account-tx-conditions/strategy';
  const redis = new Redis({ host: REDIS_URL, port: 6379, password: "pump2pump" }); 
  dotenv.config();
  
  // RabbitMQ Configuration
  const RABBITMQ_SERVER = process.env.RABBITMQ_HOSTNAME || 'kroocoin.xyz';
  const RABBITMQ_QUEUE = 'accounts';
  const RABBITMQ_PORT = 5672;
  const RABBITMQ_USERNAME = 'pump';
  const RABBITMQ_PASSWORD = 'pump2pump';
  
  const START_ACCOUNT="C68ihvpfBjxts7iEkstnLrE4eLVo2xwigfEKRc2UcAAF";

  /**
   * 
   * 
   *    TODO: load list from database and inject online 
   * 
   */
  // Interfaces
  interface CompiledInstruction {
    programIdIndex: number;
    accounts: Uint8Array;
    data: Uint8Array;
  }
  
  interface Message {
    header: MessageHeader | undefined;
    accountKeys: Uint8Array[];
    recentBlockhash: Uint8Array;
    instructions: CompiledInstruction[];
    versioned: boolean;
    addressTableLookups: MessageAddressTableLookup[];
  }
  
  interface MessageHeader {
    numRequiredSignatures: number;
    numReadonlySignedAccounts: number;
    numReadonlyUnsignedAccounts: number;
  }
  
  interface MessageAddressTableLookup {
    accountKey: Uint8Array;
    writableIndexes: Uint8Array;
    readonlyIndexes: Uint8Array;
  }
  
  interface ConnectionCommand {
    address: string;
    command: 'connect' | 'disconnect' | 'reset';
    timestamp?: number;
  }
  
  // Constants
  const ENDPOINT = "http://grpc.solanavibestation.com:10000";
  const COMMITMENT = CommitmentLevel.PROCESSED;
  
  //const COMMITMENT = CommitmentLevel.CONFIRMED;
  
  
  
  // Track addresses for monitoring
  class AddressManager {
    private addresses: Set<string> = new Set();
    private stream: ClientDuplexStream<SubscribeRequest, SubscribeUpdate> | null = null;
    private client: Client | null = null;
    private isUpdating: boolean = false;
    private pendingUpdates: boolean = false;
    private initAddresses: string[] = [];
    
    // New properties for batching
    private pendingAddresses: Set<string> = new Set();
    private pendingRemovals: Set<string> = new Set();
    private updateTimer: NodeJS.Timeout | null = null;
    private updateIntervalMs: number = 1000; // 1 second minimum between updates
  
    constructor() {
      // this.addresses.add('6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF61');
    }
  
    setClient(client: Client) {
      this.client = client;
    }
    initAddressesList(addresses: string[]) {
      this.initAddresses = addresses;
    }
  
    setStream(stream: ClientDuplexStream<SubscribeRequest, SubscribeUpdate>) {
      this.stream = stream;
    }
  
    async addAddress(address: string): Promise<void> {
      if (this.addresses.has(address)) {
        console.log(`Address ${address} is already being monitored`);
        return;
      }
  
      // Instead of immediately adding and updating, queue the address
      this.pendingAddresses.add(address);
      console.log(`Queued address for monitoring: ${address}`);
      
      // Schedule batch update if not already scheduled
      this.scheduleBatchUpdate();
    }
  
    async removeAddress(address: string): Promise<void> {
      // Check if address exists in main set OR is pending for addition
      const addressExists = this.addresses.has(address) || this.pendingAddresses.has(address);
      
      if (!addressExists) {
        console.log(`Address ${address} is not being monitored or queued`);
        return;
      }
    
      // If the address is in the pending additions, just remove it from there
      if (this.pendingAddresses.has(address)) {
        this.pendingAddresses.delete(address);
        console.log(`Removed address from pending additions: ${address}`);
        return; // No need to queue it for removal if it wasn't yet added
      }
      
      // Otherwise queue it for removal from the main set
      this.pendingRemovals.add(address);
      console.log(`Queued address for removal: ${address}`);
      
      // Schedule batch update if not already scheduled
      this.scheduleBatchUpdate();
    }

    // Add a new method to reset all manually added addresses
    async resetAddresses(): Promise<void> {
      console.log("Resetting all manually added addresses and reloading from database...");
      
      // Clear all pending operations
      this.pendingAddresses.clear();
      this.pendingRemovals.clear();
      
      // Cancel any pending update
      if (this.updateTimer !== null) {
        clearTimeout(this.updateTimer);
        this.updateTimer = null;
      }
      
      // Clear all manually added addresses (keep only the init ones)
      this.addresses.clear();
      
      try {
        // Reload addresses from database
        const freshAccounts = await getEnabledAccountsForGRPC();
        this.initAddressesList(freshAccounts);
        console.log(`Reloaded ${freshAccounts.length} addresses from database`);
        
        // Update subscription with reset addresses
        await this.updateSubscription();
        
        console.log("Address reset completed successfully");
      } catch (error) {
        console.error("Error resetting addresses:", error);
      }
    }
  
    // New method to schedule batch updates
    private scheduleBatchUpdate(): void {
      if (this.updateTimer === null) {
        this.updateTimer = setTimeout(() => {
          this.processBatchUpdate();
        }, this.updateIntervalMs);
      }
    }
  
    // New method to process batch updates
    private async processBatchUpdate(): Promise<void> {
      this.updateTimer = null;
  
      // Apply all pending changes to the addresses set
      if (this.pendingAddresses.size > 0 || this.pendingRemovals.size > 0) {
        console.log(`Processing batch update: ${this.pendingAddresses.size} additions, ${this.pendingRemovals.size} removals`);
        
        let anyChanges = false;
        
        // Process removals
        if (this.pendingRemovals.size > 0) {
          this.pendingRemovals.forEach(address => {
            if (this.addresses.has(address)) {
              this.addresses.delete(address);
              anyChanges = true;
            }
          });
          this.pendingRemovals.clear();
        }
        
        // Process additions
        if (this.pendingAddresses.size > 0) {
          this.pendingAddresses.forEach(address => {
            if (!this.addresses.has(address)) {
              this.addresses.add(address);
              anyChanges = true;
            }
          });
          this.pendingAddresses.clear();
        }
        
        // Only update subscription if there were actual changes
        if (anyChanges) {
          await this.updateSubscription();
        }
      }
    }
  
    async updateSubscription(): Promise<void> {
      if (!this.client || !this.stream || this.isUpdating) {
        this.pendingUpdates = true;
        return;
      }
      
      try {
        this.isUpdating = true;
        console.log("Updating subscription with modified address list...");
        
        // Create request with updated addresses
        const request = this.createSubscribeRequest();
        
        // Send the updated request on the SAME stream (key difference!)
        await sendSubscribeRequest(this.stream, request);
        
        console.log("Subscription updated successfully with new address list");
        const addressList = Array.from(this.addresses);
        console.log(`Currently monitoring ${addressList.length} addresses`);
        
        this.pendingUpdates = false;
      } catch (error) {
        console.error("Error updating subscription:", error);
        
        // If there's an error, we may need to recreate the stream
        if (error instanceof Error && error.message.includes('429')) {
          console.log("Rate limit detected, will try to reconnect after delay");
          setTimeout(() => this.reconnectStream(), 30000);
        } else {
          setTimeout(() => this.updateSubscription(), 5000);
        }
      } finally {
        this.isUpdating = false;
        
        // If there were pending updates while we were processing,
        // trigger another update
        if (this.pendingUpdates) {
          setTimeout(() => this.updateSubscription(), 1000);
        }
      }
    }
  
    // Keep this as a fallback for stream errors
    async reconnectStream(): Promise<void> {
      if (!this.client || this.isUpdating) return;
      
      try {
        this.isUpdating = true;
        console.log("Reconnecting gRPC stream (fallback method)...");
        
        // Close existing stream if active
        if (this.stream) {
          this.stream.end();
        }
        
        // Create new stream
        this.stream = await this.client.subscribe();
        
        // Set up event handlers
        this.stream.on('data', handleData);
        this.stream.on("error", (error: Error) => {
          console.error('Stream error:', error);
          // Try to reconnect after a delay
          setTimeout(() => this.reconnectStream(), 5000);
        });
        
        // Send subscribe request with addresses
        const request = this.createSubscribeRequest();
        await sendSubscribeRequest(this.stream, request);
        console.log("Stream reconnected successfully");
        
      } catch (error) {
        console.error("Error reconnecting stream:", error);
        // Try again after a delay
        setTimeout(() => this.reconnectStream(), 5000);
      } finally {
        this.isUpdating = false;
      }
    }
  
    createSubscribeRequest(): SubscribeRequest {
      const accountList = [START_ACCOUNT,...this.addresses,...this.initAddresses];

      
      return {
        accounts: {},
        slots: {},
        transactions: {
          pumpFun: {
            accountInclude: accountList,
            accountExclude: [],
            accountRequired: [],
          }
        },
        transactionsStatus: {},
        entry: {},
        blocks: {},
        blocksMeta: {},
        commitment: COMMITMENT,
        accountsDataSlice: [],
        ping: undefined,
      };
    }
  
    getAddresses(): string[] {
      return [...this.addresses];
    }
  }
  

  
  
  // Global address manager
  const addressManager = new AddressManager();
 
  
  async function main(): Promise<void> {
    const initAccounts=await getEnabledAccountsForGRPC();
    const client = new Client(ENDPOINT, undefined, {});
    addressManager.setClient(client);
    addressManager.initAddressesList(initAccounts);
    // Initialize RabbitMQ connection for receiving address commands
    initRabbitMQ();
    
    try {
      // Initial connection - create once and reuse
      const stream = await client.subscribe();
      addressManager.setStream(stream);
      
      // Create initial request
      const request = addressManager.createSubscribeRequest();
      
      await sendSubscribeRequest(stream, request);
      console.log('Geyser connection established - watching ACCOUNTS. \n');
      
      // Setup event handlers
      stream.on('data', handleData);
      stream.on("error", async (error: Error) => {
        console.error('Stream error:', error);
        // Only recreate the stream if we get an error
        setTimeout(() => addressManager.reconnectStream(), 5000);
      });
      
      // Keep process running
      await new Promise(() => {});
      
    } catch (error) {
      console.error('Error in subscription process:', error);
      process.exit(1);
    }
  }
  
  function sendSubscribeRequest(
    stream: ClientDuplexStream<SubscribeRequest, SubscribeUpdate>,
    request: SubscribeRequest
  ): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      stream.write(request, (err: Error | null) => {
        if (err) {
          reject(err);
        } else {
          resolve();
        }
      });
    });
  }
  
  function handleData(data: SubscribeUpdate): void {
    if (!isSubscribeUpdateTransaction(data) || !data.filters.includes('pumpFun')) {
      return;
    }
    const transaction = data.transaction?.transaction;
    const message = transaction?.transaction?.message;
    if (message?.accountKeys[0]) {
      //console.log(new PublicKey(message.accountKeys[0]).toBase58());
    } else {
      console.log("Account key is undefined");
    }
  
    if (!transaction || !message) {
      return;
    }
  
 
  
    const formattedSignature = convertSignature(transaction.signature);
    formatData(data,message, formattedSignature.base58, data.transaction.slot);

  }
  
  function isSubscribeUpdateTransaction(data: SubscribeUpdate): data is SubscribeUpdate & { transaction: SubscribeUpdateTransaction } {
    return (
      'transaction' in data &&
      typeof data.transaction === 'object' &&
      data.transaction !== null &&
      'slot' in data.transaction &&
      'transaction' in data.transaction
    );
  }
  
  function convertSignature(signature: Uint8Array): { base58: string } {
    return { base58: bs58.encode(Buffer.from(signature)) };
  }
  
  /**
   * 
   * 
   * 
   * 
   * 
   * 
   *                     MAIN LOGIC
   * 
   * 
   * 
   * 
   * 
   * 

   */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  function formatData(data: SubscribeUpdate,message: Message, signature: string, slot: string) {

  
    const accountKeys = message.accountKeys;
    const owner = new PublicKey(accountKeys[0]).toBase58(); 
    if (data.transaction ) {
     applyStrategies(data.transaction, owner, redis);
    }


  }
  

  
  function initRabbitMQ(): void {
    amqp.connect({
      protocol: 'amqp',
      hostname: RABBITMQ_SERVER,
      port: RABBITMQ_PORT,
      username: RABBITMQ_USERNAME,
      password: RABBITMQ_PASSWORD,
    }, (error0, connection) => {
      if (error0) {
        throw error0;
      }
  
      connection.createChannel((error1, channel) => {
        if (error1) {
          throw error1;
        }
  
        channel.assertQueue(RABBITMQ_QUEUE, {
          durable: true
        });
  
        console.log(` [*] Waiting for address messages in ${RABBITMQ_QUEUE}. To exit press CTRL+C`);
  
        channel.consume(RABBITMQ_QUEUE, (msg) => {
          if (msg !== null) {
            try {
              const command = JSON.parse(msg.content.toString()) as ConnectionCommand;
              processCommand(command);
              channel.ack(msg);
            } catch (error) {
              console.error('Error processing message:', error);
              channel.nack(msg);
            }
          }
        }, {
          noAck: false
        });
      });
  
      // Handle graceful shutdown
      process.on('SIGINT', () => {
        console.log('Shutting down gRPC stream manager...');
        connection.close();
        process.exit(0);
      });
    });
  }
  
  function processCommand(command: ConnectionCommand): void {
    console.log(`Processing command: ${command.command} for address: ${command.address}`);
    
    const timestamp = new Date().getTime();
    if (command.timestamp && timestamp - command.timestamp * 1000 > 60000) {
      console.log(` [x] Skipping command due to time difference: ${timestamp - command.timestamp * 1000}ms`);
      return;
    }
  
    switch (command.command) {
      case 'connect':
        addressManager.addAddress(command.address);
        break;
      case 'disconnect':
        addressManager.removeAddress(command.address);
        break;
      case 'reset':
        // Handle reset command - this doesn't require an address
        addressManager.resetAddresses();
        break;
    }
  }
  
  
  main().catch((err) => {
    console.error('Unhandled error in main:', err);
    process.exit(1);
  });


