import { getConnection } from '../common/db';

export const findUserByUsername = async (username: string) => {
    const connection = await getConnection();
    console.log('Finding user by username:', username);
    const [rows]: [any[], any] = await connection.query('SELECT * FROM users WHERE username = ?', [username]);
    console.log('Query result:', rows);
    connection.end();
    return rows.length > 0 ? rows[0] : null;
};

export const authenticateUser = async (username: string, password: string) => {
    console.log('Authenticating user:', { username, password });
    const user = await findUserByUsername(username);
    if (user && user.password === password) {
        console.log('Authentication successful');
        return { id: user.id, username: user.username, role: user.role };
    }
    console.log('Authentication failed');
    return null;
};
