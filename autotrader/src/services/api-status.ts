import express from 'express';
import fetch from 'node-fetch';

const app = express();
const port = 3333;

const RPC_URL = "https://solana-mainnet.api.syndica.io/api-key/21KG2L6E3kGURokSr4VwfrtYMbqnqVNYKJKCBTQv2jsiLs97iW8TQv98fcz5kvVDhS3MjVmGt91jZ3UGQpGD7zaPCuxpwgCJbek";
const ACCOUNT_ADDRESS = "68s9x6ng4T5jeKe2PSZMRNESm2LTLxiDSAU5UkJPg1je";

async function getBalance(): Promise<number> {
    const response = await fetch(RPC_URL, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            jsonrpc: '2.0',
            id: 1,
            method: 'getBalance',
            params: [ACCOUNT_ADDRESS]
        })
    });

    const data = await response.json();
    const balanceInSol = data.result?.value / ********** || 0;
    return Number(balanceInSol.toFixed(4)); // Format to 4 decimal places
}

app.get('/api-info', async (_req, res) => {
    try {
        const balance = await getBalance();
        res.json({ balance });
    } catch (error) {
        console.error('Error fetching balance:', error);
        res.status(500).json({ error: 'Failed to fetch balance' });
    }
});

app.listen(port, '0.0.0.0', () => {
    console.log(`API Status service listening at http://0.0.0.0:${port}`);
});
