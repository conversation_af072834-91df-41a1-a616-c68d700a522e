import { Connection, PublicKey } from '@solana/web3.js';
import { updateDevWalletTxInfo,get_wallets } from '../common/db';

const SYNDICA_RPC_URL = 'https://solana-mainnet.api.syndica.io/api-key/21KG2L6E3kGURokSr4VwfrtYMbqnqVNYKJKCBTQv2jsiLs97iW8TQv98fcz5kvVDhS3MjVmGt91jZ3UGQpGD7zaPCuxpwgCJbek';
const connection = new Connection(SYNDICA_RPC_URL, 'confirmed');


async function updateWalletsWithLastTx() {

  const walletRows = await get_wallets();
  for (const row of walletRows as any[]) {
    const address = row.address;
    let last_tx_date = 0;
    let drained = 0;
    let balance = 0;

    try {
      const pubkey = new PublicKey(address);

      try {
        balance = await connection.getBalance(pubkey);
      } catch (e) {
        console.error(`Error getting balance for ${address}:`, e);
        // Only set drained if we can't get balance AND it's a network error
        drained = 1;
      }

      const sigs = await connection.getSignaturesForAddress(pubkey, { limit: 1 });
      if (sigs.length > 0 && sigs[0].blockTime) {
        last_tx_date = sigs[0].blockTime;
      }

      // Set drained only when balance is very low (indicating funds were transferred out)
      if (balance / 1e9 < 0.01) {
        console.log(`Setting ${address} as drained due to low balance: ${balance / 1e9} SOL`);
        drained = 1;
      }

      await updateDevWalletTxInfo(address, last_tx_date, drained, balance / 1e9);

      console.log(
        `Updated ${address} - last_tx_date: ${last_tx_date}, drained: ${drained}, balance: ${balance / 1e9} SOL`
      );
    } catch (err) {
      console.error(`Error processing ${address}:`, err);
    }
  }


}

updateWalletsWithLastTx()
  .then(() => {
    console.log('All wallets updated.');
  })
  .catch(console.error);
