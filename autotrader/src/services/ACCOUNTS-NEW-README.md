# Deep Account Tracking System

This system tracks developer accounts and their associated accounts up to 3 levels deep to identify trading accounts.

## Overview

The account tracking system combines gRPC monitoring for main accounts with RPC scanning for deeper level accounts:

1. **Main Accounts**: Monitored via gRPC for real-time transaction updates
2. **Follow1 Accounts**: First level distribution accounts that receive funds from main accounts
3. **Follow2 Accounts**: Second level distribution accounts that receive funds from follow1 accounts
4. **Trading Accounts**: Final level accounts that receive funds from follow2 accounts

## Architecture

The system uses a hybrid approach:
- **gRPC**: For real-time monitoring of main accounts (via Yellowstone gRPC)
- **Alchemy RPC**: For scanning follow1 and follow2 accounts periodically
- **Redis**: For storing account information and relationships
- **MySQL**: For persisting account data and tracking

## Account Levels

```
Main Account (gRPC)
    │
    ├── Follow1 Account (RPC scan)
    │       │
    │       ├── Follow2 Account (RPC scan)
    │       │       │
    │       │       └── Trading Account
    │       │
    │       └── Follow2 Account (RPC scan)
    │               │
    │               └── Trading Account
    │
    └── Follow1 Account (RPC scan)
            │
            └── Follow2 Account (RPC scan)
                    │
                    └── Trading Account
```

## How It Works

1. Main accounts are loaded from the database and monitored via gRPC
2. When a main account sends >1 SOL to another account, that account is marked as a follow1 account
3. Follow1 accounts are scanned periodically using Alchemy RPC
4. When a follow1 account sends funds to another account, that account is marked as a follow2 account
5. Follow2 accounts are also scanned periodically
6. When a follow2 account sends funds to another account, that account is marked as a trading account
7. All accounts are stored in Redis with their app attribution preserved

## Running the System

1. Build the TypeScript files:
   ```
   npm run build
   ```

2. Start the account tracking system:
   ```
   npm run accounts-new
   ```

## Configuration

The system uses the following configuration:
- **MIN_SOL_TRANSFER**: Minimum SOL amount to consider for tracking (default: 1 SOL)
- **SCAN_INTERVAL**: How often to scan follow1 and follow2 accounts (default: 60 seconds)
- **ALCHEMY_RPC_URL**: The Alchemy RPC endpoint for scanning accounts

## Redis Data Structure

Accounts are stored in Redis with the following structure:
```json
{
  "addressType": "main|follow1|follow2|regular",
  "address": "account_address",
  "app": "app_identifier"
}
```

The `app` identifier is preserved throughout the account hierarchy, allowing you to track which trading accounts belong to which app/project.

## Database Integration

The system adds accounts to the database with descriptive names to help identify their relationships:
- Follow1 accounts: `follow1_from_{main_account}`
- Follow2 accounts: `follow2_from_{follow1_account}`
- Trading accounts: `regular_from_{follow2_account}`
