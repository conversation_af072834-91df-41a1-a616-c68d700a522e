
import { Keypair } from '@solana/web3.js';
import { setBuyCouner, updatMintTradeDataBuy, updatMintTradeDataSell, updatMintBuyTokens, updatMintBuyTx, updateLastSoldTime } from '../../common/db';
import { sellTokenPercentage, buyToken, getTokenBalance ,sellTokensJito,checkTransactionStatus} from '../../common/solana';
//import { closeTokenAccount } from '../../common/solana';
/**
 * 
 *    Sell conditions
 *  1. sell over 2.5 sol 
 *  2. sell over 20mins !!!
*   3. sell over 50%  market cap
 * 
 */


export async function tradeBuySellAuto1(mint: string, isBuy: boolean, amount: number, mcap: number, tradeData: any, appConfig: any) {

  const sellAboveMC = tradeData.min_market_cup * (1 + (appConfig.max_mc_pct / 100));
  const sellBelowPct = 30;
  //nst platform = appConfig.platform;
  const buySol = appConfig.buy_sol;
  const sellBelowMC = tradeData.min_market_cup * (1 - (sellBelowPct / 100));
  const sellAboveProfit = appConfig.max_profit_sol;
  const sellMaxUserBuy = appConfig.max_user_buy_sol;
  const buyBelowProfit = appConfig.min_profit_sol;
  const buyBelowPct = 30;
  const is_simulation = Number(appConfig.is_simulation);
  const buyBelowMC = tradeData.min_market_cup * (1 + (buyBelowPct / 100));
  const tokenBalance = tradeData.buy_token_amount;

  console.log(`Params: sellAboveMC: ${sellAboveMC}, sellBelowMC: ${sellBelowMC}, sellAboveProfit: ${sellAboveProfit}, sellMaxUserBuy: ${sellMaxUserBuy}, buyBelowProfit: ${buyBelowProfit}, buyBelowMC: ${buyBelowMC}`);
  //const min_market_cup = tradeData.min_market_cup*(1-(appconfig.min_mc_pct/100));

  console.log(`smulation mode: ${is_simulation}`);

  if (tradeData.trade_status === 'active') {

    let buy_counter = 0;
    if (tradeData.buy_counter) { buy_counter = tradeData.buy_counter; }

    let buy = false;
    if (tradeData.cumulative_diff < buyBelowProfit && mcap < buyBelowMC) {
      buy = true;
    }

    if (buy) {
      await updatMintTradeDataBuy(mint, 'bought', buySol, 0, '', mcap, is_simulation);
      
      if (is_simulation == 0) {
        console.log(`Trade mode`);
        //if (platform == 'PUMP') {
          buyToken(mint, `${buySol}`, "100.0", 'Low').then(async (tx) => {
            console.log(`Bought: ${mint} - ${tx}`);
            if (tx) {
              await updatMintBuyTx(mint, tx);
              setBuyCouner(mint, buy_counter + 1);
            }
            //else{
            //  await updatMintTradeDataBuy(mint, 'active', buySol, 0, '', mcap, is_simulation);
           // }

          });
        //}
      }

    }

  } else if (tradeData.trade_status === 'bought' || tradeData.buy_token_amount > 0) {

    let sell = false;
    if (tradeData.cumulative_diff > sellAboveProfit || (isBuy && amount > sellMaxUserBuy) || (tradeData.time_diff_seconds > 600)) {
      sell = true;
    } else if (mcap > sellAboveMC || mcap < sellBelowMC) {
      sell = true;
    }


    if (sell) {
      console.log(`======= SELL: ${mint} - ${mcap} - ${tradeData.cumulative_diff} - ${tradeData.time_diff_seconds}`);

      // SELL once 
      
      
      await updatMintTradeDataSell(mint, 'sold', buySol, 0, '', mcap, is_simulation);

      const owner = new Keypair();



      try {
        if (is_simulation == 0) {

          if (Number(tokenBalance) > 0) {
            const latestBlockHash="";
          await sellTokensJito(mint, tokenBalance,latestBlockHash,owner.publicKey.toString())
                  .then(async (tx) => {
                      console.log(`Sold: ${mint} - ${tx}`);
                      // Check transaction status and exit accordingly
                      if (typeof tx === 'string') {
                          const success = await checkTransactionStatus(tx);
                          if (success) {
                              await updatMintTradeDataSell(mint, 'sold', buySol, 0, '', mcap, is_simulation);
                              await updateLastSoldTime(mint);
                          }else{
                          
                            await updatMintTradeDataSell(mint, 'bought', buySol, tokenBalance, '', mcap, is_simulation);
                            await updateLastSoldTime(mint);

                          }
                          
                      } else {
                          console.log('No transaction ID returned');
                        
                      }
                  })
              .catch(async (error) => {  
                  console.error(`Failed to sell tokens for mint: ${mint} - ${(error as Error).message}`);
                  await updatMintTradeDataSell(mint, 'bought', buySol, tokenBalance, '', mcap, is_simulation);
                  await updateLastSoldTime(mint);
              });


              

            }

         
      
            // await sellTokenPercentage(mint, "100", "1", 'Low').then(async (tx) => {
            //   console.log(`Sold: ${mint} - ${tx}`);
            //   await updatMintTradeDataSell(mint, 'sold', buySol, 0, '', mcap, is_simulation);
            //   await updateLastSoldTime(mint);

            // });
  

        }

      } catch (error) {
        await updatMintTradeDataBuy(mint, 'bought', buySol, tokenBalance, '', mcap, is_simulation);

        console.error(`Failed to sell tokens for mint: ${mint} - ${(error as Error).message}`);
      }



      if (is_simulation == 0) {
        console.log(`Trade mode`);
        try {
          setTimeout(async () => {
            try {

              getTokenBalance(mint).then(async (balance) => {
                if (balance && balance > 0) {
                  //if (platform == 'PUMP') {
                    await sellTokenPercentage(mint, "100", "1", 'Low').then(async (tx) => {
                      console.log(`Sold: ${mint} - ${tx}`);
                      //TODO: disconnect socket
                      await updatMintTradeDataSell(mint, 'sold', buySol, 0, '', mcap, is_simulation);
                      await updateLastSoldTime(mint);
                      //await closeTokenAccount(mint);
                      //await sendRabbitMessage(JSON.stringify({ "address": mint, "command":"disconnect" }), 'mints');
                    });
                  //}
                }else{

                  await updatMintTradeDataSell(mint, 'sold', buySol, 0, '', mcap, is_simulation);
                  await updateLastSoldTime(mint);

                }
              });

              // get balance


            } catch (error) {
              console.error(`Failed to sell tokens for mint: ${mint} - ${(error as Error).message}`);
            }
          }, 6000);
        } catch (error) {
          console.error(`Failed to sell tokens for mint: ${mint} - ${(error as Error).message}`);
        }
      }





    }
    if (is_simulation == 0) {
      if (tradeData.buy_token_amount == 0) {
        getTokenBalance(mint).then(async (balance) => {
          if (balance) { updatMintBuyTokens(mint, balance); }
        });
      }
    }

  } else if (tradeData.trade_status === 'sold') {

    // if not rugged 10 after I sell set to active


  } else if (tradeData.trade_status === 'inactive') {
    if (is_simulation == 0) {

      getTokenBalance(mint).then(async (balance) => {
        if (balance && balance > 0) {
          //if (platform == 'PUMP') {
            await sellTokenPercentage(mint, "100", "0.5", 'Low').then(async (tx) => {
              console.log(`Sold: ${mint} - ${tx}`);
              await updatMintTradeDataSell(mint, 'sold', buySol, 0, '', mcap, is_simulation);
              await updateLastSoldTime(mint);
              //await closeTokenAccount(mint);
              //await sendRabbitMessage(JSON.stringify({ "address": mint, "command":"disconnect" }), 'mints');
            });
          //}
        }
      });

    }
  }

}