import fetch from 'node-fetch';
import { httpsAgent } from '../common/agent';

const RPC_URL = 'https://mainnet.block-engine.jito.wtf/api/v1/transactions';


export const startSessionWarmup = async () => {
  console.log("🟢 Starting background warmup");
  while (true) {
    try {
      await fetch(RPC_URL, {
        method: 'POST',
        body: JSON.stringify({
          jsonrpc: '2.0',
          id: 1,
          method: 'sendTransaction',
          params: ["3kwUogs3Xs3u8ocMX1gaXaLLsy6sXyYNJPoX1vZ2uZ9jXo2QX8rSxb8Sge38qoxK1Hk3xGJEQ8ZvoNHBmtFo8ZcuRYCF7zNZ3HkYMrNCG67BgTJLHPDPqSv4hJrLqUgEqw56VcL1zRhYr98TKyHSuxbzgxFwZZoKK1UjgbVxpph5kKizKmEgPvXZ3nnE6Zt3ZH9vTzTfjpwcE6XSaga9DXujUk3Xv5n3LVsigc52E4wc8XojG6bcrwzusKgT9DAWdDSiKE1QVh3qvaFaGxzoxCHovz49fdqxYKTvBpM2Dxtsxwqo57eKt9zuAJZXB7vcJxDYRKDkeBDtXuFD1yzTKZNTBoDUaPRHXscuwiVvy92Zk2nEry83mp5xuGVRYoRBeQzGDB1mzt21wb1CwvxpPAdf8SLkEVKVj3E9WJtY6g3wxiuUzMn6jnGBM8xGCVrjsmXPbxFKjsYTy97C2ju9iw6EfQLcM6teJ2M61b82NjgwptUuKPB98439GsSsH6siTA78WgwuxLpm5gNBrPy8TXXUZbD3VdnEGFh1Gxj1RRkEhdZhfZbxoFrcWzzVZD8XxwNKXmJDpFNNMMPS4NKQz7kaAAgpZaAdLhaqY5dsZ5K3sqpxw15sDC13eMQ9rzU64MwijM7ZfGzMbhofXdwswWzvnzvYRE7ixngVpUTtofsZx5ASvQ6ViN49DCTvuQNhDP9w7QrN9yDBbezQJ5GMqyEZU5tLsgFBMLMa3WJzyChYoVsne9XxVQ424EyuehrFLcMUnmHa7YFDh4S8EKjoGeGqN"]
        }),
        headers: { 'Content-Type': 'application/json' },
        agent: httpsAgent,
      });
    } catch (err) {
      console.error("🔥 Warmup failed:", err);
    }
    await new Promise((res) => setTimeout(res, 4000));
  }
};