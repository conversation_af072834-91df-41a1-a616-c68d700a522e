import Client, {
  CommitmentLevel,
  SubscribeRequest,
  SubscribeUpdate,
  SubscribeUpdateTransaction,
} from "@triton-one/yellowstone-grpc";
import { ClientDuplexStream } from '@grpc/grpc-js';
import { Connection, PublicKey, VersionedTransactionResponse, LAMPORTS_PER_SOL } from '@solana/web3.js';
import amqp from 'amqplib/callback_api';
import bs58 from 'bs58';
import dotenv from 'dotenv';
import { Redis } from 'ioredis';
import { REDIS_URL, REDIS_PORT, REDIS_PASSWORD } from "../common/config";
import { getEnabledAccountsForGRPC, addAppAccountToDBWithName } from '../common/db';
import { getRedisClient } from "../common/redis";
import { addRedisString, getRedisString } from "../common/redis";
import { redisAddress } from "../common/types";

dotenv.config();

function lamportsToSol(lamports: number): number {
  return lamports / LAMPORTS_PER_SOL;
}

// Constants
const ENDPOINT = "http://grpc.solanavibestation.com:10000";
const COMMITMENT = CommitmentLevel.PROCESSED;
const SYNDICA_RPC_URL1 = "https://solana-mainnet.api.syndica.io/api-key/21KG2L6E3kGURokSr4VwfrtYMbqnqVNYKJKCBTQv2jsiLs97iW8TQv98fcz5kvVDhS3MjVmGt91jZ3UGQpGD7zaPCuxpwgCJbek";
const ALCHEMY_RPC_URL="https://solana-mainnet.g.alchemy.com/v2/********************************";
const SYNDICA_RPC_URL=ALCHEMY_RPC_URL;
const MIN_SOL_TRANSFER = 1 * 1e9; // 1 SOL in lamports
const SCAN_INTERVAL = 20000; // 1 minute
const RABBITMQ_SERVER = process.env.RABBITMQ_HOSTNAME || 'kroocoin.xyz';
const RABBITMQ_QUEUE = 'accounts';
const RABBITMQ_PORT = 5672;
const RABBITMQ_USERNAME = 'pump';
const RABBITMQ_PASSWORD = 'pump2pump';
const START_ACCOUNT = "C68ihvpfBjxts7iEkstnLrE4eLVo2xwigfEKRc2UcAAF";

// Interfaces
interface CompiledInstruction {
  programIdIndex: number;
  accounts: Uint8Array;
  data: Uint8Array;
}

interface Message {
  header: MessageHeader | undefined;
  accountKeys: Uint8Array[];
  recentBlockhash: Uint8Array;
  instructions: CompiledInstruction[];
  versioned: boolean;
  addressTableLookups: MessageAddressTableLookup[];
}

interface MessageHeader {
  numRequiredSignatures: number;
  numReadonlySignedAccounts: number;
  numReadonlyUnsignedAccounts: number;
}

interface MessageAddressTableLookup {
  accountKey: Uint8Array;
  writableIndexes: Uint8Array;
  readonlyIndexes: Uint8Array;
}

interface ConnectionCommand {
  address: string;
  command: 'connect' | 'disconnect' | 'reset';
  timestamp?: number;
}

interface AccountToScan {
  address: string;
  app: string;
  level: 'main' | 'follow1' | 'follow2' | 'trading';
  lastSignature?: string;
}

// Add new type for account balance tracking
interface AccountBalanceInfo extends AccountToScan {
  balance?: number;
  lastSignature?: string;
  lastBalanceCheck?: number;
}

// Track last known balances and transaction signatures
interface AccountState {
  balance: number;
  lastSignature: string;
  lastCheck: number;
}

const accountStates = new Map<string, AccountState>();

// Global variables
const redis = getRedisClient();
const syndicaConnection = new Connection(SYNDICA_RPC_URL);
const accountsToScan: Map<string, AccountToScan> = new Map();
const processedSignatures: Set<string> = new Set();
const accountBalances = new Map<string, AccountBalanceInfo>();

// Function to get multiple account balances efficiently
async function getMultipleAccountBalances(addresses: string[]): Promise<Map<string, number>> {
  try {
    const publicKeys = addresses.map(addr => new PublicKey(addr));
    const accounts = await syndicaConnection.getMultipleAccountsInfo(publicKeys);
    
    return new Map(
      accounts.map((account, index) => [
        addresses[index],
        account ? lamportsToSol(account.lamports) : 0
      ])
    );
  } catch (error) {
    console.error('Error fetching multiple account balances:', error);
    return new Map();
  }
}

// Track addresses for monitoring with gRPC
class AddressManager {
  private addresses: Set<string> = new Set();
  private stream: ClientDuplexStream<SubscribeRequest, SubscribeUpdate> | null = null;
  private client: Client | null = null;
  private isUpdating: boolean = false;
  private initAddresses: string[] = [];
  private hasInitializedFromDB: boolean = false; // New flag to track initialization

  constructor() {}

  setClient(client: Client) {
    this.client = client;
  }

  // Modified to only allow one-time initialization
  async initAddressesList(addresses: string[]) {
    if (this.hasInitializedFromDB) {
      console.log('Addresses already initialized from database, ignoring update request');
      return;
    }
    
    this.initAddresses = addresses;
    this.hasInitializedFromDB = true;
    
    // Initial subscription setup
    if (this.stream) {
      try {
        const request = this.createSubscribeRequest();
        await sendSubscribeRequest(this.stream, request);
        console.log(`Initially monitoring ${this.initAddresses.length} addresses from database`);
      } catch (error) {
        console.error('Error setting up initial subscription:', error);
        process.exit(1); // Exit if we can't set up initial subscription
      }
    }
  }

  setStream(stream: ClientDuplexStream<SubscribeRequest, SubscribeUpdate>) {
    this.stream = stream;
  }

  // Modified to prevent address additions after initialization
  async addAddress(address: string): Promise<void> {
    if (this.hasInitializedFromDB) {
      console.log('Database addresses already loaded, ignoring new address:', address);
      return;
    }
    
    if (this.addresses.has(address)) {
      return;
    }

    this.addresses.add(address);
  }

  // Modified to prevent address removals after initialization
  async removeAddress(address: string): Promise<void> {
    if (this.hasInitializedFromDB) {
      console.log('Database addresses already loaded, ignoring remove request:', address);
      return;
    }

    if (!this.addresses.has(address)) {
      return;
    }

    this.addresses.delete(address);
  }

  // Modified to prevent resetting after initialization
  async resetAddresses(): Promise<void> {
    if (this.hasInitializedFromDB) {
      console.log('Database addresses already loaded, ignoring reset request');
      return;
    }
    
    this.addresses.clear();
  }

  async updateSubscription(): Promise<void> {
    if (!this.hasInitializedFromDB) {
      console.log('Waiting for database initialization...');
      return;
    }

    if (!this.client || !this.stream || this.isUpdating) {
      return;
    }

    // Only allow the initial subscription
    if (this.stream && this.hasInitializedFromDB) {
      console.log('Initial subscription already established, ignoring update request');
      return;
    }
  }

  createSubscribeRequest(): SubscribeRequest {
    // Only use initAddresses after database initialization
    const accountList = this.hasInitializedFromDB 
      ? [...this.initAddresses] 
      : [START_ACCOUNT, ...this.addresses, ...this.initAddresses];

    return {
      accounts: {},
      slots: {},
      transactions: {
        pumpFun: {
          accountInclude: accountList,
          accountExclude: [],
          accountRequired: [],
        }
      },
      transactionsStatus: {},
      entry: {},
      blocks: {},
      blocksMeta: {},
      commitment: COMMITMENT,
      accountsDataSlice: [],
      ping: undefined,
    };
  }

  async reconnectStream(): Promise<void> {
    if (!this.client || this.isUpdating) return;
    
    try {
      this.isUpdating = true;
      console.log('Attempting to reconnect gRPC stream...');
      
      if (this.stream) {
        try {
          this.stream.end();
          this.stream.destroy();
        } catch (e) {
          console.log('Error closing existing stream:', e);
        }
      }
      
      this.stream = await this.client.subscribe();
      
      // Reestablish subscription with original database addresses
      const request = this.createSubscribeRequest();
      await sendSubscribeRequest(this.stream, request);
      console.log(`Stream reconnected successfully, monitoring ${this.initAddresses.length} addresses from database`);
      
      // Reset stream event handlers
      this.stream.on('data', handleData);
      this.stream.on("error", async (error: Error) => {
        console.error('Stream error:', error);
        setTimeout(() => this.reconnectStream(), 5000);
      });
      
    } catch (error) {
      console.error("Error reconnecting stream:", error);
      setTimeout(() => this.reconnectStream(), 5000);
    } finally {
      this.isUpdating = false;
    }
  }

  getAddresses(): string[] {
    return this.hasInitializedFromDB ? [...this.initAddresses] : [...this.addresses];
  }
}

// Global address manager
const addressManager = new AddressManager();

// Main function
async function main(): Promise<void> {
  const initAccounts = await getEnabledAccountsForGRPC();
  const client = new Client(ENDPOINT, undefined, {});
  addressManager.setClient(client);
  addressManager.initAddressesList(initAccounts);
  
  // Initialize RabbitMQ connection for receiving address commands
  initRabbitMQ();
  
  // Initialize the accounts to scan with the main accounts
  for (const account of initAccounts) {
    const redisData = await getRedisString(redis, account);
    if (redisData) {
      try {
        const accountInfo = JSON.parse(redisData) as redisAddress;
        if (accountInfo.app) {
          accountsToScan.set(account, {
            address: account,
            app: accountInfo.app,
            level: 'main'
          });
        }
      } catch (error) {
        console.error(`Error parsing Redis data for account ${account}:`, error);
      }
    }
  }
  
  try {
    // Initial connection - create once and reuse
    const stream = await client.subscribe();
    addressManager.setStream(stream);
    
    // Create initial request
    const request = addressManager.createSubscribeRequest();
    
    await sendSubscribeRequest(stream, request);
    console.log('Geyser connection established - watching ACCOUNTS. \n');
    
    // Setup event handlers
    stream.on('data', handleData);
    stream.on("error", async (error: Error) => {
      console.error('Stream error:', error);
      // Only recreate the stream if we get an error
      setTimeout(() => addressManager.reconnectStream(), 5000);
    });
    
    // Start the account scanning process
    startAccountScanning();
    
    // Keep process running
    await new Promise(() => {});
    
  } catch (error) {
    console.error('Error in subscription process:', error);
    process.exit(1);
  }
}

function sendSubscribeRequest(
  stream: ClientDuplexStream<SubscribeRequest, SubscribeUpdate>,
  request: SubscribeRequest
): Promise<void> {
  return new Promise<void>((resolve, reject) => {
    stream.write(request, (err: Error | null) => {
      if (err) {
        reject(err);
      } else {
        resolve();
      }
    });
  });
}

function handleData(data: SubscribeUpdate): void {
  if (!isSubscribeUpdateTransaction(data) || !data.filters.includes('pumpFun')) {
    return;
  }
  
  const transaction = data.transaction?.transaction;
  const message = transaction?.transaction?.message;
  
  if (!transaction || !message) {
    return;
  }

  const formattedSignature = convertSignature(transaction.signature);
  processGrpcTransaction(data, message, formattedSignature.base58, data.transaction.slot);
}

function isSubscribeUpdateTransaction(data: SubscribeUpdate): data is SubscribeUpdate & { transaction: SubscribeUpdateTransaction } {
  return (
    'transaction' in data &&
    typeof data.transaction === 'object' &&
    data.transaction !== null &&
    'slot' in data.transaction &&
    'transaction' in data.transaction
  );
}

function convertSignature(signature: Uint8Array): { base58: string } {
  return { base58: bs58.encode(Buffer.from(signature)) };
}

// Process transaction from gRPC
async function processGrpcTransaction(data: SubscribeUpdate, message: Message, signature: string, slot: string) {
  // Skip if we've already processed this signature
  if (processedSignatures.has(signature)) {
    return;
  }
  
  processedSignatures.add(signature);
  
  const accountKeys = message.accountKeys;
  const owner = new PublicKey(accountKeys[0]).toBase58();
  
  // Check if this is one of our tracked accounts
  const redisData = await getRedisString(redis, owner);
  if (!redisData) {
    return;
  }
  
  try {
    const accountInfo = JSON.parse(redisData) as redisAddress;
    const app = accountInfo.app;
    
    if (!app) {
      return;
    }
    
    // Process the transaction to find outgoing transfers
    if (data.transaction) {
      await processOutgoingTransfers(data.transaction, owner, app);
    }
  } catch (error) {
    console.error(`Error processing transaction for account ${owner}:`, error);
  }
}

// Process outgoing transfers from a transaction
async function processOutgoingTransfers(tx: SubscribeUpdateTransaction, owner: string, app: string) {
  if (!tx.transaction?.meta?.preBalances || !tx.transaction?.meta?.postBalances || !tx.transaction?.transaction?.message?.accountKeys) {
    console.log('Missing transaction data');
    return;
  }

  const preBalances = tx.transaction.meta.preBalances;
  const postBalances = tx.transaction.meta.postBalances;
  const keys = tx.transaction.transaction.message.accountKeys.map(key => {
    try {
      return new PublicKey(key).toBase58();
    } catch (error) {
      console.error('Invalid public key:', key);
      return null;
    }
  }).filter((key): key is string => key !== null);
  
  for (let j = 0; j < postBalances.length; j++) {
    const received = Number(postBalances[j]) - Number(preBalances[j] || 0);
    
    if (received > MIN_SOL_TRANSFER && j < keys.length) {
      const recipient = keys[j];
      const currentBalance = postBalances[j];
      
      if (!recipient) {
        console.log('Invalid recipient address');
        continue;
      }

      // Update account state
      const signature = tx.transaction?.transaction?.signatures?.[0] 
        ? Buffer.from(tx.transaction.transaction.signatures[0]).toString() 
        : '';

      accountStates.set(recipient, {
        balance: Number(currentBalance),
        lastSignature: signature,
        lastCheck: Date.now()
      });

      // Only scan if this is a new follow account we haven't seen before
      if (!accountsToScan.has(recipient)) {
        const newAccountInfo: AccountToScan = {
          address: recipient,
          app: app,
          level: 'follow1',
          lastSignature: signature,
        };
        
        accountsToScan.set(recipient, newAccountInfo);
        
        try {
          await scanAccountTransactions(newAccountInfo);
        } catch (error) {
          console.error(`Error scanning new account ${recipient}:`, error);
        }
      }
      
      const name = recipient ? `follow1_from_${recipient.substring(0, 8)}` : 'unknown_follow1';
      
      // Update Redis and DB
      try {
        await addRedisString(redis, recipient, JSON.stringify({
          addressType: "follow1",
          address: recipient,
          app: app
        }));
        
        await addAppAccountToDBWithName(
          recipient,
          Number(currentBalance),
          app,
          signature,
          name
        );
      } catch (error) {
        console.error(`Error updating Redis/DB for ${recipient}:`, error);
      }
    }
  }
}

// Start the account scanning process
function startAccountScanning() {
  console.log("Starting account scanning process...");
  
  setInterval(async () => {
    // Make a copy of the accounts to scan to avoid modification during iteration
    const accountsToProcess = new Map(accountsToScan);
    
    for (const [address, accountInfo] of accountsToProcess) {
      try {
        // Skip main accounts as they are already monitored by gRPC
        if (accountInfo.level === 'main') {
          continue;
        }
        
        console.log(`Scanning account: ${address} (${accountInfo.level}) for app: ${accountInfo.app}`);
        await scanAccountTransactions(accountInfo);
      } catch (error) {
        console.error(`Error scanning account ${address}:`, error);
      }
    }
  }, SCAN_INTERVAL);
}

// Scan account transactions using Alchemy RPC
async function scanAccountTransactions(accountInfo: AccountToScan) {
  if (!accountInfo || !accountInfo.address) {
    console.error('Invalid account info provided to scanAccountTransactions');
    return;
  }

  try {
    const publicKey = new PublicKey(accountInfo.address);
    
    const redisAccountData = await getRedisString(redis, accountInfo.address);
    if (!redisAccountData) return;
    
    const redisAccount = JSON.parse(redisAccountData);
    const app = redisAccount.app;
    const masterAddress = await getRedisString(redis, app);

    // Get transactions with proper options
    const transactions = await syndicaConnection.getSignaturesForAddress(
      new PublicKey(accountInfo.address),
      { limit: 10 }
    );

    for (const tx of transactions) {
      if (processedSignatures.has(tx.signature)) continue;
      processedSignatures.add(tx.signature);

      // Add maxSupportedTransactionVersion parameter
      const transaction = await syndicaConnection.getTransaction(
        tx.signature,
        {
          maxSupportedTransactionVersion: 0,
          commitment: 'confirmed'
        }
      );
      
      if (!transaction) continue;

      const preBalances = transaction.meta?.preBalances || [];
      const postBalances = transaction.meta?.postBalances || [];
      
      // Get account keys using the correct method
      const accountKeys = transaction.transaction.message.getAccountKeys();
      
      for (let i = 0; i < preBalances.length; i++) {
        const sent = Number(postBalances[i]) - Number(preBalances[i] || 0);
        if (sent <= 0) continue;
        
        const accountKey = accountKeys.get(i);
        if (!accountKey) {
          console.log(`No account key found for index ${i}`);
          continue;
        }
        
        const recipient = accountKey.toBase58();
        const addressType = redisAccount.addressType || '';

        let newType = '';
        if (app === 'auto5') {
          if (sent >= 30_000000000 && sent <= 500_000000000 && 
              recipient !== masterAddress && accountInfo.address === masterAddress) {
            newType = 'follow1';
          }
          if (sent >= 30_000000000 && sent <= 90_000000000 && 
              addressType === 'follow1' && recipient !== masterAddress && 
              accountInfo.address !== masterAddress) {
            newType = 'minter';
          }
          if (sent < 20_000000000 && addressType === 'follow1' && 
              recipient !== masterAddress && accountInfo.address !== masterAddress) {
            newType = 'follow2';
          }
          if (sent < 20_000000000 && addressType === 'follow2' && 
              recipient !== masterAddress && accountInfo.address !== masterAddress) {
            newType = 'regular';
            await sendRabbitMessage(JSON.stringify({ 
              "address": accountInfo.address, 
              "command": "disconnect" 
            }), 'accounts');
          }
        }

        if (newType) {
          // Add to Redis with correct account type structure
          await addRedisString(redis, recipient, JSON.stringify({
            addressType: newType,
            address: recipient,
            app: app
          }));

          if (newType === 'follow1' || newType === 'follow2') {
            await sendRabbitMessage(JSON.stringify({ 
              "address": recipient, 
              "command": "connect" 
            }), 'accounts');
          }

          // Add to scan queue if not a regular account
          if (newType !== 'regular') {
            accountsToScan.set(recipient, {
              address: recipient,
              app: app,
              level: newType as 'follow1' | 'follow2'
            });
          }

          // Add to database with name
          await addAppAccountToDBWithName(
            recipient,
            Number(sent),
            app,
            tx.signature,
            `${newType}_from_${accountInfo.address.substring(0, 8)}`
          );
        }
      }
    }
  } catch (error) {
    console.error(`Error scanning account ${accountInfo.address}:`, error);
  }
}

// Process transaction from RPC
async function processRpcTransaction(transaction: VersionedTransactionResponse, accountInfo: AccountToScan) {
  try {
    const accountKeys = transaction.transaction.message.staticAccountKeys.map(key => key.toBase58());
    const preBalances = transaction.meta?.preBalances || [];
    const postBalances = transaction.meta?.postBalances || [];
    
    // Skip token transactions
    if ((transaction.meta?.preTokenBalances?.length || 0) > 0 || 
        (transaction.meta?.postTokenBalances?.length || 0) > 0) {
      return;
    }
    
    // Find the index of our account
    const accountIndex = accountKeys.findIndex(key => key === accountInfo.address);
    if (accountIndex === -1) {
      return;
    }
    
    // Check for outgoing transfers
    const sent = Number(preBalances[accountIndex]) - Number(postBalances[accountIndex] || 0);
    
    // Skip if not a significant outgoing transfer
    if (sent < MIN_SOL_TRANSFER) {
      return;
    }
    
    // Find the recipient(s)
    for (let i = 0; i < postBalances.length; i++) {
      if (i === accountIndex) continue; // Skip the sender
      
      const received = Number(postBalances[i]) - Number(preBalances[i] || 0);
      
      // If this account received a significant amount
      if (received > 0) {
        const recipient = accountKeys[i];
        
        // Determine the level of the recipient based on the sender's level
        let newType = '';
        
        if (accountInfo.level === 'follow1') {
          newType = 'follow2';
          console.log(`Found follow2 account: ${recipient} from follow1 account: ${accountInfo.address}`);
        } else if (accountInfo.level === 'follow2') {
          newType = 'regular';
          console.log(`Found trading account: ${recipient} from follow2 account: ${accountInfo.address}`);
        }
        
        if (newType) {
          // Add to Redis
          await addRedisString(redis, recipient, JSON.stringify({
            addressType: newType,
            address: recipient,
            app: accountInfo.app
          }));
          
          // Add to scan queue if it's not a trading account
          if (newType !== 'regular') {
            accountsToScan.set(recipient, {
              address: recipient,
              app: accountInfo.app,
              level: newType as 'follow1' | 'follow2'
            });
            
            // Add to database with name
            await addAppAccountToDBWithName(
              recipient, 
              received, 
              accountInfo.app, 
              transaction.transaction.signatures[0], 
              `${newType}_from_${accountInfo.address.substring(0, 8)}`
            );
          }
        }
      }
    }
  } catch (error) {
    console.error(`Error processing RPC transaction for account ${accountInfo.address}:`, error);
  }
}

// Initialize RabbitMQ for receiving commands
function initRabbitMQ(): void {
  amqp.connect({
    protocol: 'amqp',
    hostname: RABBITMQ_SERVER,
    port: RABBITMQ_PORT,
    username: RABBITMQ_USERNAME,
    password: RABBITMQ_PASSWORD,
  }, (error0, connection) => {
    if (error0) {
      throw error0;
    }

    connection.createChannel((error1, channel) => {
      if (error1) {
        throw error1;
      }

      channel.assertQueue(RABBITMQ_QUEUE, {
        durable: true
      });

      console.log(` [*] Waiting for address messages in ${RABBITMQ_QUEUE}. To exit press CTRL+C`);

      channel.consume(RABBITMQ_QUEUE, (msg) => {
        if (msg !== null) {
          try {
            const command = JSON.parse(msg.content.toString()) as ConnectionCommand;
            processCommand(command);
            channel.ack(msg);
          } catch (error) {
            console.error('Error processing message:', error);
            channel.nack(msg);
          }
        }
      }, {
        noAck: false
      });
    });

    // Handle graceful shutdown
    process.on('SIGINT', () => {
      console.log('Shutting down account tracking system...');
      connection.close();
      process.exit(0);
    });
  });
}

// Process RabbitMQ commands
function processCommand(command: ConnectionCommand): void {
  console.log(`Processing command: ${command.command} for address: ${command.address}`);
  
  const timestamp = new Date().getTime();
  if (command.timestamp && timestamp - command.timestamp * 1000 > 20000) {
    console.log(` [x] Skipping command due to time difference: ${timestamp - command.timestamp * 1000}ms`);
    return;
  }

  switch (command.command) {
    case 'connect':
      addressManager.addAddress(command.address);
      break;
    case 'disconnect':
      addressManager.removeAddress(command.address);
      break;
    case 'reset':
      // Handle reset command - this doesn't require an address
      addressManager.resetAddresses();
      break;
  }
}

// Helper function to send messages to RabbitMQ
async function sendRabbitMessage(message: string, queue: string): Promise<void> {
  return new Promise((resolve, reject) => {
    amqp.connect({
      protocol: 'amqp',
      hostname: RABBITMQ_SERVER,
      port: RABBITMQ_PORT,
      username: RABBITMQ_USERNAME,
      password: RABBITMQ_PASSWORD,
    }, (error0, connection) => {
      if (error0) {
        reject(error0);
        return;
      }

      connection.createChannel((error1, channel) => {
        if (error1) {
          connection.close();
          reject(error1);
          return;
        }

        channel.assertQueue(queue, {
          durable: true
        });

        channel.sendToQueue(queue, Buffer.from(message), {
          persistent: true
        });

        setTimeout(() => {
          connection.close();
          resolve();
        }, 500);
      });
    });
  });
}

// Start the application
main().catch((err) => {
  console.error('Unhandled error in main:', err);
  process.exit(1);
});

// Setup graceful shutdown
process.on('SIGTERM', async () => {
  console.log('Received SIGTERM. Closing connections...');
  try {
    // Close Redis connection
    await redis.quit();
    console.log('Redis connection closed.');
    process.exit(0);
  } catch (error) {
    console.error('Error during shutdown:', error);
    process.exit(1);
  }
});
