

import {  VersionedTransactionResponse} from '@solana/web3.js';
import { Redis } from 'ioredis';
import { addRedisString , getRedisString } from '../../common/redis';
import { redisAddress } from '../../common/types';


export async function auto1getOutgoingAccounts(transaction: VersionedTransactionResponse,signature:string,accountKeys: string[],accountAddress:string,accountApp:string,redis:Redis) {
        if (transaction && transaction?.meta?.postTokenBalances?.length == 0 && transaction?.meta?.preTokenBalances?.length == 0 && transaction?.meta?.postBalances) {     
            const accountKeys = transaction.transaction.message.staticAccountKeys.map(key => key.toBase58());
            const keys = accountKeys.map(key => key);
            let redisAddress=""
            const postBalances =transaction?.meta?.postBalances;
            const balanceCounts = postBalances.reduce<{ [key: number]: number }>((acc, balance) => {
                if (balance == ********** ) {
                    redisAddress = "minter";
                    acc[balance] = (acc[balance] || 0) + 1;
                }
                return acc;
            }, {});

            const accountsWithBalance = Object.keys(balanceCounts).filter((balance: string) => balanceCounts[Number(balance)] > 0);
            const accounts = accountsWithBalance.map(balance => {
                const indexes = [];
                let idx = postBalances.indexOf(Number(balance));
                while (idx != -1) {
                    indexes.push(idx);
                    idx = postBalances.indexOf(Number(balance), idx + 1);
                }
                return indexes.map(index => keys[index]);
                
            });
       
                const flattenedAccounts = accounts.flat();

            if ( accounts.length > 0) {
                try {
                    const redisAccount : redisAddress = JSON.parse(await getRedisString(redis, accountAddress));
                    const auto=redisAccount.app;


                    for (const account of flattenedAccounts) {
                        //console.log('account:',account);
                        
                        if (auto != "") {
                            console.log("auto1getOutgoingAccounts ~ auto:", auto,redisAddress,account)
                            //console.log('adding Redis:', account,auto,accountAddress);
                            addRedisString(redis, account,JSON.stringify({addressType:redisAddress,address:account,app:auto}));
                        }
                        
                        //console.log('Added account to Redis:', account);
                    }
                } catch (error) {
                    console.error('Error adding accounts to Redis:', error);
                }
            }
            



    }        
      


    }