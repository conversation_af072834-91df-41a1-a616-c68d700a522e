

import { VersionedTransactionResponse} from '@solana/web3.js';
import { 
    addMint,
    getMint} from "../../common/db"; 
import { sendToDiscord } from '../../common/discord';
import { PUMPFUN_DISCORD_WEBHOOK } from '../../common/config';


export async function getTxTokenAddress(transaction: VersionedTransactionResponse,signature:string) {
        if (transaction?.meta?.postTokenBalances) {
            const accountKeys = transaction.transaction.message.staticAccountKeys.map(key => key.toBase58());
            const keys = accountKeys.map(key => key);
            const signer = keys[0];
            for (const postTokenBalance of transaction.meta.postTokenBalances) {


                if (postTokenBalance.uiTokenAmount.uiAmount && postTokenBalance.uiTokenAmount.uiAmount > ********* && postTokenBalance.owner != signer) {
                    console.log('Transaction:',signature);
                    console.log('mint:',postTokenBalance.mint);
                    console.log('owner:',postTokenBalance.owner);
                    console.log('uiAmount:',postTokenBalance.uiTokenAmount.uiAmount);
                    const mint= await getMint(postTokenBalance.mint);

                    if (!mint) {    
                       await addMint(signer,postTokenBalance.mint,"auto3");
                       //send to 
                      // sendRabbitMessage(JSON.stringify({ "mint": postTokenBalance.mint, "timestamp": timestamp, "app": "auto3" }), 'snipes');
                       await sendToDiscord(PUMPFUN_DISCORD_WEBHOOK, `New token auto3: [${postTokenBalance.mint}](https://neo.bullx.io/terminal?chainId=1399811149&address=${postTokenBalance.mint})`);
                       
                    }
                }
            }   
        }


    }