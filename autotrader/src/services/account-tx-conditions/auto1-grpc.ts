import { <PERSON>Key } from '@solana/web3.js';
import { Redis } from 'ioredis';
import { getRedisString, addRedisString } from '../../common/redis';

import  {
    SubscribeUpdateTransaction,
  } from "@triton-one/yellowstone-grpc";




export async function auto1getOutgoingAccounts(tx: SubscribeUpdateTransaction ,signature:string,accountKeys: string[],accountAddress:string,accountApp:string,redis:Redis) {
    const transaction=tx.transaction;
    if (transaction && transaction.transaction && transaction.transaction.message && transaction?.meta?.postTokenBalances?.length == 0 && transaction?.meta?.preTokenBalances?.length == 0 && transaction?.meta?.postBalances) {     
        const accountKeys = transaction.transaction.message.accountKeys.map(key => new PublicKey(key).toBase58());
        const keys = accountKeys.map(key => key);
        const redisAddress=""
        const postBalances =transaction?.meta?.postBalances;
        const balanceCounts = postBalances.reduce<{ [key: number]: number }>((acc, balance) => {
            //if (Number(balance) > 500000) {
            if (Number(balance) === **********) {
                acc[Number(balance)] = (acc[Number(balance)] || 0) + 1;
            }
            return acc;
        }, {});

        const accountsWithBalance = Object.keys(balanceCounts).filter((balance: string) => balanceCounts[Number(balance)] > 0);
        const accounts = accountsWithBalance.map(balance => {
            const indexes = [];
            let idx = postBalances.indexOf(balance.toString());
            while (idx != -1) {
                indexes.push(idx);
                idx = postBalances.indexOf(balance.toString(), idx + 1);
            }
            return indexes.map(index => keys[index]);
            
        });
   
            const flattenedAccounts = accounts.flat();

        if ( accounts.length > 0) {
            try {
                const redisAccountData = await getRedisString(redis, accountAddress);
                if (redisAccountData) {
                    const redisAccount  = JSON.parse(redisAccountData);
                    const auto=redisAccount.app;


                    for (const account of flattenedAccounts) {
                        //console.log('account:',account);
                        
                        if (auto != "") {
                            console.log("Adding to redis ~ auto1 :", auto,redisAddress,account)
                            addRedisString(redis, account,JSON.stringify({addressType:"minter",address:account,app:auto}));
                        }
                        
                        //console.log('Added account to Redis:', account);
                    }
                }
            } catch (error) {
                console.error('Error adding accounts to Redis:', error);
            }
        }
        



}        
  


}