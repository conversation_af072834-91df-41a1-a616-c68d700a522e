import { PublicKey } from "@solana/web3.js";
import Redis from "ioredis";
import { addRedisString, getRedisString } from "../../common/redis";
import {
  SubscribeUpdateTransaction,
} from "@triton-one/yellowstone-grpc";
import { sendRabbitMessage } from "../../common/rabbitmq";

export interface TokenCreationStrategy {
  name: string;
  app: string;
  detect: (transaction: SubscribeUpdateTransaction) => boolean;
  handle: (transaction: SubscribeUpdateTransaction, accountAddress: string, redis: Redis) => Promise<void>;
}

export const FiveSolMinterStrategy: TokenCreationStrategy = {
  name: '5_SOL_MINTER',
  app: 'auto1',

  detect(tx: SubscribeUpdateTransaction): boolean {
    const transaction = tx.transaction;
    if (!transaction || !transaction.transaction || !transaction.transaction.message) return false;

    const { preTokenBalances, postTokenBalances, postBalances } = transaction.meta || {};

    if ((preTokenBalances?.length || 0) > 0 || (postTokenBalances?.length || 0) > 0) return false;
    if (!postBalances || postBalances.length === 0) return false;

    return postBalances.some(balance => Number(balance) === **********);
  },

  async handle(tx: SubscribeUpdateTransaction, accountAddress: string, redis: Redis): Promise<void> {
    const transaction = tx.transaction;
    if (!transaction?.transaction?.message) return;

    const message = transaction.transaction.message;
    const keys = message.accountKeys.map(key => new PublicKey(key).toBase58());
    const postBalances = transaction.meta?.postBalances || [];

    // Identify all indexes with 5 SOL
    const fiveSolIndexes: number[] = [];
    postBalances.forEach((balance, idx) => {
      if (Number(balance) === **********) {
        fiveSolIndexes.push(idx);
      }
    });

    if (fiveSolIndexes.length === 0) return;

    try {
      const redisAccountData = await getRedisString(redis, accountAddress);
      if (!redisAccountData) return;

      const redisAccount = JSON.parse(redisAccountData);
      const app = redisAccount.app;

      if (!app) return;
      if (app !== this.app) return;

      for (const index of fiveSolIndexes) {
        const account = keys[index];
        await addRedisString(redis, account, JSON.stringify({
          addressType: 'minter',
          address: account,
          app: app
        }));

        console.log(`Added 5 SOL minter account to Redis: ${account}`);
      }
    } catch (err) {
      console.error('Error processing 5_SOL_MINTER strategy:', err);
    }
  }
};


export const SixtySolMinterStrategy: TokenCreationStrategy = {
  name: '60_SOL_MINTER_CHAIN',
  app: 'auto5',

  detect(tx: SubscribeUpdateTransaction): boolean {
    const transaction = tx.transaction;
    if (!transaction || !transaction.transaction || !transaction.transaction.message) return false;
    const postBalances = transaction.meta?.postBalances || [];
    const preBalances = transaction.meta?.preBalances || [];

    const diffBalances = preBalances.map((pre, idx) => Number(pre) - Number(postBalances[idx] || 0));

    return diffBalances.some(diff => diff >= 100000 && diff <= 500_000000000);
  },

  async handle(tx: SubscribeUpdateTransaction, accountAddress: string, redis: Redis): Promise<void> {
    const transaction = tx.transaction;
    if (!transaction || !transaction.transaction || !transaction.transaction.message) return;
    const message = transaction.transaction.message;
    if (!message) return;
    const keys = message.accountKeys.map(key => new PublicKey(key).toBase58());
    const preBalances = transaction.meta?.preBalances || [];
    const postBalances = transaction.meta?.postBalances || [];

    const redisAccountData = await getRedisString(redis, accountAddress);
   
    if (!redisAccountData) return;
    const redisAccount = JSON.parse(redisAccountData);
    const app = redisAccount.app;
    const masterAddress = await getRedisString(redis, app);

    for (let i = 0; i < preBalances.length; i++) {
      //const sent = Number(preBalances[i]) - Number(postBalances[i] || 0);
      const sent = Number(postBalances[i]) - Number(preBalances[i] || 0);
      if (sent <= 0) continue;
      const recipient = keys[i];

      let newType = '';
      const addressType = redisAccount.addressType || '';
      if (app === 'auto5') {
        if (sent >= 30_000000000 && sent <= 500_000000000 && recipient != masterAddress && accountAddress == masterAddress) {
          //console.log(this.name,   'master:', masterAddress, 'account:', accountAddress,'sent:', sent,'recipient:', recipient,);
          newType = 'follow1';
        }
        if (sent >= 30_000000000 && sent <= 90_000000000 && addressType === 'follow1' && recipient != masterAddress && accountAddress != masterAddress) {
          newType = 'minter';
          console.log("minter:",  'recipient:', recipient,);
          //console.log(this.name,   'master:', masterAddress, 'account:', accountAddress,'sent:', sent,'recipient:', recipient,);
        }
        if (sent < 20_000000000 && addressType === 'follow1' && recipient != masterAddress  && accountAddress != masterAddress ) {
          newType = 'follow2';
          //await sendRabbitMessage(JSON.stringify({ "address": accountAddress, "command": "disconnect" }), 'accounts');
          //console.log(this.name,   'master:', masterAddress, 'account:', accountAddress,'sent:', sent,'recipient:', recipient,);
        }
        if (sent < 20_000000000 && addressType === 'follow2' && recipient != masterAddress  && accountAddress != masterAddress) {
          newType = 'regular';
          await sendRabbitMessage(JSON.stringify({ "address": accountAddress, "command": "disconnect" }), 'accounts');
          console.log("regular:",  'recipient:', recipient,);
          //console.log("regular:",   'master:', masterAddress, 'account:', accountAddress,'sent:', sent,'recipient:', recipient,);
        }
      }

      if (newType) {
        if (newType == 'follow1' || newType == 'follow2') {
          // add to dataase 
          await sendRabbitMessage(JSON.stringify({ "address": recipient, "command": "connect" }), 'accounts');
          
        }
        //console.log("To redis:",this.name,   'master:', masterAddress, 'account:', accountAddress,'sent:', sent,'recipient:', recipient,);
        await addRedisString(redis, recipient, JSON.stringify({
          addressType: newType,
          address: recipient,
          app: app
        }));



      }
    }
  }
};






const strategies: TokenCreationStrategy[] = [
  FiveSolMinterStrategy, SixtySolMinterStrategy];

export async function applyStrategies(transaction: SubscribeUpdateTransaction, accountAddress: string, redis: Redis) {
  for (const strategy of strategies) {
    if (strategy.detect(transaction)) {
      await strategy.handle(transaction, accountAddress, redis);
    }
  }
}