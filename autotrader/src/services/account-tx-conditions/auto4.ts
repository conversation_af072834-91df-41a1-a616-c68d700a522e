

import {  VersionedTransactionResponse} from '@solana/web3.js';
import { Redis } from 'ioredis';
import { addRedisString , getRedisString } from '../../common/redis';
import { redisAddress } from '../../common/types';


export async function auto4getOutgoingAccounts(transaction: VersionedTransactionResponse,signature:string,accountKeys: string[],accountAddress:string,accountApp:string,redis:Redis) {
    //const keys = accountKeys.map(key => key);
    //const accuntKeyIndex = accountKeys.indexOf(accountAddress); 

        console.log('auto4getOutgoingAccounts:',accountAddress);
        if (transaction && transaction?.meta?.postTokenBalances?.length == 0 && transaction?.meta?.preTokenBalances?.length == 0 && transaction?.meta?.postBalances) {
            
            const accountKeys = transaction.transaction.message.staticAccountKeys.map(key => key.toBase58());
            const keys = accountKeys.map(key => key);
            //const signer = keys[0];
            let addressType="reguler";
            const postBalances =transaction?.meta?.postBalances;
            const balanceCounts = postBalances.reduce<{ [key: number]: number }>((acc, balance) => {
                if (balance > *********** && balance < ************ ) {   
                    addressType = "regular";    
                    acc[balance] = (acc[balance] || 0) + 1;
                } else if (balance > ************* ) {
                    addressType = "minter";
                    acc[balance] = (acc[balance] || 0) + 1;
                }
                return acc;
            }, {});

            const accountsWithBalance = Object.keys(balanceCounts).filter((balance: string) => balanceCounts[Number(balance)] > 0);
            const accounts = accountsWithBalance.map(balance => {
                const indexes = [];
                let idx = postBalances.indexOf(Number(balance));
                while (idx != -1) {
                    indexes.push(idx);
                    idx = postBalances.indexOf(Number(balance), idx + 1);
                }
                return indexes.map(index => keys[index]);
                
            });
       
                const flattenedAccounts = accounts.flat();

            if ( accounts.length > 0) {
                try {
                    const redisAccount : redisAddress = JSON.parse(await getRedisString(redis, accountAddress));
                    const auto=redisAccount.app;
                    


                    for (const account of flattenedAccounts) {
                        //console.log('account:',account);
                        
                        if (auto != "") {
                            console.log("🚀 ~ auto4getOutgoingAccounts ~ auto:", auto,addressType,account)
                            //console.log('adding Redis:', account,auto,accountAddress);
                            addRedisString(redis, account,JSON.stringify({addressType:addressType,address:account,app:auto}));
                        }
                        
                        //console.log('Added account to Redis:', account);
                    }
                } catch (error) {
                    console.error('Error adding accounts to Redis:', error);
                }
            }
    }        
    }