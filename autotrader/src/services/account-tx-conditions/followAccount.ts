import { VersionedTransactionResponse } from "@solana/web3.js";
import { logger } from "../../common/logger";
import { addAppAccountToDBWithName } from "../../common/db";
import { ignoreAccounts } from "../../common/config";
const followSol = 100;
const followSolLamports = followSol * 1e9;

export async function followAccount(
    transaction: VersionedTransactionResponse,
    accountAddress: string
) {
    const accountKeys = transaction.transaction.message.staticAccountKeys.map(
        (key) => key.toBase58()
    );

    const accuntKeyIndex = accountKeys.indexOf(accountAddress);

    if (
        transaction.meta?.postBalances &&
        transaction.meta?.postBalances.length > 0 &&
        transaction.meta?.preBalances[accuntKeyIndex] > followSolLamports
    ) {
        const preBalances = transaction.meta.preBalances;
        const postBalances = transaction.meta.postBalances;
        const signature = transaction.transaction.signatures[0];
        let maxGain = 0;
        let maxGainIndex = -1;

        for (let i = 0; i < preBalances.length; i++) {
            const gain = postBalances[i] - preBalances[i];
            if (gain > maxGain) {
                maxGain = gain;
                maxGainIndex = i;
            }
        }

        if (maxGainIndex !== -1) {
            const destinationAccount = accountKeys[maxGainIndex];
            if (maxGain > 85 * 1e9) {
                logger.debug(`Add  REGULAR account: ${destinationAccount}`);
                //ignore if
                if (ignoreAccounts.includes(destinationAccount)) {
                    logger.debug("Ignore account:", destinationAccount);
                    return;
                }
                await addAppAccountToDBWithName(
                    destinationAccount,
                    postBalances[maxGainIndex],
                    "auto3",
                    signature,
                    "regular"
                );
            }
            //logger.debug(`Disable EMPTY account: ${accountAddress}`);
            //await disableAccount(accountAddress);
        }
    }
}