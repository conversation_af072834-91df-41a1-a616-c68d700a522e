import { VersionedTransactionResponse } from "@solana/web3.js";
import { Redis } from "ioredis";
import { addRedisString, getRedisString } from "../../common/redis";
import { redisAddress } from "../../common/types";

export async function getOutgoingAccounts(
    transaction: VersionedTransactionResponse,
    signature: string,
    accountKeys: string[],
    accountAddress: string,
    accountApp: string,
    redis: Redis
) {
    //const keys = accountKeys.map(key => key);
    // const accuntKeyIndex = accountKeys.indexOf(accountAddress);

    // if (transaction.meta?.preBalances && transaction.meta?.postBalances) {
    //     const preBalances = transaction.meta.preBalances;
    //     const postBalances = transaction.meta.postBalances;
    //     const accountIndex = accountKeys.indexOf(accountAddress);

    //     if (accountIndex !== -1) {
    //         const outgoingTransfer = preBalances[accountIndex] - postBalances[accountIndex];
    //         if (outgoingTransfer > 1 * 1e9 && (!transaction.meta?.postTokenBalances || transaction.meta?.postTokenBalances.length === 0)) {
    //             console.log(`Outgoing SOL transfer greater than 1 SOL detected for ${accountAddress}: ${outgoingTransfer / 1e9} SOL`);
    //             const outgoingAccountIndex = postBalances.findIndex((balance, index) => index !== accountIndex && balance > preBalances[index]);
    //             if (outgoingAccountIndex !== -1) {
    //                 const outgoingAccount = accountKeys[outgoingAccountIndex];
    //                 console.log(`Outgoing address: ${outgoingAccount}`);
    //                 addRedisString(redis, outgoingAccount,accountApp);
    //             }
    //             //TODO: add to redis set
    //         }
    //     }
    // }

    if (
        transaction &&
        transaction?.meta?.postTokenBalances?.length == 0 &&
        transaction?.meta?.preTokenBalances?.length == 0 &&
        transaction?.meta?.postBalances
    ) {
        const accountKeys = transaction.transaction.message.staticAccountKeys.map(
            (key) => key.toBase58()
        );
        const keys = accountKeys.map((key) => key);
        //const signer = keys[0];
        console.log(keys);
        const postBalances = transaction?.meta?.postBalances;
        const balanceCounts = postBalances.reduce<{ [key: number]: number }>(
            (acc, balance) => {
                if (balance > **********) {
                    acc[balance] = (acc[balance] || 0) + 1;
                }
                return acc;
            },
            {}
        );

        const accountsWithBalance = Object.keys(balanceCounts).filter(
            (balance: string) => balanceCounts[Number(balance)] > 0
        );
        const accounts = accountsWithBalance.map((balance) => {
            const indexes = [];
            let idx = postBalances.indexOf(Number(balance));
            while (idx != -1) {
                indexes.push(idx);
                idx = postBalances.indexOf(Number(balance), idx + 1);
            }
            return indexes.map((index) => keys[index]);
        });

        const flattenedAccounts = accounts.flat();

        if (accounts.length > 0) {
            try {
                const redisAccount: redisAddress = JSON.parse(
                    await getRedisString(redis, accountAddress)
                );
                const auto = redisAccount.app;

                for (const account of flattenedAccounts) {
                    //console.log('account:',account);

                    if (auto != "") {
                        console.log("adding Redis:", account, auto, accountAddress);
                        addRedisString(
                            redis,
                            account,
                            JSON.stringify({
                                addressType: "regular",
                                address: account,
                                app: auto,
                            })
                        );
                    }

                    //console.log('Added account to Redis:', account);
                }
            } catch (error) {
                console.error("Error adding accounts to Redis:", error);
            }
        }
    }
}
