import Client, {
    CommitmentLevel,
    SubscribeRequest,
    SubscribeUpdate,
  } from "@triton-one/yellowstone-grpc";
  import { ClientDuplexStream } from "@grpc/grpc-js";
  import { Connection, Commitment, PublicKey, Keypair, Transaction, ComputeBudgetProgram, LAMPORTS_PER_SOL, SystemProgram, TransactionInstruction, TransactionMessage, VersionedTransaction } from '@solana/web3.js';
  import dotenv from 'dotenv';
  import bs58 from 'bs58';
  import { httpsAgent } from '../common/agent';
  import { RPC_URL_CS } from "../common/config";
  import { bufferFromUInt64 } from "../common/trade";
  import { getBondingCurveAddresses } from "../test/curve";
  import { createAssociatedTokenAccountInstruction, getAssociatedTokenAddress, TOKEN_PROGRAM_ID } from "@solana/spl-token";
  import { FEE_RECIPIENT, GLOBAL, PUMP_FUN_ACCOUNT, PUMP_FUN_PROGRAM, RENT } from "../common/solana";
  
  dotenv.config();
  const ENDPOINT = "http://grpc.solanavibestation.com:10000";
  const RPC_URL = "https://solana-mainnet.api.syndica.io/api-key/21KG2L6E3kGURokSr4VwfrtYMbqnqVNYKJKCBTQv2jsiLs97iW8TQv98fcz5kvVDhS3MjVmGt91jZ3UGQpGD7zaPCuxpwgCJbek";
  const COMMITMENT: Commitment = 'confirmed';
  const TEST_TOKEN_ADDRESS = "6WXcS4PVPubMkV87z6u1ShudYxoN4LhJ5MVBAYgpump";
  const TRADING_ACCOUNT = "68s9x6ng4T5jeKe2PSZMRNESm2LTLxiDSAU5UkJPg1je";
  let txPlacementTime = 0;
  let txSignature: string | undefined;
  let hasExecutedTrade = false;
  const jitoEndpoint = 'https://mainnet.block-engine.jito.wtf/api/v1/transactions';

  // Initialize Solana connection
  const connection = new Connection(RPC_URL_CS, {
    commitment: COMMITMENT,
    confirmTransactionInitialTimeout: 60000,
    httpAgent: httpsAgent,
  });
  

  const CHAINSTACK_RPC = "https://nd-656-324-033.p2pify.com/7026d0c4e4356e4f9fc0a1170759e31f";
const SOLANA_CONNECTION = new Connection(CHAINSTACK_RPC, {wsEndpoint:process.env.SOLANA_WSS, commitment: "confirmed"});
console.log(`Connected to Solana RPC at ${CHAINSTACK_RPC.slice(0, -36)}`);

// Decodes the provided environment variable private key and generates a Keypair.
const privateKey = new Uint8Array(bs58.decode(process.env.PRIVATE_KEY!));
const FROM_KEYPAIR = Keypair.fromSecretKey(privateKey);
console.log(`Initial Setup: Public Key - ${FROM_KEYPAIR.publicKey.toString()}`);

// Config priority fee and amount to transfer
const PRIORITY_RATE = 50000000; // MICRO_LAMPORTS
const AMOUNT_TO_TRANSFER = 0.001 * LAMPORTS_PER_SOL;

// Instruction to set the compute unit price for priority fee
const PRIORITY_FEE_INSTRUCTIONS = ComputeBudgetProgram.setComputeUnitPrice({microLamports: PRIORITY_RATE});


  async function getValidBlockhash(retries = 1): Promise<string> {
    for (let i = 0; i < retries; i++) {
      try {
        console.log(`Attempt ${i + 1}: Getting latest blockhash...`);
        const { blockhash } = await connection.getLatestBlockhash('confirmed');
        console.log(`Got blockhash: ${blockhash}`);
        return blockhash;
      } catch (error) {
        console.error(`Attempt ${i + 1} failed to get blockhash:`, error);
        if (i === retries - 1) throw error;
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    throw new Error('Failed to get valid blockhash after retries');
  }
  
  async function executeTestTrade(retries = 1): Promise<void> {
    for (let i = 0; i < retries; i++) {
      try {
        console.log(`Trade execution attempt ${i + 1}/${retries}`);
        
        // Get fresh blockhash each time
        const blockhash = await getValidBlockhash();
        txPlacementTime = Date.now();
        
        console.log(`Trade execution started at: ${txPlacementTime}`);
        console.log(`Using blockhash: ${blockhash}`);
  
        const result = await buyTokensJito(
          TEST_TOKEN_ADDRESS,
          "0.1",
          blockhash,
          1000000000,
          1000000000
        );
  
        if (typeof result === 'string' && result.length > 20) {  // Likely a signature
          txSignature = result;
          console.log(`Transaction submitted with signature: ${txSignature}`);
          return;
        } else {
          const errorMsg = typeof result === 'object' ? 
            (result.message || JSON.stringify(result)) : result;
            
          console.error(`Attempt ${i + 1}: Failed to submit transaction:`, errorMsg);
          
          // Check if the error is related to blockhash
          const isBlockhashError = 
            (typeof errorMsg === 'string' && 
            (errorMsg.includes('Blockhash not found') || 
             errorMsg.includes('BlockhashNotFound'))) ||
            (typeof result === 'object' && 
             result.code === 32002);
          
          if (isBlockhashError) {
            console.log("Blockhash error detected, getting fresh blockhash immediately...");
            continue; // Skip the delay and try again immediately with a fresh blockhash
          }
          
          // For other errors, use backoff strategy
          const backoffTime = Math.min(2000 * Math.pow(2, i), 15000);
          console.log(`Waiting ${backoffTime}ms before retry...`);
          await new Promise(resolve => setTimeout(resolve, backoffTime));
        }
      } catch (error) {
        console.error(`Attempt ${i + 1}: Error executing trade:`, error);
        const backoffTime = Math.min(2000 * Math.pow(2, i), 15000);
        console.log(`Waiting ${backoffTime}ms before retry...`);
        await new Promise(resolve => setTimeout(resolve, backoffTime));
      }
    }
    
    console.error("All retry attempts failed");
    process.exit(1);
  }
  
  function createSubscribeRequest(): SubscribeRequest {
    // Map Solana Commitment to CommitmentLevel

    
    return {
      accounts: {},
      slots: {},
      transactions: {
        pumpFun: {
          accountInclude: [TRADING_ACCOUNT],
          accountExclude: [],
          accountRequired: [],
        }
      },
      transactionsStatus: {},
      entry: {},
      blocks: {},
      blocksMeta: {},
      commitment: CommitmentLevel.PROCESSED,
      accountsDataSlice: [],
      ping: undefined,
    };
  }
  
  async function sendSubscribeRequest(
    stream: ClientDuplexStream<SubscribeRequest, SubscribeUpdate>,
    request: SubscribeRequest
  ): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      stream.write(request, (err: Error | null) => {
        if (err) {
          reject(err);
        } else {
          resolve();
        }
      });
    });
  }
  
  function handleStreamData(data: SubscribeUpdate): void {
    // Handle transaction confirmation
    console.log("Received data:", data);
    const txConfirmTime = Date.now();
    const latency = txConfirmTime - txPlacementTime;
    
    console.log(`Transaction confirmed!`);
    console.log(`Placement time: ${txPlacementTime}`);
    console.log(`Confirmation time: ${txConfirmTime}`);
    console.log(`Total latency: ${latency}ms`);
    
    process.exit(0);


    // if (txSignature && data.transaction?.transaction?.signature) {
    //   const receivedSignature = bs58.encode(Buffer.from(data.transaction.transaction.signature));
  
    //   if (receivedSignature === txSignature) {
    //     const txConfirmTime = Date.now();
    //     const latency = txConfirmTime - txPlacementTime;
        
    //     console.log(`Transaction confirmed!`);
    //     console.log(`Placement time: ${txPlacementTime}`);
    //     console.log(`Confirmation time: ${txConfirmTime}`);
    //     console.log(`Total latency: ${latency}ms`);
        
    //     process.exit(0);
    //   }
    // }
  }
  
  async function main() {
    try {
      console.log("Starting main execution...");
      
      // Execute trade first

      
      // Only set up monitoring if we have a transaction signature

        const client = new Client(ENDPOINT, undefined, {});
        console.log("Connecting to gRPC stream...");
        
        const stream = await client.subscribe();
        console.log("Stream connected, setting up subscription...");
  
        const request = createSubscribeRequest();
        await sendSubscribeRequest(stream, request);
        console.log("Subscription established, monitoring for transactions...");
  
        stream.on('data', handleStreamData);
        stream.on('error', (error: Error) => {
          console.error('Stream error:', error);
          process.exit(1);
        });

        sendTransactionWithPriorityFee();

    } catch (error) {
      console.error("Error in main:", error);
      process.exit(1);
    }
  }
  
  process.on('SIGINT', () => {
    console.log('Shutting down...');
    process.exit(0);
  });
  
  export async function buyTokensJito(tokenCA: string, amount: string, latestBlockHash: string, virtual_sol_reserves: number, virtual_token_reserves: number) {
    const jitoEndpoint = RPC_URL_CS; 
    const privateKey = process.env.PRIVATE_KEY ? bs58.decode(process.env.PRIVATE_KEY) : new Uint8Array();
    const signer = Keypair.fromSecretKey(privateKey);
    const solAmount = parseFloat(amount);
  
    // Create a new transaction
    const transaction = new Transaction();
    
    // Add priority fee instruction
    const priorityFee = ComputeBudgetProgram.setComputeUnitPrice({
      microLamports: 500_000
    });
    transaction.add(priorityFee);
    
    // Get buy instructions
    const buyInstructions = await getBuyInstructionsNoRPC(
      bs58.encode(privateKey), 
      tokenCA, 
      solAmount, 
      virtual_sol_reserves, 
      virtual_token_reserves
    );
    
    if (!buyInstructions || buyInstructions.length === 0) {
      console.log('Request preparation: Failed to get buy instructions');
      return "Failed to get buy instructions";
    }
    
    // Add all instructions to the transaction
    buyInstructions.forEach(instruction => {
      transaction.add(instruction);
    });
    
    // Set fee payer and blockhash
    transaction.feePayer = signer.publicKey;
    transaction.recentBlockhash = latestBlockHash;
    
    // Sign transaction
    transaction.sign(signer);
    
    // Serialize and send
    const serializedTransaction = transaction.serialize();
    console.log(`Request preparation: Transaction serialized successfully (${serializedTransaction.length} bytes)`);
    
    try {
      console.log(`Sending request to ${jitoEndpoint}...`);
      const startTime = Date.now();
      
      const response = await fetch(jitoEndpoint, {
        method: 'POST',
        body: JSON.stringify({
          jsonrpc: '2.0',
          id: 1,
          method: 'sendTransaction',
          params: [bs58.encode(serializedTransaction), { encoding: 'base58' }]
        }),
        headers: { 'Content-Type': 'application/json' },
      });
      
      const responseTime = Date.now() - startTime;
      const responseText = await response.text();
      
      console.log(`Response received in ${responseTime}ms`);
      console.log(`Response status: ${response.status}`);
      console.log(`Response body: ${responseText}`);
      
      const responseJson = JSON.parse(responseText);
      
      if (response.status === 200 && responseJson.result) {
        return responseJson.result;
      } else if (responseJson.error) {
        console.error(`Transaction error: ${responseJson.error.message || 'Unknown error'}`);
        return responseJson.error;
      } else {
        return `HTTP Error: ${response.status}`;
      }
    } catch (error) {
      console.log('Request failed. Network or server error:', error);
      return error;
    }
  }
  
  export async function getBuyInstructionsNoRPC(privateKey: string, tokenAddress: string, amount: number, virtual_sol_reserves: number, virtual_token_reserves: number) {
    const instructions: TransactionInstruction[] = [];
    const { bondingCurve, associatedBondingCurve } = getBondingCurveAddresses(tokenAddress);
    console.log('bonding assoc mint:', bondingCurve, associatedBondingCurve, tokenAddress);
    
    const walletPrivateKey = Keypair.fromSecretKey(bs58.decode(privateKey));
    const owner = walletPrivateKey.publicKey;
    const token = new PublicKey(tokenAddress);
    
    const tokenAccountAddress = await getAssociatedTokenAddress(token, owner, false);
    
    // Add ATA creation instruction if needed
    try {
      await connection.getTokenAccountBalance(tokenAccountAddress);
      // Token account exists, no need to create
      console.log("Token account exists, not adding creation instruction");
    } catch (error) {
      // Token account doesn't exist, add instruction to create it
      console.log("Token account doesn't exist, adding creation instruction",error);
      instructions.push(
        createAssociatedTokenAccountInstruction(
          walletPrivateKey.publicKey, 
          tokenAccountAddress, 
          walletPrivateKey.publicKey, 
          token
        )
      );
    }
    
    const tokenAccount = tokenAccountAddress;
    const tokenOut = 400000 * 1000000;
    
    const amountWithSlippage = amount * 2;
    const maxSolCost = Math.floor(amountWithSlippage * LAMPORTS_PER_SOL);
    const ASSOCIATED_USER = tokenAccount;
    const USER = owner;
    const BONDING_CURVE = new PublicKey(bondingCurve);
    const ASSOCIATED_BONDING_CURVE = new PublicKey(associatedBondingCurve);
    
    const keys = [
      { pubkey: GLOBAL, isSigner: false, isWritable: false },
      { pubkey: FEE_RECIPIENT, isSigner: false, isWritable: true },
      { pubkey: token, isSigner: false, isWritable: false },
      { pubkey: BONDING_CURVE, isSigner: false, isWritable: true },
      { pubkey: ASSOCIATED_BONDING_CURVE, isSigner: false, isWritable: true },
      { pubkey: ASSOCIATED_USER, isSigner: false, isWritable: true },
      { pubkey: USER, isSigner: true, isWritable: true },  // Changed to isSigner: true since this is the user's wallet
      { pubkey: SystemProgram.programId, isSigner: false, isWritable: false },
      { pubkey: TOKEN_PROGRAM_ID, isSigner: false, isWritable: false },
      { pubkey: RENT, isSigner: false, isWritable: false },
      { pubkey: PUMP_FUN_ACCOUNT, isSigner: false, isWritable: false },
      { pubkey: PUMP_FUN_PROGRAM, isSigner: false, isWritable: false }
    ];
    
    const data = Buffer.concat([
      bufferFromUInt64('16927863322537952870'), 
      bufferFromUInt64(tokenOut), 
      bufferFromUInt64(maxSolCost)
    ]);
    
    const buyInstruction = new TransactionInstruction({
      keys: keys,
      programId: PUMP_FUN_PROGRAM,
      data: data
    });
    
    instructions.push(buyInstruction);
    return instructions;
  }
  
  console.log("Starting program...");
  main().catch((err) => {
    console.error("Unhandled error:", err);
    process.exit(1);
  });

  async function sendTransactionWithPriorityFee() {
    // Create instructions for the transaction
    const jitoTipAccount = new PublicKey('DttWaMuVvTiduZRnguLF7jNxTgiMBZ1hyAumKUiL2KRL'); 
    const tipLamports = 1_000; 
    const jitoEndpoint = 'https://mainnet.block-engine.jito.wtf/api/v1/transactions';
    const jito_CONNECTION = new Connection(jitoEndpoint, { commitment: "confirmed"});
    const instructions: TransactionInstruction[] = [
        // SystemProgram.transfer({
        //     fromPubkey: FROM_KEYPAIR.publicKey,
        //     toPubkey: jitoTipAccount,
        //     lamports: tipLamports,
        //   }),

      SystemProgram.transfer({
        fromPubkey: FROM_KEYPAIR.publicKey,
        toPubkey: FROM_KEYPAIR.publicKey,
        lamports: AMOUNT_TO_TRANSFER
      }),
      PRIORITY_FEE_INSTRUCTIONS
    ];
  
    // Get the latest blockhash
    const latestBlockhash = await SOLANA_CONNECTION.getLatestBlockhash('confirmed');
    console.log(" ✅ - Fetched latest blockhash. Last Valid Height:", latestBlockhash.lastValidBlockHeight);
  
    // Generate the transaction message
    const messageV0 = new TransactionMessage({
      payerKey: FROM_KEYPAIR.publicKey,
      recentBlockhash: latestBlockhash.blockhash,
      instructions: instructions
    }).compileToV0Message();
    console.log(" ✅ - Compiled Transaction Message");
  
    // Create a VersionedTransaction and sign it
    const transaction = new VersionedTransaction(messageV0);
    transaction.sign([FROM_KEYPAIR]);
    console.log(" ✅ - Transaction Signed");
  
    console.log(`Sending ${AMOUNT_TO_TRANSFER / LAMPORTS_PER_SOL} SOL from ${FROM_KEYPAIR.publicKey} to ${FROM_KEYPAIR.publicKey} with priority fee rate ${PRIORITY_RATE} microLamports`);
  
    try {
      // Send the transaction to the network
      //txPlacementTime = Date.now();
      
      await SOLANA_CONNECTION.sendTransaction(transaction, {  skipPreflight: true });
      txPlacementTime = Date.now();

      console.log(" ✅ - Transaction sent to network");
  
    //   // Confirm the transaction
    //   const confirmation = await SOLANA_CONNECTION.confirmTransaction({
    //     signature: txid,
    //     blockhash: latestBlockhash.blockhash,
    //     lastValidBlockHeight: latestBlockhash.lastValidBlockHeight
    //   }, 'processed');
    //   if (confirmation.value.err) {
    //     throw new Error("🚨 Transaction not confirmed.");
    //   }
  
      // Get the transaction result

    //   const txResult = await SOLANA_CONNECTION.getTransaction(txid, {maxSupportedTransactionVersion: 0})
    //   console.log('🚀 Transaction Successfully Confirmed!', '\n', `https://solscan.io/tx/${txid}`);
    //   console.log(`Transaction Fee: ${txResult?.meta?.fee} Lamports`);
    } catch (error) {
      console.log(error);
    }
  }