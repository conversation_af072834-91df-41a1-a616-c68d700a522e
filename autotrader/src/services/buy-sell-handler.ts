import amqp from 'amqplib/callback_api';
import dotenv from 'dotenv';
import { getMintTradeData, updateMaxMC, getAppConfig } from "../common/db";
import { tradeBuySellAuto1 } from './buy-sell-conditions/auto1';


dotenv.config();

const RABBITMQ_SERVER = process.env.RABBITMQ_HOSTNAME || '';
const RABBITMQ_QUEUE = 'trade';
const RABBITMQ_PORT = parseInt(process.env.RABBITMQ_PORT || '5672');
const RABBITMQ_USERNAME = process.env.RABBITMQ_USERNAME || '';
const RABBITMQ_PASSWORD = process.env.RABBITMQ_PASSWORD || '';




const seenMessages = new Set<string>();

amqp.connect({
  protocol: 'amqp',
  hostname: RABBITMQ_SERVER,
  port: RABBITMQ_PORT,
  username: RABBITMQ_USERNAME,
  password: RABBITMQ_PASSWORD,
}, (error0, connection) => {
  if (error0) {
    throw error0;
  }
  connection.createChannel((error1, channel) => {
    if (error1) {
      throw error1;
    }
    channel.assertQueue(RABBITMQ_QUEUE, {
      durable: true
    });

    console.log(` [*] Waiting for messages in ${RABBITMQ_QUEUE}. To exit press CTRL+C`);

    channel.purgeQueue(RABBITMQ_QUEUE);
    console.log(`Queue '${RABBITMQ_QUEUE}' has been purged.`);
    channel.consume(RABBITMQ_QUEUE, async (msg) => {
      if (msg !== null) {
        const content = msg.content.toString();
        const message = JSON.parse(content);
        const mint = message.mint;
        const isBuy = message.isBuy;
        const amount = parseFloat(message.amount);
        const mcap = message.mcap;
        //const timestamp = message.timestamp;
        const hash = message.hash;
        const app = message.app;


        if (seenMessages.has(hash)) {
          channel.ack(msg);
          return;
        }

        seenMessages.add(hash);


        const appConfig = await getAppConfig(app);
        const tradeData = await getMintTradeData(mint);
        let trade = "buy";
        if (!isBuy) {
          trade = "sell";
        }

        if (mcap || mcap > 0) {
          if (tradeData && tradeData.time_diff_seconds) {
            const tsec = `${tradeData.time_diff_seconds}`;
            const tradediff = `(${tradeData.total_buys.toFixed(2)}-${tradeData.total_sells.toFixed(2)})=${tradeData.cumulative_diff.toFixed(2)}`;
            console.log(`${tsec.padEnd(6)} ${app} ${mint} ${tradeData.trade_status} ${tradediff.padEnd(20)} trade:${trade.padEnd(6)} amount:${amount.toFixed(2).padStart(6)} mc:${mcap.toFixed(0).padEnd(6)} app:${app}  holding:${tradeData.buy_token_amount.toFixed(2).padStart(10)}  `);

            //console.log(tradeData);
            //
            //if (app == 'auto1') {
             // if (!appConfig[0].is_simulation) {

                await tradeBuySellAuto1(mint, isBuy, amount, mcap, tradeData,appConfig[0]);
              
             
           // }


            updateMaxMC(mint, tradeData.max_market_cup);

          }
        }

        channel.ack(msg);

      }
    }, {
      noAck: false
    });
  });
});