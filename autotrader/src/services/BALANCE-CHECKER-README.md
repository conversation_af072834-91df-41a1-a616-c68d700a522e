# Dev Wallet Balance Checker

This service continuously monitors and updates the balance of all dev wallets in the `dev_wallets` table.

## Overview

The balance checker service:
- Fetches all dev wallets from the `dev_wallets` table
- Checks their current SOL balances using Alchemy RPC
- Updates the `balance` column in the database with the latest balance
- Runs continuously with configurable intervals
- Processes accounts in batches to avoid rate limits

## Features

- **Batch Processing**: Processes accounts in batches of 100 to avoid RPC rate limits
- **Real-time Updates**: Updates balance data every 30 seconds (configurable)
- **Error Handling**: Continues processing even if individual accounts fail
- **Progress Tracking**: Logs progress and significant balance changes
- **Graceful Shutdown**: Handles SIGINT and SIGTERM signals properly

## Configuration

Key configuration options in `balance-checker.ts`:

```typescript
const BATCH_SIZE = 100; // Process accounts in batches
const CHECK_INTERVAL = 30000; // Check balances every 30 seconds
const ALCHEMY_RPC_URL = "..."; // RPC endpoint for balance checks
```

## Running the Service

1. Build the TypeScript files:
   ```bash
   npm run build
   ```

2. Start the balance checker:
   ```bash
   npm run balance-checker
   ```

## Output

The service provides detailed logging:

- **Progress Updates**: Shows how many accounts have been processed
- **Balance Alerts**: 
  - ⚠️ Low balance (< 0.01 SOL but > 0)
  - 💸 Zero balance accounts
  - 💰 High balance accounts (> 1 SOL)
- **Completion Status**: Shows total processing time for each cycle

Example output:
```
🚀 Starting Dev Wallet Balance Checker...
📊 Check interval: 30 seconds
📦 Batch size: 100 accounts per batch
🔗 RPC endpoint: https://solana-mainnet.g.alchemy.com/v2/...

🔄 Starting balance check cycle at 2024-01-15T10:30:00.000Z
📋 Found 1,250 dev wallets to check
✅ Processed 10/1250 accounts - Latest: ABC...XYZ = 0.005000 SOL
⚠️  Low balance detected - Entity 123: DEF...UVW = 0.008500 SOL
💸 Zero balance - Entity 456: GHI...RST = 0 SOL
💰 High balance - Entity 789: JKL...MNO = 2.450000 SOL
✅ Completed balance check for all 1,250 dev wallets
⏱️  Balance check completed in 45.32 seconds
```

## Database Integration

The service updates the `balance` column in the `dev_wallets` table:

```sql
UPDATE dev_wallets 
SET balance = ? 
WHERE address = ?
```

This allows other parts of the system to:
- Track wallet balance changes over time
- Identify drained or low-balance wallets
- Monitor fund movements between wallets
- Generate alerts for significant balance changes

## Integration with Other Services

The balance checker works alongside:

- **pumpswap-follower.ts**: Uses balance data to identify when wallets are drained
- **accounts-new.ts**: Can use balance information for account tracking decisions
- **mints-grpc.ts**: May reference balance data for trading decisions

## Error Handling

The service includes robust error handling:
- Individual account failures don't stop the entire batch
- Network errors are logged but don't crash the service
- Database connection errors are handled gracefully
- Rate limiting is managed through batch processing and delays

## Performance Considerations

- Uses Alchemy RPC for reliable batch balance checking
- Processes 100 accounts per batch with small delays between batches
- Runs every 30 seconds to balance freshness with API usage
- Logs are optimized to show progress without overwhelming output

## Monitoring

Monitor the service by watching for:
- Regular completion messages every 30 seconds
- Error messages that might indicate RPC or database issues
- Balance alerts for accounts of interest
- Processing time increases that might indicate performance issues
