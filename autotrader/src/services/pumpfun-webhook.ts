import Fastify from 'fastify';
import mysql from 'mysql2/promise';
import dotenv from 'dotenv';
import { sendToDiscord } from '../common/discord';
import { Redis } from 'ioredis';
import { getRedisString, addRedisString } from '../common/redis';
import {
  REDIS_URL, PUMPFUN_DISCORD_WEBHOOK
} from '../common/config';
import { getAppStatus } from '../common/db';
import { updateDBSolPrice } from '../common/pump';
import { redisAddress } from '../common/types';
import { sendRabbitMessage } from '../common/rabbitmq';
const redis = new Redis({ host: REDIS_URL, port: 6379, password: "pump2pump" });

dotenv.config();

const fastify = Fastify({ logger: false });

const pool = mysql.createPool({
  host: process.env.MYSQL_HOST,
  user: process.env.MYSQL_USER,
  password: process.env.MYSQL_PASSWORD,
  database: process.env.MYSQL_DATABASE,
});
console.log("start service");
fastify.post('/webhook', async (request, reply) => {
  const data = request.body as any;
  const sig = data[0].signature;
  const type = data[0].type;
  const timestamp = data[0].timestamp;
  const slot = data[0].slot;
  const owner = data[0].feePayer;

  try {
    let appType = "";
    let addressType = "";
    const account =await getRedisString(redis, owner);
    if (account){
      const redisAccount : redisAddress = JSON.parse(account);
      appType=redisAccount.app;
      addressType=redisAccount.addressType;
    }



    if (appType != "" && addressType == "minter") {
      updateDBSolPrice();
      if (! await getAppStatus(appType)) {
        console.log(`[${new Date().toISOString()}] App ${appType} is disabled`);
        return reply.send({ status: "success" });
      }



      const currentDate = new Date().toISOString();
      console.log(`[${currentDate}] New transaction: ${sig} ${type} ${timestamp} ${slot} ${owner} ${appType}`);
      for (const token of data[0].tokenTransfers) {
        if (token.mint !== "So11111111111111111111111111111111111111112") {
          const mint = token.mint;
          const currentTime = new Date().toLocaleTimeString('en-US', { hour12: false });
          console.log(`${currentTime} mint: ${mint}  }`);

          await pool.query('INSERT INTO rugger_mints (rugger, trade_status, mint, buy_sol_amount, buy_token_amount, sell_sol_amount, sell_token_amount, buy_tx, sell_tx, buy_mc, sell_mc, created_at, app) VALUES (?,?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)', [owner, "active", mint, 0, 0, 0, 0, 0, 0, 0, 0, new Date(), appType]);

          //addAddressToWebhook(mint)
          //TODO: send to websocket rabbitmq
          
          addRedisString(redis, mint,JSON.stringify({addressType:"token",address:mint,app:appType}));
          await sendRabbitMessage(JSON.stringify({ "address": mint, "command":"connect" }), 'mints');
          sendToDiscord(PUMPFUN_DISCORD_WEBHOOK, `New token (${appType}): ${mint} ${data[0].signature} ${currentDate}`);

          break;
        }
      }
    }
  } catch (error) {
    console.error('Failed to retrieve Rugger accounts:', error);
  }

  return reply.send({ status: "success" });
});

fastify.listen({ port: 7008, host: '0.0.0.0' }, (err, address) => {
  if (err) {
    fastify.log.error(err);
    process.exit(1);
  }
  fastify.log.info(`Server is running on ${address}`);
});