import {
    getEnabledAccounts,
    AddressApp,
    updateAccountStatus,
    updateAccountSignature
} from "../common/db";

import { Connection, PublicKey, VersionedTransactionResponse } from '@solana/web3.js';
import dotenv from 'dotenv';
import {
    REDIS_PASSWORD,
    REDIS_PORT,
    RPC_URL,
    REDIS_URL,
    ignoreAccounts
} from '../common/config';

import { Redis } from 'ioredis';
import { getRedisString } from '../common/redis';
import { logger } from "../common/logger";
import { auto1getOutgoingAccounts } from './account-tx-conditions/auto1';
import { auto4getOutgoingAccounts } from './account-tx-conditions/auto4';
import { redisAddress } from '../common/types';

dotenv.config();

let redis: Redis;


async function getTransactions(redis: Redis, accountAddress: string, last_sig?: string | undefined) {
    const connection = new Connection(RPC_URL);
    const limit = 50;
    //TODO: reduce to 10 and find until last_sig or max 1000 in 100 iteratiosn. 
    const before = undefined;
    const publicKey = new PublicKey(accountAddress);
    const signatures = await connection.getSignaturesForAddress(publicKey, { before, limit: limit });

    const firstSig = signatures[0].signature;
    const sigs = [];
    //Get only valid transactioins
    for (const signature of signatures) {
        if (signature.err === null) {

            if (signature.signature === last_sig) {
                break;
            }
            sigs.push(signature.signature);
        }
    }



    //get bulk transaction data
    const txs = await connection.getTransactions(sigs, {
        maxSupportedTransactionVersion: 0,
    });
    //logger.debug(`txs: ${txs.length}`);
    for (const transaction of txs) {
        if (transaction) {
            await processTransaction(redis, transaction, accountAddress);
        }
    }

    await updateAccountSignature(accountAddress, firstSig);

    await updateAccountStatus(accountAddress, "exists");
}


async function processTransaction(redis: Redis, transaction: VersionedTransactionResponse, accountAddress: string) {
    const signature = transaction.transaction.signatures[0];
    const redisdata   = await getRedisString(redis, accountAddress);
    let jsonData: redisAddress ;
    let accountApp="";
    if (redisdata) {
        jsonData = JSON.parse(redisdata);
        accountApp = jsonData.app;
    }
     

    const accountKeys = transaction.transaction.message.staticAccountKeys.map(key => key.toBase58());
    
    //logger.debug(`app: ${accountApp}  signature: ${signature}  account: ${accountAddress}`);
    //await followAccount(transaction,signature,accountAddress,accountApp);

    // Check for outgoing SOL transfer greater than 1 SOL
    if (accountApp == "auto1") {
        await auto1getOutgoingAccounts(transaction, signature, accountKeys, accountAddress, accountApp, redis);
    }
    if (accountApp == "auto4") {
        await auto4getOutgoingAccounts(transaction, signature, accountKeys, accountAddress, accountApp, redis);
    }
    //await getOutgoingAccounts(transaction,signature,accountKeys,accountAddress,accountApp,redis);

    // rule 1: get mint 
    //await getTxTokenAddress(transaction,signature);            

}



(async () => {

    redis = new Redis({ host: REDIS_URL, port: REDIS_PORT, password: REDIS_PASSWORD });

    let isProcessing = false;

    setInterval(async () => {
        if (isProcessing) {

            return;
        }

        isProcessing = true;
        try {

            const accountsApp: AddressApp[] = await getEnabledAccounts("auto%");


            for (const account of accountsApp) {
                if (ignoreAccounts.includes(account.address)) {
                    logger.debug('Ignore account:', account.address);
                    continue;
                }
                logger.debug(` ====(${account.app}) Processing account: ${account.address} Balance: ${account.balance / 1e9} =====`);
                await getTransactions(redis, account.address, account.last_sig);

            }
            //logger.debug('Processing completed');

        } catch (error) {
            console.error('Error occurred:', error);
        } finally {
            isProcessing = false;
        }
    }, 2000);
    //redis.disconnect();


})();