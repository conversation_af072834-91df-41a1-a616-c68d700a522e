/* eslint-disable @typescript-eslint/no-unused-vars */
import Client, {
  CommitmentLevel,
  SubscribeRequest,
  SubscribeUpdate,
  SubscribeUpdateTransaction,
} from "@triton-one/yellowstone-grpc";
//import { sendToDiscord } from "../common/discord";
import dotenv from "dotenv";
import { ClientDuplexStream } from "@grpc/grpc-js";
import { PublicKey } from "@solana/web3.js";
import bs58 from "bs58";
import { getRedisString, getRedisClient } from "../common/redis";
import { redisAddress } from "../common/types";
import { getAppStatus, saveTransactionsToDatabase, updatMintTradeDataBuy, updatMintTradeDataSell, getAppConfigAll } from "../common/db";
import { getDbPool } from "../common/db-pool";
import { RPC_URL_CS, RPC_URL_SYNDICA } from "../common/config";
import { JITO_MAINNET_TIP_ACCOUNTS } from "../common/config";
import { BorshCoder } from "@coral-xyz/anchor";
import { ruleIsSignedByAuto1Owner } from "../common/pump-rules";
// eslint-disable-next-line @typescript-eslint/no-require-imports
const idl = require("../common/idl-new.json");
const coder = new BorshCoder(idl as any);
import { Connection, clusterApiUrl } from "@solana/web3.js";
import https from "https";
import fetch from "node-fetch";
import { httpsAgent } from '../common/agent';
import { startSessionWarmup } from './mints-grpc-connwarmer';
import { Keypair, Transaction,VersionedTransaction,TransactionInstruction, SystemProgram, ComputeBudgetProgram } from '@solana/web3.js';
import { getSellInstructionNoRPC, getBuyInstructionNoRPC } from "../common/trade";

dotenv.config();

let tradeMeasure = 0.0;
let appConfigs: ConfigByApp;
let latestBlockhash: string | null = null;
let lastValidBlockHeightPRIV: number = 0;
let lastBlockhashTime: number = 0;
const connection = new Connection(RPC_URL_SYNDICA, 'processed');
const mintTracker: { [key: string]: MintStats } = {};
const ENDPOINT = "http://grpc.solanavibestation.com:10000";
const PUMP_FUN_PROGRAM_ID = "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P";
// Use the shared Redis client instead of creating a new connection
const redis = getRedisClient();
const PUMP_FUN_CREATE_IX_DISCRIMINATOR = Buffer.from([24, 30, 200, 40, 5, 28, 7, 119,]);
const TRADING_ACCOUNT = "68s9x6ng4T5jeKe2PSZMRNESm2LTLxiDSAU5UkJPg1je";
const COMMITMENT = CommitmentLevel.PROCESSED;
const FILTER_CONFIG = {
  programIds: [PUMP_FUN_PROGRAM_ID, TRADING_ACCOUNT],
  instructionDiscriminators: [PUMP_FUN_CREATE_IX_DISCRIMINATOR],
};

const devAccounts = new Set();
const jitoEndpoint = 'https://mainnet.block-engine.jito.wtf/api/v1/transactions';

const ACCOUNTS_TO_INCLUDE = [
  {
    name: "mint",
    index: 0,
  },
];

const agent = new https.Agent({
  keepAlive: true,
  keepAliveMsecs: 60000 // Set keepAlive timeout to 1 minute (60000 ms)
});

interface FormattedTransactionData {
  signature: string;
  slot: string;
  [accountName: string]: string;
}

type MintStats = {
  // Creation data
  created_at: number;  // Unix timestamp
  name: string;
  symbol: string;
  owner: string;
  signature: string;

  // Trading stats
  sum_buys: number;
  sum_sells: number;
  dev_profit: number;
  tokenAmount?: number;  // Added tokenAmount property
  solAmount?: number;  // Added solAmount property
  buyPrice?: number;  // Added buyPrice property

  // Market data
  app: string;  // Will be updated during trades
  mcap_current: number;
  mcap_max: number;
};

type RedisAddress = {
  addressType: string;
  address: string;
  app: string;
};

// Use the shared database pool instead of creating a new one
const dbPool = getDbPool();

interface CompiledInstruction {
  programIdIndex: number;
  accounts: Uint8Array;
  data: Uint8Array;
}

interface Message {
  header: MessageHeader | undefined;
  accountKeys: Uint8Array[];
  recentBlockhash: Uint8Array;
  instructions: CompiledInstruction[];
  versioned: boolean;
  addressTableLookups: MessageAddressTableLookup[];
}

interface MessageHeader {
  numRequiredSignatures: number;
  numReadonlySignedAccounts: number;
  numReadonlyUnsignedAccounts: number;
}

interface MessageAddressTableLookup {
  accountKey: Uint8Array;
  writableIndexes: Uint8Array;
  readonlyIndexes: Uint8Array;
}
type TradeEvent = {
  mint: string;
  solAmount: number;
  tokenAmount: number;
  isBuy: boolean;
  user: string;
  timestamp: number;
  virtualSolReserves: number;
  virtualTokenReserves: number;
  realSolReserves: number;
  realTokenReserves: number;
  signature?: string;
  signer?: string;
  block_id?: number;
  app?: string;
};

type AutoConfig = {
  enabled: number;
  app: string;
  is_simulation: number;
  buy_sol: number;
  max_time_sec: number;
  min_time_sec: number;
  max_profit_sol: number;
  min_profit_sol: number;
  max_user_buy_sol: number;
  max_mc_pct: number;
};

type ConfigByApp = {
  [key: string]: Omit<AutoConfig, 'app'>;
};

function convertConfigs(configs: AutoConfig[]): ConfigByApp {
  const result: ConfigByApp = {};

  configs.forEach(config => {
    const { app, ...rest } = config;
    result[app] = rest;
  });

  return result;
}


async function updateLatestBlockhash(): Promise<void> {
  try {
    const { blockhash, lastValidBlockHeight } = await connection.getLatestBlockhash();
    lastValidBlockHeightPRIV = lastValidBlockHeight;
    latestBlockhash = blockhash;
    lastBlockhashTime = Date.now();
    //console.log(`Updated blockhash: ${blockhash}, height: ${lastValidBlockHeight}`);
  } catch (error) {
    console.error('Failed to fetch latest blockhash:', error);
  }
}

function startBlockhashUpdateProcess(): void {
  // Initial update
  updateLatestBlockhash();

  // Update every 2 seconds
  setInterval(updateLatestBlockhash, 2000);
}

function getLatestBlockhash(): string {
  if (!latestBlockhash) {
    return "test";
  }
  return latestBlockhash;
}

function eventDecode(data: string): any {
  try {
    const instruction = coder.events.decode(
      Buffer.from(bs58.decode(data)).slice(8).toString("base64")
    );
    return instruction;
  } catch (error) {
    console.error("Event decode error:", error);
    // Return null instead of throwing
    return null;
  }
}

async function main(): Promise<void> {
  //startBlockhashUpdateProcess();
  startSessionWarmup();
  appConfigs = convertConfigs(await getAppConfigAll());
  console.log("App Configs:", appConfigs);

  while (true) {
    try {
      const stream = await reconnectStream();
      await handleStreamEvents(stream);
    } catch (error) {
      console.error("Main loop error:", error);
      console.log("Retrying in 5 seconds...");
      await new Promise(resolve => setTimeout(resolve, 5000));
    }
  }
}

function createSubscribeRequest(): SubscribeRequest {
  return {
    accounts: {},
    slots: {},
    transactions: {
      pumpFun: {
        accountInclude: FILTER_CONFIG.programIds,
        accountExclude: [],
        accountRequired: [],// Add this to request metadata
      },
    },
    transactionsStatus: {},
    entry: {},
    blocks: {},
    blocksMeta: { blockmetadata: {} },
    commitment: COMMITMENT,
    accountsDataSlice: [],
    ping: undefined,
  };
}

function sendSubscribeRequest(
  stream: ClientDuplexStream<SubscribeRequest, SubscribeUpdate>,
  request: SubscribeRequest
): Promise<void> {
  return new Promise<void>((resolve, reject) => {
    stream.write(request, (err: Error | null) => {
      if (err) {
        reject(err);
      } else {
        resolve();
      }
    });
  });
}

function handleStreamEvents(
  stream: ClientDuplexStream<SubscribeRequest, SubscribeUpdate>
): Promise<void> {
  return new Promise<void>((resolve, reject) => {
    stream.on("data", async (data) => {
      try {
        await handleData(data);
      } catch (error) {
        console.error("Error processing data:", error);
        // Don't let decoding errors crash the stream
        if (error instanceof Error && (error.message.includes("Invalid bool") || error.message.includes("decode"))) {
          console.log("Recoverable decoding error - continuing...");
          return;
        }
      }
    });

    stream.on("error", async (error: Error) => {
      console.error("Stream error:", error);
      stream.end();

      // Attempt to reconnect
      console.log("Attempting to reconnect in 5 seconds...");
      setTimeout(async () => {
        try {
          const client = new Client(ENDPOINT, undefined, {});
          const newStream = await client.subscribe();
          const request = createSubscribeRequest();
          await sendSubscribeRequest(newStream, request);
          console.log("Successfully reconnected to gRPC stream");
          await handleStreamEvents(newStream);
        } catch (reconnectError) {
          console.error("Reconnection failed:", reconnectError);
          // Try again after delay
          setTimeout(() => handleStreamEvents(stream), 5000);
        }
      }, 5000);
    });

    stream.on("end", () => {
      console.log("Stream ended - attempting to reconnect...");
      setTimeout(() => handleStreamEvents(stream), 5000);
    });
  });
}

function handleData(data: SubscribeUpdate): void {
  try {
    const transaction = data.transaction?.transaction;
    const message = transaction?.transaction?.message;

    if (data.blockMeta?.blockhash) {
      latestBlockhash = data.blockMeta.blockhash;
      lastBlockhashTime = Date.now();
      //console.log(`Updated blockhash: ${latestBlockhash}`);
      //console.log("get lbh:",getLatestBlockhash());
    }

    if (!transaction || !message) {
      return;
    }

    const formattedSignature = convertSignature(transaction.signature);
    formatData(message, formattedSignature.base58, data);
  } catch (error) {
    // Log the error but don't throw
    console.error("Error in handleData:", error);
    if (error instanceof Error && error.message.includes("Invalid bool")) {
      console.log("Skipping transaction due to bool decoding error");
      return;
    }
    throw error; // Rethrow other errors
  }
}

function isSubscribeUpdateTransaction(
  data: SubscribeUpdate
): data is SubscribeUpdate & { transaction: SubscribeUpdateTransaction } {
  return (
    "transaction" in data &&
    typeof data.transaction === "object" &&
    data.transaction !== null &&
    "slot" in data.transaction &&
    "transaction" in data.transaction
  );
}

function convertSignature(signature: Uint8Array): { base58: string } {
  return { base58: bs58.encode(Buffer.from(signature)) };
}

async function formatData(
  message: Message,
  signature: string,
  data: SubscribeUpdate
): Promise<FormattedTransactionData | undefined> {
  const slot = data.transaction?.slot;
  const blockHash = data.blockMeta?.blockhash;
  const accountKeys = message.accountKeys;
  const owner = new PublicKey(accountKeys[0]).toBase58();

  const account = await getRedisString(redis, owner);
  let tradeData: TradeEvent | null = null;


  let appType = "";
  let addressType = "";



  data.transaction?.transaction?.meta?.innerInstructions?.forEach((innerInstruction) => {
    innerInstruction.instructions.forEach((instruction) => {
      const decodedEvent = eventDecode(bs58.encode(instruction.data));
      if (decodedEvent?.name === "TradeEvent") {
        tradeData = decodeTradeEventValues(instruction.data);
        if (tradeData) {
          processTradeEvent(tradeData, message, data);
        }

      }
      if (decodedEvent?.name === "CreateEvent") {
        const createData = decodeCreateEventValues(instruction.data);



        if (createData) {
          processCreateEvent(createData, message, data);

        }

        ;
      }
    });
  });



  try {
    if (account) {
      const redisAccount: redisAddress = JSON.parse(account);
      appType = redisAccount.app;
      addressType = redisAccount.addressType;

      if (appType != "" && addressType == "minter") {
        //updateDBSolPrice();
        if (!(await getAppStatus(appType))) {
          //     console.log(
          //       `[${new Date().toISOString()}] App ${appType} is disabled ${account}`
          //     );
          return;
        }

        //const currentDate = new Date().toISOString();

        // Example of using the database pool for queries:
        // await dbPool.execute('INSERT INTO rugger_mints (rugger, trade_status, mint, buy_sol_amount, buy_token_amount, sell_sol_amount, sell_token_amount, buy_tx, sell_tx, buy_mc, sell_mc, created_at, app) VALUES (?,?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)', [owner, "active", mint, 0, 0, 0, 0, 0, 0, 0, 0, new Date(), appType]);

        //TODO: setup inmemory array and calculate profit inmemory . init tokens["tokenname"] = {totabuys:0,totalsells:0,profit:0,mcap:0,apptype:"app01","status":"bought","tokenamount":3333333,"selltx":2222,"buytx":11111,"sellmc":3333,"buymc":11111}
        //TODO: Buy token in async put record in db . later on streadm check what i buy for any token and update database.
        //TODO: send out trades data out to add to

        //addRedisString(redis, mint,JSON.stringify({addressType:"token",address:mint,app:appType}));
        //await sendRabbitMessage(JSON.stringify({ "address": mint, "command":"connect" }), 'mints');
        //await sendRabbitMessage(JSON.stringify({ "address": appType, "command":"reset" }), 'accounts');
        //sendToDiscord(PUMPFUN_DISCORD_WEBHOOK, `gRPC (${appType}): ${mint} } ${currentDate}`);

        // console.log("Mint: ", mint," owner: ", owner);

        // const includedAccounts = ACCOUNTS_TO_INCLUDE.reduce<Record<string, string>>((acc, { name, index }) => {
        //   const accountIndex = matchingInstruction.accounts[index];
        //   const publicKey = accountKeys[accountIndex];
        //   acc[name] = new PublicKey(publicKey).toBase58();
        //   return acc;
        // }, {});

        //console.log("======================================💊 New Pump.fun Mint Detected!======================================");
        //console.table({signature,slot,...includedAccounts,owner});
        //console.log("\n");
      }
    } else {
      return;
    }
  } catch (error) {
    console.error("Failed to retrieve Rugger accounts:", error);
    return;
  }

  return;
}

function matchesInstructionDiscriminator(ix: CompiledInstruction): boolean {
  return (
    ix?.data &&
    FILTER_CONFIG.instructionDiscriminators.some((discriminator) =>
      Buffer.from(discriminator).equals(ix.data.slice(0, 8))
    )
  );
}

function decodeTradeEventValues(data: Uint8Array): TradeEvent | null {
  const buffer = Buffer.from(data).slice(8); // Remove first 8 bytes for the event CPI

  const mint = bs58.encode(buffer.slice(8, 40));
  const solAmount = buffer.readBigUInt64LE(40);
  const tokenAmount = buffer.readBigUInt64LE(48);
  // Convert to boolean properly - any non-zero value is true
  const isBuy = buffer[56] !== 0;
  const user = bs58.encode(buffer.slice(57, 89));
  const timestamp = buffer.readBigInt64LE(89);
  const virtualSolReserves = buffer.readBigUInt64LE(97);
  const virtualTokenReserves = buffer.readBigUInt64LE(105);
  const realSolReserves = buffer.readBigUInt64LE(113);
  const realTokenReserves = buffer.readBigUInt64LE(121);

  return {
    mint,
    solAmount: Number(solAmount),
    tokenAmount: Number(tokenAmount),
    isBuy,
    user,
    timestamp: Number(timestamp),
    virtualSolReserves: Number(virtualSolReserves),
    virtualTokenReserves: Number(virtualTokenReserves),
    realSolReserves: Number(realSolReserves),
    realTokenReserves: Number(realTokenReserves),
  };
}

function decodeCreateEventValues(data: Uint8Array): { name: string, symbol: string, mint: string } | null {
  try {
    const buffer = Buffer.from(data);
    //console.log("Raw buffer:", buffer.toString('hex'));

    // Skip the first 16 bytes (8 bytes discriminator + 8 bytes of additional data)
    const dataBuffer = buffer.slice(16);

    // First 4 bytes are name length (u32 LE)
    const nameLength = dataBuffer.readUInt32LE(0);
    if (nameLength > 100 || nameLength < 0) {
      return null;
    }

    // Read name
    const name = dataBuffer.slice(4, 4 + nameLength).toString('utf8');

    // Read symbol length
    const symbolLength = dataBuffer.readUInt32LE(4 + nameLength);
    if (symbolLength > 20 || symbolLength < 0) {
      return null;
    }

    // Read symbol
    const symbol = dataBuffer.slice(4 + nameLength + 4, 4 + nameLength + 4 + symbolLength).toString('utf8');

    // Read URI length
    const uriStart = 4 + nameLength + 4 + symbolLength;
    const uriLength = dataBuffer.readUInt32LE(uriStart);

    // Skip URI and read mint (last 32 bytes)
    const mintStart = uriStart + 4 + uriLength;
    const mint = bs58.encode(dataBuffer.slice(mintStart, mintStart + 32));

    return { name, symbol, mint };
  } catch (error) {
    console.error('Failed to decode CreateEvent:', error);
    return null;
  }
}

async function processCreateEvent(createData: { name: string, symbol: string, mint: string }, message: Message, data: SubscribeUpdate): Promise<void> {
  try {
    const blockHash = data.blockMeta?.blockhash;
    const sig = data.transaction?.transaction?.signature || [];
    const sigtxt = sig instanceof Uint8Array ? convertSignature(sig) : { base58: "invalid signature" };
    const owner = new PublicKey(message.accountKeys[0]).toString();

    const redisData = await redis.get(owner);
    if (!redisData) {
      return;
    }

    const userInfo: RedisAddress = JSON.parse(redisData);
    if (!userInfo.app) {
      return;
    }
    if (userInfo.addressType !== "minter") {
      return;
    }


    if (!mintTracker[createData.mint]) {
      mintTracker[createData.mint] = {
        // Creation data
        created_at: Math.floor(Date.now() / 1000), // Unix timestamp
        name: createData.name,
        symbol: createData.symbol,
        owner: owner,
        signature: sigtxt.base58,

        // Trading stats
        sum_buys: 0,
        sum_sells: 0,
        dev_profit: 0,

        // Market data
        app: userInfo.app,  // Will be updated during trades
        mcap_current: 0,
        mcap_max: 0
      };


      if (latestBlockhash) {
        const virtual_sol_reserves = 0;
        const virtual_token_reserves = 0;
        if (userInfo.app == "auto5") {
          console.log(mintTracker[createData.mint]);
          tradeMeasure = Date.now();
          buyTokensJito(createData.mint, "0.1", latestBlockhash, virtual_sol_reserves, virtual_token_reserves);
          console.log(`-----BOUGHT----:   https://neo.bullx.io/terminal?chainId=1399811149&address=${createData.mint}`);
        } else {
          console.log(userInfo.app);
          if (appConfigs[userInfo.app] && appConfigs[userInfo.app].enabled == 1) {
            console.log(`----- !!!! Simulation buy !!!----:   https://neo.bullx.io/terminal?chainId=1399811149&address=${createData.mint}`);
            console.log(mintTracker[createData.mint]);
           
          }

        }
      }
      await dbPool.execute('INSERT INTO rugger_mints (rugger, trade_status, mint, buy_sol_amount, buy_token_amount, sell_sol_amount, sell_token_amount, buy_tx, sell_tx, buy_mc, sell_mc, created_at, app) VALUES (?,?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)', [owner, "active", createData.mint, 0, 0, 0, 0, 0, 0, 0, 0, new Date(), userInfo.app]);



    }

    // console.log("CreateEvent detected -",
    //   "mint:", createData.mint,
    //   "name:", createData.name,
    //   "symbol:", createData.symbol,
    //   "owner:", new PublicKey(accountKeys[0]).toString(),
    //   "blockHash:", getLatestBlockhash(),
    //   "signature:", sigtxt.base58
    // );

  } catch (error) {
    console.error('Failed to process CreateEvent:', error);
  }
}

async function processTradeEvent(tradeData: TradeEvent, message: Message, data: SubscribeUpdate): Promise<void> {
  try {
    const {
      user,
      mint,
      isBuy,
      solAmount,
      tokenAmount,
      virtualSolReserves,
      virtualTokenReserves
    } = tradeData;

    // Use the cached blockhash instead of relying on blockMeta
    const blockHash = getLatestBlockhash();
    const sig = data.transaction?.transaction?.signature;
    const sigtxt = sig instanceof Uint8Array ? convertSignature(sig) : { base58: "invalid signature" };

    //console.log(JSON.stringify(data));
    if (!mintTracker[mint]) {
      return;
    }

    if (mintTracker[mint].app != "auto5" && mintTracker[mint].app != "auto7") { return; }
    


    const solAmountInSol = solAmount / 1e9;
    const price = solAmount / tokenAmount;
    const solPrice = 132;
    const marketCap = price * 1e6 * solPrice;
    mintTracker[mint].mcap_current = marketCap;


    if (mintTracker[mint].app == "auto5") {
      if (marketCap > 46000 || solAmountInSol > 0.9 || marketCap < 10000) {

        if (blockHash && mintTracker[mint].tokenAmount) {
          console.log(`Mcap above  ${marketCap} or trade above ${solAmountInSol}. Initiating sell...`);
          tradeMeasure = Date.now();

          sellTokensJito(mint, `${mintTracker[mint].tokenAmount}`, blockHash, mintTracker[mint].devPubKey);
          console.log(`Sell order placed for ${mint}`);
          // Reset dev profit and token amount after selling
          mintTracker[mint].dev_profit = 0;
          mintTracker[mint].tokenAmount = 0;
          updatMintTradeDataSell(mint, 'sold', solAmountInSol, tokenAmount, sigtxt.base58, marketCap, 0);
        }
      }
    }

    const redisData = await redis.get(user);
    if (redisData) {
      return;
    }

    //TODO: go away if auto7 jitoplusimpodent

    // const instructions = data.transaction?.transaction?.transaction?.message?.instructions;
    // if (instructions && instructions.length >= 2) {
    //   const firstInstruction = instructions[0];
    //   const secondInstruction = instructions[1];

    //   if (firstInstruction.programIdIndex < message.accountKeys.length) {
    //     const firstInstructionProgramId = new PublicKey(message.accountKeys[firstInstruction.programIdIndex]).toBase58();
    //     if (firstInstructionProgramId === SystemProgram.programId.toBase58()) {
    //       // Decode system transfer instruction
    //       const transferData = SystemProgram.transfer({
    //         fromPubkey: new PublicKey(message.accountKeys[firstInstruction.accounts[0]]),
    //         toPubkey: new PublicKey(message.accountKeys[firstInstruction.accounts[1]]),
    //         lamports: Buffer.from(firstInstruction.data).readBigUInt64LE(0)
    //       }).data;

    //       const destination = new PublicKey(message.accountKeys[firstInstruction.accounts[1]]).toBase58();
    //       const lamports = Number(transferData.readBigUInt64LE(8));
    //       const source = new PublicKey(message.accountKeys[firstInstruction.accounts[0]]).toBase58();

    //       if (JITO_MAINNET_TIP_ACCOUNTS.includes(destination) ) {
    //         if (secondInstruction.programIdIndex < message.accountKeys.length) {
    //           const secondInstructionProgramId = new PublicKey(message.accountKeys[secondInstruction.programIdIndex]).toBase58();
    //           if (secondInstructionProgramId === "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL") {
    //             // This is equivalent to checking for "createIdempotent" in the original code
                
    //             //devAccounts.add(source);
    //             const redis = getRedisClient();
    //             await redis.set(source, JSON.stringify({
    //               addressType: "regular",
    //               address: source,
    //               app: "auto7"
    //             }));
    //             redis.disconnect();
    //             return;

    //             // If you want to print when the source is not in devAccounts, you can do:
    //             // if (!devAccounts.has(source)) {
    //             //   console.log(`New dev account: ${source}`);
    //             // }
    //           }
    //         }
    //         return;
    //       }
    //     }
    //   }
    // }


    if (user == TRADING_ACCOUNT) {
      console.log("=======OwNER TRADING========");
      if (blockHash) {
        const currTime = Date.now();
        const reactionTime = currTime - tradeMeasure;

        if (isBuy) {
          console.log("reaction:", reactionTime.toFixed(0) + "ms", "Buying", mint, solAmount, tokenAmount);
          updatMintTradeDataBuy(mint, 'bought', solAmount, tokenAmount, sigtxt.base58, marketCap, 0);
        } else {
          console.log("reaction:", reactionTime.toFixed(0) + "ms", "Selling", mint, solAmount, tokenAmount);
          updatMintTradeDataSell(mint, 'sold', solAmount, tokenAmount, sigtxt.base58, marketCap, 0);
        }



        if (isBuy) {
          mintTracker[mint].tokenAmount = (mintTracker[mint].tokenAmount || 0) + tokenAmount;
          mintTracker[mint].solAmount = (mintTracker[mint].solAmount || 0) + solAmount;
          mintTracker[mint].buyPrice = price;
        }

      }

    }

    // Calculate profit based on the latest trade



    if (isBuy) {
      mintTracker[mint].sum_buys += solAmountInSol;
    } else {
      mintTracker[mint].sum_sells += solAmountInSol;
    }

    // Calculate dev profit
    mintTracker[mint].dev_profit = mintTracker[mint].sum_buys - mintTracker[mint].sum_sells;

    mintTracker[mint].mcap_max = Math.max(mintTracker[mint].mcap_max, marketCap);
    //mintTracker[mint].app = userInfo.app;

    // Print in one line
    console.log(
      // `BlockHash: ` +getLatestBlockhash() + ` | ` +
      // `TxHash: ${sigtxt.base58 || 'unknown'} | ` +
      `${mintTracker[mint].symbol}`,
      ` ${isBuy ? 'BUY' : 'SELL'} | ` +
      ` ${solAmountInSol.toFixed(4)} SOL | ` +
      `Dev Profit: ${mintTracker[mint].dev_profit.toFixed(4)} SOL` +
      `MCap: $${marketCap.toFixed(2)} | ` +
      `User: ${user} | ` +
      `App: ${mintTracker[mint].app} | `
      //`Token: ${mint} | ` +

      //`Tokens: ${tokenAmount} | ` +

      // `Total Buys: ${mintTracker[mint].sum_buys.toFixed(4)} SOL | ` +
      // `Total Sells: ${mintTracker[mint].sum_sells.toFixed(4)} SOL | ` +

    );
    tradeData.block_id = Math.floor(Date.now() / 1000);
    tradeData.signer = user;
    tradeData.signature = sigtxt.base58;
    tradeData.app = mintTracker[mint].app;



    // Check if dev profit is above 1 SOL and sell if it is
    if (mintTracker[mint].dev_profit > 1.78) {

      if (blockHash && mintTracker[mint].tokenAmount) {
        console.log(`Dev profit above 1 SOL for ${mint}. Initiating sell...`);
        sellTokensJito(mint, `${mintTracker[mint].tokenAmount}`, blockHash);
        sellTokensJito(mint, `${mintTracker[mint].tokenAmount}`, blockHash);
        console.log(`Sell order placed for ${mint}`);
        // Reset dev profit and token amount after selling
        mintTracker[mint].dev_profit = 0;
        mintTracker[mint].tokenAmount = 0;

      }
    }
  


    saveTransactionsToDatabase([tradeData], solPrice);



    /**
     * 
     * mint	varchar(100)	
account	varchar(100)	
signer	varchar(100)	
sol_amount	varchar(100) NULL	
token_amount	varchar(45) NULL	
trade	varchar(10) NULL	
price	decimal(10,10) NULL	
market_cup	int NULL [0]	
block_time	int	
block_id	int	
signature	varchar(100)	
app	varchar(20)	
     */
    // add to database


  } catch (error) {
    console.error('Error processing trade event:', error);
  }
}

// Setup graceful shutdown to close connections
process.on('SIGINT', async () => {
  console.log('Received SIGINT. Closing connections...');
  try {
    // Close Redis connection
    await redis.quit();
    console.log('Redis connection closed.');

    // Close database pool
    await dbPool.end();
    console.log('Database pool closed.');

    process.exit(0);
  } catch (error) {
    console.error('Error during shutdown:', error);
    process.exit(1);
  }
});

process.on('SIGTERM', async () => {
  console.log('Received SIGTERM. Closing connections...');
  try {
    // Close Redis connection
    await redis.quit();
    console.log('Redis connection closed.');

    // Close database pool
    await dbPool.end();
    console.log('Database pool closed.');

    process.exit(0);
  } catch (error) {
    console.error('Error during shutdown:', error);
    process.exit(1);
  }
});

main().catch((err) => {
  console.error("Unhandled error in main:", err);
  process.exit(1);
});

export { getLatestBlockhash };

// Add reconnection helper
async function reconnectStream(): Promise<ClientDuplexStream<SubscribeRequest, SubscribeUpdate>> {
  try {
    const client = new Client(ENDPOINT, undefined, {});
    const stream = await client.subscribe();
    const request = createSubscribeRequest();
    await sendSubscribeRequest(stream, request);
    console.log("Successfully created new gRPC stream connection");
    return stream;
  } catch (error) {
    console.error("Error creating new stream:", error);
    throw error;
  }
}

export async function sellTokensJito(tokenCA: string, amount: string, latestBlockHash: string, wallet: Uint8Array,devPubKey: string) {
  tradeMeasure = Date.now();
  const jitoTipAccount = new PublicKey('DttWaMuVvTiduZRnguLF7jNxTgiMBZ1hyAumKUiL2KRL');
  const jitoEndpoint = 'https://mainnet.block-engine.jito.wtf/api/v1/transactions';

  const privateKey = process.env.PRIVATE_KEY ? bs58.decode(process.env.PRIVATE_KEY) : new Uint8Array();
  const signer = Keypair.fromSecretKey(privateKey);

  const tokenBalance = parseFloat(amount);
  const tipLamports = 1_000_000;

  const transaction = new Transaction();
  const sellInstruction = await getSellInstructionNoRPC(bs58.encode(privateKey), tokenCA, tokenBalance,signer,devPubKey);

  transaction.add(
    SystemProgram.transfer({
      fromPubkey: signer.publicKey,
      toPubkey: jitoTipAccount,
      lamports: tipLamports,
    })
  );

  if (!sellInstruction) {
    console.log('Request preparation: Failed to get sell instruction');
    return;
  }

  transaction.add(sellInstruction);
  transaction.feePayer = signer.publicKey;
  transaction.recentBlockhash = latestBlockHash;
  transaction.sign(signer);

  const serializedTransaction = bs58.encode(transaction.serialize());
  console.log(`Request preparation: Transaction serialized successfully (${serializedTransaction.length} bytes)`);

  try {
    // The connection is already warmed up here, should be much faster
    console.log(`Sending request to ${jitoEndpoint}...`);
    const startTime = Date.now();

    const response = await fetch(jitoEndpoint, {
      method: 'POST',
      body: JSON.stringify({
        jsonrpc: '2.0',
        id: 1,
        method: 'sendTransaction',
        params: [serializedTransaction]
      }),
      headers: { 'Content-Type': 'application/json' },
      agent: httpsAgent,
    });

    const responseTime = Date.now() - startTime;
    const responseText = await response.text();

    console.log(`Response received in ${responseTime}ms`);
    console.log(`Response status: ${response.status}`);
    console.log(`Response body: ${responseText}`);

    return response.status === 200 ? JSON.parse(responseText).result : response;
  } catch (error) {
    console.log('Request failed. Network or server error.');
    return error;
  }
}

export async function buyTokensJito(tokenCA: string, amount: string, latestBlockHash: string, virtual_sol_reserves: number, virtual_token_reserves: number) {
  const jitoTipAccount = new PublicKey('DttWaMuVvTiduZRnguLF7jNxTgiMBZ1hyAumKUiL2KRL');
  const jitoEndpoint2 = 'https://mainnet.block-engine.jito.wtf/api/v1/transactions';
  const jitoEndpoint = "https://nd-656-324-033.p2pify.com/7026d0c4e4356e4f9fc0a1170759e31f"; 
  const privateKey = process.env.PRIVATE_KEY ? bs58.decode(process.env.PRIVATE_KEY) : new Uint8Array();
  const signer = Keypair.fromSecretKey(privateKey);

  const solAmount = parseFloat(amount);
  const tipLamports = 1_000;

  const transaction = new Transaction();
  const buyInstruction = await getBuyInstructionNoRPC(bs58.encode(privateKey), tokenCA, solAmount, virtual_sol_reserves, virtual_token_reserves);

  const priorityFee = ComputeBudgetProgram.setComputeUnitPrice({
    microLamports: 500_000 // Adjust this value based on network conditions
  });
  transaction.add(priorityFee);

  // transaction.add(
  //   SystemProgram.transfer({
  //     fromPubkey: signer.publicKey,
  //     toPubkey: jitoTipAccount,
  //     lamports: tipLamports,
  //   })
  // );


  if (!buyInstruction) {
    console.log('Request preparation: Failed to get sell instruction');
    return;
  }

  transaction.add(buyInstruction);
  transaction.feePayer = signer.publicKey;
  transaction.recentBlockhash = latestBlockHash;
  console.log(transaction);
  transaction.sign(signer);


  // const instructions: TransactionInstruction[] = [
  //   buyInstruction,
  //   priorityFee
  // ];


  // const messageV0 = new TransactionMessage({
  //   payerKey: FROM_KEYPAIR.publicKey,
  //   recentBlockhash: latestBlockhash.blockhash,
  //   instructions: instructions
  // }).compileToV0Message();
  // const transactionV0 = new VersionedTransaction(messageV0);

  const serializedTransaction = bs58.encode(transaction.serialize());
  console.log(`Request preparation: Transaction serialized successfully (${serializedTransaction.length} bytes)`);

  try {
    // The connection is already warmed up here, should be much faster
    console.log(`Sending request to ${jitoEndpoint}...`);
    const startTime = Date.now();

    // const response = await fetch(jitoEndpoint, {
    //   method: 'POST',
    //   body: JSON.stringify({
    //     jsonrpc: '2.0',
    //     id: 1,
    //     method: 'sendTransaction',
    //     params: [serializedTransaction]
    //   }),
    //   headers: { 'Content-Type': 'application/json' },
    //   agent: httpsAgent,
    // });

     const CHAINSTACK_RPC = "https://nd-656-324-033.p2pify.com/7026d0c4e4356e4f9fc0a1170759e31f";
     const SOLANA_CONNECTION = new Connection(CHAINSTACK_RPC, { commitment: "confirmed"});
     const txid = await SOLANA_CONNECTION.sendRawTransaction(bs58.decode(serializedTransaction), { maxRetries: 15,skipPreflight: true });



    const responseTime = Date.now() - startTime;
    //const responseText = await response.text();

    console.log(`Response received in ${responseTime}ms`);
    // console.log(`Response status: ${response.status}`);
    // console.log(`Response body: ${responseText}`);

    // return response.status === 200 ? JSON.parse(responseText).result : response;
  } catch (error) {
    console.log('Request failed. Network or server error.');
    return error;
  }
}
