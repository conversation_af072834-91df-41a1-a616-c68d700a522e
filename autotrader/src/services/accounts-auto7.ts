import Client, {
  CommitmentLevel,
  SubscribeRequest,
  SubscribeUpdate,
} from "@triton-one/yellowstone-grpc";
import { ClientDuplexStream } from '@grpc/grpc-js';
import bs58 from 'bs58';
import { Redis } from 'ioredis';
import { REDIS_URL } from "../common/config";
import { followAccountInline } from "../common/solana";

const GRPC_ENDPOINT = "http://grpc.solanavibestation.com:10000";
const MASTER_ACCOUNT="FnpdsZiFrLcfYsqiYb7pYxH4jAhbqzB5SyLGQnx4RABC"
type AddressType = 'main' | 'follow1' | 'follow2' | 'minter' | 'regular';
type ProcessStatus = 'none' | 'processedBAL' | 'processedTX';

interface AccountInfo {
  address: string;
  type: AddressType;
  currentBalance: number;
  latestTransaction: string;
  status: ProcessStatus;
  app: string;
}

class AccountTracker {
  private client: Client;
  private stream: ClientDuplexStream<SubscribeRequest, SubscribeUpdate> | null = null;
  private scanList: Map<string, AccountInfo> = new Map();
  private masterAddress: string;
  private isInitialized: boolean = false;
  private redis!: Redis;
  private redisReconnectAttempts: number = 0;
  private readonly MAX_RECONNECT_ATTEMPTS = 20;

  constructor(client: Client, masterAddress: string) {
    this.client = client;
    this.masterAddress = masterAddress;
    this.initializeRedis();
  }

  private initializeRedis() {
    this.redis = new Redis({
      host: REDIS_URL,
      port: 6379,
      password: "pump2pump",
      retryStrategy: (times) => {
        this.redisReconnectAttempts = times;
        if (times > this.MAX_RECONNECT_ATTEMPTS) {
          console.error(`Redis reconnection failed after ${times} attempts. Restarting process...`);
          process.exit(1); // Process manager should restart the application
        }
        const delay = Math.min(times * 100, 3000);
        console.log(`Retrying Redis connection in ${delay}ms... (Attempt ${times})`);
        return delay;
      },
      maxRetriesPerRequest: null, // Retry forever
      enableAutoPipelining: true,
      autoResubscribe: true,
      reconnectOnError: (err) => {
        const targetError = 'READONLY';
        if (err.message.includes(targetError)) {
          return true;
        }
        return false;
      }
    });

    this.redis.on('error', (err) => {
      console.error('Redis error:', err);
      // Don't crash on error - let retryStrategy handle reconnection
    });

    this.redis.on('connect', () => {
      console.log('Redis connected');
      this.redisReconnectAttempts = 0;
    });

    this.redis.on('ready', () => {
      console.log('Redis ready');
    });

    this.redis.on('close', () => {
      console.log('Redis connection closed. Attempting to reconnect...');
    });

    this.redis.on('reconnecting', (ms: number) => {
      console.log(`Redis reconnecting in ${ms}ms...`);
    });
  }

  private async safeRedisOperation<T>(operation: () => Promise<T>): Promise<T | null> {
    try {
      return await operation();
    } catch (error) {
      console.error('Redis operation failed:', error);
      // Attempt to reconnect if connection is lost
      if (!this.redis.status || this.redis.status === 'end') {
        console.log('Redis connection lost, reinitializing...');
        this.initializeRedis();
      }
      return null;
    }
  }

  public async start(): Promise<void> {
    this.initializeStream();
  }

  private async initializeStream() {
    try {
      this.stream = await this.client.subscribe();
      const request: SubscribeRequest = {
        accounts: {},
        slots: {},
        transactions: {
          accountSubscribe: {
            accountInclude: [this.masterAddress],
            accountExclude: [],
            accountRequired: [],
          }
        },
        commitment: CommitmentLevel.PROCESSED,
        transactionsStatus: {},
        blocks: {},
        blocksMeta: {},
        entry: {},
        accountsDataSlice: [],
        ping: undefined
      };

      if (this.stream) {
        await this.stream.write(request);
        
        this.stream.on('data', (data: SubscribeUpdate) => this.processTransactionGRPC(data));
        this.stream.on('error', (error) => {
          console.error('Stream error:', error);
          this.reconnectStream();
        });
      }
    } catch (error) {
      console.error('Failed to initialize stream:', error);
      setTimeout(() => this.initializeStream(), 5000);
    }
  }

  private async processTransactionGRPC(data: SubscribeUpdate) {
    if (!('transaction' in data) || !data.transaction?.transaction) return;

    const tx = data.transaction.transaction;
    if (!tx.signature || !tx.transaction?.message) return;
    
    const accounts = tx.transaction.message.accountKeys || [];
    const accountsAsStrings = accounts.map(account => bs58.encode(Buffer.from(account)));

    const preBalances = tx.meta?.preBalances || [];
    const postBalances = tx.meta?.postBalances || [];

    // Find all SOL transfers from MASTER_ACCOUNT
    for (let i = 0; i < accountsAsStrings.length; i++) {
      const account = accountsAsStrings[i];
      
      // Skip if not the MASTER_ACCOUNT
      if (account !== MASTER_ACCOUNT) continue;

      // Calculate how much SOL was sent from MASTER_ACCOUNT
      const masterPreBalance = Number(preBalances[i]) || 0;
      const masterPostBalance = Number(postBalances[i]) || 0;
      const solSent = (masterPreBalance - masterPostBalance) / 1e9; // Convert lamports to SOL

      // Check if the amount sent is between 4 and 7 SOL
      if (solSent >= 4 && solSent <= 7) {
        // Check for balance changes in other accounts
        for (let j = 0; j < accountsAsStrings.length; j++) {
          if (j === i) continue; // Skip self

          const destinationAccount = accountsAsStrings[j];
          const preBal = preBalances[j] || 0;
          const postBal = postBalances[j] || 0;
          
          // If there was a balance increase in the destination account
          if (postBal > preBal) {
            // Mark the destination account as "minter" in Redis
               //TODO: implement check
                 
                followAccountInline(MASTER_ACCOUNT,destinationAccount, 1, 20, 0);                // Immediately return if max depth is reached
          }
        }
      }
    }
  }



  private async reconnectStream() {
    if (this.stream) {
      try {
        this.stream.destroy();
      } catch (error) {
        console.error('Error destroying stream:', error);
      }
    }
    await this.initializeStream();
  }


}

export default AccountTracker;

// Usage example:
async function main() {
  console.log('\n🔌 Connecting to services...');
  console.log(`GRPC Endpoint: ${GRPC_ENDPOINT}`);

  const client = new Client(GRPC_ENDPOINT, undefined, {});
  const masterAddress = MASTER_ACCOUNT;
  
  console.log('\n🔧 Creating AccountTracker...');
  const tracker = new AccountTracker(client, masterAddress);

  // Graceful shutdown handling
  const shutdown = async (signal: string) => {
    console.log(`\nReceived ${signal}. Gracefully shutting down...`);
    try {
      if (tracker['redis']) {
        console.log('Closing Redis connection...');
        await tracker['redis'].quit();
      }
      console.log('Shutdown complete');
      process.exit(0);
    } catch (error) {
      console.error('Error during shutdown:', error);
      process.exit(1);
    }
  };

  process.on('SIGINT', () => shutdown('SIGINT'));
  process.on('SIGTERM', () => shutdown('SIGTERM'));
  process.on('uncaughtException', (error) => {
    console.error('Uncaught Exception:', error);
    shutdown('uncaughtException');
  });

  await tracker.start();
}

main().catch(error => {
  console.error('\n❌ Fatal error:', error);
  process.exit(1);
});
