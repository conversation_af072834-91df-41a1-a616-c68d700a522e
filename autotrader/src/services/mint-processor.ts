import amqp from 'amqplib';
import dotenv from 'dotenv';
import { Redis } from 'ioredis';
import { processPumpTransaction } from '../common/pump';
import { processSwapTransaction } from '../common/solana';
import { saveTransactionsToDatabase, getSolPrice } from '../common/db';
import { checkRedisKey, getRedisString } from '../common/redis';
import { REDIS_URL } from '../common/config';
import { sendRabbitMessage } from '../common/rabbitmq';
import crypto from 'crypto';

dotenv.config();

const RABBITMQ_URL = 'amqp://pump:<EMAIL>';
const QUEUE_NAME = 'tx';

const redis = new Redis({ host: REDIS_URL, port: 6379, password: "pump2pump" });

// eslint-disable-next-line @typescript-eslint/no-unused-vars
async function processTransaction(signature: string, mint: string) {
    try {
        //console.log(`Processing transaction: ${signature} for mint: ${mint}`);
        
        // First try to process as a Pump transaction
        let trade = await processPumpTransaction(signature);
        
        // If not a Pump transaction, try as a Swap transaction
        if (!trade) {
            trade = await processSwapTransaction(redis, signature);
        }
        
        if (trade) {
            const isMember = await checkRedisKey(redis, trade.owner);
            if (!isMember) {
                console.log(`${trade.tradeType}  ${(trade.solAmount / 1e9).toFixed(4)}  mint: ${trade.mint}  ${trade.owner}`);
                
                const isBuy = trade.tradeType === "buy";
                const redisAddress = await getRedisString(redis, trade.mint);
                let app = "";
                if (redisAddress) {
                    app = JSON.parse(redisAddress).app;
                }

                
                const tstamp = new Date().getTime() / 1000;
                
                const tradeEventRecord = [{
                    mint: trade.mint,
                    solAmount: trade.solAmount,
                    tokenAmount: trade.tokenAmount,
                    isBuy: isBuy,
                    user: trade.owner,
                    timestamp: tstamp,
                    virtualSolReserves: 0,
                    virtualTokenReserves: 0,
                    realSolReserves: 0,
                    realTokenReserves: 0,
                    signature: signature,
                    signer: trade.owner,
                    block_id: 0, // No slot information from RabbitMQ
                    app: app
                }];
                
                let solPrice = 120;
                const redisSolPrice=await getRedisString(redis, "solPrice");
                if (redisSolPrice) {
                    solPrice = parseFloat(redisSolPrice);
                } else {
                    solPrice = await getSolPrice();
                }
                const price = trade.solAmount / trade.tokenAmount;
                const marketCap = price * 1e6 * solPrice;
                

                const solAmountFormatted = (trade.solAmount / 1e9).toFixed(3);
                const hash = crypto.createHash('md5').update(trade.mint + trade.owner + isBuy + trade.solAmount + signature).digest("hex");
                await saveTransactionsToDatabase(tradeEventRecord, solPrice);
                sendRabbitMessage(JSON.stringify({"mint":trade.mint, "isBuy":isBuy ,"amount":solAmountFormatted, "timestamp":tstamp,"account":trade.owner,"mcap":marketCap,"hash":hash,"app":app }),'trade');

                // If app is auto1 or auto4 and market cap is small, remove from webhook
                if ((app === "auto1" || app === "auto4") && marketCap < 6000) {
                    await sendRabbitMessage(JSON.stringify({ "address": trade.mint, "command":"disconnect" }), 'mints');
            
                }
            }
        } else {
            console.log(`No trade details found for signature: ${signature}`);
        }
    } catch (error) {
        console.error(`Error processing transaction ${signature}:`, error);
    }
}

async function startConsumer() {
    try {
        // Connect to RabbitMQ
        const connection = await amqp.connect(RABBITMQ_URL);
        const channel = await connection.createChannel();
        
        // Make sure queue exists
        await channel.assertQueue(QUEUE_NAME, { durable: true });
        
        console.log(`RabbitMQ consumer connected and waiting for messages from queue: ${QUEUE_NAME}`);
        
        // Consume messages
        channel.consume(QUEUE_NAME, async (message) => {
            if (message) {
                try {
                    const content = message.content.toString();
                    const data = JSON.parse(content);
                    
                    // Check if message contains required data
                    if (data.signature && data.mint) {
                        await processTransaction(data.signature, data.mint);
                    } else {
                        console.warn('Received incomplete message data:', data);
                    }
                    
                    // Acknowledge message
                    channel.ack(message);
                } catch (error) {
                    console.error('Error processing message:', error);
                    // Reject message and requeue
                    channel.nack(message, false, true);
                }
            }
        });
        
        // Handle graceful shutdown
        process.on('SIGINT', async () => {
            console.log('Closing RabbitMQ connection');
            await channel.close();
            await connection.close();
            process.exit(0);
        });
        
    } catch (error) {
        console.error('RabbitMQ connection error:', error);
        process.exit(1);
    }
}

// Start the consumer
startConsumer().catch(console.error);