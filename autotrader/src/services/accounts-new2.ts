import Client, {
  CommitmentLevel,
  SubscribeRequest,
  SubscribeUpdate,
} from "@triton-one/yellowstone-grpc";
import { ClientDuplexStream } from '@grpc/grpc-js';
import { Connection, PublicKey, VersionedTransactionResponse, LAMPORTS_PER_SOL } from '@solana/web3.js';
import bs58 from 'bs58';
import { getEnabledAccountsForGRPC } from '../common/db';
import { getRedisClient } from "../common/redis";

// Constants
const ALCHEMY_RPC_URL = "https://solana-mainnet.g.alchemy.com/v2/********************************";
const GRPC_ENDPOINT = "http://grpc.solanavibestation.com:10000";
const MIN_SOL_TRANSFER = ********; // 1 SOL in lamports
const BALANCE_CHECK_INTERVAL = 3000; // 10 seconds
const MAX_ACCOUNTS_PER_BATCH = 100; // <PERSON> accounts to check balance at once
//const PUMP_FUN_PROGRAM_ID = "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P";
// const REDIS_URL = "redis://localhost:6379"; // Removed, now imported from config
//const RPC_URL_SYNDICA = "https://solana-mainnet.api.syndica.io/api-key/21KG2L6E3kGURokSr4VwfrtYMbqnqVNYKJKCBTQv2jsiLs97iW8TQv98fcz5kvVDhS3MjVmGt91jZ3UGQpGD7zaPCuxpwgCJbek"
const RPC_URL_CS="https://solana-mainnet.core.chainstack.com/59d8fa6af4368b9e6e8f8a68f06517c3"
//const RPC_ANKR="https://rpc.ankr.com/solana/3a2ca3f89ce8d11cbaa43746a9e34a4f6ed020d7a1ef37a62065e8323b1f32ac"
//const ALCHEMY_RPC_URL=RPC_ANKR
// Types
type AddressType = 'main' | 'follow1' | 'follow2' | 'minter' | 'regular';
type ProcessStatus = 'none' | 'processedBAL' | 'processedTX';

interface AccountInfo {
  address: string;
  type: AddressType;
  currentBalance: number;
  latestTransaction: string;
  status: ProcessStatus;
  app: string;
}

class AccountTracker {
  private client: Client;
  private stream: ClientDuplexStream<SubscribeRequest, SubscribeUpdate> | null = null;
  private connection: Connection;
  private scanList: Map<string, AccountInfo> = new Map();
  private masterAddress: string;
  private processedTransactions: Set<string> = new Set();
  private isInitialized: boolean = false;

  constructor(client: Client, connection: Connection, masterAddress: string) {
    this.client = client;
    this.connection = connection;
    this.masterAddress = masterAddress;
  }

  async initialize(dbAddresses: string[]) {
    if (this.isInitialized) {
      console.log('Already initialized');
      return;
    }

    console.log('\n🔍 Initializing AccountTracker:');
    console.log('================================');
    console.log(`Master Address: ${this.masterAddress}`);
    console.log(`Total addresses from DB: ${dbAddresses.length}`);
    console.log('--------------------------------');

    // Set up initial addresses from DB as main accounts
    for (const address of dbAddresses) {
      this.scanList.set(address, {
        address,
        type: 'main',
        currentBalance: 0,
        latestTransaction: '',
        status: 'none',
        app: 'auto5'
      });
      console.log(`➕ Added main account: ${address}`);
    }

    console.log('\n📊 Current Scan List Status:');
    console.log('================================');
    this.printScanListStatus();

    // Start balance checker
    //this.startBalanceChecker();
    
    // Initialize gRPC stream
    await this.initializeStream();
    
    this.isInitialized = true;
    console.log('\n✅ Initialization complete!');
  }

  private printScanListStatus() {
    const stats = {
      total: 0,
      main: 0,
      follow1: 0,
      follow2: 0,
      minter: 0,
      regular: 0
    };

    this.scanList.forEach(info => {
      stats.total++;
      stats[info.type]++;
    });

    console.log('Account Type Distribution:');
    console.log(`- Total Accounts: ${stats.total}`);
    console.log(`- Main Accounts: ${stats.main}`);
    console.log(`- Follow1 Accounts: ${stats.follow1}`);
    console.log(`- Follow2 Accounts: ${stats.follow2}`);
    console.log(`- Minter Accounts: ${stats.minter}`);
    console.log(`- Regular Accounts: ${stats.regular}`);
    
    console.log('\nDetailed Account List:');
    this.scanList.forEach((info, address) => {
      console.log(`- ${address}:`);
      console.log(`  Type: ${info.type}`);
      console.log(`  Status: ${info.status}`);
      console.log(`  App: ${info.app}`);
      console.log(`  Balance: ${info.currentBalance}`);
    });
  }

  private async initializeStream() {
    try {
      this.stream = await this.client.subscribe();
      const scanAddresess= Array.from(this.scanList.keys())
      const request: SubscribeRequest = {
        accounts: {},
        slots: {},
        transactions: {
          accountSubscribe: {
            accountInclude: scanAddresess,
            accountExclude: [],
            accountRequired: [],
          }
        },
        commitment: CommitmentLevel.PROCESSED,
        transactionsStatus: {},
        blocks: {},
        blocksMeta: {},
        entry: {},
        accountsDataSlice: [],
        ping: undefined
      };

      if (this.stream) {
        await this.stream.write(request);
        
        this.stream.on('data', (data: SubscribeUpdate) => this.processTransactionGRPC(data));
        this.stream.on('error', (error) => {
          console.error('Stream error:', error);
          this.reconnectStream();
        });
      }
    } catch (error) {
      console.error('Failed to initialize stream:', error);
      setTimeout(() => this.initializeStream(), 5000);
    }
  }

  private async processTransactionGRPC(data: SubscribeUpdate) {
    if (!('transaction' in data) || !data.transaction?.transaction) return;

    const tx = data.transaction.transaction;
    if (!tx.signature || !tx.transaction?.message) return;
    
    const signature = bs58.encode(Buffer.from(tx.signature));

    // Skip if there are any token transfers
    // if (tx.meta?.preTokenBalances?.length > 0 || 
    //     tx.meta?.postTokenBalances?.length > 0) {
    //   return;
    // }

    if ((tx.meta?.preTokenBalances?.length ?? 0) > 0 || 
    (tx.meta?.postTokenBalances?.length ?? 0) > 0) {
  return; // Skip if there are token transfers
}
    

    const preBalances = tx.meta?.preBalances || [];
    const postBalances = tx.meta?.postBalances || [];
    const accounts = tx.transaction.message.accountKeys || [];

    // Process only native SOL transfers
    for (let i = 0; i < accounts.length; i++) {
      const sent = Number(preBalances[i]) - Number(postBalances[i]);
      
      // Skip if not a significant SOL transfer
      if (sent <= MIN_SOL_TRANSFER) continue;

      const sender = new PublicKey(accounts[i]).toBase58();
      const senderInfo = this.scanList.get(sender);
      
      // Skip if sender is not in our scan list
      if (!senderInfo) continue;

      // Look for the recipient of this transfer
      for (let j = 0; j < accounts.length; j++) {
        if (i === j) continue;

        const recipient = new PublicKey(accounts[j]).toBase58();
        const received = Number(postBalances[j]) - Number(preBalances[j]);

        // Only process if we found a matching receive amount
        if (received > 0) {
          console.log(`Native SOL transfer detected: ${sender} sent ${sent/LAMPORTS_PER_SOL} SOL to ${recipient}`);
          
          const senderInfo: AccountInfo = {
            address: sender,
            type: this.scanList.get(sender)?.type || 'main',
            currentBalance: Number(postBalances[i]),
            latestTransaction: signature,
            status: 'processedTX',
            app: this.scanList.get(sender)?.app || 'auto5'
          };

          await this.processTransfer(senderInfo, recipient, sent);
          break; // Found the recipient, no need to continue inner loop
        }
      }
    }
  }

  private async processTransfer(senderInfo: AccountInfo, recipient: string, sent: number) {
    let newType: AddressType | null = null;
    const redis = getRedisClient(); 
    if (recipient === this.masterAddress) return;
    if (senderInfo.app === 'auto5') {
    if (
          sent >= 30_000000000 && 
          sent <= 500_000000000 && 
          senderInfo.address === this.masterAddress
        ) {
        newType = 'follow1'; 
      }
    else if (
          sent >= 30_000000000 && 
          sent <= 120_000000000 && 
          senderInfo.type === 'follow1'
        ) {
        newType = 'minter';
        try {
          await redis.set(recipient, JSON.stringify({
            addressType: "minter",
            address: recipient,
            app: "auto5"
          }));
          await redis.set(senderInfo.address, JSON.stringify({
            addressType: "minter",
            address: senderInfo.address,
            app: "auto5"
          }));

        } catch (err) {
          console.error("Error setting Redis value:", err);
        }
      }
      else if (sent < 20_000000000 && 
               senderInfo.type === 'follow1' && 
               senderInfo.address !== this.masterAddress) {
        newType = 'follow2';

      }
      else if (sent < 20_000000000 && 
               senderInfo.type === 'follow2' && 
               senderInfo.address !== this.masterAddress
        ) {
        newType = 'regular';
        this.scanList.delete(senderInfo.address);
        try {
          await redis.set(recipient, JSON.stringify({
            addressType: "regular",
            address: recipient,
            app: "auto5"
          }));
        } catch (err) {
          console.error("Error setting Redis value:", err);
        }
      }
    }
    
    if (newType === 'follow1' || newType === 'follow2') {
      console.log(newType,":",  'recipient:', recipient, " added to scanlist");
      // Update scan list with new account type
      this.scanList.set(recipient, {
        address: recipient,
        type: newType,
        currentBalance: 0,
        latestTransaction: '',
        status: 'none',
        app: 'auto5'
      });
    }
  }

  private async startBalanceChecker() {
    setInterval(async () => {
      const accountsToCheck = Array.from(this.scanList.entries())
        .filter(([, info]) => 
          (info.status === 'none' || info.status === 'processedTX') && 
          info.type !== 'regular' && 
          info.type !== 'main'
        )
        .map(([address]) => address);

      const typeCounts = {
        follow1: 0,
        follow2: 0,
        minter: 0
      };
      accountsToCheck.forEach(address => {
        const info = this.scanList.get(address);
        if (info) {
          typeCounts[info.type as keyof typeof typeCounts]++;
        }
      });
      console.log("Accounts to check by type:", JSON.stringify(typeCounts));
      for (let i = 0; i < accountsToCheck.length; i += MAX_ACCOUNTS_PER_BATCH) {
        const batch = accountsToCheck.slice(i, i + MAX_ACCOUNTS_PER_BATCH);
        await this.checkBalances(batch);
      }
      //console.log("account balance acounts:",accountsToCheck.length);
    }, BALANCE_CHECK_INTERVAL);
  }

  private async checkBalances(addresses: string[]) {
    try {
      let publicKeys = addresses.map(addr => new PublicKey(addr));

      // Remove follow2 accounts with processedTX status
      addresses = addresses.filter(address => {
        const accountInfo = this.scanList.get(address);
        if (accountInfo && accountInfo.type === 'follow2' && accountInfo.status === 'processedTX') {
          this.scanList.delete(address);
          return false;
        }
        return true;
      });

      // Update publicKeys array after filtering
      publicKeys = addresses.map(addr => new PublicKey(addr));

      const chainstackConnection = new Connection(RPC_URL_CS, 'confirmed');
      const balances = await chainstackConnection.getMultipleAccountsInfo(publicKeys);
      //console.log(" amount of accounts" ,addresses.length,addresses);
      for (let i = 0; i < addresses.length; i++) {
        const address = addresses[i];
        const accountInfo = this.scanList.get(address);
        if (!accountInfo) continue;

        const balance = balances[i]?.lamports || 0;




        if (accountInfo.status === 'none') {
          accountInfo.currentBalance = balance;
          accountInfo.status = 'processedBAL';
          await this.scanTransactions(address);
        } else if (accountInfo.status === 'processedTX' && balance !== accountInfo.currentBalance) {
          accountInfo.currentBalance = balance;
          accountInfo.status = 'processedBAL';
          //console.log(`Balance changed for ${address}: ${balance}`);
        } else if (accountInfo.status === 'processedTX' && balance == 0 ){
          //remove account from accountinfo
          //console.log("Removing account from accountinfo:",address,accountInfo.type);
          this.scanList.delete(address);
        }

        this.scanList.set(address, accountInfo);
      }
    } catch (error) {
      console.error('Error checking balances:', error);
    }
  }


  private async processTransactionRPC(transaction: VersionedTransactionResponse, accountInfo: AccountInfo) {
    try {
      // Check if there are any token transfers
      if ((transaction.meta?.preTokenBalances?.length ?? 0) > 0 || 
          (transaction.meta?.postTokenBalances?.length ?? 0) > 0) {
        return; // Skip if there are token transfers
      }

      const currentTimestamp = new Date().getTime() / 1000;
      const txTimestamp = transaction.blockTime || 0;
      
      if (currentTimestamp - txTimestamp > 60) {
        return;
      }

      const accountKeys = transaction.transaction.message.staticAccountKeys.map(key => key.toBase58());
      const preBalances = transaction.meta?.preBalances || [];
      const postBalances = transaction.meta?.postBalances || [];
      
      // Find the index of our tracked account
      const accountIndex = accountKeys.findIndex(key => key === accountInfo.address);
      if (accountIndex === -1) return;

      // Check if this is an outgoing transaction (balance decreased)
      const sent = Number(preBalances[accountIndex]) - Number(postBalances[accountIndex]);
      if (sent <= MIN_SOL_TRANSFER) return;

      // Find the recipient (account that received SOL)
      for (let j = 0; j < accountKeys.length; j++) {
        if (j === accountIndex) continue;

        const recipient = accountKeys[j];
        const received = Number(postBalances[j]) - Number(preBalances[j]);

        // Only process if we found a matching receive amount
        if (received > 0) {
          console.log(`Native SOL transfer detected: ${accountInfo.address} sent ${sent/LAMPORTS_PER_SOL} SOL to ${recipient}`);
          // Process the transfer
          await this.processTransfer({
            ...accountInfo,
            currentBalance: Number(postBalances[accountIndex])
          }, recipient, sent);
        }
      }
    } catch (error) {
      console.error('Error processing transaction:', error);
    }
  }

  private async scanTransactions(address: string) {
    const accountInfo = this.scanList.get(address);

    if (!accountInfo || accountInfo.status !== 'processedBAL') return;

    try {
      // Get signatures without 'until' parameter
      const signatures = await this.connection.getSignaturesForAddress(
        new PublicKey(address),
        { 
          limit: 200
        }
      );

      // No signatures found
      if (signatures.length === 0) {
        accountInfo.status = 'processedTX';
        this.scanList.set(address, accountInfo);
        return;
      }

      // If we have a stored last signature, find where to start from
      let signaturestoProcess = signatures;
      if (accountInfo.latestTransaction) {
        const lastIndex = signatures.findIndex(
          sig => sig.signature === accountInfo.latestTransaction
        );
        
        if (lastIndex !== -1) {
          // Only process transactions newer than our last processed one
          signaturestoProcess = signatures.slice(0, lastIndex);
          if (signaturestoProcess.length === 0) {
            // No new transactions to process
            accountInfo.status = 'processedTX';
            this.scanList.set(address, accountInfo);
            return;
          }
        }
      }

      // Update the latest transaction BEFORE processing
      accountInfo.latestTransaction = signatures[0].signature;
      
      console.log(`Processing ${signaturestoProcess.length} new transactions for ${address}`);

      // Process each new transaction
      for (const sigInfo of signaturestoProcess) {
        if (sigInfo.err) continue;
        
        const transaction = await this.connection.getTransaction(sigInfo.signature, {
          maxSupportedTransactionVersion: 0
        });
        
        if (transaction) {
          await this.processTransactionRPC(transaction, accountInfo);
        }
      }

      // Update status
      accountInfo.status = 'processedTX';
      this.scanList.set(address, accountInfo);
      
    } catch (error) {
      console.error(`Error scanning transactions for ${address}:`, error);
    }
  }

  private async reconnectStream() {
    if (this.stream) {
      try {
        this.stream.destroy();
      } catch (error) {
        console.error('Error destroying stream:', error);
      }
    }
    await this.initializeStream();
  }

  private async startTransactionProcessor() {
    setInterval(async () => {
      const addressesToProcess = Array.from(this.scanList.entries())
        .filter(([, info]) => info.status === 'processedBAL')
        .map(([address]) => address);

      for (const address of addressesToProcess) {
        await this.scanTransactions(address);
      }
      //console.log("account tx acounts:",addressesToProcess.length);
    }, BALANCE_CHECK_INTERVAL);
  }

  public async start(): Promise<void> {
    if (!this.isInitialized) {
      throw new Error('AccountTracker must be initialized before starting');
    }

    console.log('\n🚀 Starting AccountTracker');
    console.log('================================');
    this.printScanListStatus();
    
    // Start both balance checker and transaction processor
    this.startBalanceChecker();
    this.startTransactionProcessor();
    
    return new Promise(() => {
      console.log('\n🔄 AccountTracker is now running...');
    });
  }
}

export default AccountTracker;

// Usage example:
async function main() {
  console.log('\n🔌 Connecting to services...');
  console.log(`GRPC Endpoint: ${GRPC_ENDPOINT}`);
  console.log(`Alchemy RPC: ${ALCHEMY_RPC_URL}`);

  const client = new Client(
    GRPC_ENDPOINT,
    undefined,
    {}
  );

  const connection = new Connection(ALCHEMY_RPC_URL, 'confirmed');
  const masterAddress = "35VgtJDt1B3n1GRKdKrpVfHjeMTmaeYwgY8DgFPxzRSP";

  console.log('\n📥 Fetching addresses from database...');
  const initialAddresses = await getEnabledAccountsForGRPC();
  console.log(`Found ${initialAddresses.length} enabled addresses:`);
  initialAddresses.forEach((addr: string) => console.log(`→ ${addr}`));

  console.log('\n🔧 Creating AccountTracker...');
  const tracker = new AccountTracker(client, connection, masterAddress);
  
  
  await tracker.initialize(initialAddresses);
  await tracker.start();
}

main().catch(error => {
  console.error('\n❌ Fatal error:', error);
  process.exit(1);
});
