import Fastify from 'fastify';
import axios from 'axios';
import formbody from '@fastify/formbody';

const app = Fastify();
const PORT = 4444;

app.register(formbody);

/**
 * For a given token, returns:
 *    Map<user, Trade[]>
 * Where Trade only has the fields you care about: price and timestamp
 */
type Trade = {
    sol_amount: number;
    timestamp: number;
    token_amount: number;
    is_buy: boolean;
};

const fetchUserTradesForToken = async (token: string): Promise<Map<string, Trade[]>> => {
    let offset = 0;
    const limit = 100;
    const userTrades = new Map<string, Trade[]>();

    while (true) {
        try {
            const url = `https://frontend-api-v3.pump.fun/trades/all/${token}?limit=${limit}&offset=${offset}&minimumSize=0`;
            const response = await axios.get(url);
            const data = response.data;

            if (data.length === 0) break;
            //await new Promise(resolve => setTimeout(resolve, 1000));
            data.forEach((entry: any) => {
                if (entry.user) {
                    if (!userTrades.has(entry.user)) userTrades.set(entry.user, []);
                    userTrades.get(entry.user)!.push({
                        sol_amount: entry.sol_amount,
                        timestamp: entry.timestamp,
                        token_amount: entry.token_amount,
                        is_buy: entry.is_buy
                    });
                }
            });

            offset += limit;
        } catch (error) {
            console.error(`Error fetching data for token ${token}:`, error);
            break;
        }
    }
    // Optionally sort by timestamp so newest is last:
    userTrades.forEach(list => list.sort((a, b) => a.timestamp - b.timestamp));
    return userTrades;
};

app.get('/', async (req, reply) => {
    reply.type('text/html').send(`
        <html>
        <body>
            <form action="/compare" method="post">
                <label>Enter tokens (one per line):</label><br>
                <textarea name="tokens" rows="10" cols="50" placeholder="Enter one token per line"></textarea><br><br>
                <button type="submit">OK</button>
            </form>
        </body>
        </html>
    `);
});

app.post('/compare', async (req, reply) => {
    try {
        const { tokens } = req.body as { tokens: string };
        const tokenList = tokens
            .split('\n')
            .map((token: string) => token.trim())
            .filter(Boolean);

        if (tokenList.length < 2) {
            return reply.send('Please enter at least 2 tokens.');
        }

        // For each token, get Map<user, Trade[]>
        const allUserTradeMaps: Map<string, Trade[]>[] =
            await Promise.all(tokenList.map(fetchUserTradesForToken));

        // Find common users (intersection)
        let commonUsers = new Set([...allUserTradeMaps[0].keys()]);
        for (let i = 1; i < allUserTradeMaps.length; i++) {
            commonUsers = new Set(
                [...commonUsers].filter(u => allUserTradeMaps[i].has(u))
            );
        }

        // For each common user, show their last trade for each token
        let resultHtml = `<h3>Common Users (${commonUsers.size}):</h3><table border="1"><tr><th>User</th>`;
        tokenList.forEach((token, idx) => {
            resultHtml += `<th>${token} (first trade)</th>`;
        });
        resultHtml += '</tr>';

        for (const user of commonUsers) {
            resultHtml += `<tr><td>${user}</td>`;
            allUserTradeMaps.forEach((userTradeMap, tokenIdx) => {
                const userTrades = userTradeMap.get(user)!;
                const lastTrade = userTrades[0];
                // Show sol_amount, buy/sell, and timestamp if you want
                resultHtml += `<td>${(lastTrade.sol_amount/lastTrade.token_amount*1e6*170).toFixed(0)} Mcap, Buy sol: ${(lastTrade.sol_amount/1e9).toFixed(3)} Total trades: ${userTrades.length} [${new Date(lastTrade.timestamp*1000).toLocaleString()}]</td>`;
            });
            resultHtml += '</tr>';
        }
        resultHtml += `</table><p><a href="/">Back to form</a></p>`;

        reply.type('text/html').send(`
            <html>
            <body>
                ${resultHtml}
            </body>
            </html>
        `);
    } catch (error: any) {
        console.error('Error comparing tokens:', error);
        reply.status(500).send(`Error comparing tokens: ${error.message}`);
    }
});

app.listen({ port: PORT, host: '0.0.0.0' }, (err, address) => {
    if (err) {
        console.error('Error starting server:', err);
        process.exit(1);
    }
    console.log(`Server running at ${address}`);
});