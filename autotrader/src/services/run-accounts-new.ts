import { spawn } from 'child_process';
import path from 'path';
import fs from 'fs';

// Path to the compiled JavaScript file
const scriptPath = path.join(__dirname, 'accounts-new.js');

// Check if the compiled file exists
if (!fs.existsSync(scriptPath)) {
  console.error(`Error: ${scriptPath} does not exist. Make sure to compile the TypeScript files first.`);
  process.exit(1);
}

console.log(`Starting accounts-new.js from ${scriptPath}`);

// Spawn the process
const child = spawn('node', [scriptPath], {
  stdio: 'inherit',
  detached: false
});

// Handle process events
child.on('error', (err) => {
  console.error('Failed to start process:', err);
});

child.on('exit', (code, signal) => {
  if (code !== 0) {
    console.log(`Process exited with code ${code} and signal ${signal}`);
  }
});

// Handle signals to gracefully shut down the child process
process.on('SIGINT', () => {
  console.log('Received SIGINT. Shutting down child process...');
  child.kill('SIGINT');
});

process.on('SIGTERM', () => {
  console.log('Received SIGTERM. Shutting down child process...');
  child.kill('SIGTERM');
});
