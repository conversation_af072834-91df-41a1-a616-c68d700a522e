import { Connection, PublicKey } from '@solana/web3.js';
import dotenv from 'dotenv';
import { getAllDevWallets, updateDevWalletBalance } from '../common/db';

dotenv.config();

const SYNDICA_RPC_URL = "https://solana-mainnet.api.syndica.io/api-key/21KG2L6E3kGURokSr4VwfrtYMbqnqVNYKJKCBTQv2jsiLs97iW8TQv98fcz5kvVDhS3MjVmGt91jZ3UGQpGD7zaPCuxpwgCJbek";
const ALCHEMY_RPC_URL = "https://solana-mainnet.g.alchemy.com/v2/********************************";

// Use Alchemy for balance checking as it's more reliable for batch requests
const connection = new Connection(ALCHEMY_RPC_URL, 'confirmed');

type DevWallet = {
  address: string;
  dev_entity_id: number;
  balance: number;
};

const BATCH_SIZE = 100; // Process accounts in batches to avoid rate limits
const CHECK_INTERVAL = 30000; // Check balances every 30 seconds

async function checkAccountBalances(wallets: DevWallet[]) {
  console.log(`Checking balances for ${wallets.length} dev wallets...`);

  let updatedCount = 0;
  let unchangedCount = 0;

  // Process wallets in batches
  for (let i = 0; i < wallets.length; i += BATCH_SIZE) {
    const batch = wallets.slice(i, i + BATCH_SIZE);

    try {
      const publicKeys = batch.map(wallet => new PublicKey(wallet.address));
      const accounts = await connection.getMultipleParsedAccounts(publicKeys);

      // Process each account in the batch
      for (let j = 0; j < batch.length; j++) {
        const wallet = batch[j];
        const account = accounts.value[j];
        const balance = account === null ? 0 : account.lamports;
        const balanceInSol = balance / 1_000_000_000;
        const previousBalance = wallet.balance || 0;

        // Only update if balance has changed (with small tolerance for floating point precision)
        const balanceChanged = Math.abs(balanceInSol - previousBalance) > 0.000001;

        if (balanceChanged) {
          try {
            // Update balance in database only if it changed
            await updateDevWalletBalance(wallet.address, balanceInSol);
            updatedCount++;

            // Log balance changes
            const change = balanceInSol - previousBalance;
            const changeStr = change > 0 ? `+${change.toFixed(6)}` : change.toFixed(6);
            console.log(`🔄 Balance changed - Entity ${wallet.dev_entity_id}: ${wallet.address} = ${balanceInSol.toFixed(6)} SOL (${changeStr})`);

            // Log significant balance states
            if (balanceInSol < 0.01 && balanceInSol > 0) {
              console.log(`⚠️  Low balance detected - Entity ${wallet.dev_entity_id}: ${wallet.address} = ${balanceInSol.toFixed(6)} SOL`);
            } else if (balanceInSol === 0 && previousBalance > 0) {
              console.log(`💸 Account drained - Entity ${wallet.dev_entity_id}: ${wallet.address} = 0 SOL (was ${previousBalance.toFixed(6)})`);
            } else if (balanceInSol > 1 && previousBalance <= 1) {
              console.log(`💰 High balance reached - Entity ${wallet.dev_entity_id}: ${wallet.address} = ${balanceInSol.toFixed(3)} SOL`);
            }

          } catch (error) {
            console.error(`❌ Error updating balance for ${wallet.address}:`, error);
          }
        } else {
          unchangedCount++;
        }

        // Log progress every 50 accounts
        if ((i + j + 1) % 50 === 0) {
          console.log(`✅ Processed ${i + j + 1}/${wallets.length} accounts - Updated: ${updatedCount}, Unchanged: ${unchangedCount}`);
        }
      }

      // Small delay between batches to avoid rate limiting
      if (i + BATCH_SIZE < wallets.length) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }

    } catch (error) {
      console.error(`❌ Error processing batch starting at index ${i}:`, error);
      // Continue with next batch even if this one fails
    }
  }

  console.log(`✅ Completed balance check for all ${wallets.length} dev wallets`);
  console.log(`📊 Summary: ${updatedCount} balances updated, ${unchangedCount} unchanged`);
}

async function runBalanceChecker() {
  console.log('🚀 Starting Dev Wallet Balance Checker...');
  console.log(`📊 Check interval: ${CHECK_INTERVAL / 1000} seconds`);
  console.log(`📦 Batch size: ${BATCH_SIZE} accounts per batch`);
  console.log(`🔗 RPC endpoint: ${ALCHEMY_RPC_URL.substring(0, 50)}...`);
  
  let isRunning = false;
  
  const checkBalances = async () => {
    if (isRunning) {
      console.log('⏳ Previous balance check still running, skipping this cycle...');
      return;
    }
    
    isRunning = true;
    const startTime = Date.now();
    
    try {
      // Get all dev wallets from database
      const wallets = await getAllDevWallets() as DevWallet[];
      
      if (wallets.length === 0) {
        console.log('📭 No dev wallets found in database');
        return;
      }
      
      console.log(`\n🔄 Starting balance check cycle at ${new Date().toISOString()}`);
      console.log(`📋 Found ${wallets.length} dev wallets to check`);
      
      await checkAccountBalances(wallets);
      
      const duration = (Date.now() - startTime) / 1000;
      console.log(`⏱️  Balance check completed in ${duration.toFixed(2)} seconds\n`);
      
    } catch (error) {
      console.error('❌ Error in balance check cycle:', error);
    } finally {
      isRunning = false;
    }
  };
  
  // Run initial check
  await checkBalances();
  
  // Set up interval for regular checks
  setInterval(checkBalances, CHECK_INTERVAL);
  
  console.log('✅ Balance checker is now running...');
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Received SIGINT. Shutting down balance checker...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Received SIGTERM. Shutting down balance checker...');
  process.exit(0);
});

// Start the balance checker
runBalanceChecker().catch((error) => {
  console.error('💥 Fatal error in balance checker:', error);
  process.exit(1);
});
