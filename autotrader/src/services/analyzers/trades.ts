import Fastify from 'fastify';
import { getMintTradeDataMonitor } from "../../common/db";
import dotenv from "dotenv";

dotenv.config();

const fastify = Fastify({ logger: false });

interface QueryParams {
  mint: string;
}

fastify.get('/', async (request, reply) => {
  reply.type('text/html').send(`
    <html>
      <head>
        <title>Mint Trade Data</title>
        <style>
          table {
            width: 100%;
            border-collapse: collapse;
          }
          th, td {
            border: 1px solid black;
            padding: 3px;
            text-align: left;
            font-size: 10px;
          }
          th {
            cursor: pointer;
          }
          .highlight {
            background-color: yellow;
          }
          .light-gray {
            background-color: lightgray;
          }
          .gray {
            background-color: gray;
          }
          .light-green {
            background-color: lightgreen;
          }
          .light-red {
            background-color: lightcoral;
          }
          .yellow {
            background-color: yellow;
          }
          .purple {
            background-color: purple;
            color: white;
          }
          .account-buy {
            background-color: green;
            color: white;
          }
          .account-sell {
            background-color: red;
            color: white;
          }
        </style>
      </head>
      <body>
        <h1>Mint Trade Data</h1>
        <form method="GET" action="/data">
          <label for="mint">Mint:</label>
          <input type="text" id="mint" name="mint" required>
          <button type="submit">Get Data</button>
        </form>
        <button id="toggleReload">Toggle Auto Reload</button>
        <div id="table-container"></div>
        <script>
          let autoReload = false;
          let reloadInterval;

          document.getElementById('toggleReload').addEventListener('click', () => {
            autoReload = !autoReload;
            if (autoReload) {
              console.log('Auto reload enabled');
              reloadInterval = setInterval(() => {
                location.reload();
              }, 1000);
            } else {
              console.log('Auto reload disabled');
              clearInterval(reloadInterval);
            }
          });

          function sortTable(n) {
            const table = document.getElementById("tradeTable");
            let rows, switching, i, x, y, shouldSwitch, dir, switchcount = 0;
            switching = true;
            dir = "asc";
            while (switching) {
              switching = false;
              rows = table.rows;
              for (i = 1; i < (rows.length - 1); i++) {
                shouldSwitch = false;
                x = rows[i].getElementsByTagName("TD")[n];
                y = rows[i + 1].getElementsByTagName("TD")[n];
                if (dir == "asc") {
                  if (x.innerHTML.toLowerCase() > y.innerHTML.toLowerCase()) {
                    shouldSwitch = true;
                    break;
                  }
                } else if (dir == "desc") {
                  if (x.innerHTML.toLowerCase() < y.innerHTML.toLowerCase()) {
                    shouldSwitch = true;
                    break;
                  }
                }
              }
              if (shouldSwitch) {
                rows[i].parentNode.insertBefore(rows[i + 1], rows[i]);
                switching = true;
                switchcount++;
              } else {
                if (switchcount == 0 && dir == "asc") {
                  dir = "desc";
                  switching = true;
                }
              }
            }
          }

          function toggleHighlight(event) {
            const row = event.currentTarget;
            row.classList.toggle('highlight');
          }

          document.addEventListener('DOMContentLoaded', () => {
            const rows = document.querySelectorAll('#tradeTable tbody tr');
            rows.forEach(row => {
              row.addEventListener('click', toggleHighlight);
            });
          });
        </script>
      </body>
    </html>
  `);
});

fastify.get<{ Querystring: QueryParams }>('/data', async (request, reply) => {
  const mint = request.query.mint;
  const data = await getMintTradeDataMonitor(mint);

  if (!data) {
    reply.type('text/html').send('<p>No data found for the given mint.</p>');
    return;
  }

  let tableRows = data.map(row => {
    let tradeDiffClass = '';
    const tradeDiff = row.trade_diff / 1e9;
    if (tradeDiff > 1.7 && tradeDiff < 2) {
      tradeDiffClass = 'light-gray';
    } else if (tradeDiff >= 2) {
      tradeDiffClass = 'gray';
    }

    let tradeClass = '';
    if (row.trade === 'buy') {
      tradeClass = 'light-green';
    } else if (row.trade === 'sell') {
      tradeClass = 'light-red';
    }

    let solAmountClass = '';
    const solAmount = row.sol_amount / 1e9;
    if (solAmount >= 0.5 && solAmount < 0.95) {
      solAmountClass = 'yellow';
    } else if (solAmount >= 0.95) {
      solAmountClass = 'purple';
    }

    let accountClass = '';
    if (row.account === '68s9x6ng4T5jeKe2PSZMRNESm2LTLxiDSAU5UkJPg1je') {
      accountClass = row.trade === 'buy' ? 'account-buy' : 'account-sell';
    }

    return `
      <tr class="${accountClass}">
        <td>${row.account}</td>
        <td class="${solAmountClass}">${solAmount.toFixed(2)}</td>
        <td class="${tradeClass}">${row.trade}</td>
        <td>${row.market_cup}</td>
        <td>${new Date(row.block_time * 1000).toLocaleString()}</td>
        <td>${(row.buy_sum / 1e9).toFixed(2)}</td>
        <td>${(row.sell_sum / 1e9).toFixed(2)}</td>
        <td class="${tradeDiffClass}">${tradeDiff.toFixed(2)}</td>
      </tr>
    `;
  }).join('');

  reply.type('text/html').send(`
    <html>
      <head>
        <title>Mint Trade Data</title>
        <style>
          table {
            width: 100%;
            border-collapse: collapse;
          }
          th, td {
            border: 1px solid black;
            padding: 4px;
            text-align: left;
          }
          th {
            cursor: pointer;
          }
          .highlight {
            background-color: yellow;
          }
          .light-gray {
            background-color: lightgray;
          }
          .gray {
            background-color: gray;
          }
          .light-green {
            background-color: lightgreen;
          }
          .light-red {
            background-color: lightcoral;
          }
          .yellow {
            background-color: yellow;
          }
          .purple {
            background-color: purple;
            color: white;
          }
          .account-buy {
            background-color: green;
            color: white;
          }
          .account-sell {
            background-color: red;
            color: white;
          }
        </style>
      </head>
      <body>
        <h1>Mint Trade Data</h1>
        <form method="GET" action="/data">
          <label for="mint">Mint:</label>
          <input type="text" id="mint" name="mint" value="${mint}" required>
          <button type="submit">Get Data</button>
        </form>
        <button id="toggleReload">Toggle Auto Reload</button>
        <table id="tradeTable">
          <thead>
            <tr>
              <th onclick="sortTable(0)">Account</th>
              <th onclick="sortTable(1)">SOL Amount</th>
              <th onclick="sortTable(2)">Trade</th>
              <th onclick="sortTable(3)">Market Cap</th>
              <th onclick="sortTable(4)">Block Time</th>
              <th onclick="sortTable(5)">Buy Sum</th>
              <th onclick="sortTable(6)">Sell Sum</th>
              <th onclick="sortTable(7)">Trade Diff</th>
            </tr>
          </thead>
          <tbody>
            ${tableRows}
          </tbody>
        </table>
        <script>
          let autoReload = false;
          let reloadInterval;

          document.getElementById('toggleReload').addEventListener('click', () => {
            autoReload = !autoReload;
            if (autoReload) {
              console.log('Auto reload enabled');
              reloadInterval = setInterval(() => {
                location.reload();
              }, 1000);
            } else {
              console.log('Auto reload disabled');
              clearInterval(reloadInterval);
            }
          });

          function sortTable(n) {
            const table = document.getElementById("tradeTable");
            let rows, switching, i, x, y, shouldSwitch, dir, switchcount = 0;
            switching = true;
            dir = "asc";
            while (switching) {
              switching = false;
              rows = table.rows;
              for (i = 1; i < (rows.length - 1); i++) {
                shouldSwitch = false;
                x = rows[i].getElementsByTagName("TD")[n];
                y = rows[i + 1].getElementsByTagName("TD")[n];
                if (dir == "asc") {
                  if (x.innerHTML.toLowerCase() > y.innerHTML.toLowerCase()) {
                    shouldSwitch = true;
                    break;
                  }
                } else if (dir == "desc") {
                  if (x.innerHTML.toLowerCase() < y.innerHTML.toLowerCase()) {
                    shouldSwitch = true;
                    break;
                  }
                }
              }
              if (shouldSwitch) {
                rows[i].parentNode.insertBefore(rows[i + 1], rows[i]);
                switching = true;
                switchcount++;
              } else {
                if (switchcount == 0 && dir == "asc") {
                  dir = "desc";
                  switching = true;
                }
              }
            }
          }

          function toggleHighlight(event) {
            const row = event.currentTarget;
            row.classList.toggle('highlight');
          }

          document.addEventListener('DOMContentLoaded', () => {
            const rows = document.querySelectorAll('#tradeTable tbody tr');
            rows.forEach(row => {
              row.addEventListener('click', toggleHighlight);
            });
          });
        </script>
      </body>
    </html>
  `);
});

fastify.listen({ port: 7017, host: '0.0.0.0' }, (err, address) => {
  if (err) {
    fastify.log.error(err);
    process.exit(1);
  }
  fastify.log.info(`Server is running on ${address}`);
});


