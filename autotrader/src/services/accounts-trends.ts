/* eslint-disable @typescript-eslint/no-unused-vars */
import Client, {
  CommitmentLevel,
  SubscribeRequest,
  SubscribeUpdate,
  SubscribeUpdateTransaction,
} from "@triton-one/yellowstone-grpc";
//import { sendToDiscord } from "../common/discord";
import dotenv from "dotenv";
import { ClientDuplexStream } from "@grpc/grpc-js";
import { PublicKey } from "@solana/web3.js";
import bs58 from "bs58";
import { getRedisString, getRedisClient } from "../common/redis";
import { redisAddress } from "../common/types";
import { getAppStatus, saveTransactionsToDatabase, updatMintTradeDataBuy, updatMintTradeDataSell, getAppConfigAll, addMintTrend } from "../common/db";
import { getDbPool } from "../common/db-pool";
import { PUMPFUN_DISCORD_WEBHOOK, RPC_URL_CS, RPC_URL_SYNDICA } from "../common/config";
import { JITO_MAINNET_TIP_ACCOUNTS } from "../common/config";
import { BorshCoder } from "@coral-xyz/anchor";
import { ruleIsSignedByAuto1Owner } from "../common/pump-rules";
// eslint-disable-next-line @typescript-eslint/no-require-imports
const idl = require("../common/idl-new.json");
const coder = new BorshCoder(idl as any);
import { Connection, clusterApiUrl } from "@solana/web3.js";
import fs from "fs";
import https from "https";
import fetch from "node-fetch";
import { httpsAgent } from '../common/agent';
import { startSessionWarmup } from './mints-grpc-connwarmer';
import { Keypair, Transaction, SystemProgram, ComputeBudgetProgram } from '@solana/web3.js';
import { getSellInstructionNoRPC, getBuyInstructionNoRPC } from "../common/trade";
import { TRADERS_PROGRAMS } from "../common/config";
import {
  addTradeData,
  getResults,
  TokenTracker
} from "../common/tokenTraderMatcher";
import { addMintTrendsTrade, getAddressesFromMintTrendsTrades } from "../common/db";
import { sendToDiscord } from "../common/discord";
import { sendPushoverMessage } from "../common/pushover";
dotenv.config();

const dataBuffer = new Map<string, string>();

const ttracker = new TokenTracker(5);
const minSolForTracer = 1; // Match when users have at least 2 tokens in common

const traderAccounts = new Set();
let tradeMeasure = 0.0;
let appConfigs: ConfigByApp;
let latestBlockhash: string | null = null;
let lastBlockhashTime: number = 0;
const connection = new Connection(RPC_URL_SYNDICA, 'processed');
const mintTracker: { [key: string]: MintStats } = {};
const mintTrends: {
  [key: string]: {
    owner: string,
    symbol: string,
    created_at?: number, // Optional property added
    vol10sec: number,
    vol10secTrades: number,
    vol10secUsers: number,
    vol10secBuys: number,
    vol10secSells: number,
    vol1min: number,
    vol1minTrades: number,
    vol1minUsers: number,
    vol1minBuys: number,
    vol1minSells: number,
    vol5min: number,
    vol5minTrades: number,
    vol5minUsers: number,
    vol5minBuys: number,
    vol5minSells: number,
    vol15min: number,
    vol15minTrades: number,
    vol15minUsers: number,
    vol15minBuys: number,
    vol15minSells: number,
    volAll: number,
    price: number,
    marketCap: number,
    maxMarketCap: number,
    minMarketCap: number,
    usersTotalBuySol: number,
    usersTotalSellSol: number,
    usersHoldingCount: number,
    usersHoldingTokens: number,
    devTotalBuySol: number,
    devTotalSellSol: number,
    devHoldingCount: number,
    devHoldingTokens: number,
    customStatus: boolean,
    customCounter: number;
    trades: {
      amount: number,
      tokenAmount: number,
      timestamp: number,
      user: string,
      isBuy: boolean
      isDev: boolean
    }[],
    users: {
      [userAddress: string]: {
        tokenBalance: number,
        totalSpentSol: number,
        totalGotSol: number,
        lastTradeTimestamp: number
        isDev: boolean
      }
    }
  }
} = {};

const dev_traders = new Set<string>();

const ENDPOINT = "http://grpc.solanavibestation.com:10000";
const PUMP_FUN_PROGRAM_ID = "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P";
// Use the shared Redis client instead of creating a new connection
const redis = getRedisClient();
const PUMP_FUN_CREATE_IX_DISCRIMINATOR = Buffer.from([24, 30, 200, 40, 5, 28, 7, 119,]);
const TRADING_ACCOUNT = "68s9x6ng4T5jeKe2PSZMRNESm2LTLxiDSAU5UkJPg1je";
const COMMITMENT = CommitmentLevel.PROCESSED;
const FILTER_CONFIG = {
  programIds: [PUMP_FUN_PROGRAM_ID, TRADING_ACCOUNT],
  instructionDiscriminators: [PUMP_FUN_CREATE_IX_DISCRIMINATOR],
};

const devAccounts = new Set();
const jitoEndpoint = 'https://mainnet.block-engine.jito.wtf/api/v1/transactions';

const ACCOUNTS_TO_INCLUDE = [
  {
    name: "mint",
    index: 0,
  },
];

const agent = new https.Agent({
  keepAlive: true,
  keepAliveMsecs: 60000 // Set keepAlive timeout to 1 minute (60000 ms)
});


interface FormattedTransactionData {
  signature: string;
  slot: string;
  [accountName: string]: string;
}

type MintStats = {
  // Creation data
  created_at: number;  // Unix timestamp
  name: string;
  symbol: string;
  owner: string;
  signature: string;

  // Trading stats
  sum_buys: number;
  sum_sells: number;
  dev_profit: number;
  tokenAmount?: number;  // Added tokenAmount property
  solAmount?: number;  // Added solAmount property
  buyPrice?: number;  // Added buyPrice property

  // Market data
  app: string;  // Will be updated during trades
  mcap_current: number;
  mcap_max: number;
};

type RedisAddress = {
  addressType: string;
  address: string;
  app: string;
};

// Use the shared database pool instead of creating a new one
const dbPool = getDbPool();

interface CompiledInstruction {
  programIdIndex: number;
  accounts: Uint8Array;
  data: Uint8Array;
}

interface Message {
  header: MessageHeader | undefined;
  accountKeys: Uint8Array[];
  recentBlockhash: Uint8Array;
  instructions: CompiledInstruction[];
  versioned: boolean;
  addressTableLookups: MessageAddressTableLookup[];
}

interface MessageHeader {
  numRequiredSignatures: number;
  numReadonlySignedAccounts: number;
  numReadonlyUnsignedAccounts: number;
}

interface MessageAddressTableLookup {
  accountKey: Uint8Array;
  writableIndexes: Uint8Array;
  readonlyIndexes: Uint8Array;
}
type TradeEvent = {
  mint: string;
  solAmount: number;
  tokenAmount: number;
  isBuy: boolean;
  user: string;
  timestamp: number;
  virtualSolReserves: number;
  virtualTokenReserves: number;
  realSolReserves: number;
  realTokenReserves: number;
  signature?: string;
  signer?: string;
  block_id?: number;
  app?: string;
};

type AutoConfig = {
  enabled: number;
  app: string;
  is_simulation: number;
  buy_sol: number;
  max_time_sec: number;
  min_time_sec: number;
  max_profit_sol: number;
  min_profit_sol: number;
  max_user_buy_sol: number;
  max_mc_pct: number;
};

type ConfigByApp = {
  [key: string]: Omit<AutoConfig, 'app'>;
};

function convertConfigs(configs: AutoConfig[]): ConfigByApp {
  const result: ConfigByApp = {};

  configs.forEach(config => {
    const { app, ...rest } = config;
    result[app] = rest;
  });

  return result;
}




async function updateLatestBlockhash(): Promise<void> {
  try {
    const { blockhash, lastValidBlockHeight } = await connection.getLatestBlockhash();
    latestBlockhash = blockhash;
    lastBlockhashTime = Date.now();
    //console.log(`Updated blockhash: ${blockhash}, height: ${lastValidBlockHeight}`);
  } catch (error) {
    console.error('Failed to fetch latest blockhash:', error);
  }
}

function startBlockhashUpdateProcess(): void {
  // Initial update
  updateLatestBlockhash();

  // Update every 2 seconds
  setInterval(updateLatestBlockhash, 2000);
}

function getLatestBlockhash(): string {
  if (!latestBlockhash) {
    return "test";
  }
  return latestBlockhash;
}

function eventDecode(data: string): any {
  try {
    const instruction = coder.events.decode(
      Buffer.from(bs58.decode(data)).slice(8).toString("base64")
    );
    return instruction;
  } catch (error) {
    console.error("Event decode error:", error);
    // Return null instead of throwing
    return null;
  }
}

async function bootstrapGRPC(): Promise<void> {
  while (true) {
    try {
      const stream = await reconnectStream();
      await handleStreamEvents(stream);
    } catch (error) {
      console.error("Main loop error:", error);
      console.log("Retrying in 5 seconds...");
      await new Promise(resolve => setTimeout(resolve, 5000));
    }
  }
}

async function mainLoop(): Promise<void> {
  while (true) {
    await new Promise(resolve => setTimeout(resolve, 1));

    // Check if new messages are available
    if (dataBuffer.size > 0) {
      console.log(`Main Loop: Processing ${dataBuffer.size} messages`);
      for (const [key, value] of dataBuffer.entries()) {
        console.log(Date.now());
        console.log(`[Main Loop] Processing Message: ${key} -> ${value}`);
        dataBuffer.delete(key);  // Remove after processing
      }
    }
  }
}


async function main(): Promise<void> {

  const dev_accounts = await getAddressesFromMintTrendsTrades();
  for (const account of dev_accounts) {
    dev_traders.add(account);
  }



  //startBlockhashUpdateProcess();
  startSessionWarmup();

  appConfigs = convertConfigs(await getAppConfigAll());
  console.log("App Configs:", appConfigs);
  await Promise.all([bootstrapGRPC(), mainLoop()]);
  // while (true) {
  //   try {
  //     const stream = await reconnectStream();
  //     await handleStreamEvents(stream);
  //   } catch (error) {
  //     console.error("Main loop error:", error);
  //     console.log("Retrying in 5 seconds...");
  //     await new Promise(resolve => setTimeout(resolve, 5000));
  //   }
  // }
}

function createSubscribeRequest(): SubscribeRequest {
  return {
    accounts: {},
    slots: {},
    transactions: {
      pumpFun: {
        accountInclude: FILTER_CONFIG.programIds,
        accountExclude: [],
        accountRequired: [],// Add this to request metadata
      },
    },
    transactionsStatus: {},
    entry: {},
    blocks: {},
    blocksMeta: { blockmetadata: {} },
    commitment: COMMITMENT,
    accountsDataSlice: [],
    ping: undefined,
  };
}

function sendSubscribeRequest(
  stream: ClientDuplexStream<SubscribeRequest, SubscribeUpdate>,
  request: SubscribeRequest
): Promise<void> {
  return new Promise<void>((resolve, reject) => {
    stream.write(request, (err: Error | null) => {
      if (err) {
        reject(err);
      } else {
        resolve();
      }
    });
  });
}

function handleStreamEvents(
  stream: ClientDuplexStream<SubscribeRequest, SubscribeUpdate>
): Promise<void> {
  return new Promise<void>((resolve, reject) => {
    stream.on("data", async (data) => {
      try {
        await handleData(data);
      } catch (error) {
        console.error("Error processing data:", error);
        // Don't let decoding errors crash the stream
        if (error instanceof Error && (error.message.includes("Invalid bool") || error.message.includes("decode"))) {
          console.log("Recoverable decoding error - continuing...");
          return;
        }
      }
    });

    stream.on("error", async (error: Error) => {
      console.error("Stream error:", error);
      stream.end();

      // Attempt to reconnect
      console.log("Attempting to reconnect in 5 seconds...");
      setTimeout(async () => {
        try {
          const client = new Client(ENDPOINT, undefined, {});
          const newStream = await client.subscribe();
          const request = createSubscribeRequest();
          await sendSubscribeRequest(newStream, request);
          console.log("Successfully reconnected to gRPC stream");
          await handleStreamEvents(newStream);
        } catch (reconnectError) {
          console.error("Reconnection failed:", reconnectError);
          // Try again after delay
          setTimeout(() => handleStreamEvents(stream), 5000);
        }
      }, 5000);
    });

    stream.on("end", () => {
      console.log("Stream ended - attempting to reconnect...");
      setTimeout(() => handleStreamEvents(stream), 5000);
    });
  });
}

function handleData(data: SubscribeUpdate): void {
  try {
    const transaction = data.transaction?.transaction;
    const message = transaction?.transaction?.message;

    if (data.blockMeta?.blockhash) {
      latestBlockhash = data.blockMeta.blockhash;
      lastBlockhashTime = Date.now();
      //console.log(`Updated blockhash: ${latestBlockhash}`);
      //console.log("get lbh:",getLatestBlockhash());
    }

    if (!transaction || !message) {
      return;
    }

    const formattedSignature = convertSignature(transaction.signature);
    formatData(message, formattedSignature.base58, data);
  } catch (error) {
    // Log the error but don't throw
    console.error("Error in handleData:", error);
    if (error instanceof Error && error.message.includes("Invalid bool")) {
      console.log("Skipping transaction due to bool decoding error");
      return;
    }
    throw error; // Rethrow other errors
  }
}

function isSubscribeUpdateTransaction(
  data: SubscribeUpdate
): data is SubscribeUpdate & { transaction: SubscribeUpdateTransaction } {
  return (
    "transaction" in data &&
    typeof data.transaction === "object" &&
    data.transaction !== null &&
    "slot" in data.transaction &&
    "transaction" in data.transaction
  );
}

function convertSignature(signature: Uint8Array): { base58: string } {
  return { base58: bs58.encode(Buffer.from(signature)) };
}

async function formatData(
  message: Message,
  signature: string,
  data: SubscribeUpdate
): Promise<FormattedTransactionData | undefined> {
  const slot = data.transaction?.slot;
  const blockHash = data.blockMeta?.blockhash;
  const accountKeys = message.accountKeys;
  const owner = new PublicKey(accountKeys[0]).toBase58();

  const account = await getRedisString(redis, owner);
  let tradeData: TradeEvent | null = null;


  let appType = "";
  let addressType = "";



  data.transaction?.transaction?.meta?.innerInstructions?.forEach((innerInstruction) => {
    innerInstruction.instructions.forEach((instruction) => {
      const decodedEvent = eventDecode(bs58.encode(instruction.data));
      if (decodedEvent?.name === "TradeEvent") {
        tradeData = decodeTradeEventValues(instruction.data);
        if (tradeData) {
          processTradeEvent(tradeData, message, data);
        }

      }
      if (decodedEvent?.name === "CreateEvent") {
        const createData = decodeCreateEventValues(instruction.data);



        if (createData) {
          processCreateEvent(createData, message, data);

        }

        ;
      }
    });
  });



  try {
    if (account) {
      const redisAccount: redisAddress = JSON.parse(account);
      appType = redisAccount.app;
      addressType = redisAccount.addressType;

      if (appType != "" && addressType == "minter") {
        //updateDBSolPrice();
        if (!(await getAppStatus(appType))) {
          //     console.log(
          //       `[${new Date().toISOString()}] App ${appType} is disabled ${account}`
          //     );
          return;
        }

        //const currentDate = new Date().toISOString();

        // Example of using the database pool for queries:
        // await dbPool.execute('INSERT INTO rugger_mints (rugger, trade_status, mint, buy_sol_amount, buy_token_amount, sell_sol_amount, sell_token_amount, buy_tx, sell_tx, buy_mc, sell_mc, created_at, app) VALUES (?,?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)', [owner, "active", mint, 0, 0, 0, 0, 0, 0, 0, 0, new Date(), appType]);

        //TODO: setup inmemory array and calculate profit inmemory . init tokens["tokenname"] = {totabuys:0,totalsells:0,profit:0,mcap:0,apptype:"app01","status":"bought","tokenamount":3333333,"selltx":2222,"buytx":11111,"sellmc":3333,"buymc":11111}
        //TODO: Buy token in async put record in db . later on streadm check what i buy for any token and update database.
        //TODO: send out trades data out to add to

        //addRedisString(redis, mint,JSON.stringify({addressType:"token",address:mint,app:appType}));
        //await sendRabbitMessage(JSON.stringify({ "address": mint, "command":"connect" }), 'mints');
        //await sendRabbitMessage(JSON.stringify({ "address": appType, "command":"reset" }), 'accounts');
        //sendToDiscord(PUMPFUN_DISCORD_WEBHOOK, `gRPC (${appType}): ${mint} } ${currentDate}`);

        // console.log("Mint: ", mint," owner: ", owner);

        // const includedAccounts = ACCOUNTS_TO_INCLUDE.reduce<Record<string, string>>((acc, { name, index }) => {
        //   const accountIndex = matchingInstruction.accounts[index];
        //   const publicKey = accountKeys[accountIndex];
        //   acc[name] = new PublicKey(publicKey).toBase58();
        //   return acc;
        // }, {});

        //console.log("======================================💊 New Pump.fun Mint Detected!======================================");
        //console.table({signature,slot,...includedAccounts,owner});
        //console.log("\n");
      }
    } else {
      return;
    }
  } catch (error) {
    console.error("Failed to retrieve Rugger accounts:", error);
    return;
  }

  return;
}


function decodeTradeEventValues(data: Uint8Array): TradeEvent | null {
  const buffer = Buffer.from(data).slice(8); // Remove first 8 bytes for the event CPI

  const mint = bs58.encode(buffer.slice(8, 40));
  const solAmount = buffer.readBigUInt64LE(40);
  const tokenAmount = buffer.readBigUInt64LE(48);
  // Convert to boolean properly - any non-zero value is true
  const isBuy = buffer[56] !== 0;
  const user = bs58.encode(buffer.slice(57, 89));
  const timestamp = buffer.readBigInt64LE(89);
  const virtualSolReserves = buffer.readBigUInt64LE(97);
  const virtualTokenReserves = buffer.readBigUInt64LE(105);
  const realSolReserves = buffer.readBigUInt64LE(113);
  const realTokenReserves = buffer.readBigUInt64LE(121);

  return {
    mint,
    solAmount: Number(solAmount),
    tokenAmount: Number(tokenAmount),
    isBuy,
    user,
    timestamp: Number(timestamp),
    virtualSolReserves: Number(virtualSolReserves),
    virtualTokenReserves: Number(virtualTokenReserves),
    realSolReserves: Number(realSolReserves),
    realTokenReserves: Number(realTokenReserves),
  };
}

function decodeCreateEventValues(data: Uint8Array): { name: string, symbol: string, mint: string, uri: string } | null {
  try {
    const buffer = Buffer.from(data);
    //console.log("Raw buffer:", buffer.toString('hex'));

    // Skip the first 16 bytes (8 bytes discriminator + 8 bytes of additional data)
    const dataBuffer = buffer.slice(16);

    // First 4 bytes are name length (u32 LE)
    const nameLength = dataBuffer.readUInt32LE(0);
    if (nameLength > 100 || nameLength < 0) {
      return null;
    }

    // Read name
    const name = dataBuffer.slice(4, 4 + nameLength).toString('utf8');

    // Read symbol length
    const symbolLength = dataBuffer.readUInt32LE(4 + nameLength);
    if (symbolLength > 20 || symbolLength < 0) {
      return null;
    }

    // Read symbol
    const symbol = dataBuffer.slice(4 + nameLength + 4, 4 + nameLength + 4 + symbolLength).toString('utf8');

    // Read URI length
    const uriStart = 4 + nameLength + 4 + symbolLength;
    const uriLength = dataBuffer.readUInt32LE(uriStart);
    if (uriLength > 200 || uriLength < 0) {
      return null;
    }

    // Read URI
    const uri = dataBuffer.slice(uriStart + 4, uriStart + 4 + uriLength).toString('utf8');

    // Read mint (last 32 bytes)
    const mintStart = uriStart + 4 + uriLength;
    const mint = bs58.encode(dataBuffer.slice(mintStart, mintStart + 32));

    return { name, symbol, mint, uri };
  } catch (error) {
    console.error('Failed to decode CreateEvent:', error);
    return null;
  }
}

async function processCreateEvent(createData: { name: string, symbol: string, mint: string, uri: string }, message: Message, data: SubscribeUpdate): Promise<void> {

  //signer
  const signer = new PublicKey(message.accountKeys[0]).toBase58();
  // try {
  //   const response = await fetch(createData.uri);
  //   const metadata = await response.json();

  //   const {
  //     name,
  //     symbol,
  //     description,
  //     image,
  //     showName,
  //     createdOn,
  //     twitter,
  //     website,
  //   } = metadata;

  //   console.log("Extracted Metadata:", {
  //     name,
  //     symbol,
  //     description,
  //     image,
  //     showName,
  //     createdOn,
  //     twitter,
  //     website,
  //   });
  // } catch (error) {
  //   console.error("Failed to fetch or parse metadata from URI:", error);
  // }

  const solAmount = 0;
  const isBuy = true;
  const tokenAmount = 0;
  mintTrends[createData.mint] = {
    owner: signer, // Add appropriate owner value
    symbol: createData.symbol,
    created_at: Math.floor(Date.now() / 1000), // Unix timestamp
    vol10sec: solAmount,
    vol10secTrades: 1,
    vol10secUsers: 1,
    vol10secBuys: isBuy ? 1 : 0,
    vol10secSells: isBuy ? 0 : 1,
    vol1min: solAmount,
    vol1minTrades: 1,
    vol1minUsers: 1,
    vol1minBuys: isBuy ? 1 : 0,
    vol1minSells: isBuy ? 0 : 1,
    vol5min: solAmount,
    vol5minTrades: 1,
    vol5minUsers: 1,
    vol5minBuys: isBuy ? 1 : 0,
    vol5minSells: isBuy ? 0 : 1,
    vol15min: solAmount,
    vol15minTrades: 1,
    vol15minUsers: 1,
    vol15minBuys: isBuy ? 1 : 0,
    vol15minSells: isBuy ? 0 : 1,
    volAll: solAmount,
    usersHoldingCount: 0,
    usersHoldingTokens: 0,
    usersTotalBuySol: 0,
    usersTotalSellSol: 0,
    devHoldingCount: 0,
    devHoldingTokens: 0,
    devTotalBuySol: 0,
    devTotalSellSol: 0,
    price: 0,
    marketCap: 0,
    maxMarketCap: 0, // Add appropriate maxMarketCap value
    minMarketCap: 9999999, // Add appropriate maxMarketCap value
    customStatus: false,
    customCounter: 0,
    trades: [{
      amount: solAmount,
      tokenAmount: tokenAmount,
      timestamp: Date.now(),
      user: "",
      isBuy: false,
      isDev: false
    }],
    users: {} // Initialize users as an empty object
  };



}

async function processTradeEvent(tradeData: TradeEvent, message: Message, data: SubscribeUpdate): Promise<void> {
  try {
    const {
      user,
      mint,
      isBuy,
      solAmount,
      tokenAmount,
      virtualSolReserves,
      virtualTokenReserves
    } = tradeData;


    let isDev = true;

    const accounts = message.accountKeys.map(key => new PublicKey(key).toBase58());
    let hasTraderProgram = false;
    for (const instruction of message.instructions) {
      const programId = accounts[instruction.programIdIndex];
      if (TRADERS_PROGRAMS.includes(programId)) {
        hasTraderProgram = true;
        isDev = false;
        break;
      }
    }



    if (!mintTrends[tradeData.mint]) {
      return;
    }

    if (!isDev && !traderAccounts.has(user)) {
      traderAccounts.add(user);
    }


    const solAmountInSol = solAmount / 1e9;
    const price = solAmount / tokenAmount;
    const solPrice = 170;
    const marketCap = price * 1e6 * solPrice;
    mintTrends[mint].price = price;
    mintTrends[mint].marketCap = marketCap;
    mintTrends[mint].maxMarketCap = Math.max(mintTrends[mint].maxMarketCap, marketCap);
    if (mintTrends[mint].maxMarketCap > 6000) {
      mintTrends[mint].minMarketCap = Math.min(mintTrends[mint].minMarketCap, marketCap);
    }


    const currentTime = Date.now();

    // Initialize time-based volumes if they don't exist
    if (!mintTrends[tradeData.mint].trades) {
      mintTrends[tradeData.mint].trades = [];
    }

    // Add new trade with timestamp
    mintTrends[tradeData.mint].trades!.push({
      amount: solAmount,
      tokenAmount: tokenAmount,
      timestamp: currentTime,
      user: tradeData.user,
      isBuy: tradeData.isBuy,
      isDev: isDev,
    });

    // Update user data
    if (!mintTrends[tradeData.mint].users[tradeData.user]) {
      mintTrends[tradeData.mint].users[tradeData.user] = {
        tokenBalance: 0,
        totalSpentSol: 0,
        totalGotSol: 0,
        lastTradeTimestamp: 0,
        isDev: isDev
      };
    }

    const userData = mintTrends[tradeData.mint].users[tradeData.user];
    if (tradeData.isBuy) {
      userData.tokenBalance += tradeData.tokenAmount;
      userData.totalSpentSol += tradeData.solAmount;
    } else {
      userData.tokenBalance -= tradeData.tokenAmount;
      userData.totalGotSol += tradeData.solAmount;
    }
    userData.lastTradeTimestamp = currentTime;




    //   users: { 
    //     [userAddress: string]: {
    //       tokenBalance: number,
    //       totalSpentSol: number,
    //       totalGotSol: number,
    //       lastTradeTimestamp: number
    //     }
    //   }
    // } 

    // Calculate usersHoldingCount and usersHoldingTokens
    let usersHoldingCount = 0;
    let usersHoldingTokens = 0;
    let devHoldingCount = 0;
    let devHoldingTokens = 0;
    for (const user in mintTrends[tradeData.mint].users) {
      const userData = mintTrends[tradeData.mint].users[user];
      if (userData.tokenBalance > 0) {
        if (userData.isDev) {
          devHoldingCount++;
          devHoldingTokens += userData.tokenBalance;
        } else {
          usersHoldingCount++;
          usersHoldingTokens += userData.tokenBalance;
        }
      }
    }
    mintTrends[tradeData.mint].usersHoldingCount = usersHoldingCount;
    mintTrends[tradeData.mint].usersHoldingTokens = usersHoldingTokens / 1e6;
    mintTrends[tradeData.mint].devHoldingCount = devHoldingCount;
    mintTrends[tradeData.mint].devHoldingTokens = devHoldingTokens / 1e6;
    const percentageHolding = ((usersHoldingTokens / 1e6) / 1e9 * 100).toFixed(2);
    const percentageDevHolding = ((devHoldingTokens / 1e6) / 1e9 * 100).toFixed(2);



    let usersTotalBuySol = 0;
    let usersTotalSellSol = 0;
    for (const trade of mintTrends[tradeData.mint].trades) {
      if (!trade.isDev) {
        if (trade.isBuy) {
          usersTotalBuySol += trade.amount;
        } else {
          usersTotalSellSol += trade.amount;
        }
      } else {
        if (trade.isBuy) {
          mintTrends[tradeData.mint].devTotalBuySol += trade.amount;
        } else {
          mintTrends[tradeData.mint].devTotalSellSol += trade.amount;
        }
      }
    }
    mintTrends[tradeData.mint].usersTotalBuySol = usersTotalBuySol;
    mintTrends[tradeData.mint].usersTotalSellSol = usersTotalSellSol;

    //dev profit
    const dev_profit = ((mintTrends[tradeData.mint].usersTotalBuySol - mintTrends[tradeData.mint].usersTotalSellSol) / 1e9).toFixed(4);

    let userTotalTrades = 0;
    let devTotalTrades = 0;
    for (const trade of mintTrends[tradeData.mint].trades) {
      if (!trade.isDev) {
        userTotalTrades++;
      } else {
        devTotalTrades++;
      }
    }

    // Clean up old trades and recalculate volumes
    const tenSecAgo = currentTime - 10 * 1000;
    const oneMinAgo = currentTime - 60 * 1000;
    const fiveMinAgo = currentTime - 5 * 60 * 1000;
    const fifteenMinAgo = currentTime - 15 * 60 * 1000;

    // Make sure trades array exists
    const trades: { amount: number; timestamp: number; user: string; isBuy: boolean, isDev: boolean }[] = mintTrends[tradeData.mint].trades || [];

    // Calculate 10-second metrics
    mintTrends[tradeData.mint].vol10sec = trades
      .filter((trade: { timestamp: number }) => trade.timestamp > tenSecAgo)
      .reduce((sum, trade) => sum + trade.amount, 0);

    mintTrends[tradeData.mint].vol10secUsers = new Set(
      trades
        .filter((trade: { timestamp: number; user: string }) => trade.timestamp > tenSecAgo)
        .map((trade) => trade.user)
    ).size;

    mintTrends[tradeData.mint].vol10secBuys = trades
      .filter((trade: { timestamp: number; isBuy: boolean }) => trade.timestamp > tenSecAgo && trade.isBuy)
      .length;
    mintTrends[tradeData.mint].vol10secSells = trades
      .filter((trade: { timestamp: number; isBuy: boolean }) => trade.timestamp > tenSecAgo && !trade.isBuy)
      .length;

    mintTrends[tradeData.mint].vol1minBuys = trades
      .filter((trade: { timestamp: number; isBuy: boolean }) => trade.timestamp > oneMinAgo && trade.isBuy)
      .length;
    mintTrends[tradeData.mint].vol1minSells = trades
      .filter((trade: { timestamp: number; isBuy: boolean }) => trade.timestamp > oneMinAgo && !trade.isBuy)
      .length;

    mintTrends[tradeData.mint].vol5minBuys = trades
      .filter((trade: { timestamp: number; isBuy: boolean }) => trade.timestamp > fiveMinAgo && trade.isBuy)
      .length;
    mintTrends[tradeData.mint].vol5minSells = trades
      .filter((trade: { timestamp: number; isBuy: boolean }) => trade.timestamp > fiveMinAgo && !trade.isBuy)
      .length;

    mintTrends[tradeData.mint].vol15minBuys = trades
      .filter((trade: { timestamp: number; isBuy: boolean }) => trade.timestamp > fifteenMinAgo && trade.isBuy)
      .length;
    mintTrends[tradeData.mint].vol15minSells = trades
      .filter((trade: { timestamp: number; isBuy: boolean }) => trade.timestamp > fifteenMinAgo && !trade.isBuy)
      .length;


    mintTrends[tradeData.mint].vol10secTrades = trades
      .filter((trade: { timestamp: number }) => trade.timestamp > tenSecAgo)
      .length;

    // Calculate 1-minute metrics
    mintTrends[tradeData.mint].vol1min = trades
      .filter((trade: { timestamp: number }) => trade.timestamp > oneMinAgo)
      .reduce((sum, trade) => sum + trade.amount, 0);

    mintTrends[tradeData.mint].vol1minUsers = new Set(
      trades
        .filter((trade: { timestamp: number; user: string }) => trade.timestamp > oneMinAgo)
        .map((trade) => trade.user)
    ).size;


    mintTrends[tradeData.mint].vol1minTrades = trades
      .filter((trade: { timestamp: number }) => trade.timestamp > oneMinAgo)
      .length;

    mintTrends[tradeData.mint].vol5min = trades
      .filter((trade: { timestamp: number }) => trade.timestamp > fiveMinAgo)
      .reduce((sum, trade) => sum + trade.amount, 0);

    mintTrends[tradeData.mint].vol5minUsers = new Set(
      trades
        .filter((trade: { timestamp: number; user: string }) => trade.timestamp > fiveMinAgo)
        .map((trade) => trade.user)
    ).size;


    mintTrends[tradeData.mint].vol5minTrades = trades
      .filter((trade: { timestamp: number }) => trade.timestamp > fiveMinAgo)
      .length;

    mintTrends[tradeData.mint].vol15min = trades
      .filter((trade: { timestamp: number }) => trade.timestamp > fifteenMinAgo)
      .reduce((sum, trade) => sum + trade.amount, 0);

    mintTrends[tradeData.mint].vol15minTrades = trades
      .filter((trade: { timestamp: number }) => trade.timestamp > fifteenMinAgo)
      .length;

    mintTrends[tradeData.mint].vol15minUsers = new Set(
      trades
        .filter((trade: { timestamp: number; user: string }) => trade.timestamp > fifteenMinAgo)
        .map((trade) => trade.user)
    ).size;


    // Update running total and current price/mcap
    mintTrends[tradeData.mint].volAll += solAmount;
    // Clean up trades older than 15 minutes
    mintTrends[tradeData.mint].trades = mintTrends[tradeData.mint].trades!.filter((trade) => trade.timestamp > fifteenMinAgo);

    const now = Math.floor(Date.now() / 1000);
    const age = now - (mintTrends[tradeData.mint].created_at ?? now);

    //if ( mintTrends[tradeData.mint].vol10secBuys > 20 && age < 120) {
    //if ( mintTrends[tradeData.mint].vol10secBuys > 20 && age < 120) {
    // if (mintTrends[tradeData.mint].maxMarketCap > 8000  && age < 120  && mintTrends[tradeData.mint].vol5minTrades > 50 &&  mintTrends[tradeData.mint].marketCap < 4800)

    // if maxMC above 5000 and minMC < 4300 and max maxMC2 > 5000
    //if ( mintTrends[tradeData.mint].vol10secTrades > 10 && age < 60  ) {


    /**
     * 
     *   tracer
     * 
     */

    // if (solAmountInSol > minSolForTracer ) {
    //   ttracker.addTradeData(tradeData.user,tradeData.mint);
    // }


    // const matches = ttracker.getResults();



    //   if (JSON.stringify(matches) !== JSON.stringify(previousMatches)) {


    //       if (matches.length > 0) {
    //         matches.forEach((match, index) => {
    //           console.log(`\nMatch #${index + 1}:`);
    //           console.log(`Tokens: ${match.users[0]} and ${match.users[1]}`);
    //           console.log(`common users tokens (${match.commonTokenCount}): ${match.commonTokens.join(', ')}`);
    //           for (const token of match.commonTokens) {
    //             dev_traders.add(token);
    //           }

    //         });

    //       }
    // }

    // if ( dev_traders.has(tradeData.user)) {

    //   addMintTrendsTrade({
    //     mint: tradeData.mint,
    //     address: tradeData.user,
    //     buyDelay: age,
    //     isBuy: tradeData.isBuy,
    //     solAmount: solAmount,
    //     tokenAmount: tokenAmount,
    //     price: price,
    //     marketCup: marketCap,
    //     bot: "bot",
    //     maxMarketCup: mintTrends[tradeData.mint].maxMarketCap
    //   });
    // }


    const tradersWallets = ["********************************************",
"22kFm8b2Vb7itdz51dNGd1tn9hkKgF82sc2jNEQkGafp",
"26kZ9rg8Y5pd4j1tdT4cbT8BQRu5uDbXkaVs3L5QasHy",
"28ipXVfkdmu1PDowCHbcSfzkpH9edZmSiVoDhY5xGVfR",
"2CXbN6nuTTb4vCrtYM89SfQHMMKGPAW4mvFe6Ht4Yo6z",
"2F19ZXrvQDCoYXdBoFBiBcEmgrAKYAheZHnQxZksHQhN",
"2HWT2KLLdN2wxYTqdSuko5SBzg2SEJASgV4GE2tD7TML",
"2JUsjGsrU9aKKWRFGxSKNV61HXbPZakM5JJYufwFaWhm",
"2kv8X2a9bxnBM8NKLc6BBTX2z13GFNRL4oRotMUJRva9",
"2QMXjBufQprAfEutzh4gMWRuMp1impcjk5BLpfYEHAsb",
"2YJbcB9G8wePrpVBcT31o8JEed6L3abgyCjt5qkJMymV",
"********************************************",
"********************************************",
"********************************************",
"********************************************",
"********************************************",
"********************************************",
"48kx1v5iNdpwmWKAcfwNKL8brRCZqATJrmrCAw8wnN29",
"4aDdi3EiDPMbeZ3e5BvbFMt4vfJaoahaHxZuwKQRtFc1",
"4Be9CvxqHW6BYiRAxW9Q3xu1ycTMWaL5z8NX4HR3ha7t",
"4DdrfiDHpmx55i4SPssxVzS9ZaKLb8qr45NKY9Er9nNh",
"4hSXPtxZgXFpo6Vxq9yqxNjcBoqWN3VoaPJWonUtupzD",
"4jRX4iW2F5wBnfYMyB7RjS2PU5MjXrST3fB9DoV4BjHa",
"4jSmEmBkuBi1JxZHM1khkf57xPR2oW1EnkRxqehq1cSK",
"4nwfXw7n98jEQn93VWY7Cuf1jnn1scHXuXCPGVYS9k6T",
"4S9U8HckRngscHWrW418cG6Suw62dhEZzmyrT2hxSye5",
"4Uq8kK9rzb1BhLKez5LgfzQLHUn2TTX1LRnyfkxFBrDo",
"4XMPyWFsYdNcCN4FG8geyytyTeUNacn4QundBzMqbGGT",
"53BnNc49Ajgstciq3CRoyxuBpkkW1r8pgPyvr7JGYnsh",
"5B52w1ZW9tuwUduueP5J7HXz5AcGfruGoX6YoAudvyxG",
"5Etkx7VQFFVMSTtTLWWZoYGn8iPWYLr1TrC3xyrY2fyf",
"5f8dDi7o8tGYkFVALB6VixhStULNDxxqNivgWEfr2z17",
"5PA7XYpE4p78fWvH1HiSD1SQiRKGUWgbWc6jxzWiDpYT",
"5PAhQiYdLBd6SVdjzBQDxUAEFyDdF5ExNPQfcscnPRj5",
"5rkPDK4JnVAumgzeV2Zu8vjggMTtHdDtrsd5o9dhGZHD",
"5sLbBRU6KBpfSVXHrLtEWJmfyLtbbjidmVeiUzjPEMe5",
"5TuiERc4X7EgZTxNmj8PHgzUAfNHZRLYHKp4DuiWevXv",
"5tzFkiKscXHK5ZXCGbXZxdw7gTjjD1mBwuoFbhUvuAi9",
"5vR5f7BtxvHCGw1EciwKuEv5EDyvnD3aavrHqRjXyevg",
"5YkZmuaLhrPjFv4vtYE2mcR6J4JEXG1EARGh8YYFo8s4",
"62FZUSWPMX9pofoV1uWHMdzFJRjwMa1LHgh2zhdEB7Zj",
"68iSVp3CiuSuGnhLnK3djK3LKweRnsLkTZsQVxL87ZbH",
"6LChaYRYtEYjLEHhzo4HdEmgNwu2aia8CM8VhR9wn6n7",
"6oRffJ7gwusToVfF1Sfbtd7Nf2TjcFFS4419ya34En5d",
"6quKVtdvEjtxwRN7DnACboG1iiGyh17QB34ZfkdxJLH1",
"6RYVUPzp1rPaDQth8LaNLaMcZg4HRe3L9LhpJDV9ZZWf",
"6ZddMpdAgo1BNncaQkZcLCCj6ZGwMpLSpiwfMHg9cVxc",
"71CPXu3TvH3iUKaY1bNkAAow24k6tjH473SsKprQBABC",
"73LnJ7G9ffBDjEBGgJDdgvLUhD5APLonKrNiHsKDCw5B",
"7ABz8qEFZTHPkovMDsmQkm64DZWN5wRtU7LEtD2ShkQ6",
"7CmXBbwsE5gcuDisSVC1syRgrVC5kYb9Lwb54M8ZadW1",
"7Dt5oUpxHWuKH8bCTXDLz2j3JyxA7jEmtzqCG6pnh96X",
"7iabBMwmSvS4CFPcjW2XYZY53bUCHzXjCFEFhxeYP4CY",
"7M3AinEXjZ7okFPh8Xw7ESEayZFZzFAKGJTf6yNATPpn",
"7N4bAyZX6z39RozQh7GC8VQmgjvjkkWmSyAt1wdMjEmq",
"7QZGS7MQ4S6hRmE8iXoFTXgQ2hXVUCho2ZhgeWvLNPZT",
"7SDs3PjT2mswKQ7Zo4FTucn9gJdtuW4jaacPA65BseHS",
"7tiRXPM4wwBMRMYzmywRAE6jveS3gDbNyxgRrEoU6RLA",
"7VBTpiiEjkwRbRGHJFUz6o5fWuhPFtAmy8JGhNqwHNnn",
"7w7tQnyfZUpdHNTbTX9MVZHhWdaNcYKZuWaSSweMJxUJ",
"831qmkeGhfL8YpcXuhrug6nHj1YdK3aXMDQUCo85Auh1",
"831yhv67QpKqLBJjbmw2xoDUeeFHGUx8RnuRj9imeoEs",
"86AEJExyjeNNgcp7GrAvCXTDicf5aGWgoERbXFiG1EdD",
"8deJ9xeUvXSJwicYptA9mHsU2rN2pDx37KWzkDkEXhU6",
"8iRuMD31AxPHHVGhDNbKHdRnuYriBMHCQbNHVfGy4PTz",
"8MaVa9kdt3NW4Q5HyNAm1X5LbR8PQRVDc1W8NMVK88D5",
"8mEWo5njoMR1CDemTQ739jDDkjvgHCcuQLL4cWojxKoT",
"8QVfDrEPy3NqgecFea7N9nhiv1ZtYur8TSx53TzNvNpb",
"8rvAsDKeAcEjEkiZMug9k8v1y8mW6gQQiMobd89Uy7qR",
"8yJFWmVTQq69p6VJxGwpzW7ii7c5J9GRAtHCNMMQPydj",
"8zFZHuSRuDpuAR7J6FzwyF3vKNx4CVW3DFHJerQhc7Zd",
"96sErVjEN7LNJ6Uvj63bdRWZxNuBngj56fnT9biHLKBf",
"98T65wcMEjoNLDTJszBHGZEX75QRe8QaANXokv4yw3Mp",
"99i9uVA7Q56bY22ajKKUfTZTgTeP5yCtVGsrG9J4pDYQ",
"9cqgXsqhksd16hLnupey9jt86jKDfbD6ZWVANccW1233",
"9FNz4MjPUmnJqTf6yEDbL1D4SsHVh7uA8zRHhR5K138r",
"9HhafAZc6jo6rXxeJ7F4RN1BQfsTm6xLRs6YYAq6nhAn",
"9jyqFiLnruggwNn4EQwBNFXwpbLM9hrA4hV59ytyAVVz",
"9N5pGaJFEsdwsRyB4XFuJPqTxmPqzUbj6SViTn4KHiTa",
"9nJ72hhtwyGHoUbEN3xfGuiLnrX3SnLYX52hkoahn2YH",
"9ru9BSVFSUqSRQFwdjjnkzQGf9yUdpRTjpjXPKc4BvxX",
"9UWZFoiCHeYRLmzmDJhdMrP7wgrTw7DMSpPiT2eHgJHe",
"9uyDy9VDBw4K7xoSkhmCAm8NAFCwu4pkF6JeHUCtVKcX",
"9wpuL27tRTSzK8pZL8euLs1rTnTgx3ixKzSv9XjdZwux",
"A77HErqtfN1hLLpvZ9pCtu66FEtM8BveoaKbbMoZ4RiR",
"AAhnidQyZbh2ej1Ubqc3DAP7oKM72akPzxtYpaZpUe8r",
"AbcX4XBm7DJ3i9p29i6sU8WLmiW4FWY5tiwB9D6UBbcE",
"AC5RDfQFmDS1deWZos921JfqscXdByf8BKHs5ACWjtW2",
"ACzzqa4zbRwgyYBd7dwrgZMFf4Fn7Wir1qA4zpeue3uU",
"AJ6MGExeK7FXmeKkKPmALjcdXVStXYokYNv9uVfDRtvo",
"AM84n1iLdxgVTAyENBcLdjXoyvjentTbu5Q6EpKV1PeG",
"AMPhmcp3uVyYShFnaJDtLAM22CvJPLxwVFWarLHf2Fw5",
"AonDkSa2EZ19U365YrY3bKLDbLf7cU1herGFTGxXYrcm",
"ApRnQN2HkbCn7W2WWiT2FEKvuKJp9LugRyAE1a9Hdz1",
"ATFRUwvyMh61w2Ab6AZxUyxsAfiiuG1RqL6iv3Vi9q2B",
"Av3xWHJ5EsoLZag6pr7LKbrGgLRTaykXomDD5kBhL9YQ",
"AVAZvHLR2PcWpDf8BXY4rVxNHYRBytycHkcB5z5QNXYm",
"B3wagQZiZU2hKa5pUCj6rrdhWsX3Q6WfTTnki9PjwzMh",
"B5ScJw8Dw7e1zXoh2ut2F81WVXmGaPWqZZ6JEYBZfAJK",
"BCnqsPEtA1TkgednYEebRpkmwFRJDCjMQcKZMMtEdArc",
"BHhRbqadwedgwerPRKQYyf3QHvQyMJ6tFjt5rybaCxjU",
"BiqkWhrJ4dfpE6TeNY34tA4GssG23SBH8CU8mqB7xZ6t",
"BkWpyDT1BGN3AxVsnmpg2TF1LCTeXhHWNhwht2pwjkki",
"BT2sdwJeDWqXNUNBJwtKWBD7RjquACfjo1rT9vpZLUSA",
"BTf4A2exGK9BCVDNzy65b9dUzXgMqB4weVkvTMFQsadd",
"BtMBMPkoNbnLF9Xn552guQq528KKXcsNBNNBre3oaQtr",
"BYhH8QatT4Aue5TxLCiYeHBcZ2ufr6KCwdQPQnedVewy",
"c3XGUoDSBaJDA8qaJ5pUkCnamMERwZLJBVjxdkNepGo",
"Cbj482ar1f5b7RvoEFJZEPCeZQ3SYEA5oxFYXmX4Wrio",
"CCUcjek5p6DLoH2YNtjizxYhAnStXAQAGVxhp1cYJF7w",
"CfyPKSXiFRLnq8cKrefgHKEFjFunwFMDZootqdbeZdfE",
"CkxVhktjqYuhsVfNQzqEZkwQ2gMU1wEFPM3FSSVXGjM9",
"CNYdrnBpRb1qgdDkEtjAH3Rv2wGjx2DJkwR29mfj16WD",
"CwodPaTCAow3H5LygvkAXTuUKyNHavRLyiTCfWnXn2Zx",
"CyaE1VxvBrahnPWkqm5VsdCvyS2QmNht2UFrKJHga54o",
"D3Z7weHeLGWA7eg1qwVB66NCs8YLxiDTBVE6Eb1tTmwg",
"D9VNWx8oZX788VRQ9H4qyvHASiQLfWbd2MP3DHKSFvK5",
"DfMxre4cKmvogbLrPigxmibVTTQDuzjdXojWzjCXXhzj",
"DHEKS3dMShJfdqoUQ5EgGZA5DdKZopMwqD2hWqK2i6gW",
"DiTQyLH8aT9D7aPMNTxwcTbRddMuY8X9qm4gmVvTBPM3",
"DjpzbYPXReZzSPPgT4DntqdD9qxcrf4euMeb1dY7nqm6",
"DKgvpfttzmJqZXdavDwTxwSVkajibjzJnN2FA99dyciK",
"DNfuF1L62WWyW3pNakVkyGGFzVVhj4Yr52jSmdTyeBHm",
"DpNVrtA3ERfKzX4F8Pi2CVykdJJjoNxyY5QgoytAwD26",
"DUQYDumJCrByjbSNtAzQsyG7iipmFQCXmF2EUeugRYMR",
"DYAn4XpAkN5mhiXkRB7dGq4Jadnx6XYgu8L5b3WGhbrt",
"E69FvNvAc1d484uZMWyKR4LDSCmsTxsxaoGnhG5QyfRc",
"ECCKBDWX3MkEcf3bULbLBb9FvrEQLsmPMFTKFpvjzqgP",
"EcJWNtETrzdbj8s2dXpaE4Tu4r7fxALD6TNw9H8S6ksz",
"EdDCRfDDeiiDXdntrP59abH4DXHFNU48zpMPYisDMjA7",
"EHg5YkU2SZBTvuT87rUsvxArGp3HLeye1fXaSDfuMyaf",
"EhQ7iJNjXFiajR5zwJmXoKo5faQc2X2JHtFx7zWJKTaV",
"EnQLCLB7NWojruXXNopgH7jhkwoHihTpuzsrtsM2UCSe",
"EVNLVqUuCdbU2ebAwmSPWRcQ73iR4W5cho6shH7fXXir",
"EXPw1tMuJFsx8vfpeHwuwxLXjMKcXj9vMEHAZNPrVbfU",
"EY5udSh8BjxKq3zqKJVtNgRWr1jWkcJtCrHCvAoBLmRW",
"F2SuErm4MviWJ2HzKXk2nuzBC6xe883CFWUDCPz6cyWm",
"F5TjPySiUJMdvqMZHnPP85Rc1vErDGV5FR5P2vdVm429",
"F72vY99ihQsYwqEDCfz7igKXA5me6vN2zqVsVUTpw6qL",
"F9TWs2Jmwpcvt8Zzr1wLnA9f4UQKj1huX74eM8vDoXTm",
"FAwHi11KyJVh2pR2b2vNFEbbunTVkMFpdNBuD2dh9NRf",
"FEDt3TPMu2KTJpzPBMmxHN3zebs1oYGJrbxuE1ycKG9s",
"Fi2hrxExy6TJnKcbPtQpo6iZzX9SUVbB9mDw6d29NgCn",
"FiwjgGxrWeq4Bjnf9nNFA1wdWZCCZyLqgureN7qpEowi",
"FKY9K9K2WjXP5i4FrAo3EZsyfqV4u9CjQAwj59QXh8Uv",
"FQh5cst4aKhCZbNxqunns4wZViD52Z2PVTNR3eSYQBhw",
"FrNpLY7k6ktsp9em8c2fThYJe5yncE9iC4ArFXWLBs4g",
"FvTBarKFhrnhL9Q55bSJnMmAdXisayUb5u96eLejhMF9",
"FWznbcNXWQuHTawe9RxvQ2LdCENssh12dsznf4RiouN5",
"G1pRtSyKuWSjTqRDcazzKBDzqEF96i1xSURpiXj3yFcc",
"G3g1CKqKWSVEVURZDNMazDBv7YAhMNTjhJBVRTiKZygk",
"G4fiaoKwBPTXH2JMekciosBUqjAQY3rQpWyGwZzPpb98",
"G5nxEXuFMfV74DSnsrSatqCW32F34XUnBeq3PfDS7w5E",
"GFJhtZuENEB9StZiacHUd1aoBoCtY2wWLskhgwcyfaYN",
"GfqQkmJZ8h8Th7epmpyJgBXBtm7JA11L8KbnPxisDeHc",
"GfXQesPe3Zuwg8JhAt6Cg8euJDTVx751enp9EQQmhzPH",
"Gitmf7ap4gYgbEVzkaiy4sLtXev4kHPu8VYdbpcXTV3w",
"GJA1HEbxGnqBhBifH9uQauzXSB53to5rhDrzmKxhSU65",
"GTvBQnRvAPweU2qmYg8MDLND2PAAyYFKe35aKQGMRDaL",
"HABhDh9zrzf8mA4SBo1yro8M6AirH2hZdLNPpuvMH6iA",
"HaZtFxgw99iM97LxmwFuDW6k4MP1XwsWTGoy7GUoSELj",
"HcPE7DGP3iRzBv9BJ4fYA5D5rLK84CHaRTn3dwukGxXe",
"HCQb4Qrtk4qCfChDH8XvM5onymmCSmRj9bddV6QCPdRe",
"HD5fsofvcEaSimExUkPQxYPec3nT8g2tP2tw8oGPrpBT",
"HiMomFaRLrcRuasmmpE9ixUrQKa4EgoP5r2HaHo73Qt1",
"HJXRy9JA6xXhFPZCNfJhenAq6MpfTpV83X4p5nhzQiaz",
"HkFt55P3PhRWHXoTFeuvkKEE4ab26xZ1bk6UmXV88Pwz",
"HLv6yCEpgjQV9PcKsvJpem8ESyULTyh9HjHn9CtqSek1",
"HrHJputHcA8mkwrcQB3GkrJ2u1f7Fm4LzAZptSYSsUcf",
"HrWt6V3kjv26bfUTzBroVAp87W62AWD3LsRfsNdgXJrc",
"HtucFepgUkMpHdrYsxMqjBNN6qVBdjmFaLZneNXopuJm",
"HyYNVYmnFmi87NsQqWzLJhUTPBKQUfgfhdbBa554nMFF",
"iWinRYGEWcaFFqWfgjh28jnqWL72XUMmUfhADpTQaRL",
"J359swDwM6DMrpTveYZt6Z9AVhZHJ3mVs1y8Gn4wKYGF",
"JDd3hy3gQn2V982mi1zqhNqUw1GfV2UL6g76STojCJPN",
"kQdJVZvix2BPCz2i46ErUPk2a74Uf37QZL5jsRdAD8y",
"LJ2X58hCR38qtvLBTEhwVAMALz7bbqR1YVvq2mnVnqq",
"m7Kaas3Kd8FHLnCioSjCoSuVDReZ6FDNBVM6HTNYuF7",
"mpa4abUkjQoAvPzREkh5Mo75hZhPFQ2FSH6w7dWKuQ5",
"na9E1w4pXVEcVMtqxkQHDs93TayXggt1yrxZbE2epfZ",
"niggerd597QYedtvjQDVHZTCCGyJrwHNm2i49dkm5zS",
"orcACRJYTFjTeo2pV8TfYRTpmqfoYgbVi9GeANXTCc8",
"RFSqPtn1JfavGiUD4HJsZyYXvZsycxf31hnYfbyG6iB",
"RH6epK9UW5vA5rm23FcLi4rPuTJjwya6UQJeG1tigv3",
"suqh5sHtr8HyJ7q8scBimULPkPpA557prMG47xCHQfK",
"vQ33AcEii7mciXznW7TAqzpv18Z77PQHxSfJ7xNBHwU",
"VyGNiGZ3PS3kHmnk5h3fkrcMBjLKFgX5acsnS4qaYKy",
"XDgpjPubjN5soLAFmAMjD2uVR1XZxUKgNpMU6Eo97H7",]


    //  previousMatches = matches;

    //if (mintTrends[tradeData.mint].maxMarketCap > 6000  && age < 120   &&  mintTrends[tradeData.mint].minMarketCap < 4800 && marketCap > 5000 && !mintTrends[tradeData.mint].customStatus) {
    //&& mintTrends[tradeData.mint].vol15minTrades > 100

    //if ( mintTrends[tradeData.mint].vol15minUsers > 30 &&  marketCap > 20000  && marketCap < 1500000 && age < 900  && !mintTrends[tradeData.mint].customStatus) {


      const traderWalletCount = Object.keys(mintTrends[tradeData.mint].users).filter(user =>
        tradersWallets.includes(user)
      ).length;
      

      
    if (traderWalletCount> 2 && traderWalletCount > mintTrends[tradeData.mint].customCounter ) {
      console.log(`https://mevx.io/solana/${tradeData.mint} v: ${(mintTrends[tradeData.mint].volAll / 1e9 * 150).toFixed(0)} age: ${age} t(u/d):${userTotalTrades}/${devTotalTrades} ${marketCap.toFixed(0)}   uH%: ${mintTrends[tradeData.mint].usersHoldingCount} u/dH%: ${percentageHolding}/${percentageDevHolding}% d$: ${dev_profit}`);
      sendToDiscord(PUMPFUN_DISCORD_WEBHOOK, `https://mevx.io/solana/${tradeData.mint} selected usercount: ${traderWalletCount} v: ${(mintTrends[tradeData.mint].volAll / 1e9 * 150).toFixed(0)} age: ${age} t(u/d):${userTotalTrades}/${devTotalTrades} ${marketCap.toFixed(0)}   uH%: ${mintTrends[tradeData.mint].usersHoldingCount} u/dH%: ${percentageHolding}/${percentageDevHolding}% d$: ${dev_profit}`);
    }
    mintTrends[tradeData.mint].customCounter=traderWalletCount;

    // if (traderWalletCount> 2 && traderWalletCount > mintTrends[tradeData.mint].customCounter && !mintTrends[tradeData.mint].customStatus ) {
    //   mintTrends[tradeData.mint].customStatus = true;
    //   sendPushoverMessage(`RUGGER ALERT`);
    // }


  //   if ( userTotalTrades > 10 &&  marketCap > 20000  && marketCap < 1500000 && age < 900  && !mintTrends[tradeData.mint].customStatus) {
  //  // if (user == "CkUZV387xnoGpF7wC2moMa6mPmAgCvTT4pWgzq4M9fCD" && !mintTrends[tradeData.mint].customStatus) {
  //     mintTrends[tradeData.mint].customStatus = true;
  //     dataBuffer.set(`msg-${Date.now()}`, `mint:${tradeData.mint}`);
  //     await addMintTrend(tradeData.mint, mintTrends[tradeData.mint]);
  //     console.log(`https://mevx.io/solana/${tradeData.mint} v: ${(mintTrends[tradeData.mint].volAll / 1e9 * 150).toFixed(0)} age: ${age} t(u/d):${userTotalTrades}/${devTotalTrades} ${marketCap.toFixed(0)}   uH%: ${mintTrends[tradeData.mint].usersHoldingCount} u/dH%: ${percentageHolding}/${percentageDevHolding}% d$: ${dev_profit}`);


  //    }




  } catch (error) {
    console.error('Error processing trade event:', error);
  }
}

// Setup graceful shutdown to close connections
process.on('SIGINT', async () => {
  console.log('Received SIGINT. Closing connections...');
  try {
    // Close Redis connection
    await redis.quit();
    console.log('Redis connection closed.');

    // Close database pool
    await dbPool.end();
    console.log('Database pool closed.');

    process.exit(0);
  } catch (error) {
    console.error('Error during shutdown:', error);
    process.exit(1);
  }
});

process.on('SIGTERM', async () => {
  console.log('Received SIGTERM. Closing connections...');
  try {
    // Close Redis connection
    await redis.quit();
    console.log('Redis connection closed.');

    // Close database pool
    await dbPool.end();
    console.log('Database pool closed.');

    process.exit(0);
  } catch (error) {
    console.error('Error during shutdown:', error);
    process.exit(1);
  }
});

main().catch((err) => {
  console.error("Unhandled error in main:", err);
  process.exit(1);
});

export { getLatestBlockhash };

// Add reconnection helper
async function reconnectStream(): Promise<ClientDuplexStream<SubscribeRequest, SubscribeUpdate>> {
  try {
    const client = new Client(ENDPOINT, undefined, {});
    const stream = await client.subscribe();
    const request = createSubscribeRequest();
    await sendSubscribeRequest(stream, request);
    console.log("Successfully created new gRPC stream connection");
    return stream;
  } catch (error) {
    console.error("Error creating new stream:", error);
    throw error;
  }
}

// export async function sellTokensJito(tokenCA: string, amount: string, latestBlockHash: string, wallet: Uint8Array,devPubKey: string) {
//   tradeMeasure = Date.now();
//   const jitoTipAccount = new PublicKey('DttWaMuVvTiduZRnguLF7jNxTgiMBZ1hyAumKUiL2KRL');
//   const jitoEndpoint = 'https://mainnet.block-engine.jito.wtf/api/v1/transactions';

//   const privateKey = process.env.PRIVATE_KEY ? bs58.decode(process.env.PRIVATE_KEY) : new Uint8Array();
//   const signer = Keypair.fromSecretKey(privateKey);

//   const tokenBalance = parseFloat(amount);
//   const tipLamports = 1_000_000;

//   const transaction = new Transaction();
//   const sellInstruction = await getSellInstructionNoRPC(bs58.encode(privateKey), tokenCA, tokenBalance,signer,devPubKey);

//   transaction.add(
//     SystemProgram.transfer({
//       fromPubkey: signer.publicKey,
//       toPubkey: jitoTipAccount,
//       lamports: tipLamports,
//     })
//   );

//   if (!sellInstruction) {
//     console.log('Request preparation: Failed to get sell instruction');
//     return;
//   }

//   transaction.add(sellInstruction);
//   transaction.feePayer = signer.publicKey;
//   transaction.recentBlockhash = latestBlockHash;
//   transaction.sign(signer);

//   const serializedTransaction = bs58.encode(transaction.serialize());
//   console.log(`Request preparation: Transaction serialized successfully (${serializedTransaction.length} bytes)`);

//   try {
//     // The connection is already warmed up here, should be much faster
//     console.log(`Sending request to ${jitoEndpoint}...`);
//     const startTime = Date.now();

//     const response = await fetch(jitoEndpoint, {
//       method: 'POST',
//       body: JSON.stringify({
//         jsonrpc: '2.0',
//         id: 1,
//         method: 'sendTransaction',
//         params: [serializedTransaction]
//       }),
//       headers: { 'Content-Type': 'application/json' },
//       agent: httpsAgent,
//     });

//     const responseTime = Date.now() - startTime;
//     const responseText = await response.text();

//     console.log(`Response received in ${responseTime}ms`);
//     console.log(`Response status: ${response.status}`);
//     console.log(`Response body: ${responseText}`);

//     return response.status === 200 ? JSON.parse(responseText).result : response;
//   } catch (error) {
//     console.log('Request failed. Network or server error.');
//     return error;
//   }
// }

// export async function buyTokensJito(tokenCA: string, amount: string, latestBlockHash: string, virtual_sol_reserves: number, virtual_token_reserves: number) {
//   const jitoTipAccount = new PublicKey('DttWaMuVvTiduZRnguLF7jNxTgiMBZ1hyAumKUiL2KRL');
//   const jitoEndpoint = 'https://mainnet.block-engine.jito.wtf/api/v1/transactions';
//   //const jitoEndpoint = RPC_URL_CS; 
//   const privateKey = process.env.PRIVATE_KEY ? bs58.decode(process.env.PRIVATE_KEY) : new Uint8Array();
//   const signer = Keypair.fromSecretKey(privateKey);

//   const solAmount = parseFloat(amount);
//   const tipLamports = 1_000;

//   const transaction = new Transaction();
//   const buyInstruction = await getBuyInstructionNoRPC(bs58.encode(privateKey), tokenCA, solAmount, virtual_sol_reserves, virtual_token_reserves,signer);

//   const priorityFee = ComputeBudgetProgram.setComputeUnitPrice({
//     microLamports: 500_000 // Adjust this value based on network conditions
//   });
//   transaction.add(priorityFee);

//   transaction.add(
//     SystemProgram.transfer({
//       fromPubkey: signer.publicKey,
//       toPubkey: jitoTipAccount,
//       lamports: tipLamports,
//     })
//   );


//   if (!buyInstruction) {
//     console.log('Request preparation: Failed to get sell instruction');
//     return;
//   }

//   transaction.add(buyInstruction);
//   transaction.feePayer = signer.publicKey;
//   transaction.recentBlockhash = latestBlockHash;
//   console.log(transaction);
//   transaction.sign(signer);



//   const serializedTransaction = bs58.encode(transaction.serialize());
//   console.log(`Request preparation: Transaction serialized successfully (${serializedTransaction.length} bytes)`);

//   try {
//     // The connection is already warmed up here, should be much faster
//     console.log(`Sending request to ${jitoEndpoint}...`);
//     const startTime = Date.now();

//     const response = await fetch(jitoEndpoint, {
//       method: 'POST',
//       body: JSON.stringify({
//         jsonrpc: '2.0',
//         id: 1,
//         method: 'sendTransaction',
//         params: [serializedTransaction]
//       }),
//       headers: { 'Content-Type': 'application/json' },
//       agent: httpsAgent,
//     });

//     const responseTime = Date.now() - startTime;
//     const responseText = await response.text();

//     console.log(`Response received in ${responseTime}ms`);
//     console.log(`Response status: ${response.status}`);
//     console.log(`Response body: ${responseText}`);

//     return response.status === 200 ? JSON.parse(responseText).result : response;
//   } catch (error) {
//     console.log('Request failed. Network or server error.');
//     return error;
//   }
// }
