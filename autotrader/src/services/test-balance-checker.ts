import { Connection, PublicKey } from '@solana/web3.js';
import dotenv from 'dotenv';
import { getAllDevWallets, updateDevWalletBalance } from '../common/db';

dotenv.config();

const ALCHEMY_RPC_URL = "https://solana-mainnet.g.alchemy.com/v2/********************************";
const connection = new Connection(ALCHEMY_RPC_URL, 'confirmed');

type DevWallet = {
  address: string;
  dev_entity_id: number;
  balance: number;
};

async function testBalanceChecker() {
  console.log('🧪 Testing Balance Checker...');
  
  try {
    // Get a few dev wallets for testing
    const allWallets = await getAllDevWallets() as DevWallet[];
    console.log(`📋 Found ${allWallets.length} total dev wallets in database`);
    
    if (allWallets.length === 0) {
      console.log('❌ No dev wallets found in database. Cannot run test.');
      return;
    }
    
    // Test with first 5 wallets
    const testWallets = allWallets.slice(0, 5);
    console.log(`🔬 Testing with first ${testWallets.length} wallets:`);
    
    for (const wallet of testWallets) {
      console.log(`  - Entity ${wallet.dev_entity_id}: ${wallet.address}`);
    }
    
    console.log('\n🔄 Checking balances...');
    
    // Check balances
    const publicKeys = testWallets.map(wallet => new PublicKey(wallet.address));
    const accounts = await connection.getMultipleParsedAccounts(publicKeys);
    
    console.log('\n📊 Balance Results:');
    for (let i = 0; i < testWallets.length; i++) {
      const wallet = testWallets[i];
      const account = accounts.value[i];
      const balance = account === null ? 0 : account.lamports;
      const balanceInSol = balance / 1_000_000_000;
      const previousBalance = wallet.balance || 0;

      console.log(`  ${i + 1}. Entity ${wallet.dev_entity_id}: ${wallet.address}`);
      console.log(`     Current Balance: ${balanceInSol.toFixed(3)} SOL (${balance} lamports)`);
      console.log(`     Previous Balance: ${previousBalance.toFixed(3)} SOL`);

      const balanceChanged = Math.abs(balanceInSol - previousBalance) > 0.001;
      console.log(`     Balance Changed: ${balanceChanged ? 'YES' : 'NO'}`);

      if (balanceChanged) {
        const change = balanceInSol - previousBalance;
        const changeStr = change > 0 ? `+${change.toFixed(3)}` : change.toFixed(3);
        console.log(`     Change: ${changeStr} SOL`);
      }

      try {
        // Test database update
        await updateDevWalletBalance(wallet.address, balanceInSol);
        console.log(`     ✅ Database updated successfully`);
      } catch (error) {
        console.log(`     ❌ Database update failed: ${error}`);
      }

      console.log('');
    }
    
    console.log('✅ Balance checker test completed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
testBalanceChecker().then(() => {
  console.log('🏁 Test finished');
  process.exit(0);
}).catch((error) => {
  console.error('💥 Test error:', error);
  process.exit(1);
});
