import { getAccountAuto2 } from "../common/db"; 
import { Connection, PublicKey } from '@solana/web3.js';
import dotenv from 'dotenv';
import { RPC_URL } from '../common/config';
import { addAppAccountToDB } from '../common/db';
dotenv.config();

/**
 *     Logic to follow account
 *  x  1. Get account from db
 *  x  2. Check account balance
 *  x  3 If balance is less than 0.1 
 *  x  4 get allsignatures from account 
 *  x  5 process all signatures. gettrasactions 
 *  x   6 check all out going transactions check last 10 send out more than 10 sol 
 *  x  7 extract transaction destination account 
 *  x  8 add account in database accounts as app=auto2
 */



async function getDestinationAccount(accountAddress: string) {
    const connection = new Connection(RPC_URL);
    const limit = 100;
    const publicKey = new PublicKey(accountAddress);
    const before =  undefined;
    const signatures = await connection.getSignaturesForAddress(publicKey, { before, limit: limit });
    for (const signature of signatures) {
     
        const transaction = await connection.getTransaction(signature.signature, {
            maxSupportedTransactionVersion: 0,
          });
          if (transaction) {
            // Skip transactions that are in an error state
            if (transaction.meta && transaction.meta.err) {
              continue;
            }
            const accountKeys = transaction.transaction.message.staticAccountKeys.map(key => key.toBase58());
            const keys = accountKeys.map(key => key);
            if ( transaction.meta?.postBalances &&  transaction.meta?.postBalances.length >0 && transaction.meta?.postBalances[0] == 0 ){
               console.log('Transaction is 0 sol:', [keys[1],transaction.meta?.postBalances[1]]);
               await addAppAccountToDB(keys[1],transaction.meta?.postBalances[1],'auto2',signature.signature);
               //return [keys[1],transaction.meta?.postBalances[1]];
            } 
            return;

          }

    }

    return null;
    
}



async function getAccountBalance(account: string) {
    const connection = new Connection(RPC_URL);
    const publicKey = new PublicKey(account);
    const accountInfo = await connection.getAccountInfo(publicKey);
    try {

        if (accountInfo) {
            const balance = accountInfo.lamports;
            return balance;
        } else {
            return 0;
        }
    }  
    catch (error) {
        console.error('Error occurred:', error);    
        return 0;
    }
}

 
// main entry point
async function followAccount() {
  const account = await getAccountAuto2();
  const balance = await getAccountBalance(account);
    console.log(account,balance);
    if (balance == 0) {
        console.log('Balance is  0 sol');
        await getDestinationAccount(account);
    }else {
        console.log('Balance is not 0 sol:', balance);
    }
}

(async () => {


    setInterval(async () => {
        try {
            await followAccount();
        } catch (error) {
            console.error('Error occurred:', error);
        }
    }, 10000);



})();