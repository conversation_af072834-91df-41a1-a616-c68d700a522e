import WebSocket from 'ws';
import amqp from 'amqplib/callback_api';
import dotenv from 'dotenv';
import { updateWebsocket } from '../common/db';
import { sendRabbitMessage } from '../common/rabbitmq';

dotenv.config();

// Configuration
const MAX_CONNECTIONS = 10;
const RABBITMQ_SERVER = process.env.RABBITMQ_HOSTNAME || 'kroocoin.xyz';
const RABBITMQ_QUEUE = 'accounts';
const RABBITMQ_PORT = 5672;
const RABBITMQ_USERNAME =  'pump';
const RABBITMQ_PASSWORD =  'pump2pump';

// Interfaces
interface SolanaRPCRequest {
  jsonrpc: string;
  id: number;
  method: string;
  params: any[];
}

interface SolanaRPCResponse {
  jsonrpc: string;
  id: number;
  result?: any;
  method?: string;
  error?: {
    code: number;
    message: string;
  };
  params?: any;
}

interface ConnectionCommand {
  address: string;
  command: 'connect' | 'disconnect';
  timestamp?: number;
}

class SolanaLogsSubscriber {
  private ws: WebSocket;
  private subscriptionId: number | null = null;
  private requestId = 1;
  private active = false;
  private forcedClose = false;

  constructor(
    private wssUrl: string, 
    private accountAddress: string,
    private onMessage: (data: any) => void
  ) {
    this.ws = new WebSocket(wssUrl);
    this.setupEventListeners();
  }

  private setupEventListeners() {
    this.ws.on('open', () => {
      console.log(`WebSocket connection established for ${this.accountAddress}`);
      //updateWebsocket(this.accountAddress, 1);
      this.active = true;
      this.subscribeToLogs();
    });

    this.ws.on('message', (data: WebSocket.Data) => {
      try {
        const response = JSON.parse(data.toString()) as SolanaRPCResponse;
        
        // Handle subscription response (contains subscription ID)
        if (response.id && response.result !== undefined) {
          console.log(`Subscription confirmed with ID: ${response.result}`);
          this.subscriptionId = response.result;
        } 
        // Handle logs notification data
        else if (response.method === 'logsNotification') {
          this.handleLogsUpdate(response);
          this.onMessage(response);
        }
      } catch (error) {
        console.error(`Error parsing WebSocket message for ${this.accountAddress}:`, error);
      }
    });

    this.ws.on('error', (error) => {
      console.error(`WebSocket error for ${this.accountAddress}:`, error);
    });

    this.ws.on('close', (code, reason) => {
      console.log(`WebSocket connection closed for ${this.accountAddress}: ${code} - ${reason.toString()}`);
      this.subscriptionId = null;
      this.active = false;
      //updateWebsocket(this.accountAddress, 0);
      if (!this.forcedClose) {
        console.log(`Reconnecting WebSocket for ${this.accountAddress}`);
        this.ws = new WebSocket(this.wssUrl);
        this.setupEventListeners();
        //updateWebsocket(this.accountAddress, 0);
      }
    });
  }

  private subscribeToLogs() {
    const request: SolanaRPCRequest = {
      jsonrpc: '2.0',
      id: this.requestId++,
      method: 'logsSubscribe',
      params: [
        {
          mentions: [this.accountAddress]
        },
        {
          commitment: 'confirmed'
        }
      ]
    };

    console.log(`Subscribing to logs for account: ${this.accountAddress}`);
    this.ws.send(JSON.stringify(request));
  }

  private handleLogsUpdate(response: SolanaRPCResponse) {
    if (!response.params) return;
    /**
     * 
     * 
     *   LOGIC
     * 
     * 
     */
    const result = response.params.result;
    if (result.value?.err == null ) {
      console.log(`= ${this.accountAddress} === ${result.value?.signature}`);
      //sendRabbitMessage(JSON.stringify({ "mint": this.accountAddress, "signature": result.value?.signature, "timestamp": Math.floor(Date.now() / 1000) }), 'tx');
      
    }


  }

  public unsubscribe() {
    if (this.subscriptionId !== null) {
      const request: SolanaRPCRequest = {
        jsonrpc: '2.0',
        id: this.requestId++,
        method: 'logsUnsubscribe',
        params: [this.subscriptionId]
      };

      this.ws.send(JSON.stringify(request));
      console.log(`Unsubscribing from logs for account: ${this.accountAddress}`);
      //updateWebsocket(this.accountAddress, 0);
    }
  }

  public close() {
    this.forcedClose = true;
    if (this.active) {
      if (this.subscriptionId !== null) {
        this.unsubscribe();
      }
      this.ws.close();
      this.active = false;
      console.log(`Connection closed gracefully for ${this.accountAddress}`);
    }
  }

  public isActive(): boolean {
    return this.active;
  }

  public getAccountAddress(): string {
    return this.accountAddress;
  }
}

class SolanaConnectionManager {
  private connections: Map<string, SolanaLogsSubscriber> = new Map();
  private channel: amqp.Channel | null = null;

  constructor() {}

  public initialize(): void {
    amqp.connect({
      protocol: 'amqp',
      hostname: RABBITMQ_SERVER,
      port: RABBITMQ_PORT,
      username: RABBITMQ_USERNAME,
      password: RABBITMQ_PASSWORD,
    }, (error0, connection) => {
      if (error0) {
        throw error0;
      }

      connection.createChannel((error1, channel) => {
        if (error1) {
          throw error1;
        }

        this.channel = channel;
        channel.assertQueue(RABBITMQ_QUEUE, {
          durable: true
        });

        console.log(` [*] Waiting for messages in ${RABBITMQ_QUEUE}. To exit press CTRL+C`);

        channel.consume(RABBITMQ_QUEUE, (msg) => {
          if (msg !== null) {
            try {
              const command = JSON.parse(msg.content.toString());
              this.processCommand(command);
              channel.ack(msg);
            } catch (error) {
              console.error('Error processing message:', error);
              channel.nack(msg);
            }
          }
        }, {
          noAck: false
        });
      });

      // Handle graceful shutdown
      process.on('SIGINT', () => {
        console.log('Shutting down Solana Connection Manager...');
        this.shutdown();
        process.exit(0);
      });
    });
  }

  private processCommand(command: ConnectionCommand): void {
    console.log(`Processing command: ${command.command} for address: ${command.address}`);
    
    const timestamp = new Date().getTime();
    if (command.timestamp && timestamp - command.timestamp * 1000 > 60000) {
      console.log(` [x] Skipping command due to time difference: ${timestamp - command.timestamp * 1000}ms`);
      return;
    }

    switch (command.command) {
      case 'connect':
        this.createConnection(command.address);
        break;
      case 'disconnect':
        this.closeConnection(command.address);
        break;
    }
  }

  public shutdown(): void {
    // Close all WebSocket connections
    for (const connection of this.connections.values()) {
      connection.close();
    }
    this.connections.clear();
    

    
    console.log('All connections closed, shutdown complete');
  }

  private createConnection(address: string): void {
    // Check if connection already exists
    if (this.connections.has(address)) {
      console.log(`Connection for address ${address} already exists`);
      return;
    }
    
    // Check if we're at the maximum connection limit
    if (this.connections.size >= MAX_CONNECTIONS) {
      console.log(`Cannot create new connection: maximum limit of ${MAX_CONNECTIONS} reached`);
      return;
    }
    
    // Create new connection
    const subscriber = new SolanaLogsSubscriber(
      'wss://solana-mainnet.core.chainstack.com/59d8fa6af4368b9e6e8f8a68f06517c3',
      address,
      this.handleMessage.bind(this)
    );
    
    this.connections.set(address, subscriber);
    console.log(`Created new connection for address: ${address}, total connections: ${this.connections.size}`);
  }

  private closeConnection(address: string): void {
    const connection = this.connections.get(address);
    if (connection) {
      connection.close();
      this.connections.delete(address);
      console.log(`Closed connection for address: ${address}, remaining connections: ${this.connections.size}`);
    } else {
      console.log(`Connection not found for address: ${address}`);
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  private handleMessage(data: any): void {
    //console.log(`Received message:`, data);
    // Process incoming WebSocket message
    // For now, we just log it, but you could publish to another RabbitMQ queue
    //console.log(`Received message:`, data);
    
    // Example of how you could publish results to another queue
    // if (this.rabbitmqChannel) {
    //   const resultQueue = 'solana_wss_results';
    //   this.rabbitmqChannel.assertQueue(resultQueue, { durable: true });
    //   this.rabbitmqChannel.sendToQueue(resultQueue, Buffer.from(JSON.stringify({
    //     timestamp: new Date().toISOString(),
    //     data
    //   })));
    // }
  }
}

// Usage example
async function main() {
  const manager = new SolanaConnectionManager();
  
  try {
    manager.initialize();
    console.log('Solana Connection Manager started');
    console.log(`Max connections: ${MAX_CONNECTIONS}`);
    console.log(`Listening for commands on queue: ${RABBITMQ_QUEUE}`);
    
    
  } catch (error) {
    console.error('Failed to start Solana Connection Manager:', error);
    process.exit(1);
  }
}

// Start the application
main();


//{"address":"C68ihvpfBjxts7iEkstnLrE4eLVo2xwigfEKRc2UcAAF","command":"disconnect"}
//{"address":"C68ihvpfBjxts7iEkstnLrE4eLVo2xwigfEKRc2UcAAF","command":"disconnect"}