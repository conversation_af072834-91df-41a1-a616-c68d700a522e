
import { tradesBuffer,createTokenBuffer } from "../wamper.ts.old";



async function createToken(token: string) {
    console.log(`Creating token: ${token}`);

}


export  async function mainLoop(): Promise<void> {
    while (true) {
        await new Promise(resolve => setTimeout(resolve, 1));
  
        // check to create mint
        if (createTokenBuffer.size > 0) {
            for (const [key, value] of tradesBuffer.entries()) {
                createToken(value);
                createTokenBuffer.delete(key);  // Remove after processing
            }
        }

        if (tradesBuffer.size > 0) {
            console.log(`Main Loop: Processing ${tradesBuffer.size} messages`);
            for (const [key, value] of tradesBuffer.entries()) {
                console.log(Date.now());
                console.log(`[Main Loop] Processing Message: ${key} -> ${value}`);
                tradesBuffer.delete(key);  // Remove after processing
            }
        } 
    }
  }

 