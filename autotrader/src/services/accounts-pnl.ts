/* eslint-disable @typescript-eslint/no-unused-vars */
import Client, {
  CommitmentLevel,
  SubscribeRequest,
  SubscribeUpdate,
  SubscribeUpdateTransaction,
} from "@triton-one/yellowstone-grpc";
//import { sendToDiscord } from "../common/discord";
import dotenv from "dotenv";
import { ClientDuplexStream } from "@grpc/grpc-js";
import { PublicKey } from "@solana/web3.js";
import bs58 from "bs58";
import { getRedisString, getRedisClient } from "../common/redis";
import { redisAddress } from "../common/types";
import { getAppStatus, saveTransactionsToDatabase, updatMintTradeDataBuy, updatMintTradeDataSell, getAppConfigAll, addMintTrend } from "../common/db";
import { getDbPool } from "../common/db-pool";
import { RPC_URL_CS, RPC_URL_SYNDICA } from "../common/config";
import { JITO_MAINNET_TIP_ACCOUNTS } from "../common/config";
import { BorshCoder } from "@coral-xyz/anchor";
import { ruleIsSignedByAuto1Owner } from "../common/pump-rules";
// eslint-disable-next-line @typescript-eslint/no-require-imports
const idl = require("../common/idl-new.json");
const coder = new BorshCoder(idl as any);
import { Connection, clusterApiUrl } from "@solana/web3.js";
import fs from "fs";
import https from "https";
import fetch from "node-fetch";
import { httpsAgent } from '../common/agent';
import { startSessionWarmup } from './mints-grpc-connwarmer';
import { Keypair, Transaction, SystemProgram, ComputeBudgetProgram } from '@solana/web3.js';
import { getSellInstructionNoRPC, getBuyInstructionNoRPC } from "../common/trade";
import { TRADERS_PROGRAMS } from "../common/config";
import {   addTradeData,
  getResults,
  TokenTracker } from "../common/tokenTraderMatcher";
import { addMintTrendsTrade,getAddressesFromMintTrendsTrades } from "../common/db";
import { pnlTracker, PnlTracker } from "../common/pnl";
dotenv.config();

const ttracker = new TokenTracker(5);
const minSolForTracer=1; // Match when users have at least 2 tokens in common

let lastCheckTime = Date.now();
const traderAccounts = new Set();
let tradeMeasure = 0.0;
let appConfigs: ConfigByApp;
let latestBlockhash: string | null = null;
let lastBlockhashTime: number = 0;
const connection = new Connection(RPC_URL_SYNDICA, 'processed');
const mintTracker: { [key: string]: MintStats } = {};
const mintTrends: { 
  [key: string]: { 
    owner: string,
    symbol: string,
    created_at?: number, // Optional property added
    vol10sec: number,
    vol10secTrades: number,
    vol10secUsers: number,
    vol10secBuys: number,
    vol10secSells: number,
    vol1min: number, 
    vol1minTrades: number,
    vol1minUsers: number,
    vol1minBuys: number,
    vol1minSells: number,
    vol5min: number, 
    vol5minTrades: number,
    vol5minUsers: number,
    vol5minBuys: number,
    vol5minSells: number,
    vol15min: number,
    vol15minTrades: number,
    vol15minUsers: number,
    vol15minBuys: number,
    vol15minSells: number,
    volAll: number, 
    price: number, 
    marketCap: number, 
    maxMarketCap: number,
    minMarketCap: number,
    usersTotalBuySol: number,
    usersTotalSellSol: number,
    usersHoldingCount: number,
    usersHoldingTokens: number,
    devTotalBuySol: number,
    devTotalSellSol: number,
    devHoldingCount: number,
    devHoldingTokens: number,
    customStatus: boolean,
    trades: { 
      amount: number, 
      tokenAmount: number, 
      timestamp: number,
      user: string,
      isBuy: boolean 
      isDev: boolean
    }[],
    users: { 
      [userAddress: string]: {
        tokenBalance: number,
        totalSpentSol: number,
        totalGotSol: number,
        lastTradeTimestamp: number
        isDev: boolean
      }
    }
  } 
} = {};

const dev_traders = new Set<string>();

const ENDPOINT = "http://grpc.solanavibestation.com:10000";
const PUMP_FUN_PROGRAM_ID = "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P";
// Use the shared Redis client instead of creating a new connection
const redis = getRedisClient();
const PUMP_FUN_CREATE_IX_DISCRIMINATOR = Buffer.from([24, 30, 200, 40, 5, 28, 7, 119,]);
const TRADING_ACCOUNT = "68s9x6ng4T5jeKe2PSZMRNESm2LTLxiDSAU5UkJPg1je";
const COMMITMENT = CommitmentLevel.PROCESSED;
const FILTER_CONFIG = {
  programIds: [PUMP_FUN_PROGRAM_ID, TRADING_ACCOUNT],
  instructionDiscriminators: [PUMP_FUN_CREATE_IX_DISCRIMINATOR],
};

const devAccounts = new Set();
const jitoEndpoint = 'https://mainnet.block-engine.jito.wtf/api/v1/transactions';

const ACCOUNTS_TO_INCLUDE = [
  {
    name: "mint",
    index: 0,
  },
];

const agent = new https.Agent({
  keepAlive: true,
  keepAliveMsecs: 60000 // Set keepAlive timeout to 1 minute (60000 ms)
});


interface FormattedTransactionData {
  signature: string;
  slot: string;
  [accountName: string]: string;
}

type MintStats = {
  // Creation data
  created_at: number;  // Unix timestamp
  name: string;
  symbol: string;
  owner: string;
  signature: string;

  // Trading stats
  sum_buys: number;
  sum_sells: number;
  dev_profit: number;
  tokenAmount?: number;  // Added tokenAmount property
  solAmount?: number;  // Added solAmount property
  buyPrice?: number;  // Added buyPrice property

  // Market data
  app: string;  // Will be updated during trades
  mcap_current: number;
  mcap_max: number;
};

type RedisAddress = {
  addressType: string;
  address: string;
  app: string;
};

// Use the shared database pool instead of creating a new one
const dbPool = getDbPool();

interface CompiledInstruction {
  programIdIndex: number;
  accounts: Uint8Array;
  data: Uint8Array;
}

interface Message {
  header: MessageHeader | undefined;
  accountKeys: Uint8Array[];
  recentBlockhash: Uint8Array;
  instructions: CompiledInstruction[];
  versioned: boolean;
  addressTableLookups: MessageAddressTableLookup[];
}

interface MessageHeader {
  numRequiredSignatures: number;
  numReadonlySignedAccounts: number;
  numReadonlyUnsignedAccounts: number;
}

interface MessageAddressTableLookup {
  accountKey: Uint8Array;
  writableIndexes: Uint8Array;
  readonlyIndexes: Uint8Array;
}
type TradeEvent = {
  mint: string;
  solAmount: number;
  tokenAmount: number;
  isBuy: boolean;
  user: string;
  timestamp: number;
  virtualSolReserves: number;
  virtualTokenReserves: number;
  realSolReserves: number;
  realTokenReserves: number;
  signature?: string;
  signer?: string;
  block_id?: number;
  app?: string;
};

type AutoConfig = {
  enabled: number;
  app: string;
  is_simulation: number;
  buy_sol: number;
  max_time_sec: number;
  min_time_sec: number;
  max_profit_sol: number;
  min_profit_sol: number;
  max_user_buy_sol: number;
  max_mc_pct: number;
};

type ConfigByApp = {
  [key: string]: Omit<AutoConfig, 'app'>;
};

function convertConfigs(configs: AutoConfig[]): ConfigByApp {
  const result: ConfigByApp = {};

  configs.forEach(config => {
    const { app, ...rest } = config;
    result[app] = rest;
  });

  return result;
}




async function updateLatestBlockhash(): Promise<void> {
  try {
    const { blockhash, lastValidBlockHeight } = await connection.getLatestBlockhash();
    latestBlockhash = blockhash;
    lastBlockhashTime = Date.now();
    //console.log(`Updated blockhash: ${blockhash}, height: ${lastValidBlockHeight}`);
  } catch (error) {
    console.error('Failed to fetch latest blockhash:', error);
  }
}

function startBlockhashUpdateProcess(): void {
  // Initial update
  updateLatestBlockhash();

  // Update every 2 seconds
  setInterval(updateLatestBlockhash, 2000);
}

function getLatestBlockhash(): string {
  if (!latestBlockhash) {
    return "test";
  }
  return latestBlockhash;
}

function eventDecode(data: string): any {
  try {
    const instruction = coder.events.decode(
      Buffer.from(bs58.decode(data)).slice(8).toString("base64")
    );
    return instruction;
  } catch (error) {
    console.error("Event decode error:", error);
    // Return null instead of throwing
    return null;
  }
}

async function main(): Promise<void> {

  const dev_accounts =  await getAddressesFromMintTrendsTrades();
  for ( const account of dev_accounts) {
    dev_traders.add(account);
  }



  //startBlockhashUpdateProcess();
  startSessionWarmup();
  
  appConfigs = convertConfigs(await getAppConfigAll());
  console.log("App Configs:", appConfigs);

  while (true) {
    try {
      const stream = await reconnectStream();
      await handleStreamEvents(stream);
    } catch (error) {
      console.error("Main loop error:", error);
      console.log("Retrying in 5 seconds...");
      await new Promise(resolve => setTimeout(resolve, 5000));
    }
  }
}

function createSubscribeRequest(): SubscribeRequest {
  return {
    accounts: {},
    slots: {},
    transactions: {
      pumpFun: {
        accountInclude: FILTER_CONFIG.programIds,
        accountExclude: [],
        accountRequired: [],// Add this to request metadata
      },
    },
    transactionsStatus: {},
    entry: {},
    blocks: {},
    blocksMeta: { blockmetadata: {} },
    commitment: COMMITMENT,
    accountsDataSlice: [],
    ping: undefined,
  };
}

function sendSubscribeRequest(
  stream: ClientDuplexStream<SubscribeRequest, SubscribeUpdate>,
  request: SubscribeRequest
): Promise<void> {
  return new Promise<void>((resolve, reject) => {
    stream.write(request, (err: Error | null) => {
      if (err) {
        reject(err);
      } else {
        resolve();
      }
    });
  });
}

function handleStreamEvents(
  stream: ClientDuplexStream<SubscribeRequest, SubscribeUpdate>
): Promise<void> {
  return new Promise<void>((resolve, reject) => {
    stream.on("data", async (data) => {
      try {
        await handleData(data);
      } catch (error) {
        console.error("Error processing data:", error);
        // Don't let decoding errors crash the stream
        if (error instanceof Error && (error.message.includes("Invalid bool") || error.message.includes("decode"))) {
          console.log("Recoverable decoding error - continuing...");
          return;
        }
      }
    });

    stream.on("error", async (error: Error) => {
      console.error("Stream error:", error);
      stream.end();

      // Attempt to reconnect
      console.log("Attempting to reconnect in 5 seconds...");
      setTimeout(async () => {
        try {
          const client = new Client(ENDPOINT, undefined, {});
          const newStream = await client.subscribe();
          const request = createSubscribeRequest();
          await sendSubscribeRequest(newStream, request);
          console.log("Successfully reconnected to gRPC stream");
          await handleStreamEvents(newStream);
        } catch (reconnectError) {
          console.error("Reconnection failed:", reconnectError);
          // Try again after delay
          setTimeout(() => handleStreamEvents(stream), 5000);
        }
      }, 5000);
    });

    stream.on("end", () => {
      console.log("Stream ended - attempting to reconnect...");
      setTimeout(() => handleStreamEvents(stream), 5000);
    });
  });
}

function handleData(data: SubscribeUpdate): void {
  try {
    const transaction = data.transaction?.transaction;
    const message = transaction?.transaction?.message;

    if (data.blockMeta?.blockhash) {
      latestBlockhash = data.blockMeta.blockhash;
      lastBlockhashTime = Date.now();
      //console.log(`Updated blockhash: ${latestBlockhash}`);
      //console.log("get lbh:",getLatestBlockhash());
    }

    if (!transaction || !message) {
      return;
    }

    const formattedSignature = convertSignature(transaction.signature);
    formatData(message, formattedSignature.base58, data);
  } catch (error) {
    // Log the error but don't throw
    console.error("Error in handleData:", error);
    if (error instanceof Error && error.message.includes("Invalid bool")) {
      console.log("Skipping transaction due to bool decoding error");
      return;
    }
    throw error; // Rethrow other errors
  }
}

function isSubscribeUpdateTransaction(
  data: SubscribeUpdate
): data is SubscribeUpdate & { transaction: SubscribeUpdateTransaction } {
  return (
    "transaction" in data &&
    typeof data.transaction === "object" &&
    data.transaction !== null &&
    "slot" in data.transaction &&
    "transaction" in data.transaction
  );
}

function convertSignature(signature: Uint8Array): { base58: string } {
  return { base58: bs58.encode(Buffer.from(signature)) };
}

async function formatData(
  message: Message,
  signature: string,
  data: SubscribeUpdate
): Promise<FormattedTransactionData | undefined> {
  const slot = data.transaction?.slot;
  const blockHash = data.blockMeta?.blockhash;
  const accountKeys = message.accountKeys;
  const owner = new PublicKey(accountKeys[0]).toBase58();

  const account = await getRedisString(redis, owner);
  let tradeData: TradeEvent | null = null;


  let appType = "";
  let addressType = "";



  data.transaction?.transaction?.meta?.innerInstructions?.forEach((innerInstruction) => {
    innerInstruction.instructions.forEach((instruction) => {
      const decodedEvent = eventDecode(bs58.encode(instruction.data));
      if (decodedEvent?.name === "TradeEvent") {
        tradeData = decodeTradeEventValues(instruction.data);
        if (tradeData) {
          processTradeEvent(tradeData, message, data);
        }

      }
      if (decodedEvent?.name === "CreateEvent") {
        const createData = decodeCreateEventValues(instruction.data);



        if (createData) {
          processCreateEvent(createData, message, data);

        }

        ;
      }
    });
  });



  try {
    if (account) {
      const redisAccount: redisAddress = JSON.parse(account);
      appType = redisAccount.app;
      addressType = redisAccount.addressType;

      if (appType != "" && addressType == "minter") {
        //updateDBSolPrice();
        if (!(await getAppStatus(appType))) {
          //     console.log(
          //       `[${new Date().toISOString()}] App ${appType} is disabled ${account}`
          //     );
          return;
        }

        //const currentDate = new Date().toISOString();

        // Example of using the database pool for queries:
        // await dbPool.execute('INSERT INTO rugger_mints (rugger, trade_status, mint, buy_sol_amount, buy_token_amount, sell_sol_amount, sell_token_amount, buy_tx, sell_tx, buy_mc, sell_mc, created_at, app) VALUES (?,?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)', [owner, "active", mint, 0, 0, 0, 0, 0, 0, 0, 0, new Date(), appType]);

        //TODO: setup inmemory array and calculate profit inmemory . init tokens["tokenname"] = {totabuys:0,totalsells:0,profit:0,mcap:0,apptype:"app01","status":"bought","tokenamount":3333333,"selltx":2222,"buytx":11111,"sellmc":3333,"buymc":11111}
        //TODO: Buy token in async put record in db . later on streadm check what i buy for any token and update database.
        //TODO: send out trades data out to add to

        //addRedisString(redis, mint,JSON.stringify({addressType:"token",address:mint,app:appType}));
        //await sendRabbitMessage(JSON.stringify({ "address": mint, "command":"connect" }), 'mints');
        //await sendRabbitMessage(JSON.stringify({ "address": appType, "command":"reset" }), 'accounts');
        //sendToDiscord(PUMPFUN_DISCORD_WEBHOOK, `gRPC (${appType}): ${mint} } ${currentDate}`);

        // console.log("Mint: ", mint," owner: ", owner);

        // const includedAccounts = ACCOUNTS_TO_INCLUDE.reduce<Record<string, string>>((acc, { name, index }) => {
        //   const accountIndex = matchingInstruction.accounts[index];
        //   const publicKey = accountKeys[accountIndex];
        //   acc[name] = new PublicKey(publicKey).toBase58();
        //   return acc;
        // }, {});

        //console.log("======================================💊 New Pump.fun Mint Detected!======================================");
        //console.table({signature,slot,...includedAccounts,owner});
        //console.log("\n");
      }
    } else {
      return;
    }
  } catch (error) {
    console.error("Failed to retrieve Rugger accounts:", error);
    return;
  }

  return;
}


function decodeTradeEventValues(data: Uint8Array): TradeEvent | null {
  const buffer = Buffer.from(data).slice(8); // Remove first 8 bytes for the event CPI

  const mint = bs58.encode(buffer.slice(8, 40));
  const solAmount = buffer.readBigUInt64LE(40);
  const tokenAmount = buffer.readBigUInt64LE(48);
  // Convert to boolean properly - any non-zero value is true
  const isBuy = buffer[56] !== 0;
  const user = bs58.encode(buffer.slice(57, 89));
  const timestamp = buffer.readBigInt64LE(89);
  const virtualSolReserves = buffer.readBigUInt64LE(97);
  const virtualTokenReserves = buffer.readBigUInt64LE(105);
  const realSolReserves = buffer.readBigUInt64LE(113);
  const realTokenReserves = buffer.readBigUInt64LE(121);

  return {
    mint,
    solAmount: Number(solAmount),
    tokenAmount: Number(tokenAmount),
    isBuy,
    user,
    timestamp: Number(timestamp),
    virtualSolReserves: Number(virtualSolReserves),
    virtualTokenReserves: Number(virtualTokenReserves),
    realSolReserves: Number(realSolReserves),
    realTokenReserves: Number(realTokenReserves),
  };
}

function decodeCreateEventValues(data: Uint8Array): { name: string, symbol: string, mint: string } | null {
  try {
    const buffer = Buffer.from(data);
    //console.log("Raw buffer:", buffer.toString('hex'));

    // Skip the first 16 bytes (8 bytes discriminator + 8 bytes of additional data)
    const dataBuffer = buffer.slice(16);

    // First 4 bytes are name length (u32 LE)
    const nameLength = dataBuffer.readUInt32LE(0);
    if (nameLength > 100 || nameLength < 0) {
      return null;
    }

    // Read name
    const name = dataBuffer.slice(4, 4 + nameLength).toString('utf8');

    // Read symbol length
    const symbolLength = dataBuffer.readUInt32LE(4 + nameLength);
    if (symbolLength > 20 || symbolLength < 0) {
      return null;
    }

    // Read symbol
    const symbol = dataBuffer.slice(4 + nameLength + 4, 4 + nameLength + 4 + symbolLength).toString('utf8');

    // Read URI length
    const uriStart = 4 + nameLength + 4 + symbolLength;
    const uriLength = dataBuffer.readUInt32LE(uriStart);

    // Skip URI and read mint (last 32 bytes)
    const mintStart = uriStart + 4 + uriLength;
    const mint = bs58.encode(dataBuffer.slice(mintStart, mintStart + 32));

    return { name, symbol, mint };
  } catch (error) {
    console.error('Failed to decode CreateEvent:', error);
    return null;
  }
}

async function processCreateEvent(createData: { name: string, symbol: string, mint: string }, message: Message, data: SubscribeUpdate): Promise<void> {

//signer
const signer = new PublicKey(message.accountKeys[0]).toBase58();


const solAmount=0;
const isBuy =true;
const tokenAmount=0;
mintTrends[createData.mint] = {
  owner: signer, // Add appropriate owner value
  symbol: createData.symbol,
  created_at: Math.floor(Date.now() / 1000), // Unix timestamp
  vol10sec: solAmount,
  vol10secTrades: 1,
  vol10secUsers: 1,
  vol10secBuys: isBuy ? 1 : 0,
  vol10secSells: isBuy ? 0 : 1,
  vol1min: solAmount,
  vol1minTrades: 1,
  vol1minUsers: 1,
  vol1minBuys: isBuy ? 1 : 0,
  vol1minSells: isBuy ? 0 : 1,
  vol5min: solAmount,
  vol5minTrades: 1,
  vol5minUsers: 1,
  vol5minBuys: isBuy ? 1 : 0,
  vol5minSells: isBuy ? 0 : 1,
  vol15min: solAmount,
  vol15minTrades: 1,
  vol15minUsers: 1,
  vol15minBuys: isBuy ? 1 : 0,
  vol15minSells: isBuy ? 0 : 1,
  volAll: solAmount,
  usersHoldingCount: 0,
  usersHoldingTokens: 0,
  usersTotalBuySol: 0,
  usersTotalSellSol: 0,
  devHoldingCount: 0,
  devHoldingTokens: 0,
  devTotalBuySol: 0,
  devTotalSellSol: 0,
  price: 0,
  marketCap: 0,
  maxMarketCap: 0, // Add appropriate maxMarketCap value
  minMarketCap: 9999999, // Add appropriate maxMarketCap value
  customStatus: false,
  trades: [{
    amount: solAmount,
    tokenAmount: tokenAmount,
    timestamp: Date.now(),
    user: "",
    isBuy: false,
    isDev: false
  }],
  users: {} // Initialize users as an empty object
};



}

async function processTradeEvent(tradeData: TradeEvent, message: Message, data: SubscribeUpdate): Promise<void> {
  try {
    const {
      user,
      mint,
      isBuy,
      solAmount,
      tokenAmount,
      virtualSolReserves,
      virtualTokenReserves
    } = tradeData;

    
    let isDev=true;
    // Use the cached blockhash instead of relying on blockMeta
    //const blockHash = getLatestBlockhash();
    //const sig = data.transaction?.transaction?.signature;
    //const sigtxt = sig instanceof Uint8Array ? convertSignature(sig) : { base58: "invalid signature" };

    //console.log(JSON.stringify(data));

    // Check if any instruction involves TRADERS_PROGRAMS
    //console.log(tradeData.mint);
    const accounts = message.accountKeys.map(key => new PublicKey(key).toBase58());
    //console.log(accounts);
    let hasTraderProgram=false;
    for ( const instruction of message.instructions) {
      const programId = accounts[instruction.programIdIndex];
      if ( TRADERS_PROGRAMS.includes(programId)) {
        hasTraderProgram = true;
        isDev=false;
        break;
      }
    }
    const signature = data.transaction?.transaction?.signature || [];
    // const hasTraderProgram = (data.transaction?.transaction as any)?.message?.instructions.some((instruction: any) => {
    //   const programId = accounts[instruction.programIdIndex];
    //   console.log(instruction);
    //   //return TRADERS_PROGRAMS.includes(programId);
    // });

    // check if has trader account or user in traderAccounts

    // if (!hasTraderProgram && !traderAccounts.has(user) ) {
    //   return;
    // }

   if (!mintTrends[tradeData.mint]) {
      return;
    } 

    if ( !isDev && !traderAccounts.has(user) ) {
      traderAccounts.add(user);
    }
    

    const solAmountInSol = solAmount / 1e9;
    const price = solAmount / tokenAmount;
    const solPrice = 150;
    const marketCap = price * 1e6 * solPrice;
    mintTrends[mint].price = price;
    mintTrends[mint].marketCap = marketCap;
    mintTrends[mint].maxMarketCap = Math.max(mintTrends[mint].maxMarketCap, marketCap);
    if (mintTrends[mint].maxMarketCap > 6000) {
      mintTrends[mint].minMarketCap = Math.min(mintTrends[mint].minMarketCap, marketCap);
    }
    
    //mintTracker[mint].mcap_current = marketCap;

   
      const currentTime = Date.now();
      
      // Initialize time-based volumes if they don't exist
      if (!mintTrends[tradeData.mint].trades) {
        mintTrends[tradeData.mint].trades = [];
      }
      
      // Add new trade with timestamp
      mintTrends[tradeData.mint].trades!.push({
        amount: solAmount,
        tokenAmount: tokenAmount,
        timestamp: currentTime,
        user: tradeData.user,
        isBuy: tradeData.isBuy,
        isDev: isDev,
      });

      // Update user data
      if (!mintTrends[tradeData.mint].users[tradeData.user]) {
        mintTrends[tradeData.mint].users[tradeData.user] = {
          tokenBalance: 0,
          totalSpentSol: 0,
          totalGotSol: 0,
          lastTradeTimestamp: 0,
          isDev: isDev
        };
      }

      const userData = mintTrends[tradeData.mint].users[tradeData.user];
      if (tradeData.isBuy) {
        userData.tokenBalance += tradeData.tokenAmount;
        userData.totalSpentSol += tradeData.solAmount;
      } else {
        userData.tokenBalance -= tradeData.tokenAmount;
        userData.totalGotSol += tradeData.solAmount;
      }
      userData.lastTradeTimestamp = currentTime;
      



    //   users: { 
    //     [userAddress: string]: {
    //       tokenBalance: number,
    //       totalSpentSol: number,
    //       totalGotSol: number,
    //       lastTradeTimestamp: number
    //     }
    //   }
    // } 

    // Calculate usersHoldingCount and usersHoldingTokens
    let usersHoldingCount = 0;
    let usersHoldingTokens = 0;
    let devHoldingCount = 0;
    let devHoldingTokens = 0;
    for (const user in mintTrends[tradeData.mint].users) {
      const userData = mintTrends[tradeData.mint].users[user];
      if (userData.tokenBalance > 0) {
        if (userData.isDev) {
          devHoldingCount++;
          devHoldingTokens += userData.tokenBalance;
        } else {
          usersHoldingCount++;
          usersHoldingTokens += userData.tokenBalance;
        }
      }
    }
    mintTrends[tradeData.mint].usersHoldingCount = usersHoldingCount;
    mintTrends[tradeData.mint].usersHoldingTokens = usersHoldingTokens / 1e6;
    mintTrends[tradeData.mint].devHoldingCount = devHoldingCount;
    mintTrends[tradeData.mint].devHoldingTokens = devHoldingTokens / 1e6;
    const percentageHolding = ((usersHoldingTokens / 1e6) / 1e9 * 100).toFixed(2);
    const percentageDevHolding = ((devHoldingTokens / 1e6) / 1e9 * 100).toFixed(2);



    let usersTotalBuySol = 0;
    let usersTotalSellSol = 0;
    for (const trade of mintTrends[tradeData.mint].trades) {
      if (!trade.isDev) {
        if (trade.isBuy) {
          usersTotalBuySol += trade.amount;
        } else {
          usersTotalSellSol += trade.amount;
        }
      }else{
        if (trade.isBuy) {
          mintTrends[tradeData.mint].devTotalBuySol += trade.amount;
        } else {
          mintTrends[tradeData.mint].devTotalSellSol += trade.amount;
        }
      }
    }
    mintTrends[tradeData.mint].usersTotalBuySol = usersTotalBuySol;
    mintTrends[tradeData.mint].usersTotalSellSol = usersTotalSellSol;

    //dev profit
    const dev_profit = ((mintTrends[tradeData.mint].usersTotalBuySol - mintTrends[tradeData.mint].usersTotalSellSol)/1e9).toFixed(4);

    let userTotalTrades=0;
    let devTotalTrades=0;
    for (const trade of mintTrends[tradeData.mint].trades) {
      if (!trade.isDev) {
        userTotalTrades++;
      }else{
        devTotalTrades++;
      }
    }


    const now =  Math.floor(Date.now() / 1000);
    const age = now-(mintTrends[tradeData.mint].created_at ?? now);


//   if ( dev_traders.has(tradeData.user)) {
    
//     addMintTrendsTrade({
//       mint: tradeData.mint,
//       address: tradeData.user,
//       buyDelay: age,
//       isBuy: tradeData.isBuy,
//       solAmount: solAmount,
//       tokenAmount: tokenAmount,
//       price: price,
//       marketCup: marketCap,
//       bot: "bot",
//       maxMarketCup: mintTrends[tradeData.mint].maxMarketCap
//     });
//   }

//if ( age > 1){
// Track the trade in the PNL system
pnlTracker.processTrade(
    tradeData.user,          // trader address
    tradeData.mint,          // token address
    tradeData.isBuy,         // true for buy, false for sell
    tradeData.solAmount,     // SOL amount in lamports
    tradeData.tokenAmount,   // Token amount
    price,                   // Calculated price
    Date.now(),              // Current timestamp
    age,
    bs58.encode(signature)   // Convert Uint8Array to Base58 string
  );
    
  if (mintTrends[tradeData.mint] && mintTrends[tradeData.mint].symbol) {
      pnlTracker.setTokenSymbol(tradeData.mint, mintTrends[tradeData.mint].symbol);
    }
//}


  
  // Periodically check for profitable traders (avoid doing this on every trade for performance)
 
  const cTime = Date.now();
  if (cTime - lastCheckTime >= 60000) { // Check every 60 seconds
    lastCheckTime = cTime;
    const profitableTraders = pnlTracker.getProfitableTraders();

    if (profitableTraders.length > 0) {
      console.log(`Found ${profitableTraders.length} profitable traders!`);
      console.log(pnlTracker.printProfitableTraders());
      // You could log these, store them in the database, send alerts, etc.
    }
  }



  } catch (error) {
    console.error('Error processing trade event:', error);
  }
}

// Setup graceful shutdown to close connections
process.on('SIGINT', async () => {
  console.log('Received SIGINT. Closing connections...');
  try {
    // Close Redis connection
    await redis.quit();
    console.log('Redis connection closed.');

    // Close database pool
    await dbPool.end();
    console.log('Database pool closed.');

    process.exit(0);
  } catch (error) {
    console.error('Error during shutdown:', error);
    process.exit(1);
  }
});

process.on('SIGTERM', async () => {
  console.log('Received SIGTERM. Closing connections...');
  try {
    // Close Redis connection
    await redis.quit();
    console.log('Redis connection closed.');

    // Close database pool
    await dbPool.end();
    console.log('Database pool closed.');

    process.exit(0);
  } catch (error) {
    console.error('Error during shutdown:', error);
    process.exit(1);
  }
});

main().catch((err) => {
  console.error("Unhandled error in main:", err);
  process.exit(1);
});

export { getLatestBlockhash };

// Add reconnection helper
async function reconnectStream(): Promise<ClientDuplexStream<SubscribeRequest, SubscribeUpdate>> {
  try {
    const client = new Client(ENDPOINT, undefined, {});
    const stream = await client.subscribe();
    const request = createSubscribeRequest();
    await sendSubscribeRequest(stream, request);
    console.log("Successfully created new gRPC stream connection");
    return stream;
  } catch (error) {
    console.error("Error creating new stream:", error);
    throw error;
  }
}

// export async function sellTokensJito(tokenCA: string, amount: string, latestBlockHash: string, wallet: Uint8Array,devPubKey: string) {
//   tradeMeasure = Date.now();
//   const jitoTipAccount = new PublicKey('DttWaMuVvTiduZRnguLF7jNxTgiMBZ1hyAumKUiL2KRL');
//   const jitoEndpoint = 'https://mainnet.block-engine.jito.wtf/api/v1/transactions';

//   const privateKey = process.env.PRIVATE_KEY ? bs58.decode(process.env.PRIVATE_KEY) : new Uint8Array();
//   const signer = Keypair.fromSecretKey(privateKey);

//   const tokenBalance = parseFloat(amount);
//   const tipLamports = 1_000_000;

//   const transaction = new Transaction();
//   const sellInstruction = await getSellInstructionNoRPC(bs58.encode(privateKey), tokenCA, tokenBalance,signer,devPubKey);

//   transaction.add(
//     SystemProgram.transfer({
//       fromPubkey: signer.publicKey,
//       toPubkey: jitoTipAccount,
//       lamports: tipLamports,
//     })
//   );

//   if (!sellInstruction) {
//     console.log('Request preparation: Failed to get sell instruction');
//     return;
//   }

//   transaction.add(sellInstruction);
//   transaction.feePayer = signer.publicKey;
//   transaction.recentBlockhash = latestBlockHash;
//   transaction.sign(signer);

//   const serializedTransaction = bs58.encode(transaction.serialize());
//   console.log(`Request preparation: Transaction serialized successfully (${serializedTransaction.length} bytes)`);

//   try {
//     // The connection is already warmed up here, should be much faster
//     console.log(`Sending request to ${jitoEndpoint}...`);
//     const startTime = Date.now();

//     const response = await fetch(jitoEndpoint, {
//       method: 'POST',
//       body: JSON.stringify({
//         jsonrpc: '2.0',
//         id: 1,
//         method: 'sendTransaction',
//         params: [serializedTransaction]
//       }),
//       headers: { 'Content-Type': 'application/json' },
//       agent: httpsAgent,
//     });

//     const responseTime = Date.now() - startTime;
//     const responseText = await response.text();

//     console.log(`Response received in ${responseTime}ms`);
//     console.log(`Response status: ${response.status}`);
//     console.log(`Response body: ${responseText}`);

//     return response.status === 200 ? JSON.parse(responseText).result : response;
//   } catch (error) {
//     console.log('Request failed. Network or server error.');
//     return error;
//   }
// }

// export async function buyTokensJito(tokenCA: string, amount: string, latestBlockHash: string, virtual_sol_reserves: number, virtual_token_reserves: number) {
//   const jitoTipAccount = new PublicKey('DttWaMuVvTiduZRnguLF7jNxTgiMBZ1hyAumKUiL2KRL');
//   const jitoEndpoint = 'https://mainnet.block-engine.jito.wtf/api/v1/transactions';
//   //const jitoEndpoint = RPC_URL_CS; 
//   const privateKey = process.env.PRIVATE_KEY ? bs58.decode(process.env.PRIVATE_KEY) : new Uint8Array();
//   const signer = Keypair.fromSecretKey(privateKey);

//   const solAmount = parseFloat(amount);
//   const tipLamports = 1_000;

//   const transaction = new Transaction();
//   const buyInstruction = await getBuyInstructionNoRPC(bs58.encode(privateKey), tokenCA, solAmount, virtual_sol_reserves, virtual_token_reserves,signer);

//   const priorityFee = ComputeBudgetProgram.setComputeUnitPrice({
//     microLamports: 500_000 // Adjust this value based on network conditions
//   });
//   transaction.add(priorityFee);

//   transaction.add(
//     SystemProgram.transfer({
//       fromPubkey: signer.publicKey,
//       toPubkey: jitoTipAccount,
//       lamports: tipLamports,
//     })
//   );


//   if (!buyInstruction) {
//     console.log('Request preparation: Failed to get sell instruction');
//     return;
//   }

//   transaction.add(buyInstruction);
//   transaction.feePayer = signer.publicKey;
//   transaction.recentBlockhash = latestBlockHash;
//   console.log(transaction);
//   transaction.sign(signer);



//   const serializedTransaction = bs58.encode(transaction.serialize());
//   console.log(`Request preparation: Transaction serialized successfully (${serializedTransaction.length} bytes)`);

//   try {
//     // The connection is already warmed up here, should be much faster
//     console.log(`Sending request to ${jitoEndpoint}...`);
//     const startTime = Date.now();

//     const response = await fetch(jitoEndpoint, {
//       method: 'POST',
//       body: JSON.stringify({
//         jsonrpc: '2.0',
//         id: 1,
//         method: 'sendTransaction',
//         params: [serializedTransaction]
//       }),
//       headers: { 'Content-Type': 'application/json' },
//       agent: httpsAgent,
//     });

//     const responseTime = Date.now() - startTime;
//     const responseText = await response.text();

//     console.log(`Response received in ${responseTime}ms`);
//     console.log(`Response status: ${response.status}`);
//     console.log(`Response body: ${responseText}`);

//     return response.status === 200 ? JSON.parse(responseText).result : response;
//   } catch (error) {
//     console.log('Request failed. Network or server error.');
//     return error;
//   }
// }
