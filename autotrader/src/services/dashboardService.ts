import { getConnection } from '../common/db';

export const getAppConfig = async () => {
    const connection = await getConnection();
    const [rows] = await connection.query('SELECT * FROM app_config');
    connection.end();
    return rows;
};

export const updateAppConfig = async (id: number, data: any) => {
    const connection = await getConnection();
    await connection.query('UPDATE app_config SET ? WHERE id = ?', [data, id]);
    connection.end();
};

export const addAppConfig = async (data: any) => {
    const connection = await getConnection();
    await connection.query('INSERT INTO app_config SET ?', data);
    connection.end();
};

export const getConfigs = async () => {
    const connection = await getConnection();
    const [rows] = await connection.query('SELECT * FROM configs');
    connection.end();
    return rows;
};

export const updateConfig = async (id: number, data: any) => {
    const connection = await getConnection();
    await connection.query('UPDATE configs SET ? WHERE id = ?', [data, id]);
    connection.end();
};

export const getLatestRuggerMints = async () => {
    const connection = await getConnection();
    try {
        const [rows] = await connection.query(`
            SELECT 
                rm.*,
                COALESCE(c.sol_price, 0) as sol_price
            FROM rugger_mints rm
            CROSS JOIN (
                SELECT sol_price 
                FROM configs 
                ORDER BY id DESC 
                LIMIT 1
            ) c
            ORDER BY rm.created_at DESC 
            LIMIT 100
        `);
        return rows;
    } finally {
        connection.end();
    }
};

export const getLatestTokenTx = async () => {
    const connection = await getConnection();
    const [rows] = await connection.query('SELECT * FROM token_tx ORDER BY block_time DESC LIMIT 10');
    return rows;
    connection.end();
};
