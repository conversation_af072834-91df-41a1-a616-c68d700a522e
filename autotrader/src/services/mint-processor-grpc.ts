import Client, {
    CommitmentLevel,
    SubscribeRequest,
    SubscribeUpdate,
    SubscribeUpdateTransaction,
} from "@triton-one/yellowstone-grpc";
import { ClientDuplexStream } from "@grpc/grpc-js";
import { PublicKey } from "@solana/web3.js";
import amqp from "amqplib/callback_api";
import bs58 from "bs58";

// eslint-disable-next-line @typescript-eslint/no-require-imports
const idl = require("../common/idl-new.json");
import dotenv from "dotenv";
import { Redis } from "ioredis";

import { checkRedisKey, getRedisString } from "../common/redis";
import { REDIS_URL } from "../common/config";
import { ruleIsSignedByAuto1Owner } from "../common/pump-rules";
import crypto from "crypto";
import { sendRabbitMessage } from "../common/rabbitmq";
import { saveTransactionsToDatabase } from "../common/db";
import { BorshCoder } from "@coral-xyz/anchor";
const coder = new BorshCoder(idl as any);

dotenv.config();

// RabbitMQ Configuration
const RABBITMQ_SERVER = process.env.RABBITMQ_HOSTNAME || "kroocoin.xyz";
const RABBITMQ_QUEUE = "mints";
const RABBITMQ_PORT = 5672;
const RABBITMQ_USERNAME = "pump";
const RABBITMQ_PASSWORD = "pump2pump";

// Redis Configuration
const REDIS_PASSWORD = process.env.REDIS_PASSWORD || "pump2pump";
const REDIS_PORT = parseInt(process.env.REDIS_PORT || "6379", 10);

// Create a reusable Redis client with error handling
const createRedisClient = () => {
    const client = new Redis({
        host: REDIS_URL,
        port: REDIS_PORT,
        password: REDIS_PASSWORD,
        retryStrategy: (times) => {
            const delay = Math.min(times * 50, 2000);
            console.log(`Retrying Redis connection in ${delay}ms...`);
            return delay;
        }
    });

    client.on('error', (err) => {
        console.error('Redis connection error:', err);
    });

    client.on('connect', () => {
        console.log('Connected to Redis successfully');
    });

    return client;
};

type TradeEvent = {
    mint: string;
    solAmount: number;
    tokenAmount: number;
    isBuy: boolean;
    user: string;
    timestamp: number;
    virtualSolReserves: number;
    virtualTokenReserves: number;
    realSolReserves: number;
    realTokenReserves: number;
    signature?: string;
    signer?: string;
    block_id?: number;
    app?: string;
};

interface ConnectionCommand {
    address: string;
    command: "connect" | "disconnect";
    timestamp?: number;
}

// Constants
const ENDPOINT = "http://grpc.solanavibestation.com:10000";
const PUMP_FUN_PROGRAM_ID = "68s9x6ng4T5jeKe2PSZMRNESm2LTLxiDSAU5UkJPg1je";
const COMMITMENT = CommitmentLevel.PROCESSED;

// Track addresses for monitoring
class AddressManager {
    private addresses: Set<string> = new Set();
    private stream: ClientDuplexStream<SubscribeRequest, SubscribeUpdate> | null =
        null;
    private client: Client | null = null;
    private isUpdating: boolean = false;
    private pendingUpdates: boolean = false;

    constructor() {
        // this.addresses.add('6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF61');
    }

    setClient(client: Client) {
        this.client = client;
    }

    setStream(stream: ClientDuplexStream<SubscribeRequest, SubscribeUpdate>) {
        this.stream = stream;
    }

    async addAddress(address: string): Promise<void> {
        if (this.addresses.has(address)) {
            console.log(`Address ${address} is already being monitored`);
            return;
        }

        this.addresses.add(address);
        console.log(`Added address to monitoring: ${address}`);

        // Update subscription with the new address list
        await this.updateSubscription();
    }

    async removeAddress(address: string): Promise<void> {
        if (!this.addresses.has(address)) {
            console.log(`Address ${address} is not being monitored`);
            return;
        }

        this.addresses.delete(address);
        console.log(`Removed address from monitoring: ${address}`);

        // Update subscription with the new address list
        await this.updateSubscription();
    }

    async updateSubscription(): Promise<void> {
        if (!this.client || !this.stream || this.isUpdating) {
            this.pendingUpdates = true;
            return;
        }

        try {
            this.isUpdating = true;
            console.log("Updating subscription with modified address list...");

            // Create request with updated addresses
            const request = this.createSubscribeRequest();

            // Send the updated request on the SAME stream (key difference!)
            await sendSubscribeRequest(this.stream, request);

            console.log("Subscription updated successfully with new address list");
            const addressList = Array.from(this.addresses);
            console.log(`Currently monitoring ${addressList.length} addresses`);

            this.pendingUpdates = false;
        } catch (error) {
            console.error("Error updating subscription:", error);

            // If there's an error, we may need to recreate the stream
            if (error instanceof Error && error.message.includes("429")) {
                console.log("Rate limit detected, will try to reconnect after delay");
                setTimeout(() => this.reconnectStream(), 30000);
            } else {
                setTimeout(() => this.updateSubscription(), 5000);
            }
        } finally {
            this.isUpdating = false;

            // If there were pending updates while we were processing,
            // trigger another update
            if (this.pendingUpdates) {
                setTimeout(() => this.updateSubscription(), 1000);
            }
        }
    }

    // Keep this as a fallback for stream errors
    async reconnectStream(): Promise<void> {
        if (!this.client || this.isUpdating) return;

        try {
            this.isUpdating = true;
            console.log("Reconnecting gRPC stream (fallback method)...");

            // Close existing stream if active
            if (this.stream) {
                this.stream.end();
            }

            // Create new stream
            this.stream = await this.client.subscribe();

            // Set up event handlers
            this.stream.on("data", handleData);
            this.stream.on("error", (error: Error) => {
                console.error("Stream error:", error);
                // Try to reconnect after a delay
                setTimeout(() => this.reconnectStream(), 5000);
            });

            // Send subscribe request with addresses
            const request = this.createSubscribeRequest();
            await sendSubscribeRequest(this.stream, request);
            console.log("Stream reconnected successfully");
        } catch (error) {
            console.error("Error reconnecting stream:", error);
            // Try again after a delay
            setTimeout(() => this.reconnectStream(), 5000);
        } finally {
            this.isUpdating = false;
        }
    }

    createSubscribeRequest(): SubscribeRequest {
        const accountList = [...this.addresses];
        const programIds = [PUMP_FUN_PROGRAM_ID, ...accountList];

        return {
            accounts: {},
            slots: {},
            transactions: {
                pumpFun: {
                    accountInclude: programIds,
                    accountExclude: [],
                    accountRequired: [],
                },
            },
            transactionsStatus: {},
            entry: {},
            blocks: {},
            blocksMeta: {},
            commitment: COMMITMENT,
            accountsDataSlice: [],
            ping: undefined,
        };
    }

    getAddresses(): string[] {
        return [...this.addresses];
    }
}

function eventDecode(data: string): any {
    const instruction = coder.events.decode(
        Buffer.from(bs58.decode(data)).slice(8).toString("base64")
    );
    return instruction;
}

// Global address manager
const addressManager = new AddressManager();

async function main(): Promise<void> {
    const client = new Client(ENDPOINT, undefined, {});
    addressManager.setClient(client);

    // Initialize RabbitMQ connection for receiving address commands
    initRabbitMQ();

    try {
        // Initial connection - create once and reuse
        const stream = await client.subscribe();
        addressManager.setStream(stream);

        // Create initial request
        const request = addressManager.createSubscribeRequest();

        await sendSubscribeRequest(stream, request);
        console.log(
            "Geyser connection established - watching new Pump.fun mints. \n"
        );

        // Setup event handlers
        stream.on("data", handleData);
        stream.on("error", async (error: Error) => {
            console.error("Stream error:", error);
            // Only recreate the stream if we get an error
            setTimeout(() => addressManager.reconnectStream(), 5000);
        });

        // Keep process running
        await new Promise(() => { });
    } catch (error) {
        console.error("Error in subscription process:", error);
        process.exit(1);
    }
}

function sendSubscribeRequest(
    stream: ClientDuplexStream<SubscribeRequest, SubscribeUpdate>,
    request: SubscribeRequest
): Promise<void> {
    return new Promise<void>((resolve, reject) => {
        stream.write(request, (err: Error | null) => {
            if (err) {
                reject(err);
            } else {
                resolve();
            }
        });
    });
}

async function handleData(data: SubscribeUpdate): Promise<void> {
    if (
        !isSubscribeUpdateTransaction(data) ||
        !data.filters.includes("pumpFun")
    ) {
        return;
    }
    const transaction = data.transaction?.transaction;
    const message = transaction?.transaction?.message;
    if (message?.accountKeys[0]) {
        console.log(new PublicKey(message.accountKeys[0]).toBase58());
    } else {
        console.log("Account key is undefined");
    }

    if (!transaction || !message) {
        return;
    }

    let tradeData: TradeEvent | null = null;
    message.instructions.forEach((instruction) => {
        const decodedEvent = eventDecode(bs58.encode(instruction.data));
        if (decodedEvent?.name === "TradeEvent") {
            tradeData = decodeTradeEventValues(instruction.data);
            //console.log("Decoded Trade Event:", tradeEventData);
        }
    });

    transaction.meta?.innerInstructions?.forEach((innerInstruction) => {
        innerInstruction.instructions.forEach((instruction) => {
            const decodedEvent = eventDecode(bs58.encode(instruction.data));
            if (decodedEvent?.name === "TradeEvent") {
                tradeData = decodeTradeEventValues(instruction.data);

           
            }
        });
    });

    if (tradeData != null) {
        const tradeEventData = tradeData as TradeEvent;
        const signer = new PublicKey(message.accountKeys[0]).toBase58();
        ruleIsSignedByAuto1Owner(signer).then((result) => {
            // Create a Redis client with proper error handling
            const redis1 = createRedisClient();

            if (tradeEventData != null && result && signer != tradeEventData.user) {
                redis1.set(tradeEventData.user, JSON.stringify({
                    addressType: "regular",
                    address: tradeEventData.user,
                    app: "auto1",
                })).catch(err => {
                    console.error("Error setting Redis value:", err);
                });
            }
            
            if (tradeEventData != null) {
                const tradeType = tradeEventData.isBuy ? "buy" : "sell";
                console.log("Trade Type:====", tradeType);

                const trade = {
                    tradeType: tradeType,
                    solAmount: tradeEventData.solAmount,
                    tokenAmount: tradeEventData.tokenAmount,
                    mint: tradeEventData.mint,
                    owner: tradeEventData.user,
                };

                checkRedisKey(redis1, trade.owner).then((isMember) => {
                    if (!isMember) {
                        console.log(
                            `${trade.tradeType}  ${(trade.solAmount / 1e9).toFixed(4)}  mint: ${trade.mint}  ${trade.owner}`
                        );

                        const isBuy = trade.tradeType === "buy";
                        getRedisString(redis1, trade.mint).then((redisAddress) => {
                            let app = "";
                            if (redisAddress) {
                                app = JSON.parse(redisAddress).app;
                            }

                            const tstamp = new Date().getTime() / 1000;
                            const signature = bs58.encode(transaction.signature);

                            const tradeEventRecord = [
                                {
                                    mint: trade.mint,
                                    solAmount: trade.solAmount,
                                    tokenAmount: trade.tokenAmount,
                                    isBuy: isBuy,
                                    user: trade.owner,
                                    timestamp: tstamp,
                                    virtualSolReserves: 0,
                                    virtualTokenReserves: 0,
                                    realSolReserves: 0,
                                    realTokenReserves: 0,
                                    signature: signature,
                                    signer: trade.owner,
                                    block_id: 0, // No slot information from RabbitMQ
                                    app: app,
                                },
                            ];

                            let solPrice = 120;
                            getRedisString(redis1, "solPrice").then((redisSolPrice) => {
                                if (redisSolPrice) {
                                    solPrice = parseFloat(redisSolPrice);
                                }
                                const price = trade.solAmount / trade.tokenAmount;
                                const marketCap = price * 1e6 * solPrice;

                                const solAmountFormatted = (trade.solAmount / 1e9).toFixed(3);
                                const hash = crypto
                                    .createHash("md5")
                                    .update(
                                        trade.mint +
                                        trade.owner +
                                        isBuy +
                                        trade.solAmount +
                                        signature
                                    )
                                    .digest("hex");
                                saveTransactionsToDatabase(tradeEventRecord, solPrice);
                                sendRabbitMessage(
                                    JSON.stringify({
                                        mint: trade.mint,
                                        isBuy: isBuy,
                                        amount: solAmountFormatted,
                                        timestamp: tstamp,
                                        account: trade.owner,
                                        mcap: marketCap,
                                        hash: hash,
                                        app: app,
                                    }),
                                    "trade"
                                );

                                // If app is auto1 or auto4 and market cap is small, remove from webhook
                                if ((app === "auto1" || app === "auto4") && marketCap < 6000) {
                                    sendRabbitMessage(
                                        JSON.stringify({
                                            address: trade.mint,
                                            command: "disconnect",
                                        }),
                                        "mints"
                                    );
                                }
                                redis1.disconnect();
                            });
                        });
                    }
                }).catch(err => {
                    console.error("Error checking Redis key:", err);
                    redis1.disconnect();
                }).finally(() => {
                    // Ensure disconnection happens even if there's an error
                    setTimeout(() => {
                        if (redis1.status === 'ready') {
                            redis1.disconnect();
                        }
                    }, 1000);
                });
            }
        }).catch(err => {
            console.error("Error checking rule:", err);
        });

        const solPrice = 120;
        const price = tradeEventData.solAmount / tradeEventData.tokenAmount;
        const marketCap = price * 1e6 * solPrice;
        if ( marketCap < 5000) {
            sendRabbitMessage(JSON.stringify({ "address": tradeEventData.mint, "command":"disconnect" }), 'mints');
        }


    }
}

function isSubscribeUpdateTransaction(
    data: SubscribeUpdate
): data is SubscribeUpdate & { transaction: SubscribeUpdateTransaction } {
    return (
        "transaction" in data &&
        typeof data.transaction === "object" &&
        data.transaction !== null &&
        "slot" in data.transaction &&
        "transaction" in data.transaction
    );
}

function initRabbitMQ(): void {
    amqp.connect(
        {
            protocol: "amqp",
            hostname: RABBITMQ_SERVER,
            port: RABBITMQ_PORT,
            username: RABBITMQ_USERNAME,
            password: RABBITMQ_PASSWORD,
        },
        (error0, connection) => {
            if (error0) {
                throw error0;
            }

            connection.createChannel((error1, channel) => {
                if (error1) {
                    throw error1;
                }

                channel.assertQueue(RABBITMQ_QUEUE, {
                    durable: true,
                });

                console.log(
                    ` [*] Waiting for address messages in ${RABBITMQ_QUEUE}. To exit press CTRL+C`
                );

                channel.consume(
                    RABBITMQ_QUEUE,
                    (msg) => {
                        if (msg !== null) {
                            try {
                                const command = JSON.parse(
                                    msg.content.toString()
                                ) as ConnectionCommand;
                                processCommand(command);
                                channel.ack(msg);
                            } catch (error) {
                                console.error("Error processing message:", error);
                                channel.nack(msg);
                            }
                        }
                    },
                    {
                        noAck: false,
                    }
                );
            });

            // Handle graceful shutdown
            process.on("SIGINT", () => {
                console.log("Shutting down gRPC stream manager...");
                connection.close();
                process.exit(0);
            });
        }
    );
}

function processCommand(command: ConnectionCommand): void {
    console.log(
        `Processing command: ${command.command} for address: ${command.address}`
    );

    const timestamp = new Date().getTime();
    if (command.timestamp && timestamp - command.timestamp * 1000 > 60000) {
        console.log(
            ` [x] Skipping command due to time difference: ${timestamp - command.timestamp * 1000}ms`
        );
        return;
    }

    switch (command.command) {
        case "connect":
            addressManager.addAddress(command.address);
            break;
        case "disconnect":
            addressManager.removeAddress(command.address);
            break;
    }
}

function decodeTradeEventValues(data: Uint8Array): TradeEvent | null {
    const buffer = Buffer.from(data).slice(8); // Remove first 8 bytes for the event CPI

    const mint = bs58.encode(buffer.slice(8, 40));
    const solAmount = buffer.readBigUInt64LE(40);
    const tokenAmount = buffer.readBigUInt64LE(48);
    const isBuy = Boolean(buffer[56]);
    const user = bs58.encode(buffer.slice(57, 89));
    const timestamp = buffer.readBigInt64LE(89);
    const virtualSolReserves = buffer.readBigUInt64LE(97);
    const virtualTokenReserves = buffer.readBigUInt64LE(105);
    const realSolReserves = buffer.readBigUInt64LE(113);
    const realTokenReserves = buffer.readBigUInt64LE(121);

    return {
        mint,
        solAmount: Number(solAmount),
        tokenAmount: Number(tokenAmount),
        isBuy,
        user,
        timestamp: Number(timestamp),
        virtualSolReserves: Number(virtualSolReserves),
        virtualTokenReserves: Number(virtualTokenReserves),
        realSolReserves: Number(realSolReserves),
        realTokenReserves: Number(realTokenReserves),
    };
}

main().catch((err) => {
    console.error("Unhandled error in main:", err);
    process.exit(1);
});
