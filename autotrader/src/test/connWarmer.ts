import fetch from 'node-fetch';
import { v4 as uuidv4 } from 'uuid';
import { httpsAgent } from './agent';

const RPC_URL = 'https://solana-mainnet.api.syndica.io/api-key/21KG2L6E3kGURokSr4VwfrtYMbqnqVNYKJKCBTQv2jsiLs97iW8TQv98fcz5kvVDhS3MjVmGt91jZ3UGQpGD7zaPCuxpwgCJbek';

const createPayload = () => ({
  jsonrpc: '2.0',
  id: uuidv4(),
  method: 'getLatestBlockhash',
  params: [],
});

export const startSessionWarmup = async () => {
  console.log("🟢 Starting background warmup");
  while (true) {
    try {
      await fetch(RPC_URL, {
        method: 'POST',
        body: JSON.stringify(createPayload()),
        headers: { 'Content-Type': 'application/json' },
        agent: httpsAgent,
      });
    } catch (err) {
      console.error("🔥 Warmup failed:", err);
    }
    await new Promise((res) => setTimeout(res, 1000));
  }
};