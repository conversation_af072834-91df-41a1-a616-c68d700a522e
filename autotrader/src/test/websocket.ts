import WebSocket from 'ws';

// Configuration
const SOLANA_WSS_URL = 'wss://solana-mainnet.api.syndica.io/api-key/4TMsFG1pLWMP4zpdF895C1kYrML8t6AXKQjFB3NzRh1xMjfcMAFPTwCmpRJWa1i72SKTFxjyxBMz7uAkfJoRfR5ZX4kQURk2wwc';
const ACCOUNT_TO_MONITOR = 'C68ihvpfBjxts7iEkstnLrE4eLVo2xwigfEKRc2UcAAF';

// Interfaces
interface SolanaRPCRequest {
  jsonrpc: string;
  id: number;
  method: string;
  params: any[];
}

interface SolanaRPCResponse {
  jsonrpc: string;
  id: number;
  result?: any;
  method?: string;
  error?: {
    code: number;
    message: string;
  };
  params?: any;
}

class SolanaLogsSubscriber {
  private ws: WebSocket;
  private subscriptionId: number | null = null;
  private requestId = 1;

  constructor(private wssUrl: string, private accountAddress: string) {
    this.ws = new WebSocket(wssUrl);
    this.setupEventListeners();
  }

  private setupEventListeners() {
    this.ws.on('open', () => {
      console.log('WebSocket connection established');
      this.subscribeToLogs();
    });

    this.ws.on('message', (data: WebSocket.Data) => {
      try {
        const response = JSON.parse(data.toString()) as SolanaRPCResponse;
        
        // Handle subscription response (contains subscription ID)
        if (response.id && response.result !== undefined) {
          console.log(`Subscription confirmed with ID: ${response.result}`);
          this.subscriptionId = response.result;
        } 
        // Handle logs notification data
        else if (response.method === 'logsNotification') {
          this.handleLogsUpdate(response);
        }
      } catch (error) {
        console.error('Error parsing WebSocket message:', error);
      }
    });

    this.ws.on('error', (error) => {
      console.error('WebSocket error:', error);
    });

    this.ws.on('close', (code, reason) => {
      console.log(`WebSocket connection closed: ${code} - ${reason.toString()}`);
      this.subscriptionId = null;
    });
  }

  private subscribeToLogs() {
    const request: SolanaRPCRequest = {
      jsonrpc: '2.0',
      id: this.requestId++,
      method: 'logsSubscribe',
      params: [
        {
          mentions: [ this.accountAddress ]
        },
        {
          commitment: 'confirmed'
        }
      ]
    };

    console.log('Subscribing to logs for account:', this.accountAddress);
    this.ws.send(JSON.stringify(request));
  }

  private handleLogsUpdate(response: SolanaRPCResponse) {
    if (!response.params) return;
    
    const result = response.params.result;
    
    console.log('=== Logs Update Received ===');
    console.log(`Timestamp: ${new Date().toISOString()}`);
    console.log(`For account: ${this.accountAddress}`);
    console.log('Signature:', result.signature);
    console.log('Logs:',result.value.signature);
    if (result.value?.logs ) {
      result.value.logs.forEach((log: string, index: number) => {
        console.log(`  ${index + 1}. ${log}`);
      });
    } else {
      console.log('  No logs available');
    }
    console.log('===============================');
  }

  public unsubscribe() {
    if (this.subscriptionId !== null) {
      const request: SolanaRPCRequest = {
        jsonrpc: '2.0',
        id: this.requestId++,
        method: 'logsUnsubscribe',
        params: [this.subscriptionId]
      };

      this.ws.send(JSON.stringify(request));
      console.log(`Unsubscribing from logs for account: ${this.accountAddress}`);
    }
  }

  public close() {
    if (this.subscriptionId !== null) {
      this.unsubscribe();
    }
    this.ws.close();
  }
}

// Create and start the subscriber
const subscriber = new SolanaLogsSubscriber(SOLANA_WSS_URL, ACCOUNT_TO_MONITOR);

// Handle process termination to clean up WebSocket connection
process.on('SIGINT', () => {
  console.log('Terminating WebSocket connection...');
  subscriber.close();
  process.exit(0);
});

console.log(`Monitoring logs for Solana account: ${ACCOUNT_TO_MONITOR}`);