import { httpsAgent } from './agent';
import { startSessionWarmup } from './connWarmer';
import fetch from 'node-fetch';
import { v4 as uuidv4 } from 'uuid';

startSessionWarmup(); // run in background

// Later, whenever trade is triggered:
// const sendTradeTx = async (serializedTx: string) => {
//   const result = await fetch("https://mainnet.block-engine.jito.wtf/api/v1/transactions", {
//     method: 'POST',
//     headers: { 'Content-Type': 'application/json' },
//     body: JSON.stringify({
//       jsonrpc: '2.0',
//       id: 1,
//       method: 'sendTransaction',
//       params: [serializedTx],
//     }),
//     agent: httpsAgent,
//   });

//   const data = await result.json();
//   console.log("🟣 Trade response:", data);
// };




// Create JSON-RPC payload
const createPayload = () => ({
  jsonrpc: '2.0',
  id: uuidv4(),
  method: 'getLatestBlockhash',
  params: [],
});

// Define interface for the RPC response
interface BlockhashResponse {
  result: {
    value: {
      blockhash: string;
      lastValidBlockHeight: number;
    };
  };
}

// Function to fetch blockhash
const fetchBlockhash = async () => {
  const response = await fetch("https://solana-mainnet.api.syndica.io/api-key/21KG2L6E3kGURokSr4VwfrtYMbqnqVNYKJKCBTQv2jsiLs97iW8TQv98fcz5kvVDhS3MjVmGt91jZ3UGQpGD7zaPCuxpwgCJbek", {
    method: 'POST',
    body: JSON.stringify(createPayload()),
    headers: { 'Content-Type': 'application/json' },
    agent: httpsAgent,
  });

  const data = await response.json() as BlockhashResponse;
  return data.result.value.blockhash;
};

// Poll blockhash and measure response times
const pollBlockhash = async () => {
  while (true) {
    await new Promise((res) => setTimeout(res, 5000));
    const start = Date.now();
    const blockhash = await fetchBlockhash();
    const duration = Date.now() - start;
    console.log(`Time: ${duration}ms | Blockhash: ${blockhash}`);
    
  }
};

pollBlockhash();
