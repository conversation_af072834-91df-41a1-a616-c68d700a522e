import { MintLayout } from '@solana/spl-token';
 import { Connection, Commitment, PublicKey } from '@solana/web3.js';
 import { LiquidityPoolKeysV4 } from '@raydium-io/raydium-sdk';


export interface FilterResult {
    ok: boolean;
e?: string;
}

export interface Filter {
    execute(poolKeys: LiquidityPoolKeysV4): Promise<FilterResult>;
}

 export class RenouncedFreezeFilter implements Filter {
   private readonly commitment: Commitment;
   private readonly errorTypes: string[];
 
   private static readonly ERROR_MESSAGES = {
     renounced: 'minting authority is not renounced',
     freeze: 'token has an active freeze authority',
     fetchError: 'Failed to fetch account data for mint',
   };
 
   constructor(
     private readonly connection: Connection,
     private readonly checkMintRenounced: boolean,
     private readonly checkFreezable: boolean
   ) {
     this.commitment = this.connection.commitment ?? 'processed';
     this.errorTypes = [
       ...(checkMintRenounced ? ['mint'] : []),
       ...(checkFreezable ? ['freeze'] : []),
     ];
   }
 
   async execute(poolKeys: LiquidityPoolKeysV4): Promise<FilterResult> {
     try {
       const accountInfo = await this.connection.getAccountInfo(new PublicKey(poolKeys.baseMint), this.commitment);
       if (!accountInfo?.data) {
         return { ok: false, e: RenouncedFreezeFilter.ERROR_MESSAGES.fetchError };
       }
 
       const { mintAuthorityOption, freezeAuthorityOption } = MintLayout.decode(accountInfo.data);
 
       if ((!this.checkMintRenounced || mintAuthorityOption === 0) &&
           (!this.checkFreezable || freezeAuthorityOption === 0)) {
         return { ok: true };
       }
 
       const messages = [];
       if (this.checkMintRenounced && mintAuthorityOption !== 0) {
         messages.push(RenouncedFreezeFilter.ERROR_MESSAGES.renounced);
       }
       if (this.checkFreezable && freezeAuthorityOption !== 0) {
         messages.push(RenouncedFreezeFilter.ERROR_MESSAGES.freeze);
       }
 
       return { ok: false, e: `RenouncedFreeze -> ${messages.join(' and ')}` };
     } catch (e) {
       const errorMsg = `Error processing filter for ${this.errorTypes.join(' and ')}: ${(e as Error).message}`;

       return { ok: false, e: `RenouncedFreeze -> ${errorMsg}` };
     }
   }
 }