import Client, {
  CommitmentLevel,
  SubscribeRequest,
  SubscribeUpdate,
  SubscribeUpdateTransaction,
} from "@triton-one/yellowstone-grpc";
import { ClientDuplexStream } from '@grpc/grpc-js';
import { PublicKey } from '@solana/web3.js';
import amqp from 'amqplib/callback_api';
import bs58 from 'bs58';
import { BorshCoder } from '@coral-xyz/anchor';
// eslint-disable-next-line @typescript-eslint/no-require-imports
const idl = require("./idl-new.json");
import dotenv from 'dotenv';



 const coder = new BorshCoder(idl as any);

dotenv.config();

// RabbitMQ Configuration
const RABBITMQ_SERVER = process.env.RABBITMQ_HOSTNAME || 'kroocoin.xyz';
const RABBITMQ_QUEUE = 'gmints';
const RABBITMQ_PORT = 5672;
const RABBITMQ_USERNAME = 'pump';
const RABBITMQ_PASSWORD = 'pump2pump';

type TradeEvent = {
  mint: string;
  solAmount: number;
  tokenAmount: number;
  isBuy: boolean;
  user: string;
  timestamp: number;
  virtualSolReserves: number;
  virtualTokenReserves: number;
  realSolReserves: number;
  realTokenReserves: number;
  signature?: string;
  signer?: string;
  block_id?: number;
  app?: string;
};


// Interfaces
interface CompiledInstruction {
  programIdIndex: number;
  accounts: Uint8Array;
  data: Uint8Array;
}

interface Message {
  header: MessageHeader | undefined;
  accountKeys: Uint8Array[];
  recentBlockhash: Uint8Array;
  instructions: CompiledInstruction[];
  versioned: boolean;
  addressTableLookups: MessageAddressTableLookup[];
}

interface MessageHeader {
  numRequiredSignatures: number;
  numReadonlySignedAccounts: number;
  numReadonlyUnsignedAccounts: number;
}

interface MessageAddressTableLookup {
  accountKey: Uint8Array;
  writableIndexes: Uint8Array;
  readonlyIndexes: Uint8Array;
}

interface ConnectionCommand {
  address: string;
  command: 'connect' | 'disconnect';
  timestamp?: number;
}

// Constants
const ENDPOINT = "http://grpc.solanavibestation.com:10000";
const PUMP_FUN_PROGRAM = '6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P';
const PUMP_FUN_PROGRAM_ID = '68s9x6ng4T5jeKe2PSZMRNESm2LTLxiDSAU5UkJPg1je';
//const PUMP_FUN_PROGRAM_ID = 'metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s';
//const PUMP_FUN_CREATE_IX_DISCRIMINATOR = Buffer.from([24, 30, 200, 40, 5, 28, 7, 119]);
const PUMP_FUN_TRADE_IX_DISCRIMINATOR = Buffer.from([189, 219, 127, 211, 78, 230, 97, 238]);
//const PUMP_FUN_BUY_IX_DISCRIMINATOR = Buffer.from([102, 6, 61, 18, 1, 218, 235, 234]);
//const PUMP_FUN_SELL_IX_DISCRIMINATOR = Buffer.from([51, 230, 133, 164, 1, 127, 131, 173]);
const COMMITMENT = CommitmentLevel.PROCESSED;



//const COMMITMENT = CommitmentLevel.CONFIRMED;

// Configuration
const FILTER_CONFIG = {
  programIds: [PUMP_FUN_PROGRAM],
  instructionDiscriminators: [PUMP_FUN_TRADE_IX_DISCRIMINATOR],
};


const ACCOUNTS_TO_INCLUDE = [{
  name: "mint",
  index: 0
}];

interface FormattedTransactionData {
  signature: string;
  slot: string;
  [accountName: string]: string;
}

// Track addresses for monitoring
class AddressManager {
  private addresses: Set<string> = new Set();
  private stream: ClientDuplexStream<SubscribeRequest, SubscribeUpdate> | null = null;
  private client: Client | null = null;
  private isUpdating: boolean = false;
  private pendingUpdates: boolean = false;

  constructor() {

    // this.addresses.add('6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF61');
  }

  setClient(client: Client) {
    this.client = client;
  }

  setStream(stream: ClientDuplexStream<SubscribeRequest, SubscribeUpdate>) {
    this.stream = stream;
  }

  async addAddress(address: string): Promise<void> {
    if (this.addresses.has(address)) {
      console.log(`Address ${address} is already being monitored`);
      return;
    }

    this.addresses.add(address);
    console.log(`Added address to monitoring: ${address}`);
    
    // Update subscription with the new address list
    await this.updateSubscription();
  }

  async removeAddress(address: string): Promise<void> {
    if (!this.addresses.has(address)) {
      console.log(`Address ${address} is not being monitored`);
      return;
    }

    this.addresses.delete(address);
    console.log(`Removed address from monitoring: ${address}`);
    
    // Update subscription with the new address list
    await this.updateSubscription();
  }

  async updateSubscription(): Promise<void> {
    if (!this.client || !this.stream || this.isUpdating) {
      this.pendingUpdates = true;
      return;
    }
    
    try {
      this.isUpdating = true;
      console.log("Updating subscription with modified address list...");
      
      // Create request with updated addresses
      const request = this.createSubscribeRequest();
      
      // Send the updated request on the SAME stream (key difference!)
      await sendSubscribeRequest(this.stream, request);
      
      console.log("Subscription updated successfully with new address list");
      const addressList = Array.from(this.addresses);
      console.log(`Currently monitoring ${addressList.length} addresses`);
      
      this.pendingUpdates = false;
    } catch (error) {
      console.error("Error updating subscription:", error);
      
      // If there's an error, we may need to recreate the stream
      if (error instanceof Error && error.message.includes('429')) {
        console.log("Rate limit detected, will try to reconnect after delay");
        setTimeout(() => this.reconnectStream(), 30000);
      } else {
        setTimeout(() => this.updateSubscription(), 5000);
      }
    } finally {
      this.isUpdating = false;
      
      // If there were pending updates while we were processing,
      // trigger another update
      if (this.pendingUpdates) {
        setTimeout(() => this.updateSubscription(), 1000);
      }
    }
  }

  // Keep this as a fallback for stream errors
  async reconnectStream(): Promise<void> {
    if (!this.client || this.isUpdating) return;
    
    try {
      this.isUpdating = true;
      console.log("Reconnecting gRPC stream (fallback method)...");
      
      // Close existing stream if active
      if (this.stream) {
        this.stream.end();
      }
      
      // Create new stream
      this.stream = await this.client.subscribe();
      
      // Set up event handlers
      this.stream.on('data', handleData);
      this.stream.on("error", (error: Error) => {
        console.error('Stream error:', error);
        // Try to reconnect after a delay
        setTimeout(() => this.reconnectStream(), 5000);
      });
      
      // Send subscribe request with addresses
      const request = this.createSubscribeRequest();
      await sendSubscribeRequest(this.stream, request);
      console.log("Stream reconnected successfully");
      
    } catch (error) {
      console.error("Error reconnecting stream:", error);
      // Try again after a delay
      setTimeout(() => this.reconnectStream(), 5000);
    } finally {
      this.isUpdating = false;
    }
  }

  createSubscribeRequest(): SubscribeRequest {
    const accountList = [...this.addresses];
    const programIds = [PUMP_FUN_PROGRAM_ID, ...accountList];
    
    return {
      accounts: {},
      slots: {},
      transactions: {
        pumpFun: {
          accountInclude: programIds,
          accountExclude: [],
          accountRequired: [],
        }
      },
      transactionsStatus: {},
      entry: {},
      blocks: {},
      blocksMeta: {},
      commitment: COMMITMENT,
      accountsDataSlice: [],
      ping: undefined,
    };
  }

  getAddresses(): string[] {
    return [...this.addresses];
  }
}

function eventDecode(data: string): any {
  const instruction = coder.events.decode(Buffer.from(bs58.decode(data)).slice(8).toString('base64'));
  return instruction;
}




// Global address manager
const addressManager = new AddressManager();

async function main(): Promise<void> {
  const client = new Client(ENDPOINT, undefined, {});
  addressManager.setClient(client);
  
  // Initialize RabbitMQ connection for receiving address commands
  initRabbitMQ();
  
  try {
    // Initial connection - create once and reuse
    const stream = await client.subscribe();
    addressManager.setStream(stream);
    
    // Create initial request
    const request = addressManager.createSubscribeRequest();
    
    await sendSubscribeRequest(stream, request);
    console.log('Geyser connection established - watching new Pump.fun mints. \n');
    
    // Setup event handlers
    stream.on('data', handleData);
    stream.on("error", async (error: Error) => {
      console.error('Stream error:', error);
      // Only recreate the stream if we get an error
      setTimeout(() => addressManager.reconnectStream(), 5000);
    });
    
    // Keep process running
    await new Promise(() => {});
    
  } catch (error) {
    console.error('Error in subscription process:', error);
    process.exit(1);
  }
}

function sendSubscribeRequest(
  stream: ClientDuplexStream<SubscribeRequest, SubscribeUpdate>,
  request: SubscribeRequest
): Promise<void> {
  return new Promise<void>((resolve, reject) => {
    stream.write(request, (err: Error | null) => {
      if (err) {
        reject(err);
      } else {
        resolve();
      }
    });
  });
}

function handleData(data: SubscribeUpdate): void {
  if (!isSubscribeUpdateTransaction(data) || !data.filters.includes('pumpFun')) {
    return;
  }
  const transaction = data.transaction?.transaction;
  const message = transaction?.transaction?.message;
  if (message?.accountKeys[0]) {
    console.log(new PublicKey(message.accountKeys[0]).toBase58());
  } else {
    console.log("Account key is undefined");
  }

  if (!transaction || !message) {
    return;
  }

  const matchingInstruction = message.instructions.find(matchesInstructionDiscriminator);
  const matchingInnerInstruction = transaction.meta?.innerInstructions?.find((inner) => inner.instructions.find(matchesInstructionDiscriminator));

  message.instructions.forEach((instruction) => {
    const decodedEvent = eventDecode(bs58.encode(instruction.data));
    if (decodedEvent?.name === "TradeEvent") {
      const tradeEvent = decodeTradeEventValues(instruction.data);
      console.log("Decoded Trade Event:", tradeEvent);
    } 
  });

  transaction.meta?.innerInstructions?.forEach((innerInstruction) => {
    innerInstruction.instructions.forEach((instruction) => {
      const decodedEvent = eventDecode(bs58.encode(instruction.data));
      if (decodedEvent?.name === "TradeEvent") {
        const tradeEvent = decodeTradeEventValues(instruction.data);
        console.log("Decoded Inner Trade Event:", tradeEvent);
      } 
    });
  });

  if (!matchingInstruction && !matchingInnerInstruction) {
    return;
  }

  const formattedSignature = convertSignature(transaction.signature);
  const formattedData = formatData(data,message, formattedSignature.base58, data.transaction.slot);
  if (formattedData) {
    console.log("======================================💊 New Pump.fun Mint Detected!======================================");
    console.table(formattedData);
    console.log("\n");
  }
}

function isSubscribeUpdateTransaction(data: SubscribeUpdate): data is SubscribeUpdate & { transaction: SubscribeUpdateTransaction } {
  return (
    'transaction' in data &&
    typeof data.transaction === 'object' &&
    data.transaction !== null &&
    'slot' in data.transaction &&
    'transaction' in data.transaction
  );
}

function convertSignature(signature: Uint8Array): { base58: string } {
  return { base58: bs58.encode(Buffer.from(signature)) };
}

function formatData(data: SubscribeUpdate,message: Message, signature: string, slot: string): FormattedTransactionData | undefined {
  const matchingInstruction = message.instructions.find(matchesInstructionDiscriminator);

  if (!matchingInstruction) {
    return undefined;
  }

  const accountKeys = message.accountKeys;
  const owner = new PublicKey(accountKeys[0]).toBase58(); 
  
  const includedAccounts = ACCOUNTS_TO_INCLUDE.reduce<Record<string, string>>((acc, { name, index }) => {
    const accountIndex = matchingInstruction.accounts[index];
    const publicKey = accountKeys[accountIndex];
    acc[name] = new PublicKey(publicKey).toBase58();
    return acc;
  }, {});

  return {
    signature,
    slot,
    ...includedAccounts,
    owner
  };
}

function matchesInstructionDiscriminator(ix: CompiledInstruction): boolean {
  return ix?.data && FILTER_CONFIG.instructionDiscriminators.some(discriminator =>
    Buffer.from(discriminator).equals(ix.data.slice(0, 8))
  );
}

function initRabbitMQ(): void {
  amqp.connect({
    protocol: 'amqp',
    hostname: RABBITMQ_SERVER,
    port: RABBITMQ_PORT,
    username: RABBITMQ_USERNAME,
    password: RABBITMQ_PASSWORD,
  }, (error0, connection) => {
    if (error0) {
      throw error0;
    }

    connection.createChannel((error1, channel) => {
      if (error1) {
        throw error1;
      }

      channel.assertQueue(RABBITMQ_QUEUE, {
        durable: true
      });

      console.log(` [*] Waiting for address messages in ${RABBITMQ_QUEUE}. To exit press CTRL+C`);

      channel.consume(RABBITMQ_QUEUE, (msg) => {
        if (msg !== null) {
          try {
            const command = JSON.parse(msg.content.toString()) as ConnectionCommand;
            processCommand(command);
            channel.ack(msg);
          } catch (error) {
            console.error('Error processing message:', error);
            channel.nack(msg);
          }
        }
      }, {
        noAck: false
      });
    });

    // Handle graceful shutdown
    process.on('SIGINT', () => {
      console.log('Shutting down gRPC stream manager...');
      connection.close();
      process.exit(0);
    });
  });
}

function processCommand(command: ConnectionCommand): void {
  console.log(`Processing command: ${command.command} for address: ${command.address}`);
  
  const timestamp = new Date().getTime();
  if (command.timestamp && timestamp - command.timestamp * 1000 > 60000) {
    console.log(` [x] Skipping command due to time difference: ${timestamp - command.timestamp * 1000}ms`);
    return;
  }

  switch (command.command) {
    case 'connect':
      addressManager.addAddress(command.address);
      break;
    case 'disconnect':
      addressManager.removeAddress(command.address);
      break;
  }
}

function decodeTradeEventValues(data: Uint8Array): TradeEvent | null {
  const buffer = Buffer.from(data).slice(8); // Remove first 8 bytes for the event CPI

  const mint = bs58.encode(buffer.slice(8, 40));
  const solAmount = buffer.readBigUInt64LE(40);
  const tokenAmount = buffer.readBigUInt64LE(48);
  const isBuy = Boolean(buffer[56]);
  const user = bs58.encode(buffer.slice(57, 89));
  const timestamp = buffer.readBigInt64LE(89);
  const virtualSolReserves = buffer.readBigUInt64LE(97);
  const virtualTokenReserves = buffer.readBigUInt64LE(105);
  const realSolReserves = buffer.readBigUInt64LE(113);
  const realTokenReserves = buffer.readBigUInt64LE(121);

  return {
    mint,
    solAmount: Number(solAmount),
    tokenAmount: Number(tokenAmount),
    isBuy,
    user,
    timestamp: Number(timestamp),
    virtualSolReserves: Number(virtualSolReserves),
    virtualTokenReserves: Number(virtualTokenReserves),
    realSolReserves: Number(realSolReserves),
    realTokenReserves: Number(realTokenReserves),
  };
}

main().catch((err) => {
  console.error('Unhandled error in main:', err);
  process.exit(1);
});



