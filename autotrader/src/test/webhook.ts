import Fastify from 'fastify';
import amqp from 'amqplib/callback_api';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';


dotenv.config();

const fastify = Fastify({ logger: false });


const RABBITMQ_SERVER = process.env.RABBITMQ_HOSTNAME || '';
const RABBITMQ_QUEUE = process.env.RABBITMQ_QUEUE || 'snipes';

// eslint-disable-next-line @typescript-eslint/no-unused-vars
function sendMessage(message: string, queue: string = RABBITMQ_QUEUE) {
  amqp.connect({
    protocol: 'amqp',
    hostname: RABBITMQ_SERVER,
    port: parseInt(process.env.RABBITMQ_PORT || '5672'),
    username: process.env.RABBITMQ_USERNAME,
    password: process.env.RABBITMQ_PASSWORD,
  }, (error0, connection) => {
    if (error0) {
      throw error0;
    }
    connection.createChannel((error1, channel) => {
      if (error1) {
        throw error1;
      }
      channel.assertQueue(queue, {
        durable: true
      });

      channel.sendToQueue(queue, Buffer.from(message), {
        persistent: true
      });
      console.log(" [x] Sent %s", message);
    });
  });
}

fastify.post('/webhook', async (request, reply) => {
  const data = request.body as any;
  //const sig1 = data.event.event.transaction[0].signature;
  const sig1 = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
  const filePath = path.join(__dirname, 'tmp',`${sig1}.json`);
  fs.writeFileSync(filePath, JSON.stringify(data, null, 2));

  return reply.send({ status: "success" });
});

fastify.get('/webhook', async (request, reply) => {
  const data = request.body as any;
  console.log(data);

  return reply.send({ status: "success" });
});

fastify.listen({ port: 7012, host: '0.0.0.0' }, (err, address) => {
  if (err) {
    fastify.log.error(err);
    process.exit(1);
  }
  fastify.log.info(`Server is running on ${address}`);
  console.log(`Server is running on ${address}`);
});