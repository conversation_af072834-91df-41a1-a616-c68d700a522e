import { Command } from 'commander';
import { getTokenBalance } from '../common/solana';
import dotenv from "dotenv";
import { Connection, Transaction, SystemProgram } from '@solana/web3.js';
import axios from 'axios';
import bs58 from 'bs58';
import { Keypair, PublicKey } from '@solana/web3.js';
import { getSellInstructionNoRPC } from '../common/trade';
import { Agent } from 'https';
import fetch from 'node-fetch';

// Create persistent HTTPS agent with keepAlive
export const httpsAgent = new Agent({ 
  keepAlive: true, 
  keepAliveMsecs: 60000 // 1 minute
});

// Connection warming function - call this before any real operations
async function warmupConnection(): Promise<void> {
  console.log("Warming up connection to Jito API...");
  const jitoEndpoint = 'https://mainnet.block-engine.jito.wtf/api/v1/transactions';
  
  try {
    const startTime = Date.now();
    // Make a simple POST request to establish the TLS handshake
    const response = await fetch(jitoEndpoint, {
      method: 'POST',
      body: JSON.stringify({
        jsonrpc: '2.0',
        id: 'warmup',
        method: 'sendTransaction',
        params: []
      }),
      headers: { 'Content-Type': 'application/json' },
      agent: httpsAgent,
    });
    
    const warmupTime = Date.now() - startTime;
    console.log(`Connection warmed up in ${warmupTime}ms (status: ${response.status})`);
    
    // Wait a short time after warmup to ensure connection is established
    await new Promise(resolve => setTimeout(resolve, 200));
  } catch (error) {
    console.log(`Warmup request completed with error - connection still established`);
  }
}

dotenv.config();

const program = new Command();
const RPC_URL = 'https://solana-mainnet.api.syndica.io/api-key/21KG2L6E3kGURokSr4VwfrtYMbqnqVNYKJKCBTQv2jsiLs97iW8TQv98fcz5kvVDhS3MjVmGt91jZ3UGQpGD7zaPCuxpwgCJbek';
program
    .name('sellTokens')
    .description('Sell tokens using Jito')
    .requiredOption('-m, --mint <address>', 'token mint address')
    .option('-a, --amount <number>', 'amount of tokens to sell')
    .option('-b, --balance', 'get token balance')
    .parse(process.argv);

const options = program.opts();
const mint = options.mint;
const tokenAmount = options.amount;

// Function to check transaction status
async function checkTransactionStatus(txId: string, startTime: number): Promise<boolean> {
    const connection = new Connection(RPC_URL, 'processed');
    const maxAttempts = 30; // Maximum number of attempts (30 seconds)
    let attempts = 0;
    
    while (attempts < maxAttempts) {
        try {
            const status = await connection.getSignatureStatus(txId, {searchTransactionHistory: true});
            
            if (!status || !status.value) {
                console.log(`Transaction still processing (attempt ${attempts + 1}/${maxAttempts})...`);
                attempts++;
                
                if (attempts >= maxAttempts) {
                    console.log('Max attempts reached. Transaction status uncertain.');
                    return false;
                }
                
                // Wait 1 second before the next attempt
                await new Promise(resolve => setTimeout(resolve, 1000));
                continue;
            }
            
            if (status.value.err) {
                console.error('Transaction failed:', status.value.err);
                return false;
            }
            
            const endTime = Date.now();
            const processingTime = endTime - startTime;
            console.log(`Transaction succeeded! Processing time: ${processingTime}ms`);
            return true;
        } catch (error) {
            console.error(`Error checking transaction: ${(error as Error).message}`);
            return false;
        }
    }
    
    return false;
}

if (options.balance) {
    getTokenBalance(mint)
        .then((balance) => {
            console.log(`Balance for mint ${mint}: ${balance}`);
        })
        .catch((error) => {
            console.error(`Failed to get balance for mint: ${mint} - ${(error as Error).message}`);
        });
} else if (tokenAmount) {
    console.log("Selling now...");

    // First warm up the connection before proceeding
    warmupConnection().then(() => {
        const connection = new Connection(RPC_URL, 'processed');
        connection.getLatestBlockhash().then((blockhash) => {
            
        console.log(`Latest blockhash: ${blockhash.blockhash}`);

        const startTime = Date.now();

        sellTokensJito(mint, tokenAmount, blockhash.blockhash)
                .then(async (tx) => {
                    const submitTime = Date.now();
                    const submitDuration = submitTime - startTime;
                    console.log(`Sold: ${mint} - ${tx}`);
                    console.log(`Time to submit transaction: ${submitDuration}ms`);
                    
                    // Check transaction status and exit accordingly
                    if (typeof tx === 'string') {
                        const success = await checkTransactionStatus(tx, startTime);
                        process.exit(success ? 0 : 1);
                    } else {
                        console.log('No transaction ID returned');
                        console.error(tx);
                        process.exit(1);
                    }
                })
            .catch(async (error) => {  
                const errorTime = Date.now();
                const errorDuration = errorTime - startTime;
                console.error(`Failed to sell tokens for mint: ${mint} - ${(error as Error).message}`);
                console.error(`Time until error: ${errorDuration}ms`);
                process.exit(1);
            });
        });
    });
} else {
    console.error('Either --amount or --balance option is required');
    process.exit(1);
}

export async function sellTokensJito(tokenCA: string, amount: string, latestBlockHash: string) {
    const jitoTipAccount = new PublicKey('DttWaMuVvTiduZRnguLF7jNxTgiMBZ1hyAumKUiL2KRL'); 
    const jitoEndpoint = 'https://mainnet.block-engine.jito.wtf/api/v1/transactions';

    const privateKey = process.env.PRIVATE_KEY ? bs58.decode(process.env.PRIVATE_KEY) : new Uint8Array();
    const signer = Keypair.fromSecretKey(privateKey);
  
    const tokenBalance = parseFloat(amount); 
    const tipLamports = 1_000; 

    const transaction = new Transaction();
    const sellInstruction = await getSellInstructionNoRPC(bs58.encode(privateKey), tokenCA, tokenBalance);

    transaction.add(
      SystemProgram.transfer({
        fromPubkey: signer.publicKey,
        toPubkey: jitoTipAccount,
        lamports: tipLamports,
      })
    );

    if (!sellInstruction) {
      console.log('Request preparation: Failed to get sell instruction');
      return;
    }
  
    transaction.add(sellInstruction);
    transaction.feePayer = signer.publicKey;
    transaction.recentBlockhash = latestBlockHash;
    transaction.sign(signer);

    const serializedTransaction = bs58.encode(transaction.serialize());
    console.log(`Request preparation: Transaction serialized successfully (${serializedTransaction.length} bytes)`);
  
    try {
        // The connection is already warmed up here, should be much faster
        console.log(`Sending request to ${jitoEndpoint}...`);
        const startTime = Date.now();

        const response = await fetch(jitoEndpoint, {
          method: 'POST',
          body: JSON.stringify({
            jsonrpc: '2.0',
            id: 1,
            method: 'sendTransaction',
            params: [serializedTransaction]
          }),
          headers: { 'Content-Type': 'application/json' },
          agent: httpsAgent,
        });

        const responseTime = Date.now() - startTime;
        const responseText = await response.text();
        
        console.log(`Response received in ${responseTime}ms`);
        console.log(`Response status: ${response.status}`);
        console.log(`Response body: ${responseText}`);
        
        return response.status === 200 ? JSON.parse(responseText).result : response;
    } catch (error) {
        console.log('Request failed. Network or server error.');
        return error;
    }
}