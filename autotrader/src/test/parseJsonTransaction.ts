import { processSwapTransaction} from '../common/solana';
import { Redis } from 'ioredis';
import { REDIS_URL, REDIS_PORT, REDIS_PASSWORD } from '../common/config';


const json = `
{
    "webhookId": "wh_u8ul0w64j1o7sswy",
    "id": "whevt_dmz5kw6e8zhdmk30",
    "createdAt": "2025-03-09T13:54:48.373Z",
    "type": "ADDRESS_ACTIVITY",
    "event": {
      "transaction": [
        {
          "signature": "2mn7fsbokTQi2QTxwH3crLmMjeCaT4h9s2eWZQtPYXBbFCqRG6PU3KMpHfj6n4bDEAw9G4BAujD5kk1XF1pj8M5v",
          "transaction": [
            {
              "signatures": [
                "2mn7fsbokTQi2QTxwH3crLmMjeCaT4h9s2eWZQtPYXBbFCqRG6PU3KMpHfj6n4bDEAw9G4BAujD5kk1XF1pj8M5v"
              ],
              "message": [
                {
                  "header": [
                    {
                      "num_required_signatures": 1,
                      "num_readonly_signed_accounts": 0,
                      "num_readonly_unsigned_accounts": 7
                    }
                  ],
                  "instructions": [
                    {
                      "data": "Ge7fcT",
                      "program_id_index": 7
                    },
                    {
                      "data": "3VLRmMAbADYb",
                      "program_id_index": 7
                    },
                    {
                      "accounts": [
                        1,
                        9,
                        0,
                        10,
                        11,
                        16,
                        17
                      ],
                      "data": "WPNHsFPyEMr",
                      "program_id_index": 8
                    },
                    {
                      "accounts": [
                        0,
                        2
                      ],
                      "data": "3ipZWx49rvTHjRafzm82sqbN2X9NKsF9UoQN8djEWGzp3s5X6mkRRqZoDz8Swf6wiQmcq2TLAaPzNC43JHNYj34LShsbPyPTfTpTzgGq4JXhrVt6iyMBr3wCT67z7WhWVZ34o2AmJckKeuQTGrKSPU3bRKgnVMrG69jin6Mr8",
                      "program_id_index": 10
                    },
                    {
                      "accounts": [
                        2,
                        18,
                        0,
                        17
                      ],
                      "data": "2",
                      "program_id_index": 11
                    },
                    {
                      "accounts": [
                        11,
                        3,
                        19,
                        3,
                        3,
                        4,
                        5,
                        3,
                        3,
                        3,
                        3,
                        3,
                        3,
                        3,
                        3,
                        1,
                        2,
                        0
                      ],
                      "data": "6NDpeyVTEUsAUVQz3BRmtrT",
                      "program_id_index": 12
                    },
                    {
                      "accounts": [
                        2,
                        0,
                        0
                      ],
                      "data": "A",
                      "program_id_index": 11
                    },
                    {
                      "accounts": [
                        0,
                        14
                      ],
                      "data": "3Bxs4cD31A6ijfd1",
                      "program_id_index": 10
                    },
                    {
                      "data": "YDQxKHF5Jmd",
                      "program_id_index": 13
                    },
                    {
                      "accounts": [
                        0,
                        15
                      ],
                      "data": "3Bxs4NMRjdEwjxAj",
                      "program_id_index": 10
                    },
                    {
                      "accounts": [
                        0,
                        6
                      ],
                      "data": "3Bxs4Be45zcnCeU7",
                      "program_id_index": 10
                    }
                  ],
                  "versioned": true,
                  "account_keys": [
                    "CSKoEbVEM77UzfQrGJztDRckKSGAuosveaVZBshpWFbp",
                    "nwZiMQdNB4J2GR2j2tU6UMNaSULMozCzGcu6fAdhCYQ",
                    "GBsJEaN5CzJpXFDnUmAbgtAFKrUFgER2srSYwdS3ps7G",
                    "DYDJzUQD8QD2e7aLWpHvEc8qJ8xHCyxC6UxT6e3awEGz",
                    "2dXStdtWs8Z9eLJN8tBcXuAfvGHk9M4eexgXAmqYS5GJ",
                    "4fTBwSnfTDEHjB9GL2hSVDny1gWSh4C4doi7etZJMTk1",
                    "ADaUMid9yfUytqMBgopwjb2DTLSokTSzL1zt6iGPaS49",
                    "ComputeBudget***************************111",
                    "AFW9KCZtmtMWuhuLkF5mLY9wsk7SZrpZmuKijzcQ51Ni",
                    "38Y1REzZU3zFQUqBighTYeAUe7YL1o7AwpqRKMJ1pump",
                    "***************************11111",
                    "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
                    "675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8",
                    "4pP8eDKACuV7T2rbFPE8CHxGKDYAzSdRsdMsGvz2k4oc"
                  ],
                  "recent_blockhash": "2VUYmQHCYLDgzNXJAg2j2gRCwNUGicRfX7dN489zu2Zb",
                  "address_table_lookups": [
                    {
                      "account_key": "3n1rAmFcAkuQu2oiGJ2kbninARtNqfnc5RaC9j7rRSZC",
                      "writable_indexes": [
                        18,
                        19
                      ],
                      "readonly_indexes": [
                        10,
                        12,
                        0,
                        7
                      ]
                    }
                  ]
                }
              ]
            }
          ],
          "meta": [
            {
              "fee": 105000,
              "pre_balances": [
                **********,
                2039280,
                0,
                6124800,
                ***********,
                2039280,
                3557661,
                1,
                1398960,
                1461600,
                1,
                *********,
                1141440,
                1398960,
                *************,
                *************,
                *********,
                1009200,
                ************,
                ***********
              ],
              "post_balances": [
                3325299018,
                2039280,
                0,
                6124800,
                81288987830,
                2039280,
                5357661,
                1,
                1398960,
                1461600,
                1,
                *********,
                1141440,
                1398960,
                *************,
                *************,
                *********,
                1009200,
                ************,
                ***********
              ],
              "inner_instructions": [
                {
                  "index": 5,
                  "instructions": [
                    {
                      "accounts": [
                        1,
                        5,
                        0
                      ],
                      "data": "3qeESBupTJmd",
                      "program_id_index": 11
                    },
                    {
                      "accounts": [
                        4,
                        2,
                        19
                      ],
                      "data": "3JjtkUS7PRDy",
                      "program_id_index": 11
                    }
                  ]
                }
              ],
              "inner_instructions_none": false,
              "log_messages": [
                "Program ComputeBudget***************************111 invoke [1]",
                "Program ComputeBudget***************************111 success",
                "Program ComputeBudget***************************111 invoke [1]",
                "Program ComputeBudget***************************111 success",
                "Program AFW9KCZtmtMWuhuLkF5mLY9wsk7SZrpZmuKijzcQ51Ni invoke [1]",
                "Program log: Instruction: Initialize",
                "Program AFW9KCZtmtMWuhuLkF5mLY9wsk7SZrpZmuKijzcQ51Ni consumed 7919 of 56375 compute units",
                "Program AFW9KCZtmtMWuhuLkF5mLY9wsk7SZrpZmuKijzcQ51Ni success",
                "Program ***************************11111 invoke [1]",
                "Program ***************************11111 success",
                "Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [1]",
                "Program log: Instruction: InitializeAccount",
                "Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 3443 of 48306 compute units",
                "Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success",
                "Program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8 invoke [1]",
                "Program log: ray_log: A9hUarEFAAAABjdoAAAAAAABAAAAAAAAANhUarEFAAAA5fmg7RIAAAAsAc0HusMAAB+TjAAAAAAA",
                "Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]",
                "Program log: Instruction: Transfer",
                "Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 4645 of 27551 compute units",
                "Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success",
                "Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]",
                "Program log: Instruction: Transfer",
                "Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 4736 of 19925 compute units",
                "Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success",
                "Program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8 consumed 30541 of 44863 compute units",
                "Program 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8 success",
                "Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [1]",
                "Program log: Instruction: CloseAccount",
                "Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 2915 of 14322 compute units",
                "Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success",
                "Program ***************************11111 invoke [1]",
                "Program ***************************11111 success",
                "Program 4pP8eDKACuV7T2rbFPE8CHxGKDYAzSdRsdMsGvz2k4oc invoke [1]",
                "Program log: Received timestamp: **********",
                "Program log: Current timestamp: **********",
                "Program log: The provided timestamp is valid.",
                "Program 4pP8eDKACuV7T2rbFPE8CHxGKDYAzSdRsdMsGvz2k4oc consumed 1661 of 11257 compute units",
                "Program 4pP8eDKACuV7T2rbFPE8CHxGKDYAzSdRsdMsGvz2k4oc success",
                "Program ***************************11111 invoke [1]",
                "Program ***************************11111 success",
                "Program ***************************11111 invoke [1]",
                "Program ***************************11111 success"
              ],
              "log_messages_none": false,
              "pre_token_balances": [
                {
                  "mint": "38Y1REzZU3zFQUqBighTYeAUe7YL1o7AwpqRKMJ1pump",
                  "owner": "CSKoEbVEM77UzfQrGJztDRckKSGAuosveaVZBshpWFbp",
                  "account_index": 1,
                  "ui_token_amount": {
                    "decimals": 6,
                    "amount": "***********",
                    "ui_amount": 24451.372248,
                    "ui_amount_string": "24451.372248"
                  },
                  "program_id": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"
                },
                {
                  "mint": "So************************************11112",
                  "owner": "5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1",
                  "account_index": 4,
                  "ui_token_amount": {
                    "decimals": 9,
                    "amount": "***********",
                    "ui_amount": 81.*********,
                    "ui_amount_string": "81.*********"
                  },
                  "program_id": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"
                },
                {
                  "mint": "38Y1REzZU3zFQUqBighTYeAUe7YL1o7AwpqRKMJ1pump",
                  "owner": "5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1",
                  "account_index": 5,
                  "ui_token_amount": {
                    "decimals": 6,
                    "amount": "*********209068",
                    "ui_amount": *********.209068,
                    "ui_amount_string": "*********.209068"
                  },
                  "program_id": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"
                }
              ],
              "post_token_balances": [
                {
                  "mint": "38Y1REzZU3zFQUqBighTYeAUe7YL1o7AwpqRKMJ1pump",
                  "owner": "CSKoEbVEM77UzfQrGJztDRckKSGAuosveaVZBshpWFbp",
                  "account_index": 1,
                  "ui_token_amount": {
                    "decimals": 6,
                    "amount": "0",
                    "ui_amount": 0,
                    "ui_amount_string": "0"
                  },
                  "program_id": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"
                },
                {
                  "mint": "So************************************11112",
                  "owner": "5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1",
                  "account_index": 4,
                  "ui_token_amount": {
                    "decimals": 9,
                    "amount": "***********",
                    "ui_amount": 81.********,
                    "ui_amount_string": "81.********"
                  },
                  "program_id": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"
                },
                {
                  "mint": "38Y1REzZU3zFQUqBighTYeAUe7YL1o7AwpqRKMJ1pump",
                  "owner": "5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1",
                  "account_index": 5,
                  "ui_token_amount": {
                    "decimals": 6,
                    "amount": "*********581316",
                    "ui_amount": *********.581316,
                    "ui_amount_string": "*********.581316"
                  },
                  "program_id": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"
                }
              ],
              "loaded_writable_addresses": [
                "9RYJ3qr5eU5xAooqVcbmdeusjcViL5Nkiq7Gske3tiKq",
                "28KqHiudrpzfVkVWQ1jztQ2Aarf4W3CvTitjWEqTCkpA"
              ],
              "loaded_readonly_addresses": [
                "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL",
                "SysvarRent***************************111111",
                "So************************************11112",
                "5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1"
              ],
              "return_data_none": true,
              "compute_units_consumed": 47379
            }
          ],
          "index": 783,
          "is_vote": false
        }
      ],
      "slot": 325625861,
      "network": "SOLANA_MAINNET"
    }
  }`;

(async () => {
    try {
        
        //const tokenCA="ADDMcQw752RiCDRvuDBKurydHyPmrmRgFRntSkMstXVv";
        const tx = JSON.parse(json);

        const signature=tx?.event?.transaction[0]?.signature || undefined
        //signature ="5ZKBvVeEmQAR2R9dGDiJWoUNTwmagJJKwpTZVE3xykzXTDD9KtavaTLifegABCe2vU4Gmj3YR4dPEFv1DMKAgEnR"; // buy
        //signature = "PFdYNjcJFviDTqyoxnVL7h8GpkzdNtZpJovrZdqaYKgHeHWQ1Qg44LNzKzYtyZg5Yn44oh2SgVvdea7hRSxMB2v"; // sell

        if (signature){

            const redis = new Redis({host: REDIS_URL, port: REDIS_PORT, password: REDIS_PASSWORD});
            console.log(await processSwapTransaction(redis,signature));

            redis.disconnect();
        }
        process.exit(0);
        
        

    } catch (error) {
        console.error('Error:', error);
    }
})();