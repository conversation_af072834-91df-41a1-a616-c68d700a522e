import {
    <PERSON>Key,
    Connection,
    
  } from "@solana/web3.js";
  import {   PUMP_FUN_PROGRAM } from "../common/solana";

  
  import { RPC_URL } from "../common/config";   


  import { ASSOCIATED_TOKEN_PROGRAM_ID } from "@solana/spl-token";

  export function getBondingCurveAddresses(mintPublicKey: string): { bondingCurve: PublicKey, associatedBondingCurve: PublicKey } {
    // Convert the base58 string to PublicKey
    const mintPubkey = new PublicKey(mintPublicKey);
    
    // Derive bonding curve address
    const [bondingCurve] = PublicKey.findProgramAddressSync(
      [
        Buffer.from("bonding-curve"),
        mintPubkey.toBuffer()
      ],
      PUMP_FUN_PROGRAM
    );
    
    const PUMP_FUN_ACCOUNT = new PublicKey("Ce6TQqeHC9p8KetsN6JsjHK7UTZk7nasjjnr7XxXp9F1")

    // Derive associated bonding curve address
    // const [associatedBondingCurve] = PublicKey.findProgramAddressSync(
    //   [
    //     bondingCurve.toBuffer(),
    //     PUMP_FUN_ACCOUNT.toBuffer(),
    //     mintPubkey.toBuffer()
    //   ],
    //   ASSOCIATED_TOKEN_PROGRAM_ID
    // );


    const [associatedBondingCurve] = PublicKey.findProgramAddressSync(
        [
            bondingCurve.toBuffer(),
            new PublicKey("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA").toBuffer(),
            mintPubkey.toBuffer()
        ],
        new PublicKey("ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL")
    );

    
    return {
      bondingCurve,
      associatedBondingCurve
    };
  }


  export async function getBuyInstruction(connection: Connection,  tokenAddress: string) {
    // const coinData = await getTokenData(tokenAddress);
    // if (!coinData) {
    //   console.error('Failed to retrieve coin data...');
    //   return;
    // }
    const { bondingCurve, associatedBondingCurve } = getBondingCurveAddresses(tokenAddress);
    console.log('boding assoc mint:',bondingCurve.toBase58(),associatedBondingCurve.toBase58(),tokenAddress);

    // Fetch only the bonding curve account data
   // const bondingCurveAccount = await connection.getAccountInfo(new PublicKey("GkqsQBj4YhX5M1eypuvpHSDWvDGjTJU8LjbPQpY1jMRS"));
   // console.log('bondingCurveAccount:',bondingCurveAccount);
    //if (!bondingCurveAccount) {
    //    console.error('Failed to retrieve bonding curve data...');
    //    return;
   // }

    //const virtualSolReserves = bondingCurveAccount.data.readBigUInt64LE(8);
    //const virtualTokenReserves = bondingCurveAccount.data.readBigUInt64LE(16);

    //console.log('virtualSolReserves:',virtualSolReserves);
    //console.log('virtualTokenReserves:',virtualTokenReserves);
    // Calculate the amount of SOL to send
  }
async function main() {
    const connection = new Connection(RPC_URL);
    await getBuyInstruction(connection, "CHhHE7XC6MHnkf1v1Pb8YZnkZADMZt5hzyZqY6LpNub6");
    console.log("ABC:", "8c8sTjQVspgMMDTQo3vkiHBk3EDJ6t9KKaPUGCpvcw6x");
}

main().catch(console.error);