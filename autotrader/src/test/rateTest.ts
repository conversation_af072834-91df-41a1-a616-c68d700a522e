import { performance } from 'perf_hooks';
import axios, { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';
import { config } from 'dotenv';

// Load environment variables
config();

// Types
interface RequestResult {
  duration: number;
  status: 'success' | 'error';
  errorDetails: ErrorDetails | null;
  timestamp: string;
}

interface ErrorDetails {
  code: string | number;
  message: string;
  details: unknown;
}

interface TestResult {
  targetRate: number;
  actualRate: number;
  requestsSent: number;
  successCount: number;
  errorCount: number;
  errors: ErrorDetails[];
}

interface RPCResponse {
  jsonrpc: '2.0';
  id: number;
  result?: unknown;
  error?: {
    code: number;
    message: string;
  };
}

// Configuration
const SOLANA_RPC_URL = 'https://rpc.ankr.com/solana/3a2ca3f89ce8d11cbaa43746a9e34a4f6ed020d7a1ef37a62065e8323b1f32ac';
const MIN_RATE = Number(process.env.MIN_RATE) || 50;
const MAX_RATE = Number(process.env.MAX_RATE) || 300;
const STEP_SIZE = Number(process.env.STEP_SIZE) || 25;
const TEST_DURATION = Number(process.env.TEST_DURATION) || 10;
const COOL_DOWN = Number(process.env.COOL_DOWN) || 5;

// Sample transaction signature - should be configurable
const SAMPLE_TX_SIG = process.env.SAMPLE_TX_SIG || 
  '4EPwLbZUPsALRq8zW1yqWN7YbEETLFBsJKQi6TRQ1KriWcVQh3SmYZ3gYxVXPS8M7uf6Nt6t3FxUbLGXS42WEMGr';

/**
 * Creates an RPC request for getTransaction
 */
function createGetTransactionRequest(signature: string): AxiosRequestConfig {
  return {
    method: 'POST',
    url: SOLANA_RPC_URL,
    headers: { 
      'Content-Type': 'application/json',
      'User-Agent': 'RateTest/1.0.0'
    },
    timeout: 30000, // 30 second timeout
    data: {
      jsonrpc: '2.0',
      id: Date.now(),
      method: 'getTransaction',
      params: [
        signature,
        { encoding: 'json', maxSupportedTransactionVersion: 0 }
      ]
    }
  };
}

/**
 * Sends a single request and returns statistics
 */
async function sendRequest(): Promise<RequestResult> {
  const startTime = performance.now();
  let status: 'success' | 'error' = 'success';
  let errorDetails: ErrorDetails | null = null;
  
  try {
    const response: AxiosResponse<RPCResponse> = await axios(createGetTransactionRequest(SAMPLE_TX_SIG));
    
    // Check for RPC-level errors
    if (response.data.error) {
      status = 'error';
      errorDetails = {
        code: response.data.error.code,
        message: response.data.error.message,
        details: response.data
      };
    }
  } catch (error) {
    status = 'error';
    if (axios.isAxiosError(error)) {
      const axiosError = error as AxiosError;
      errorDetails = {
        code: axiosError.response?.status || 'unknown',
        message: axiosError.message,
        details: axiosError.response?.data || 'No details available'
      };
    } else {
      errorDetails = {
        code: 'unknown',
        message: error instanceof Error ? error.message : 'Unknown error',
        details: error
      };
    }
  }
  
  const endTime = performance.now();
  return {
    duration: endTime - startTime,
    status,
    errorDetails,
    timestamp: new Date().toISOString()
  };
}

/**
 * Run the test at a specific rate for a given duration
 */
async function runRateTest(requestsPerSecond: number): Promise<TestResult> {
  console.log(`\n--------------------------------------`);
  console.log(`Starting test at ${requestsPerSecond} requests per second...`);
  
  const interval = 1000 / requestsPerSecond;
  const results: Promise<RequestResult>[] = [];
  const errors: ErrorDetails[] = [];
  const startTime = performance.now();
  let requestsSent = 0;
  
  return new Promise((resolve) => {
    const testInterval = setInterval(() => {
      const elapsed = performance.now() - startTime;
      
      if (elapsed >= TEST_DURATION * 1000) {
        clearInterval(testInterval);
        
        void Promise.all(results).then((completedResults) => {
          const actualResults = completedResults.filter(Boolean);
          const successCount = actualResults.filter(r => r.status === 'success').length;
          const errorCount = actualResults.filter(r => r.status === 'error').length;
          
          const totalElapsed = performance.now() - startTime;
          const actualRate = (requestsSent * 1000) / totalElapsed;
          
          console.log(`Test completed at ${requestsPerSecond} req/s target:`);
          console.log(`- Requests sent: ${requestsSent}`);
          console.log(`- Successful: ${successCount}`);
          console.log(`- Failed: ${errorCount}`);
          console.log(`- Actual rate achieved: ${actualRate.toFixed(2)} req/s`);
          
          if (errorCount > 0) {
            console.log(`- Most common error: ${getMostCommonError(actualResults)}`);
          }
          
          resolve({
            targetRate: requestsPerSecond,
            actualRate,
            requestsSent,
            successCount,
            errorCount,
            errors: errors.slice(0, 5)
          });
        });
      } else {
        requestsSent++;
        const requestPromise = sendRequest().then(result => {
          if (result.status === 'error' && result.errorDetails) {
            errors.push(result.errorDetails);
          }
          return result;
        });
        results.push(requestPromise);
      }
    }, interval);
  });
}

/**
 * Find the most common error in results
 */
function getMostCommonError(results: RequestResult[]): string {
  const errorResults = results.filter(r => r.status === 'error' && r.errorDetails);
  if (errorResults.length === 0) return 'None';
  
  const errorCounts = new Map<string, number>();
  
  errorResults.forEach(r => {
    if (!r.errorDetails) return;
    const key = r.errorDetails.code.toString();
    errorCounts.set(key, (errorCounts.get(key) || 0) + 1);
  });
  
  let maxCount = 0;
  let mostCommonError = 'unknown';
  
  errorCounts.forEach((count, error) => {
    if (count > maxCount) {
      maxCount = count;
      mostCommonError = error;
    }
  });
  
  return `${mostCommonError} (${maxCount} occurrences)`;
}

/**
 * Run the complete test suite
 */
async function runRateLimitTest(): Promise<void> {
  try {
    console.log('Starting Solana RPC Rate Limit Test');
    console.log(`Target: From ${MIN_RATE} to ${MAX_RATE} requests per second`);
    console.log(`Test duration per rate: ${TEST_DURATION} seconds`);
    console.log(`Cool down between tests: ${COOL_DOWN} seconds`);
    console.log(`RPC Endpoint: ${SOLANA_RPC_URL}`);
    console.log('--------------------------------------');
    
    // Verify connection
    console.log('Sending test request to verify connection...');
    const testResult = await sendRequest();
    if (testResult.status === 'error') {
      throw new Error(`Initial test request failed: ${JSON.stringify(testResult.errorDetails)}`);
    }
    console.log('Connection verified successfully!');
    
    // Run tests with increasing rates
    for (let rate = MIN_RATE; rate <= MAX_RATE; rate += STEP_SIZE) {
      const result = await runRateTest(rate);
      
      // Check if we've hit a significant error rate
      if (result.errorCount > result.requestsSent * 0.25) {
        console.log('\n⚠️ High error rate detected - likely reached rate limit');
        break;
      }
      
      // Cool down between tests
      if (rate < MAX_RATE) {
        console.log(`\nCooling down for ${COOL_DOWN} seconds...`);
        await new Promise(resolve => setTimeout(resolve, COOL_DOWN * 1000));
      }
    }
  } catch (error) {
    console.error('Test failed:', error instanceof Error ? error.message : error);
    process.exit(1);
  }
}

// Run the test
void runRateLimitTest();
