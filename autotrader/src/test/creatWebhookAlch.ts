//const api_key = "PaLGQLrg9BMnCLln8Orqsjvi8j7qXP9o"; 

import { removeAddressFromWebhook } from "../common/alchemy-webhook";


let address = "ADDMcQw752RiCDRvuDBKurydHyPmrmRgFRntSkMstXVv";
// eslint-disable-next-line @typescript-eslint/no-require-imports
require('dotenv').config();


// Example usage:
(async () => {
    try {
        address ="2PLSeuJ7SVW9HWM9jtCH7kmAztHp2qvef7MpJn9jdKz2" ;   
        //console.log(await addAddressToWebhook(api_key, address, webhook_id));
        console.log(await removeAddressFromWebhook(address));
        //const response = await createWebhook(api_key,app_id, "ADDMcQw752RiCDRvuDBKurydHyPmrmRgFRntSkMstXVv", webhook_name);
        //const data = await response.json();
        //console.log(data);


    } catch (error) {
        console.error('Error:', error);
    }
})();