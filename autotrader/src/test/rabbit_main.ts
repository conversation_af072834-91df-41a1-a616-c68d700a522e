import { EventEmitter } from 'events';

const dataBuffer = new Map<string, string>();
const eventEmitter = new EventEmitter();

async function grpc(): Promise<void> {
    while (true) {
        await new Promise(resolve => setTimeout(resolve, 2000));
        const msg=`msg-${Date.now()}`
        console.log(`[Consumer] Consuming Message: ${msg}`);
        dataBuffer.set(`${msg}1`, "tesst");
        dataBuffer.set(`${msg}2`, "tesst");
        dataBuffer.set(`${msg}3`, "tesst");
        dataBuffer.set(`${msg}4`, "tesst");
    }


}

async function mainLoop(): Promise<void> {
    while (true) {
        await new Promise(resolve => setTimeout(resolve, 1));

        // Check if new messages are available
        if (dataBuffer.size > 0) {
            console.log(`Main Loop: Processing ${dataBuffer.size} messages`);
            for (const [key, value] of dataBuffer.entries()) {
                console.log(Date.now());
                console.log(`[Main Loop] Processing Message: ${key} -> ${value}`);
                dataBuffer.delete(key);  // Remove after processing
            }
        } 
    }
}

function setupListeners(): void {
    eventEmitter.on('newMessage', (message) => {
        console.log(`[Event] New message received: ${message}`);
    });
}

async function start(): Promise<void> {
    setupListeners();
    console.log("Starting application...");

    // Run consumer and main loop concurrently
    await Promise.all([grpc(), mainLoop()]);
}

start().catch(console.error);
