// test-pool.ts
import https from "https";
import fetch from "node-fetch";

const agent = new https.Agent({
  keepAlive: true,
  keepAliveMsecs: 60000 // Set keepAlive timeout to 1 minute (60000 ms)
});

async function sleep(ms: number): Promise<void> {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

async function run() {
  const jitoEndpoint = 'https://mainnet.block-engine.jito.wtf/api/v1/transactions';
  
  for (let i = 1; i <= 10; i++) {
    const start = Date.now();
    try {
      // Using fetch with the example structure
      const response = await fetch(jitoEndpoint, {
        method: 'POST',
        body: JSON.stringify({
          jsonrpc: '2.0',
          id: i,
          method: 'sendTransaction',
          params: ["3kwUogs3Xs3u8ocMX1gaXaLLsy6sXyYNJPoX1vZ2uZ9jXo2QX8rSxb8Sge38qoxK1Hk3xGJEQ8ZvoNHBmtFo8ZcuRYCF7zNZ3HkYMrNCG67BgTJLHPDPqSv4hJrLqUgEqw56VcL1zRhYr98TKyHSuxbzgxFwZZoKK1UjgbVxpph5kKizKmEgPvXZ3nnE6Zt3ZH9vTzTfjpwcE6XSaga9DXujUk3Xv5n3LVsigc52E4wc8XojG6bcrwzusKgT9DAWdDSiKE1QVh3qvaFaGxzoxCHovz49fdqxYKTvBpM2Dxtsxwqo57eKt9zuAJZXB7vcJxDYRKDkeBDtXuFD1yzTKZNTBoDUaPRHXscuwiVvy92Zk2nEry83mp5xuGVRYoRBeQzGDB1mzt21wb1CwvxpPAdf8SLkEVKVj3E9WJtY6g3wxiuUzMn6jnGBM8xGCVrjsmXPbxFKjsYTy97C2ju9iw6EfQLcM6teJ2M61b82NjgwptUuKPB98439GsSsH6siTA78WgwuxLpm5gNBrPy8TXXUZbD3VdnEGFh1Gxj1RRkEhdZhfZbxoFrcWzzVZD8XxwNKXmJDpFNNMMPS4NKQz7kaAAgpZaAdLhaqY5dsZ5K3sqpxw15sDC13eMQ9rzU64MwijM7ZfGzMbhofXdwswWzvnzvYRE7ixngVpUTtofsZx5ASvQ6ViN49DCTvuQNhDP9w7QrN9yDBbezQJ5GMqyEZU5tLsgFBMLMa3WJzyChYoVsne9XxVQ424EyuehrFLcMUnmHa7YFDh4S8EKjoGeGqN"]
        }),
        headers: { 'Content-Type': 'application/json' },
        agent: agent,
      });
      
      const time = Date.now() - start;
      
      // Get the response body as text
      const responseBody = await response.text();
      
      console.log(`Req ${i}: ${response.status} in ${time}ms`);
      console.log(`Response body: ${responseBody}`);
      
    } catch (error) {
      const time = Date.now() - start;
      console.log(`Req ${i}: ${time}ms :error ${error}`);
    }
    
    // Sleep between requests
    if (i < 10) {
      await sleep(5000);
    }
  }
}

run();
