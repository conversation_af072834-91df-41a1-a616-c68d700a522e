#!/usr/bin/env python3
import os
import asyncio
import json
import discord
from discord.ext import commands
from discord.ui import But<PERSON>, View
from dotenv import load_dotenv
import aio_pika
from modules.config import *
from modules.bot import get_pump_data_short  # Ensure this exists and works correctly
from mysql.connector import pooling
import modules.utils as utils
from modules.config import *
import modules.db as db
import modules.bot as botmodule
import modules.image as image
import modules.config
import requests
# Load environment variables
load_dotenv()
TOKEN = os.getenv('DISCORD_TOKEN')
GUILD = os.getenv('DISCORD_GUILD')
CHANNEL_ID1 = 1299807799391420556  # Replace with your actual channel ID
CHANNEL_ID2 = 1299809146450870434  # Replace with your actual channel ID

# Define the bot's intents and initialize the bot
intents = discord.Intents.all()
bot = commands.Bot(command_prefix="?", intents=intents)

# RabbitMQ server configuration
RABBITMQ_SERVER = "pumpfun.mooo.com"
RABBITMQ_QUEUE = "uniq_website"
RABBITMQ_USER = "pump"
RABBITMQ_PASS = "pump2pump"



MYSQL_SERVER="pumpfun.mooo.com"
MYSQL_PORT=3306
MYSQL_USER="pump2"
MYSQL_PASSWORD="pump2"
MYSQL_DB="pump"
MYSQL_POOL="mypool"
MYSQL_POOL_SIZE="10"


pool = pooling.MySQLConnectionPool(
    pool_name=MYSQL_POOL, 
    user=MYSQL_USER,
    password=MYSQL_PASSWORD,
    host=MYSQL_SERVER,
    db=MYSQL_DB)

modules.config.pool=pool



    
class PumpDataView(View):
    def __init__(self):
        super().__init__()
        self.mint = None  # Initialize mint as None

    def setmint(self, mint):
        self.mint = mint

        # Add buttons dynamically after mint is set
        self.add_item(Button(label="BullX", style=discord.ButtonStyle.link, url=f"https://backup.bullx.io/terminal?chainId=1399811149&address={self.mint}"))
        self.add_item(Button(label="MevX.io", style=discord.ButtonStyle.link, url=f"https://mevx.io/solana/{self.mint}"))
        self.add_item(Button(label="Photon", style=discord.ButtonStyle.link, url=f"https://photon-sol.tinyastro.io/en/lp/{self.mint}"))



async def process_mint(mint):
    try:
        uniq = db.get_uniq_website(pool, mint)
        if uniq:
            bot_data = await get_pump_data_short(mint)


        # "ca":data["mint"],
        # "socials":format_social_links(data),
        # "symbol":data["symbol"],
        # "name":data["name"],
        # "image_uri":data["image_uri"],
        # "mcap":data["mcap"],
        # "liquidity":liquidity,
        # "age":human_time_diff(data['created_timestamp']),
        # "thumnail":data["image_uri"]



            embed = discord.Embed(title=f"{bot_data.get('symbol', '')} ({bot_data.get('name', '')})", color=discord.Color.blue())

            # Build the description with the new data
            embed.description = (
                f":key: CA: `{bot_data.get('ca', '')}`\n"
                f":bird: Socials: {bot_data.get('socials', '')}\n"
                f":chart_with_upwards_trend: MCap: `{bot_data.get('mcap', '')}` "
                f":droplet: Liq: `{bot_data.get('liquidity', '')}` "
                f":clock1: Age: `{bot_data.get('age', '')}` "
             
            )
            
            get_uniq_image=db.get_uniq_image(pool, mint)
            if get_uniq_image:
                if get_uniq_image > 1:
                    uniq_image=f"Has {get_uniq_image-1} copies"
                else:
                    uniq_image="No copies"

            website=bot_data.get('website').replace("https://","").replace("http://","")
            if bot_data.get('twitter'):
                twitter=bot_data.get('twitter').replace("https://","").replace("http://","")
            else:
                twitter=""
            if bot_data.get('telegram'):
                telegram=bot_data.get('telegram').replace("https://","").replace("http://","")
            else:
                telegram=""
            embed.add_field(name="Website", value=f"[{website}](<{bot_data['website']}>)", inline=True)
            embed.add_field(name="Is Reachable", value=f"{bot_data['reachable']}", inline=True)
            embed.add_field(name="X.com", value=f"[{twitter}](<{bot_data['twitter']}>)", inline=False)
            if bot_data.get('telegram'):
                embed.add_field(name="Telegram", value=f"[{telegram}](<{bot_data['telegram']}>)", inline=False)
            # Add thumbnail if available
            embed.add_field(name="Is CA found", value=f"{bot_data['cafound']}", inline=True)
            embed.add_field(name="Image copies", value=f"{uniq_image}", inline=True)
            embed.add_field(name="Solana or Pump found", value=f"{bot_data['solana']}", inline=False)
            thumbnail_url = bot_data.get('thumnail')
            if thumbnail_url:
                embed.set_thumbnail(url=thumbnail_url)

            # Send the embed message
            view = PumpDataView()
            view.setmint(mint)

            channel1 = bot.get_channel(CHANNEL_ID1)
            if channel1:
                await channel1.send(embed=embed, view=view)
            else:
                print('Channel1 not found.')

            if bot_data['solana'] =="Found Solana or Pump in website text":  
                channel2 = bot.get_channel(CHANNEL_ID2)
                if channel2:
                    await channel2.send(embed=embed, view=view)
                else:
                    print('Channel1 not found.')

        else:
            print(f"Uniq website not found for mint {mint}")
    except Exception as e:
        import traceback
        traceback.print_exc()
        print(f"Error in process_mint: {e}")



async def consume_rabbitmq():
    try:
        connection = await aio_pika.connect_robust(
            host=RABBITMQ_SERVER,
            login=RABBITMQ_USER,
            password=RABBITMQ_PASS,
        )
        channel = await connection.channel()
        queue = await channel.declare_queue(RABBITMQ_QUEUE, durable=True)

        print(f"Connected to RabbitMQ. Waiting for messages in '{RABBITMQ_QUEUE}' queue...")

        async with queue.iterator() as queue_iter:
            async for message in queue_iter:
                async with message.process():
                    msg_body = json.loads(message.body)
                    mint = msg_body.get("mint")
                    if mint:
                        await process_mint(mint)
                    else:
                        print("Mint value not found in message.")
    except Exception as e:
        print(f"Error in consume_rabbitmq: {e}")

@bot.event
async def on_ready():
    print(f'Logged in as {bot.user.name}')
    channel1 = bot.get_channel(CHANNEL_ID1)
    channel2 = bot.get_channel(CHANNEL_ID2)
    if channel1:
        pass
        #await channel1.send('Hello, Discord1!')
    else:
        print('Channel1 not found.')
    if channel2:
        pass
        #await channel2.send('Hello, Discord2!')
    else:
        print('Channel2 not found.')
    bot.loop.create_task(consume_rabbitmq())
    print('Bot is ready and started consuming RabbitMQ messages.')

# Run the bot
bot.run(TOKEN)
