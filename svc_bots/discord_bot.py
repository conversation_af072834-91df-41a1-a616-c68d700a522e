#!/usr/bin/env python3
import os
from dotenv import load_dotenv
import discord
from discord.ext import commands
from discord import app_commands
from discord.ui import Button, View
import asyncio

# Import your modules
from modules.config import *
from modules.bot import get_pump_data

# Load environment variables
load_dotenv()
TOKEN = os.getenv('DISCORD_TOKEN')
GUILD = os.getenv('DISCORD_GUILD')

# Define the bot's intents and initialize the bot
intents = discord.Intents.all()
bot = commands.Bot(command_prefix="?", intents=intents)


class PumpDataView(View):
    def __init__(self):
        super().__init__()
        self.mint = None  # Initialize mint as None

    def setmint(self, mint):
        self.mint = mint

        # Add buttons dynamically after mint is set
        self.add_item(Button(label="BullX", style=discord.ButtonStyle.link, url=f"https://bullx.io/terminal?chainId=1399811149&address={self.mint}"))
        self.add_item(Button(label="MevX.io", style=discord.ButtonStyle.link, url=f"https://mevx.io/solana/{self.mint}"))
        self.add_item(Button(label="Photon", style=discord.ButtonStyle.link, url=f"https://photon-sol.tinyastro.io/en/lp/{self.mint}"))



# Event listener for when the bot is ready
@bot.event
async def on_ready():
    print(f"Bot is ready! Logged in as {bot.user} (ID: {bot.user.id})")
    try:
        # Sync the slash commands with Discord
        synced = await bot.tree.sync()
        print(f"Synced {len(synced)} command(s) with Discord.")
    except Exception as e:
        print(f"Failed to sync commands: {e}")

# Simple ping command using traditional prefix command
@bot.command()
async def ping(ctx, *args):
    await ctx.send("PONG!")

# Define the slash command for /token
@bot.tree.command(name="token", description="Get pump data for a token")
@app_commands.describe(arg='Token identifier')
async def token(interaction: discord.Interaction, arg: str):
    # Defer the response to allow time for processing
    await interaction.response.defer()
    
    try:

        mint=arg

        bot_data = await get_pump_data(arg)
        # if len(data) <= 2000:
        #     view = PumpDataView()
        #     view.setmint(mint)

        #     await interaction.edit_original_response(content=data,view=view)
        # else:
        #     await interaction.edit_original_response(content="Data is too long, sending in multiple messages...")
        #     for chunk in [data[i:i+2000] for i in range(0, len(data), 2000)]:
        #         await interaction.followup.send(chunk)

        # Create embed


        if bot_data["raydium_pool"] == "Yes":
            text=f"**{bot_data['name']}** has migrated to Raydium pool. :rocket:"
            await interaction.edit_original_response(content=text)
        else:


            embed = discord.Embed(title=f"{bot_data['symbol']} ({bot_data['name']})", color=discord.Color.blue())

            # Build the description with the new data
            embed.description = (
                f":key: CA: `{bot_data['ca']}`\n"
                f":bird: Socials: {bot_data['socials']}\n"
                f":link: Dex paid: `{bot_data['dex']}`\n"
                f":chart_with_upwards_trend: MCap: `{bot_data['mcap']}` "
                f":droplet: Liq: `{bot_data['liquidity']}` "
                f":dollar: Vol: `{bot_data['volume']}`\n"
                f":clock1: Age: `{bot_data['age']}` "
                f":crown: KOTH: `{bot_data['koth']}` "
                f":earth_africa: Migrated: `{bot_data['migrated']}`\n"
                f":clapper: StreamingLive: `{bot_data['is_currently_live']}`\n"
                f":speaking_head: Comments: `{bot_data['total_comments']}` Fake: `{bot_data['real_comments']}`\n"
                f":man_technologist: Dev Holdings: `{bot_data['dev_holdings']}`\n"
                f":first_place: Trades in First Slot: `{bot_data['trades_in_first_slot']}` ($`{bot_data['trade_amount_in_first_slot']}`)\n"
                f":bar_chart: Total Trades: :b: `{bot_data['buys']}` "
                f":regional_indicator_s: `{bot_data['sells']}`\n"
                f":inbox_tray: Wallets Received: `{bot_data['wallets_recieved']}`\n"
                f":outbox_tray: Wallets Sent: `{bot_data['wallets_sent']}`\n"
                f":people_holding_hands: Current Holders: `{bot_data['wallets_in']}` (`{bot_data['unique_users']}`)\n"
                f":baby: New Wallets Holding: `{bot_data['new_wallets_still_in']}` (`{bot_data['new_wallet']}`)"
                f" Holding: (`{bot_data['new_wallets_pct']}%`)\n"
                f":keycap_ten: TOP 10 Holdings: (`{bot_data['top10_sum']}%`) \n"
                f"`{bot_data['top10']}`\n"
                f":moneybag: Top holders holding:\n"
                f"{bot_data['top_holders']}"
                f":timer: Execution Time: `{bot_data['execution_time']}` s"
            )
            # Add thumbnail
            embed.set_thumbnail(url=bot_data['thumnail'])

            # Send the embed message
            view = PumpDataView()
            view.setmint(mint)
            await interaction.edit_original_response(embed=embed,view=view)


        # embed = discord.Embed(title="Token Data", color=discord.Color.blue())
        
        # # Add your token data as part of the embed description
        # embed.set_thumbnail(url="https://ipfs.io/ipfs/QmPNz3TkLpfAFnfFd4BKpwnVDug4W1FKguFQ19wb65D3h3")
        # embed.description = (
        #     f":bird: **Price**: $0.0000646 :bird: **Price**: $0.0000646 :bird: **Price**: $0.0000646 \n"
        #     f"**Market Cap**: $64.50K\n"
        #     f"**Liquidity**: $36.30K\n"
        #     f"**Volume**: 3.17M\n"
        #     f"**Holders**: 797\n"
        #     f"**Age**: 11 days"
        # )




# bot_data={
#         "ca":data["mint"],
#         "socials":format_social_links(data),
#         "symbol":data["symbol"],
#         "name":data["name"],
#         "image_uri":data["image_uri"],
#         "mcap":data["mcap"],
#         "liquidity":liquidity,
#         "volume":convert_to_usd(trades['volume_sells']+trades['volume_buys']),
#         "age":human_time_diff(data['created_timestamp']),
#         "koth":human_time_diff(data['king_of_the_hill_timestamp']),
#         "migrated":data["raydium_pool"],
#         "is_currently_live":data['is_currently_live'],
#         "total_comments":replies['total_comments'],
#         "trade_users":replies['trade_users'],
#         "real_comments":replies['real_comments'],
#         "fresh_commenters":fresh_commenters,
#         "dev_holdings":dev_holdings,
#         "trades_in_first_slot":trades['trades_in_first_slog'],
#         "trade_amount_in_first_slot":round(trades['trade_amount_in_first+slog']/1e9*140,2),
#         "user_buys":trades['user_buys'],
#         "user_sells":trades['user_sells'],
#         "buys":trades['buys'],
#         "sells":trades['sells'],
#         "wallets_recieved":wallets_recieved,
#         "wallets_sent":wallets_sent,
#         "unique_users":len(trades['unique_users']),
#         "new_wallet":new_wallet,
#         "wallets_in":len(wallets_in[0]),
#         "new_wallets_still_in":len(new_wallets_still_in),
#         "new_wallets_pct":new_wallets_pct,
#         "top10_sum":round(top10_sum,2),
#         "top10":top10,
#         "thumnail":data["image_uri"],
#         "execution_time":round(execution_time,2)


#     }

#     return bot_data

    # data_old= f""":green_circle: **{data['symbol']}** ({data['name']})
    # > :key: CA: `{data['mint']}`
    # > :bird: Socials: {format_social_links(data)}  
    # > :link: Dex paid: `{dex}`
    # > :chart_with_upwards_trend: MCap: `{data["mcap"]}` :droplet:  Liquidity: `{liquidity}`  :dollar:  Volume: `{convert_to_usd(trades['volume_sells']+trades['volume_buys'])}` 
    # > :clock1:  Age: `{human_time_diff(data['created_timestamp'])}`  :crown: KOTH: `{human_time_diff(data['king_of_the_hill_timestamp'])}` :earth_africa: Migrated: `{data["raydium_pool"]}`
    # > :clapper:  StreamingLive: `{data['is_currently_live']}`
    # > :speaking_head: Comments: Total:`{replies['total_comments']}` Traders: `{replies['trade_users']}` Pump.fun(users): `{replies['real_comments']}` :ghost: Fake: `{fresh_commenters}`
    # > :man_technologist: Dev holdings: `{dev_holdings}`
    # > :first_place: Trades in 1 slot: `{trades['trades_in_first_slog']}`
    # > :coin: Trade amount in 1 slot: `{round(trades['trade_amount_in_first+slog']/1e9*140,2)}$`
    # > :bar_chart: Pump.fun user trades: :b: `{trades['user_buys']}` :regional_indicator_s:  `{trades['user_sells']}`. 
    # > :bar_chart: Total Trades:`{trades['buys']+trades['sells']}` :b: `{trades['buys']}` :regional_indicator_s:  `{trades['sells']}` 
    # > :inbox_tray: No of wallets recieved tranacactions from same outsider wallet: `{wallets_recieved}`
    # > :outbox_tray: Number or wallets  send fund to same outsider account: `{wallets_sent}`
    # > :man_standing: Uniq traders since mint: `{len(trades['unique_users'])}`
    # > :baby: New wallets since mint: `{new_wallet}`
    # > :people_holding_hands: current Holders: `{len(wallets_in)}`
    # > :baby: New wallets holding: `{len(new_wallets_still_in)}`  Toknens holding: (`{new_wallets_pct}%`)  
    # > :keycap_ten: TOP 10 holdings: (`{round(top10_sum,2)}%`)  Distribution: `{top10}`
    # Execution time:{round(execution_time,2)}s



        # data = await get_pump_data(arg)
        # if len(data) <= 2000:
        #     view = PumpDataView()
        #     view.setmint(mint)

        #     await interaction.edit_original_response(content=data,view=view)
        # else:
        #     await interaction.edit_original_response(content="Data is too long, sending in multiple messages...")
        #     for chunk in [data[i:i+2000] for i in range(0, len(data), 2000)]:
        #         await interaction.followup.send(chunk)
    except Exception as e:
        # Handle any exceptions and inform the user
        await interaction.edit_original_response(content=f"An error occurred: {e}")

# Run the bot
bot.run(TOKEN)
