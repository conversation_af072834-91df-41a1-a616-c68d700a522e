#!/usr/bin/env python3
import os
import asyncio
import json
import discord
from discord.ext import commands
from discord.ui import But<PERSON>, View
from dotenv import load_dotenv
import aio_pika
from modules.config import *
from modules.bot import get_pump_data_short  # Ensure this exists and works correctly
from mysql.connector import pooling
import modules.utils as utils
from modules.config import *
import modules.db as db
import modules.bot as botmodule
import modules.config
import time
import requests
from googletrans import Translator
import urllib.parse
import pyshorteners


# Load environment variables
load_dotenv()
TOKEN = os.getenv('DISCORD_TOKEN')
GUILD = os.getenv('DISCORD_GUILD')
CHANNEL_ID1 = 1310659253123678209  # Replace with your actual channel ID
CHANNEL_ID2 = 1299809146450870434  # Replace with your actual channel ID

# Define the bot's intents and initialize the bot
intents = discord.Intents.all()
bot = commands.Bot(command_prefix="?", intents=intents)

# RabbitMQ server configuration
RABBITMQ_SERVER = "pumpfun.mooo.com"
RABBITMQ_QUEUE = "instagram"
RABBITMQ_USER = "pump"
RABBITMQ_PASS = "pump2pump"



MYSQL_SERVER="pumpfun.mooo.com"
MYSQL_PORT=3306
MYSQL_USER="pump2"
MYSQL_PASSWORD="pump2"
MYSQL_DB="pump"
MYSQL_POOL="mypool"
MYSQL_POOL_SIZE="10"


pool = pooling.MySQLConnectionPool(
    pool_name=MYSQL_POOL, 
    user=MYSQL_USER,
    password=MYSQL_PASSWORD,
    host=MYSQL_SERVER,
    db=MYSQL_DB)

modules.config.pool=pool



    
class PumpDataView(View):
    def __init__(self):
        super().__init__()
        self.mint = None  # Initialize mint as None

    def setmint(self, mint):
        self.mint = mint

        # Add buttons dynamically after mint is set
        self.add_item(Button(label="BullX", style=discord.ButtonStyle.link, url=f"https://backup.bullx.io/terminal?chainId=1399811149&address={self.mint}"))
        self.add_item(Button(label="MevX.io", style=discord.ButtonStyle.link, url=f"https://mevx.io/solana/{self.mint}"))
        self.add_item(Button(label="Photon", style=discord.ButtonStyle.link, url=f"https://photon-sol.tinyastro.io/en/lp/{self.mint}"))



async def consume_rabbitmq():
    try:
        connection = await aio_pika.connect_robust(
            host=RABBITMQ_SERVER,
            login=RABBITMQ_USER,
            password=RABBITMQ_PASS,
        )
        channel = await connection.channel()
        queue = await channel.declare_queue(RABBITMQ_QUEUE, durable=True)

        print(f"Connected to RabbitMQ. Waiting for messages in '{RABBITMQ_QUEUE}' queue...")

        async with queue.iterator() as queue_iter:
            async for message in queue_iter:
                async with message.process():
                    post = json.loads(message.body)
                    if post:
                        await send_to_gg(post)
                    else:
                        print("Mint value not found in message.")
    except Exception as e:
        print(f"Error in consume_rabbitmq: {e}")


async def send_to_gg(data):
    translator = Translator()

    # Translate caption only if it's not in English
    original_caption = data["caption"]
    translated_caption = None
    try:
        # Detect the language of the caption
        detected_lang = translator.detect(original_caption).lang

        if detected_lang != "en":
            # Translate the caption if it's not English
            translation = translator.translate(original_caption, src="auto", dest="en")
            translated_caption = translation.text
        else:
            translated_caption = None  # No translation needed
    except Exception as e:
        print(f"Error in language detection/translation: {e}")
        translated_caption = None  # Fallback if detection/translation fails

    # Build the embed description based on whether translation occurred
    if translated_caption:
        description = (

            f"**Translated Caption:**\n{translated_caption}\n\n"
            f"**Posted at:** {data['taken_at']}"
        )
    else:
        description = (
            f"**Caption:**\n{original_caption}\n\n"
            f"**Posted at:** {data['taken_at']}"
        )

    # Embed setup
    embed = discord.Embed(
        description=description,
        color=discord.Color.orange()
    )

    # Adding the footer (to simulate the profile at the top left)
    embed.set_author(
        name=f"{data['full_name']} (@{data['username']})",
        icon_url=f"{data['profile_pic_url']}"  # Replace with the actual profile image URL
    )
    
    # Add the main image
    embed.set_image(url=f"{data['image_urls'][0]}")  # Replace with the actual main image URL

    video_url=None
    if data["video_urls"] and len(data["video_urls"]) > 0:
        video_url = data["video_urls"][0]  # Get the first video URL


    encoded_data = urllib.parse.urlencode({
        "code": data["code"],
        "link_url": f"https://www.instagram.com/p/{data['code']}/",
        "image_url": data["image_urls"][0],
        "video_url": video_url
    })

    s = pyshorteners.Shortener()
    short_url = s.tinyurl.short(f"https://kroocoin.xyz/test?{encoded_data}")

    # Create the button with the shortened URL
    createToken = Button(label="Create Token", url=short_url, style=discord.ButtonStyle.link)


    # Button setup
    #createToken = Button(label="Create Token", url=f"http://pumpfun.mooo.com:7777/instagram?{encoded_data}", style=discord.ButtonStyle.link)
    visitInstagram = Button(label="Link to Post", url=f"https://www.instagram.com/p/{data['code']}/", style=discord.ButtonStyle.link)
    view = View()
    view.add_item(visitInstagram)
    view.add_item(createToken)

    # Send the embed message with button
    channel1 = bot.get_channel(CHANNEL_ID1)
    if channel1:
        await channel1.send(embed=embed, view=view)

        # Send video link if available
        if data["video_urls"] and len(data["video_urls"]) > 0:
            video_url = data["video_urls"][0]  # Get the first video URL
            await channel1.send(f"[Watch the video here]({video_url})")
    else:
        print('Channel1 not found.')




        


@bot.event
async def on_ready():
    print(f'Logged in as {bot.user.name}')
    channel1 = bot.get_channel(CHANNEL_ID1)
    if channel1:
        pass
        #await channel1.send('Hello, Discord1!')
    else:
        print('Channel2 not found.')
    bot.loop.create_task(consume_rabbitmq())
    print('Bot is ready and started consuming RabbitMQ messages.')

# Run the bot
bot.run(TOKEN)
