// ==UserScript==
// @name         Extract Articles and Send to Discord (With Auto-Reload)
// @namespace    http://tampermonkey.net/
// @version      1.3
// @description  Extract articles, send data to a Discord webhook, and reload the page every 1 minute.
// <AUTHOR> Name
// @match        https://feedly.com/*/*
// @grant        none
// ==/UserScript==

(function () {
    'use strict';

    // Function to run the main logic
    function runScript() {
        const WEBHOOK_URL = "https://discord.com/api/webhooks/1314704884997820528/Cau_LyYrt7LxqqESTjEFrOscpcdA1GuCDGP62-pjyeIAgEOqQhomIKEM31Q8Nyq4-Omv";
        const PROCESSED_KEY = "processedArticles";
        const processedArticles = new Set(JSON.parse(localStorage.getItem(PROCESSED_KEY) || "[]"));

        function saveProcessedArticles() {
            localStorage.setItem(PROCESSED_KEY, JSON.stringify(Array.from(processedArticles)));
        }

        function processArticle(article) {
            console.log("Processing article...");

            // Extract required elements
            const titleElement = article.querySelector("a.EntryTitleLink");
            const imgElement = article.querySelector("[style*='background-image']");
            const sourceElement = article.querySelector(".EntryMetadataSource");
            const descriptionElement = article.querySelector(".aRpq1uFgOK5S67Q9FvlE");
            const previewElement = article.querySelector(".aRpq1uFgOK5S67Q9FvlE > div");

            // Extract values
            const title = titleElement ? titleElement.textContent.trim() : "No title";
            const link = titleElement ? titleElement.href : "No link";
            const imgStyle = imgElement ? imgElement.style.backgroundImage : "";
            const imgUrl = imgStyle ? imgStyle.match(/url\("(.+?)"\)/)?.[1] : "No image";
            const source = sourceElement ? sourceElement.textContent.trim() : "No source";
            const description = descriptionElement ? descriptionElement.textContent.trim() : "No description available.";
            const preview = previewElement ? previewElement.textContent.trim() : "No preview available.";

            // Generate unique ID for the article
            const articleId = link || title;
            if (processedArticles.has(articleId)) {
                console.log(`Article already processed: ${title}`);
                return;
            }

            // Mark as processed
            processedArticles.add(articleId);
            saveProcessedArticles();

            // Create Discord payload
            const payload = {
                content: null,
                embeds: [
                    {
                        title: title,
                        url: link,
                        description: `Source: ${source}\n\nDescription: ${description}\n\nPreview: ${preview}`,
                        image: imgUrl !== "No image" ? { url: imgUrl } : undefined,
                        color: 3447003,
                    },
                ],
            };

            // Send the data to Discord
            fetch(WEBHOOK_URL, {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify(payload),
            })
                .then((response) => {
                    if (response.ok) {
                        console.log(`Article sent: ${title}`);
                    } else {
                        console.error("Error sending article to Discord:", response.statusText);
                    }
                })
                .catch((error) => console.error("Error:", error));
        }

        // Recursive processing of nodes
        function processNode(node) {
            if (node.nodeType === 1 && node.tagName.toLowerCase() === "article") {
                processArticle(node);
            } else if (node.nodeType === 1) {
                const articles = node.querySelectorAll("article");
                articles.forEach((article) => processArticle(article));
            }
        }

        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    processNode(node);
                });
            });
        });

        observer.observe(document.body, { childList: true, subtree: true });

        // Process articles already present in the DOM
        document.querySelectorAll("article").forEach((article) => processArticle(article));
    }

    // Auto-reload the page every 1 minute (60,000 milliseconds)
    setTimeout(() => {
        console.log("Reloading page...");
        location.reload();
    }, 60000); // 60 seconds

    // Run the script after the entire page has loaded
    window.onload = runScript;
})();
