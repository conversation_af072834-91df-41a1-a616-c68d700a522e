// ==UserScript==
// @name         Track Unique Instagram Articles and Send to Discord with Translation
// @namespace    http://tampermonkey.net/
// @version      1.2
// @description  Track unique Instagram articles, store in localStorage, translate captions if needed, and send to a Discord channel
// <AUTHOR>
// @match        https://www.instagram.com/*
// @grant        none
// ==/UserScript==

(function () {
    'use strict';

    let DISCORD_WEBHOOK_URL = "https://discord.com/api/webhooks/1315092065280856127/_uqGWOGqQlZs1cfk4ZIdKZ1c6QrIVNfwuEGRdtvmzcdJVw0XpBq21ZOSIToAGmG_e4TZ";
    let NEWBORN_DISCORD_WEBHOOK_URL = "https://discord.com/api/webhooks/1316905001796370442/S74Ppa1D8aWIdezb0psVbhRF6jo2CLZsTxnrP7FKVxPXN_NYZyOiXn0bitZz3FkHGbAv";
    let DIED_DISCORD_WEBHOOK_URL ="https://discord.com/api/webhooks/1318548737127092325/bH0Mi77D9hRJFk7YJf0EOBptmLdcWVlFVo25QsVaT1Wpb2DUd-uEzy4GxSzz0EYYtbdf";

    // Load tracked articles from localStorage
    const loadTrackedArticles = () => JSON.parse(localStorage.getItem("trackedArticles")) || [];

    function sendPushoverNotification(message,user,token) {
        const pushoverUrl = "https://api.pushover.net/1/messages.json";
        const data = {
            token: token,
            user: user,
           // token: "arbs7ccq98rqoz78pagtcqyc523hro",
            //user: "utmxhixkgtchrdahx3tfj6sxgfkbw5",
            message: message
        };

        fetch(pushoverUrl, {
            method: "POST",
            headers: {
                "Content-Type": "application/x-www-form-urlencoded"
            },
            body: new URLSearchParams(data)
        })
            .then(response => response.json())
            .then(result => {
            if (result.status === 1) {
                console.log("Notification sent successfully!");
            } else {
                console.error("Failed to send notification:", result);
            }
        })
            .catch(error => {
            console.error("Error:", error);
        });
    }




    // Save tracked articles to localStorage
    const saveTrackedArticles = (trackedArticles) => {
        localStorage.setItem("trackedArticles", JSON.stringify(trackedArticles));
    };

    const trackedArticles = loadTrackedArticles();

    const username = 'admin';
    const password = 'pumpfun';
    const credentials = btoa(`${username}:${password}`);

    const shortenURL = async (longUrl) => {
        try {
            const response = await fetch(`https://kroocoin.xyz/shorten`, {
                method: "POST",
                headers: {
                "Content-Type": "application/json"
                },
                body: JSON.stringify({
                    long_url: longUrl
                })
            });

            const data = await response.json();
            return data.short_url; // Return the shortened URL
        } catch (e) {
            console.error("Error shortening URL:", e);
            return longUrl; // Fallback to the original URL if shortening fails
        }
    };



    const extractSignificantData = (inputJson) => {
        const simplifiedData = [];
        try {
            const posts = inputJson.require[0][3][0].__bbox.require[0][3][1].__bbox.result.data.xdt_api__v1__feed__timeline__connection.edges;

            for (const post of posts) {
                try {
                    const media = post.node.media;
                    const user = media.user;
                    const code = media.code;
                    const carousel_media = media.carousel_media || [];


                    const caption = media.caption?.text || "No caption";
                    const takenAt = media.taken_at || null;
                    const takenAtHumanReadable = takenAt
                        ? new Date(takenAt * 1000).toISOString().replace("T", " ").split(".")[0]
                        : "Unknown";
                    const images = media.image_versions2?.candidates || [];
                    const imageUrls = images.map(img => img.url || "No URL");

                    let videoUrls = [];
                    if (media.video_versions?.length > 0) {
                        videoUrls = media.video_versions.map(vid => vid.url || "No URL");
                    }

                    let gallery = carousel_media.map(item =>
                    item.image_versions2?.candidates?.[0]?.url || "No URL"

                     );
                     //if ( gallery.length > 0 ) { console.log(gallery);}


                    simplifiedData.push({
                        username: user.username || "Unknown",
                        full_name: user.full_name || "Unknown",
                        profile_pic_url: user.profile_pic_url || "No URL",
                        code: code,
                        gallery: gallery,
                        caption: caption,
                        image_urls: imageUrls,
                        video_urls: videoUrls
                    });
                } catch (e) {
                    console.error("Error processing a post:", e);
                }
            }
        } catch (e) {
            console.error("Key error:", e);
        }
        return simplifiedData;
    };

    const extractJSONData = () => {
        const scriptTags = document.querySelectorAll("script[type='application/json']");
        for (const script of scriptTags) {
            const scriptText = script.textContent;
            if (scriptText && scriptText.includes("ScheduledServerJS") && scriptText.includes("xdt_api__v1__feed__timeline__connection")) {
                try {
                    const jsonData = JSON.parse(scriptText);
                    return extractSignificantData(jsonData);
                } catch (e) {
                    console.error("Error parsing JSON data:", e);
                }
            }
        }
        return [];
    };

    const translateCaption = async (text) => {
        try {
            const response = await fetch("https://api.openai.com/v1/chat/completions", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    "Authorization": "Bearer ********************************************************************************************************************************************************************" // Replace with your OpenAI API key
                },
                body: JSON.stringify({
                    model: "gpt-4o-mini",
                    messages: [
                        { "role": "system", "content": "You are a helpful assistant that translates text to English." },
                        { "role": "user", "content": `Translate the following text into English if it's not already in English. in output start with Translated.  If all text was in english then start output with Original:   \"${text}\"` }
                    ],
                    response_format: { "type": "text" },
                    temperature: 1,
                    max_tokens: 2048,
                    top_p: 1,
                    frequency_penalty: 0,
                    presence_penalty: 0
                })
            });

            const data = await response.json();
            return data.choices[0].message.content.trim();
        } catch (e) {
            console.error("Error translating caption:", e);
            return text; // Fallback to the original text
        }
    };

    const detectNewborn = async (text) => {
        try {
            const response = await fetch("https://api.openai.com/v1/chat/completions", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    "Authorization": "Bearer ********************************************************************************************************************************************************************" // Replace with your OpenAI API key
                },
                body: JSON.stringify({
                    model: "gpt-4o-mini",
                    messages: [
                        {
                            "role": "system",
                            "content": "You are a precise assistant that detects if a post is about a newborn animal or if a newborn animal has been given a new name. Only respond 'true' if the text explicitly mentions a newborn, baby animal, or its naming. Avoid false positives for mentions of general animals, wildlife, or young animals not clearly described as newborns."
                        },
                        {
                            "role": "user",
                            "content": `Detect if the post explicitly mentions a newborn animal or that a newborn animal has received a name. The response must strictly follow this JSON format: {"newborn":<true or false>, "name":"<one or more names, or none if not mentioned>", "breed":"<breed name or none if not mentioned>"}. Here is the text: \"${text}\"`
                        }
                    ],
                    temperature: 0,
                    max_tokens: 200
                })
            });

            const data = await response.json();
            console.log(JSON.parse(data.choices[0].message.content.trim()));
            return JSON.parse(data.choices[0].message.content.trim());
        } catch (e) {

            console.error("Error in translation and detection:", e);
            return { translated: "not processed:\n"+text, isNewborn: false }; // Fallback
        }
    };

    const detectDied = async (text) => {
        try {
            const response = await fetch("https://api.openai.com/v1/chat/completions", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    "Authorization": "Bearer ********************************************************************************************************************************************************************" // Replace with your OpenAI API key
                },
                body: JSON.stringify({
                    model: "gpt-4o-mini",
                    messages: [
                        {
                            "role": "system",
                            "content": "You are a strict assistant that detects if an animal explicitly passed away or died in the text. You must only respond 'true' if the text clearly states that an animal has died, passed away, or similar. Avoid marking posts about natural lifecycles, ecosystems, or vague mentions of animals. Be very cautious about false positives."
                        },
                        {
                            "role": "user",
                            "content": `Detect if the post explicitly states that an animal passed away or died. The response must strictly follow this JSON format: {"died":<true or false>, "name":"<name or none if not mentioned>", "breed":"<breed name or none if not mentioned>"}. Here is the text: \"${text}\"`
                        }
                    ],
                    temperature: 0,
                    max_tokens: 200
                })
            });

            const data = await response.json();
            console.log(JSON.parse(data.choices[0].message.content.trim()));
            return JSON.parse(data.choices[0].message.content.trim());
        } catch (e) {

            console.error("Error in translation and detection:", e);
            return { translated: "not processed:\n"+text, isNewborn: false }; // Fallback
        }
    };


    const sendToDiscord = async (article) => {
        let eventType="";
        const translatedCaption = await translateCaption(article.caption);
        const detectNewbornVar = await detectNewborn(translatedCaption);
        const detectDiedVar = await detectDied(translatedCaption);
        let animalName='';
        let animalBreed='';
        console.log(detectNewbornVar);


        if (detectNewbornVar.newborn == true ){
            if (detectNewbornVar.hasOwnProperty('name') && detectNewbornVar.hasOwnProperty('breed')) {
                animalName = encodeURIComponent(detectNewbornVar.name);
                animalBreed = encodeURIComponent(detectNewbornVar.breed);
            } else {
                console.log("Missing properties: name or breed");
            }
            eventType="newborn";
            DISCORD_WEBHOOK_URL = NEWBORN_DISCORD_WEBHOOK_URL;
        }

        if (detectDiedVar.died == true ){
            if (detectDiedVar.hasOwnProperty('name') && detectDiedVar.hasOwnProperty('breed')) {
                animalName = encodeURIComponent(detectDiedVar.name);
                animalBreed = encodeURIComponent(detectDiedVar.breed);
            } else {
                console.log("Missing properties: name or breed");
            }
            eventType="died";
            DISCORD_WEBHOOK_URL = DIED_DISCORD_WEBHOOK_URL;
        }
        const translatedUpdated=translatedCaption.replace('Translated:','').replace('Original:','').replace('"','').replace(`"`,'');
        const link_enc=encodeURIComponent(`https://www.instagram.com/p/${article.code}`);
        const img_enc=encodeURIComponent(`${article.image_urls[0]}`);
        const vid_enc=encodeURIComponent(`${article.video_urls[0]}`);
        const desc_enc=encodeURIComponent(`${translatedUpdated}`);
        const gallery=encodeURIComponent(article.gallery.join(","));



        const finalURL=`https://kroocoin.xyz/test?link_url=${link_enc}&image_url=${img_enc}&video_url=${vid_enc}&description=${desc_enc}&type=${eventType}&name=${animalName}&breed=${animalBreed}&gallery=${gallery}`;
        //const finalURL="https://example.com/test";
        const finalURLShort=await shortenURL(finalURL);

        const createTokenLink=`[Create token](${finalURLShort})`;

        const full_message=`${createTokenLink}\n${translatedCaption}`;
        const message = {
            embeds: [
                {
                    author: {
                        name: `${article.full_name} (${article.username})`,
                        icon_url: article.profile_pic_url,
                        url: `https://www.instagram.com/p/${article.code}`
                    },
                    description: full_message,
                    image: {
                        url: article.image_urls[0] || null
                    },
                    fields: article.video_urls.length
                        ? [
                              {
                                  name: "Video",
                                  value: `[Watch Video](${article.video_urls[0]})`,
                                  inline: true
                              }
                          ]
                        : [],
                    footer: {
                        text: `Post Code: ${article.code}`
                    },
                    timestamp: new Date().toISOString()
                }
            ]
        };

        try {
            await fetch(DISCORD_WEBHOOK_URL, {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify(message)
            });
            console.log("Sent to Discord:", article);
        } catch (e) {
            console.error("Error sending to Discord:", e);
        }
        if(eventType == "newborn"){
                       // token: "arbs7ccq98rqoz78pagtcqyc523hro",
            //user: "utmxhixkgtchrdahx3tfj6sxgfkbw5",
            sendPushoverNotification(`Newborn. Name:${animalName} Breed:${animalBreed}`,"utmxhixkgtchrdahx3tfj6sxgfkbw5","arbs7ccq98rqoz78pagtcqyc523hro");
            sendPushoverNotification(`Newborn. Name:${animalName} Breed:${animalBreed}`,"ug7adwprvzzmzo133c678cz85vpc9m","af5c3jtswdoan29x92sycyjqz76rbq");
        }else if (eventType == "died" ){
            sendPushoverNotification(`Died. Name:${animalName} Breed:${animalBreed}`,"utmxhixkgtchrdahx3tfj6sxgfkbw5","arbs7ccq98rqoz78pagtcqyc523hro");
            sendPushoverNotification(`Died. Name:${animalName} Breed:${animalBreed}`,"ug7adwprvzzmzo133c678cz85vpc9m","af5c3jtswdoan29x92sycyjqz76rbq");
        }
    };

    const processNewArticles = (jsonData) => {
        for (const article of jsonData) {
            if (!trackedArticles.includes(article.code)) {
                trackedArticles.push(article.code);
                saveTrackedArticles(trackedArticles);
                sendToDiscord(article); // Send each article individually
            }
        }
    };

    const jsonData = extractJSONData();
    processNewArticles(jsonData);

    const observer = new MutationObserver(() => {
        const jsonData = extractJSONData();
        processNewArticles(jsonData);
    });

    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
})();
