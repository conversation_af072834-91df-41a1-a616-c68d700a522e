// ==UserScript==
// @name         Random Page Refresher
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  Refresh the page randomly between 30 and 60 seconds
// <AUTHOR>
// @match        https://www.instagram.com/*
// @grant        none
// ==/UserScript==

(function() {
    'use strict';

    function getRandomTime() {
        // Generate a random time between 30 and 60 seconds
        return Math.random() * (38 - 29) * 1000 + 30000;
    }

    function refreshPage() {
        const randomTime = getRandomTime();
        console.log(`Page will refresh in ${Math.round(randomTime / 1000)} seconds`);
        setTimeout(() => {
            location.reload();
        }, randomTime);
    }

    refreshPage();
})();
