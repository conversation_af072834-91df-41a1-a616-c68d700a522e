// ==UserScript==
// @name         Pump Card Filter
// @namespace    http://tampermonkey.net/
// @version      1.4
// @description  Hide pump-card elements that do not contain a specific phrase, with an input for filtering, and display physical links below social links, each on a new line, without protocol prefixes.
// @match        https://backup2.bullx.io/pump-vision
// @grant        none
// ==/UserScript==

(function() {
    'use strict';

    // Create the input element for filtering
    var input = document.createElement('input');
    input.type = 'text';
    input.placeholder = 'Type to filter...';
    input.style.position = 'fixed';
    input.style.top = '10px';
    input.style.right = '10px';
    input.style.zIndex = '9999';
    input.style.padding = '5px';
    input.style.fontSize = '14px';
    input.style.backgroundColor = '#fff';
    input.style.border = '1px solid #ccc';
    input.style.borderRadius = '4px';

    // Append the input to the body
    document.body.appendChild(input);

    // Function to filter the pump-card elements
    function filterPumpCards() {
        var filterText = input.value.toLowerCase();
        var pumpCards = document.querySelectorAll('.pump-card');

        pumpCards.forEach(function(card) {
            if (card.textContent.toLowerCase().includes(filterText)) {
                card.style.display = '';
                addPhysicalLinks(card);  // Add physical links when the card is displayed
            } else {
                card.style.display = 'none';
            }
        });
    }

    // Function to add physical links below social links, each on a new line, without protocol
    function addPhysicalLinks(card) {
        var socialLinks = card.querySelectorAll('a.social-link');
        socialLinks.forEach(function(link) {
            // Check if link already has a physical URL element to avoid duplication
            if (!link.nextElementSibling || !link.nextElementSibling.classList.contains('physical-link')) {
                var physicalLinkContainer = document.createElement('div');
                physicalLinkContainer.className = 'physical-link';
                physicalLinkContainer.style.fontSize = '12px';
                physicalLinkContainer.style.color = '#555';
                physicalLinkContainer.style.marginTop = '4px';
                physicalLinkContainer.style.display = 'block';  // Ensures container is on a new line

                // Strip https:// or http:// from the URL
                var strippedLink = link.href.replace(/^https?:\/\//, '');

                var physicalLink = document.createElement('div');
                physicalLink.textContent = strippedLink;
                physicalLink.style.display = 'block';  // Forces each link to appear on its own line

                physicalLinkContainer.appendChild(physicalLink);
                link.after(physicalLinkContainer);  // Insert the physical link below each social link
            }
        });
    }

    // Add event listener to input for real-time filtering
    input.addEventListener('input', filterPumpCards);

    // Initial filter in case there's any default text
    //filterPumpCards();

    // Observe changes in the body to handle dynamically added or updated pump-card elements
    var observer = new MutationObserver(function(mutations) {
        // We can optimize by checking if mutations affect the pump-card elements
        var shouldFilter = false;
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === 1 && node.matches && node.matches('.pump-card, .pump-card *')) {
                        shouldFilter = true;
                    }
                });
                mutation.removedNodes.forEach(function(node) {
                    if (node.nodeType === 1 && node.matches && node.matches('.pump-card, .pump-card *')) {
                        shouldFilter = true;
                    }
                });
            } else if (mutation.type === 'characterData' && mutation.target.parentElement.matches('.pump-card, .pump-card *')) {
                shouldFilter = true;
            } else if (mutation.type === 'attributes' && mutation.target.matches('.pump-card, .pump-card *')) {
                shouldFilter = true;
            }
        });
        if (shouldFilter) {
            filterPumpCards();
        }
    });

    observer.observe(document.body, {
        childList: true,
        subtree: true,
        characterData: true,
        attributes: true
    });

})();
