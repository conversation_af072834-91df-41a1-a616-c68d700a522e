// ==UserScript==
// @name         CopyTweets_webhook
// @namespace    http://tampermonkey.net/
// @version      4.3.3
// @description  Collects tweets from Discord and sends them to a Discord webhook with enhanced formatting.
// @match        https://discord.com/*
// @grant        GM_xmlhttpRequest
// @connect      discord.com
// ==/UserScript==

(function () {
    'use strict';

    const WEBHOOK_URL = "https://discord.com/api/webhooks/1314921975210311701/24totTISWEu7uyu9alvqc1OJUP6kEei_hzmx73ojDHplD5DzopunFxfbYGYDJjcZDvOI";

       function replaceAssetPaths(html) {
        return html.replace(/\/assets\/.*.svg/g, '#');
    }
    // Utility function to replace emoji images in HTML with their alt text
    function replaceEmojiImages(html) {
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = html;
        const emojiImages = tempDiv.querySelectorAll('img[data-type="emoji"]');

        emojiImages.forEach(img => {
            const altText = img.getAttribute('alt') || '';
            const textNode = document.createTextNode(altText);
            img.parentNode.replaceChild(textNode, img);
        });

        return tempDiv.innerHTML;
    }

    // Utility function to strip HTML tags
    function stripHTML(html) {
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = html;
        return tempDiv.textContent || tempDiv.innerText || '';
    }

    // Initialize local storage for storing Twitter links
    function initializeLocalStorage() {
        if (!localStorage.getItem('tweetLinks')) {
            localStorage.setItem('tweetLinks', JSON.stringify([]));
        }
    }

    // Get stored tweet links from local storage
    function getStoredLinks() {
        return JSON.parse(localStorage.getItem('tweetLinks') || '[]');
    }

    // Update stored tweet links in local storage
    function updateStoredLinks(links) {
        localStorage.setItem('tweetLinks', JSON.stringify(links));
    }

    // Add a tweet link to local storage if it doesn't already exist
    function addLinkToStorage(tweetLink) {
        const storedLinks = getStoredLinks();
        if (!storedLinks.includes(tweetLink)) {
            storedLinks.push(tweetLink);
            updateStoredLinks(storedLinks);
            return true; // Indicates a new link was added
        }
        return false; // Indicates the link already exists
    }

    // Send tweet data to the Discord webhook
    function sendToWebhook(tweetData) {

           const embed = {
                author: {
                    name: `${tweetData.authorName} (${tweetData.authorHandle}) ${tweetData.actionType}: ${tweetData.targetUser}`,
                    url: tweetData.tweetLink,
                    icon_url: tweetData.authorIcon
                },
                description: `${tweetData.userText}`,
                color: 0x5865F2, // Bluish color for embeds
                footer: {
                    text: "Twitter",
                },
                timestamp: tweetData.timestamp,
                //fields: [
                //    {
                //        name: "Original text",
                //        value: `${tweetData.targetUserText}`,
                //        inline: true,
                //    },
                //],
            };



if (tweetData.actionType !== "Tweeted") {
    // Format `targetUserText` with `>` at the start of each line for blockquote styling
    const formattedTargetUserText = tweetData.targetUserText
        .split('\n') // Split by newline
        .filter(line => line.trim() !== '') // Remove empty lines
        .map(line => `> ${line}`) // Add `>` to the start of each line
        .join('\n'); // Join back with newline

    embed.fields = [
        {
            name: "Original text",
            value: formattedTargetUserText || "No original text available.",
            inline: false, // Keep it non-inline for better readability
        },
    ];
}

        // Add image if available
        if (tweetData.imageLinks.length > 0) {
            embed.image = { url: tweetData.imageLinks[0] };
        }

        const payload = {

            embeds: [embed],
        };

        GM_xmlhttpRequest({
            method: 'POST',
            url: WEBHOOK_URL,
            headers: {
                'Content-Type': 'application/json',
            },
            data: JSON.stringify(payload),
            onload: (response) => {
                if (response.status === 204) {
                    console.log('Tweet successfully sent to webhook:', tweetData.tweetLink);
                } else {
                    console.error('Failed to send tweet to webhook:', response);
                }
            },
            onerror: (error) => {
                console.error('Error sending tweet to webhook:', error);
            },
        });
    }

    // Discord Integration
    if (window.location.hostname === 'discord.com') {
        function extractAndSendTweet(message) {
            const tweetLinkElement = message.querySelector('a[href*="x.com"][href*="/status/"]');
            if (!tweetLinkElement) return;

            const tweetLink = tweetLinkElement.href;

            if (!addLinkToStorage(tweetLink)) {
                console.log('Duplicate tweet link, skipping:', tweetLink);
                return;
            }

            const imageAnchorElements = message.querySelectorAll('a[data-role="img"]');

            const imageLinks = [];
            imageAnchorElements.forEach(img => {
                const safeSrc = img.getAttribute('data-safe-src') || img.href;
                if (safeSrc) imageLinks.push(safeSrc);
            });


            const authorElement = message.querySelector('.embedAuthorNameLink_b0068a');
            let authorIcon = message.querySelector('.embedAuthorIcon_b0068a');
            authorIcon = authorIcon.src;

            let authorName = 'Unknown';
            let authorHandle = '';
            if (authorElement) {
                const fullName = stripHTML(authorElement.textContent.trim());
                const nameMatch = fullName.match(/^(.*?)\s*\((@[\w\d_]+)\)$/);
                if (nameMatch) {
                    authorName = nameMatch[1];
                    authorHandle = nameMatch[2];
                } else {
                    authorName = fullName;
                }
            }

            const embedTitleElement = message.querySelector('.embedTitle_b0068a .embedTitleLink_b0068a');
            let actionType = 'Tweeted';
            let targetUser = '';

            if (embedTitleElement) {
                const spanElements = embedTitleElement.querySelectorAll('span');
                if (spanElements.length >= 1) {
                    const actionText = spanElements[0].textContent.trim();

                    if (actionText.includes('Retweeted')) {
                        actionType = 'Retweeted';
                        if (spanElements.length >= 2) {
                            targetUser = spanElements[1].textContent.trim();
                        }
                    } else if (actionText.includes('Replied to')) {
                        actionType = 'Replied to';
                        if (spanElements.length >= 2) {
                            targetUser = spanElements[1].textContent.trim();
                        }
                    } else if (actionText.includes('Quoted')) {
                        actionType = 'Quoted';
                        if (spanElements.length >= 2) {
                            targetUser = spanElements[1].textContent.trim();
                        }
                    } else if (actionText.includes('Followed')) {
                        actionType = 'Followed';
                        if (spanElements.length >= 2) {
                            targetUser = spanElements[1].textContent.trim();
                        }
                    }
                }
            }

            //const descriptionElement = message.querySelector('.embedDescription_b0068a');
            //let userText = '';
            //if (descriptionElement) {
            //    const clonedDescription = descriptionElement.cloneNode(true);
            //    userText = stripHTML(replaceEmojiImages(clonedDescription.innerHTML.trim()));
            //}


            const descriptionElement = message.querySelector('.embedDescription_b0068a');
            let userText = '';
            let targetUserText = '';

if (descriptionElement) {
    // Clone the descriptionElement to avoid modifying the original DOM
    const clonedDescription = descriptionElement.cloneNode(true);

    // Find the blockquote (original tweet text for retweets)
    const blockquoteElement = clonedDescription.querySelector('blockquote');
    if (blockquoteElement) {
        // Extract and clean the blockquote text
        targetUserText = stripHTML(replaceEmojiImages(blockquoteElement.innerHTML.trim()))
            .replace(/View More/g, '') // Remove "View More"

        // Remove the blockquote to isolate the main user text
        blockquoteElement.remove();
    }

    // Extract the main user text after removing the blockquote
    userText = stripHTML(replaceEmojiImages(clonedDescription.innerHTML.trim()))
        .replace(/View More/g, ''); // Remove "View More"
}


            const tweetData = {
                authorIcon: authorIcon,
                authorName: authorName,
                authorHandle: authorHandle,
                tweetLink: tweetLink,
                userText: userText,
                targetUserText: targetUserText,
                actionType: actionType,
                targetUser: targetUser,
                imageLinks: imageLinks,
                timestamp: new Date().toISOString(),
            };

            sendToWebhook(tweetData);
        }

const messageObserver = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
                const message = node.querySelector('.messageListItem_d5deea') || node;
                if (message && !message.dataset.processed) {
                    // Add a small delay to ensure the message is fully rendered
                    setTimeout(() => {
                        if (message.dataset.processed) return; // Double-check to avoid re-processing
                        message.dataset.processed = 'true';
                        extractAndSendTweet(message);
                    }, 500); // Delay by 100ms
                }
            }
        });
    });
});


        messageObserver.observe(document.body, { childList: true, subtree: true });

        initializeLocalStorage();
    }
})();
