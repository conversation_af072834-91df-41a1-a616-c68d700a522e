#!/usr/bin/env python3
from playwright.sync_api import sync_playwright
from bs4 import BeautifulSoup
import os
import json
from datetime import datetime
from mysql.connector import pooling
from modules.config import *
import modules.db as db

import pika

COOKIES_FILE = "instagram_cookies.json"
EXTRACTED_FILE = "extracted_data.json"
SIMPLIFIED_FILE = "extracted_data_simplified.json"


MYSQL_SERVER="pumpfun.mooo.com"
MYSQL_PORT=3306
MYSQL_USER="pump2"
MYSQL_PASSWORD="pump2"
MYSQL_DB="pump"
MYSQL_POOL="mypool"
MYSQL_POOL_SIZE="20"

DATAGATHER=120

pool = pooling.MySQLConnectionPool(
    pool_name=MYSQL_POOL, 
    user=MYSQL_USER,
    password=MYSQL_PASSWORD,
    host=MYSQL_SERVER,
    db=MYSQL_DB)

RABBITMQ_SERVER="pumpfun.mooo.com"
def send_message(message,queue="instagram"):


    credentials = pika.PlainCredentials('pump', 'pump2pump')
    parameters = pika.ConnectionParameters(RABBITMQ_SERVER, 5672, '/', credentials)
    connection = pika.BlockingConnection(parameters)
    channel = connection.channel()

    # Create a queue named 'task_queue'
    channel.queue_declare(queue=queue, durable=True)

    # Publish the message to the queue
    channel.basic_publish(
        exchange='',
        routing_key=queue,
        body=message,
        properties=pika.BasicProperties(
            delivery_mode=2,  # Make message persistent
        ))
    print(" [x] Sent %r" % message)

# load json from file data.json
data=json.load(open('extracted_data_simplified.json'))
for post in data:
    #print(post)
    chk_exist=db.get_instagram_user_date(pool,post["username"],post["taken_at"])
    if not chk_exist:
        image_url=post["image_urls"][0]
        if "video_urls" in post and len(post["video_urls"])>0:
            video_url=post["video_urls"][0] 
        else:
            video_url=None
        db.insert_instagram_post(pool,post["code"],post["username"],post["full_name"],post["profile_pic_url"],post["caption"],post["taken_at"],image_url,video_url)
        send_message(json.dumps(post))
        
data=json.load(open('extracted_data_simplified1.json'))
for post in data:
    #print(post)
    chk_exist=db.get_instagram_user_date(pool,post["username"],post["taken_at"])
    if not chk_exist:
        image_url=post["image_urls"][0]
        if "video_urls" in post and len(post["video_urls"])>0:
            video_url=post["video_urls"][0] 
        else:
            video_url=None
        db.insert_instagram_post(pool,post["code"],post["username"],post["full_name"],post["profile_pic_url"],post["caption"],post["taken_at"],image_url,video_url)
        send_message(json.dumps(post))