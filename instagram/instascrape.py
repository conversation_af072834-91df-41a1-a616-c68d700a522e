#!/usr/bin/env python3
from playwright.sync_api import sync_playwright
from bs4 import BeautifulSoup
import os
import json

from datetime import datetime
from mysql.connector import pooling
from modules.config import *


COOKIES_FILE = "instagram_cookies.json"
EXTRACTED_FILE = "extracted_data.json"
SIMPLIFIED_FILE = "extracted_data_simplified.json"



MYSQL_SERVER="pumpfun.mooo.com"
MYSQL_PORT=3306
MYSQL_USER="pump2"
MYSQL_PASSWORD="pump2"
MYSQL_DB="pump"
MYSQL_POOL="mypool"
MYSQL_POOL_SIZE="20"

DATAGATHER=120

pool = pooling.MySQLConnectionPool(
    pool_name=MYSQL_POOL, 
    user=MYSQL_USER,
    password=MYSQL_PASSWORD,
    host=MYSQL_SERVER,
    db=MYSQL_DB)



def save_cookies(page):
    """Save cookies to a file."""
    cookies = page.context.cookies()
    with open(COOKIES_FILE, "w") as f:
        json.dump(cookies, f)
    print("Cookies saved.")

def load_cookies(context):
    """Load cookies from a file."""
    try:
        with open(COOKIES_FILE, "r") as f:
            cookies = json.load(f)
            context.add_cookies(cookies)
        print("Cookies loaded.")
    except FileNotFoundError:
        print("Cookies file not found. Logging in for the first time.")

def handle_cookies_prompt(page):
    """Handle the cookies prompt if it appears."""
    try:
        # Wait for the "Accept Cookies" button and click it
        page.locator("text='Allow essential and optional cookies'").click(timeout=3000)
        print("Accepted cookies.")
    except Exception as e:
        print("Cookies prompt not found or already accepted.")

def login_to_instagram(page):
    """Log in to Instagram."""
    username = "<EMAIL>"
    password = "Fall2deathstrong"

    # Go to Instagram login page
    page.goto("https://www.instagram.com/accounts/login/")
    handle_cookies_prompt(page)
    page.wait_for_selector("input[name='username']", timeout=5000)
    page.fill("input[name='username']", username)
    page.fill("input[name='password']", password)
    page.click("button[type='submit']")
    page.wait_for_selector("nav", timeout=50000)
    print("Logged in successfully.")

def extract_and_save_first_json(html_content):
    """Extract and save the first matching JSON block from <script> tags."""
    soup = BeautifulSoup(html_content, "html.parser")
    script_tags = soup.find_all("script", {"type": "application/json"})

    for script in script_tags:
        script_text = script.string
        if script_text and "ScheduledServerJS" in script_text and "xdt_api__v1__feed__timeline__connection" in script_text:
            # Save the first match locally
            with open(EXTRACTED_FILE, "w", encoding="utf-8") as f:
                f.write(script_text)  # Save as raw JSON
            print(f"Extracted JSON saved at {EXTRACTED_FILE}")
            return True
    print("No matching JSON data found.")
    return False

def extract_significant_data(input_json):
    """Simplify and extract significant data from Instagram feed JSON."""
    simplified_data = []
    
    try:
        posts = input_json["require"][0][3][0]["__bbox"]["require"][0][3][1]["__bbox"]["result"]["data"]["xdt_api__v1__feed__timeline__connection"]["edges"]
    except KeyError as e:
        print(f"Key error: {e}")
        return simplified_data
    
    for post in posts:
        try:
            media = post["node"]["media"]
            user = media["user"]
            code = media["code"]
      
            caption = media.get("caption", {}).get("text", "No caption")
            taken_at = media.get("taken_at", None)
            #taken_at_human_readable = datetime.utcfromtimestamp(taken_at).strftime('%Y-%m-%d %H:%M:%S') if taken_at else "Unknown"
            #fix above 
            taken_at_human_readable = datetime.fromtimestamp(taken_at).strftime('%Y-%m-%d %H:%M:%S') if taken_at else "Unknown"
            images = media.get("image_versions2", {}).get("candidates", [])
            image_urls = [img.get("url", "No URL") for img in images]

            video_urls=[]
            if "video_versions" in media and media["video_versions"] is not None and len(media["video_versions"])>0:
                video_urls = [img.get("url", "No URL") for img in media["video_versions"]]

            # Append relevant details
            simplified_data.append({
                "username": user.get("username", "Unknown"),
                "code": code,
                "full_name": user["full_name"],
                "profile_pic_url": user.get("profile_pic_url", "No URL"),
                "caption": caption,
                "taken_at": taken_at_human_readable,
                "image_urls": image_urls,
                "video_urls": video_urls
            })
        except KeyError as e:
            print(f"Error processing a post: {e}")
    
    return simplified_data

def simplify_and_save_json():
    """Simplify the extracted JSON and save it to a file."""
    try:
        with open(EXTRACTED_FILE, "r") as file:
            raw_data = json.load(file)
        simplified_data = extract_significant_data(raw_data)
        with open(SIMPLIFIED_FILE, "w") as file:
            json.dump(simplified_data, file, indent=4)
        print(f"Simplified JSON saved to {SIMPLIFIED_FILE}")
    except Exception as e:
        print(f"Error simplifying JSON: {e}")

def navigate_instagram_feed(page):
    """Navigate directly to Instagram feed and extract JSON."""
    try:
        page.goto("https://www.instagram.com/?variant=following", timeout=30000)
        page.wait_for_selector("article", timeout=30000)  # Wait for posts to load
        print("Feed loaded.")
        page.screenshot(path="../create_token/static/images/screenshot.jpg", full_page=True)

        # Extract and save the first matching JSON data
        html_content = page.content()
        if extract_and_save_first_json(html_content):
            simplify_and_save_json()
    except Exception as e:
        print("Failed to load feed page. Falling back to login.")
        return False
    return True

def main():
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        context = browser.new_context()

        page = context.new_page()

        if os.path.exists(COOKIES_FILE):
            load_cookies(context)
            if not navigate_instagram_feed(page):
                # If session is invalid, log in and retry
                login_to_instagram(page)
                save_cookies(page)
                navigate_instagram_feed(page)
        else:
            # No cookies, perform login
            login_to_instagram(page)
            save_cookies(page)
            navigate_instagram_feed(page)

        browser.close()

if __name__ == "__main__":
    main()
