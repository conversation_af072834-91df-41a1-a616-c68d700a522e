version: '2.0'

services:

  adminer:
    image: adminer
    restart: always
    ports:
      - 8080:8080

  frontend:
    build:
      context: svc_frontend
      dockerfile: Dockerfile
    ports:
    - 88:1234
    command: npm start
    container_name: frontend
    volumes:
      - /root/pump/svc_frontend/src:/app/src
      - /root/pump/svc_frontend/public:/app/public
    environment:
      - API_SERVER=${API_SERVER}


  api:
    build: 
      context: .
      dockerfile: ./svc_api_server/Dockerfile 
    container_name: api
    restart: on-failure
    ports:
      - "2222:2222"
    volumes:
      - /root/pump/svc_api_server:/app
      - /root/pump/modules:/app/modules
    environment:
      - MYSQL_SERVER=${MYSQL_SERVER}
      - MYSQL_PORT=${MYSQL_PORT}
      - MYSQL_USER=${MYSQL_USER}
      - MYSQL_PASSWORD=${MYSQL_PASSWORD}
      - MYSQL_DB=${MYSQL_DB}
      - MYSQL_POOL=${MYSQL_POOL}
      - MYSQL_POOL_SIZE=${MYSQL_POOL_SIZE}
      - NEO4J_SERVER=${NEO4J_SERVER}
      - NEO4J_USER=${NEO4J_USER}
      - NEO4J_PASSWORD=${NEO4J_PASSWORD}
      - NEO4J_PORT=${NEO4J_PORT}



  events:
    build: 
      context: .
      dockerfile: ./svc_event_processor/Dockerfile 
    container_name: event
    restart: on-failure
    volumes:
      - /root/pump/svc_event_processor:/app
      - /root/pump/modules:/app/modules
    environment:
      - MYSQL_SERVER=${MYSQL_SERVER}
      - MYSQL_PORT=${MYSQL_PORT}
      - MYSQL_USER=${MYSQL_USER}
      - MYSQL_PASSWORD=${MYSQL_PASSWORD}
      - MYSQL_DB=${MYSQL_DB}
      - PUSHOVER_URL=${PUSHOVER_URL}
      - PUSHOVER_TOKEN1=${PUSHOVER_TOKEN1}
      - PUSHOVER_USER1=${PUSHOVER_USER1}
      - PUSHOVER_TOKEN2=${PUSHOVER_TOKEN2}
      - PUSHOVER_USER2=${PUSHOVER_USER2}

  tx:
    build: 
      context: .
      dockerfile: ./svc_tx_processor/Dockerfile 
    container_name: tx
    restart: on-failure
    volumes:
      - /root/pump/svc_tx_processor:/app
      - /root/pump/modules:/app/modules
    environment:
      - MYSQL_SERVER=${MYSQL_SERVER}
      - MYSQL_PORT=${MYSQL_PORT}
      - MYSQL_USER=${MYSQL_USER}
      - MYSQL_PASSWORD=${MYSQL_PASSWORD}
      - MYSQL_DB=${MYSQL_DB}
      - MYSQL_POOL=${MYSQL_POOL}
      - MYSQL_POOL_SIZE=${MYSQL_POOL_SIZE}
      - NEO4J_SERVER=${NEO4J_SERVER}
      - NEO4J_USER=${NEO4J_USER}
      - NEO4J_PASSWORD=${NEO4J_PASSWORD}
      - NEO4J_PORT=${NEO4J_PORT}
      - TX_SLEEP=${TX_SLEEP}
      - BOOTSTRAP_ADDRESS=${BOOTSTRAP_ADDRESS}
      - BOOTSTRAP=${BOOTSTRAP}

  balance:
    build: 
      context: .
      dockerfile: ./svc_update_balance/Dockerfile
    container_name: balance
    restart: on-failure
    volumes:
      - /root/pump/svc_update_balance:/app
      - /root/pump/modules:/app/modules
    environment:
      - MYSQL_SERVER=${MYSQL_SERVER}
      - MYSQL_PORT=${MYSQL_PORT}
      - MYSQL_USER=${MYSQL_USER}
      - MYSQL_PASSWORD=${MYSQL_PASSWORD}
      - MYSQL_DB=${MYSQL_DB}
      - MYSQL_POOL=${MYSQL_POOL}
      - MYSQL_POOL_SIZE=${MYSQL_POOL_SIZE}
      - NEO4J_SERVER=${NEO4J_SERVER}
      - NEO4J_USER=${NEO4J_USER}
      - NEO4J_PASSWORD=${NEO4J_PASSWORD}
      - NEO4J_PORT=${NEO4J_PORT}
      - MAX_HOPS=${MAX_HOPS}
      - MIN_SOL=${MIN_SOL}
      - MAX_SIG=${MAX_SIG}
      - BOOTSTRAP=${BOOTSTRAP}
      - BOOTSTRAP_ADDRESS=${BOOTSTRAP_ADDRESS}
      - BALANCE_SLEEP=${BALANCE_SLEEP}
      - MIN_BALANCE_CHANGE=${MIN_BALANCE_CHANGE}
      - BALANCE_UPDATE_BATCH=${BALANCE_UPDATE_BATCH}

volumes:
  db_data:
