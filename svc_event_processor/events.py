#!/usr/bin/env python3
import os
import pika
import requests
import threading
import asyncio
import json
from modules.config import *
from modules.utils import get_logger

logger=get_logger("events")
# Set up logging

def send_pushover_message(message):
    
    response = requests.post('https://api.pushover.net/1/messages.json', data={
        'token': pushover_token,
        'user': pushover_api,
        'message': message
    })

    if response.status_code == 200:
        return "Message sent successfully", 200
    else:
        return f"Failed to send message: {response.text}", 500
    

def process_event(message):
    if True:
        if message:
            event = json.loads(message)
            logger.info(f"Processing event: {event}")
            if event['type'] == 'balance':
                # logic comes here
                # send_event(json.dumps({"type":"balance" ,"account": row["account"], "balance": row["balance"]}))
                if "type" in event and "account" in event and "balance" in event:
                    if event["account"] == "6tenaNVTmqwQ7FXV6XGzoPKSv79bhsmyx1pWUG7ekg2A":
                        message = f"account {event['account']} balance updated to {event['balance']}"
                        logger.info(message)
                        send_pushover_message(message)
            else:
                logger.info(f"Unknown event type: {event['type']}")
   #except Exception as e:
   #     logger.error(f"Error processing event: {e}")

# Function to process messages from RabbitMQ

def on_message(ch, method, properties, body):
    threading.Thread(target=process_event, args=(body,)).start()


# Set up RabbitMQ connection and channel
async def setup_rabbitmq():
    try:
        global pool
        #await create_pool()
        rabbitmq_host = os.getenv('RABBITMQ_HOST', '***************')
        connection = pika.BlockingConnection(pika.ConnectionParameters(rabbitmq_host))
        channel = connection.channel()
        channel.queue_declare(queue='events', durable=True)  # Declare the queue as durable
        channel.basic_consume(queue='events', on_message_callback=on_message, auto_ack=True)
        logger.info('Waiting for messages. To exit press CTRL+C')
        channel.start_consuming()
    except Exception as e:
        logger.info(f"Unexpected error: {e}")

if __name__ == '__main__':
    
    asyncio.get_event_loop().run_until_complete(setup_rabbitmq())