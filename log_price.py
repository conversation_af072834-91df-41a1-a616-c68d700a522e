#!/usr/bin/env python3
import pika
import argparse
import requests


def get_supply(token):
    url = 'https://solana-mainnet.api.syndica.io/api-key/4TMsFG1pLWMP4zpdF895C1kYrML8t6AXKQjFB3NzRh1xMjfcMAFPTwCmpRJWa1i72SKTFxjyxBMz7uAkfJoRfR5ZX4kQURk2wwc'
    headers = {
        'Content-Type': 'application/json'
    }
    payload = {
        "jsonrpc": "2.0",
        "id": 1,
        "method": "getTokenSupply",
        "params": [token]
    }

    response = requests.post(url, headers=headers, json=payload)
    
    if response.status_code == 200:
        response_json = response.json()
        if "result" in response_json and "value" in response_json["result"]:
            return 4000/140/float(response_json["result"]["value"]["uiAmount"])
        else:
            raise ValueError("Unexpected response structure")
    else:
        response.raise_for_status()



def send_message(message):
    credentials = pika.PlainCredentials('guest', 'guest')
    parameters = pika.ConnectionParameters('localhost', 5672, '/', credentials)
    connection = pika.BlockingConnection(parameters)
    channel = connection.channel()

    # Create a queue named 'token'
    channel.queue_declare(queue='token', durable=True)

    # Publish the message to the queue
    channel.basic_publish(
        exchange='',
        routing_key='token',
        body=message,
        properties=pika.BasicProperties(
            delivery_mode=2,  # Make message persistent
        ))

    connection.close()

def main():
    parser = argparse.ArgumentParser(description='Send token message to RabbitMQ.')
    parser.add_argument('token', type=str, help='The token to send to RabbitMQ')
    args = parser.parse_args()
    print(f"Adding token: {args.token}")
    send_message(args.token)
    #print(get_supply(args.token))

if __name__ == '__main__':
    main()

