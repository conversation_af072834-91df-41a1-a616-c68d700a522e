<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <title>Sigma.js with <PERSON>4j</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/sigma.js/2.4.0/sigma.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/graphology/0.25.4/graphology.umd.min.js"></script>
</head>
<body style="background: lightgrey">
    <div id="container" style="width: 800px; height: 600px; background: white"></div>
    <script>
        // Fetch the data from the Flask endpoint
        fetch('/data')
            .then(response => response.json())
            .then(data => {
                // Create a graphology graph
                const graph = new graphology.Graph();

                // Add nodes to the graph
                console.log(" nodes")
                data.nodes.forEach(node => {
                    console.log(node.id);

                    console.log("Adding node:", node);
                    graph.mergeNode(node.id, {
                        label: node.label,
                        x: node.x,
                        y: node.y,
                        size: node.size,
                        color: node.color
                    });
                });


// graph.addEdge("1", "2", { size: 5, color: "purple" });
                console.log("edges");
                 data.edges.forEach(edge => {
                    console.log(edge.source);
                    if (graph.hasNode(edge.source) && graph.hasNode(edge.target)) {
                        console.log("Adding edge:", edge);
                        graph.mergeEdge(edge.source, edge.target, {
                            size: edge.size,
                            color: edge.color
                        });
                    } else {
                        console.error("Edge references non-existing node:", edge);
                    }
                 });

                // Instantiate sigma.js and render the graph
                //new sigma(graph, document.getElementById('container'));
                const sigmaInstance = new Sigma(graph, document.getElementById("container"));
            })
            .catch(error => {
                console.error('Error fetching data:', error);
            });
    </script>
</body>
</html>
