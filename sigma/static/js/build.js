var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
/**
 * This is a minimal example of sigma. You can use it as a base to write new
 * examples, or reproducible test cases for new issues, for instance.
 */
define("index", ["require", "exports", "graphology", "sigma"], function (require, exports, graphology_1, sigma_1) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    graphology_1 = __importDefault(graphology_1);
    sigma_1 = __importDefault(sigma_1);
    var graph = new graphology_1.default();
    graph.addNode("1", { label: "Node 1", x: 0, y: 0, size: 10, color: "blue" });
    graph.addNode("2", { label: "Node 2", x: 1, y: 1, size: 20, color: "red" });
    graph.addEdge("1", "2", { size: 5, color: "purple" });
    var renderer = new sigma_1.default(graph, document.getElementById("container"));
});
