#!/usr/bin/env python3
from flask import Flask, jsonify, render_template
from flask_cors import CORS
from neo4j import GraphDatabase

app = Flask(__name__)
CORS(app) 

# Neo4j connection details
uri = "bolt://109.199.121.189:7687"
username = "neo4j"
password = "pump2pump"

# Initialize Neo4j driver
driver = GraphDatabase.driver(uri, auth=(username, password))

@app.route('/data')
def get_data():
    def fetch_data(tx):
        result = tx.run("MATCH (n)-[r]->(m) RETURN n, r, m")
        nodes = {}
        edges = set()
        for record in result:
            n = record['n']
            m = record['m']
            r = record['r']

            # Add nodes to dictionary to ensure uniqueness
            if n.element_id not in nodes:
                nodes[n.element_id] = {'id':  n.get('name', 'Node'), 'label': n.get('name', 'Node'),'label': n.get('name', 'Node'), 'x': n.get('x', 0), 'y': n.get('y', 0), 'size': 10, 'color': 'blue'}
            if m.element_id not in nodes:
                nodes[m.element_id] = {'id': n.get('name', 'Node'), 'label': m.get('name', 'Node'), 'x': m.get('x', 1), 'y': m.get('y', 1), 'size': 10, 'color': 'red'}

            # Create a unique edge identifier
            edge_id = f"{n.element_id}-{m.element_id}-{r.id}"
            # Add edges to the set to ensure uniqueness
            edges.add((edge_id, n.element_id, m.element_id))

        # Convert set to list of edge dictionaries
        edge_list = [{'id': edge_id, 'source': source, 'target': target, 'size': 1, 'color': 'purple'} for edge_id, source, target in edges]

        return {'nodes': list(nodes.values()), 'edges': edge_list}

    with driver.session() as session:
        data = session.execute_read(fetch_data)
    return jsonify(data)

@app.route('/')
def index():
    return render_template('index.html')

if __name__ == '__main__':
    app.run(debug=True,host="0.0.0.0",port=3333)
