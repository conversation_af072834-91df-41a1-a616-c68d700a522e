# pump
# pump


#transaction table 
|related_mint|from|to|sol-amount|token-amount|date|token_tranaction=true/false

from|to|sol|token

#accounts
|related_mint|account_name|balance|transactioins|age|firstblock_time|scan_time|distance from mint/level 


if over 20
start stream token - get all transaciton for time period. 
    for each signature 
        get transaction 
            get owner - save to account table 
            save transaction data to table
    

if over 70 invested

# logic 


Get block data
Iterate over block and identify New tokens
    Iterate over block data get transactons with new tokens address.
    Iterate next blocks get addresses to array up to 5 blocks
    if accounts collected execute recursion
    getbalandes=betbalances(full list)
    maxdepth=3
    for address in addresses
        signatures=getSignaturesfor address(address)
        process_transactions(sigs, account, all_transactions, token=True, depth=0)


token <--acc1 <-- big ( new transeferd X amount)
      



track sliding account program: 

check account(aaaccount)
   check account every 5 second
   if 