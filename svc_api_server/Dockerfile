# Use the official Python image
FROM python:3.12.0

# Set the working directory in the container
WORKDIR /app

# Copy the current directory contents into the container at /app
COPY svc_api_server/ /app/
COPY modules/ /app/modules/

# Install any needed packages specified in requirements.txt
RUN pip install --no-cache-dir -r requirements.txt


# Run newtokens.py when the container launches
CMD ["python", "api_server.py"]

