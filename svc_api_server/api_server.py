#!/usr/bin/env python3
from flask import Flask, jsonify, send_file, request
from flask_cors import CORS
from neo4j import GraphDatabase
#from flask_compress import Compress
from modules.config import *

import logging
import os,requests

logging.basicConfig(level=logging.INFO)

neo4j_server = os.getenv('NEO4J_SERVER')
neo4j_user = os.getenv('NEO4J_USER')
neo4j_password = os.getenv('NEO4J_PASSWORD')

app = Flask(__name__)
CORS(app)
#Compress(app) 

driver = GraphDatabase.driver(neo4j_server, auth=(neo4j_user, neo4j_password), max_connection_lifetime=10000)

def fetch_data(address):
    session = driver.session()
    try:
        query = f"""
WITH ['{address}'] AS startNodes
MATCH p = (startNode:Address)-[*2]->(endNode:Address)
WHERE startNode.name IN startNodes
AND NONE (node IN nodes(p) WHERE node:BoundaryNode:Address AND node <> startNode)
RETURN p
        """
        result = session.run(query)

        nodes = {}
        edges = []
        
        for record in result:
            path = record["p"]  # Get the path from the record

            # Process each node in the path
            for node in path.nodes:
                if node["name"] not in nodes:
                    nodes[node["name"]] = {
                        'id': node["name"],
                        'label': node.get("name", 'Node'),
                        'balance': node.get("balance", '0'),
                        'x': node.get("x", 0),
                        'y': node.get("y", 0),
                        'size': 10,
                        'role': node.get("role", "regular"),
                        'color': 'blue' 
                    }

            # Process each relationship in the path
            for rel in path.relationships:
                edge_id = f"{rel.start_node['name']}-{rel.end_node['name']}"
                edges.append({
                    'id': edge_id,
                    'source': rel.start_node["name"],
                    'target': rel.end_node["name"],
                    'size': 1,
                    'type': rel.type,
                    'label': rel.get("amount", ''),
                    'amount': rel.get("amount", ''),
                    'amount_rx': rel.get("amount_received", ''),
                    'amount_tx': rel.get("amount_send", ''),
                    'color': 'purple'
                })

        return {'nodes': list(nodes.values()), 'edges': edges}

    except Exception as error:
        logging.error('Error fetching data: %s', error)
        return None
    finally:
        session.close()

@app.route('/data', methods=['GET'])
def get_data():
    address = request.args.get('address', default=master_address)
    if not address:
        return "Address parameter is missing", 400

    data = fetch_data(address)
    if data:
        return jsonify(data)
    else:
        return "No data available", 503

@app.route('/', methods=['GET'])
def index():
    address = request.args.get('address', default='E197uwUXRjLMQBEZ4PYazDq5rGXwRB6xuTJ7f56UaLDC')
    return send_file('index.html')

@app.route('/pushover', methods=['GET'])
def send_pushover_message():
    message = request.args.get('message')
    if not message:
        return "Message parameter is missing", 400

    response = requests.post('https://api.pushover.net/1/messages.json', data={
        'token': pushover_token,
        'user': pushover_api,
        'message': message
    })

    if response.status_code == 200:
        return "Message sent successfully", 200
    else:
        return f"Failed to send message: {response.text}", 500

if __name__ == '__main__':
    app.run(port=2222, host="0.0.0.0", debug=True)
