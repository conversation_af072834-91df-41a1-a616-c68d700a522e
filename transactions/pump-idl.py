import  decoders.pump as pump
import decoders.system as system
base58_data = "58yiJoQi6w3WZGAx1j2P8WhKCNomxVCJCgWTauwXZqpWFjeZjuurQMMMJcJgZ1sxCLXtmSv3pxd5XfFqRXZX2ngKENucMvJUwGgV892EHmf8LLjqAW5vU9av4CSTrAwtorcCjp1JZw"
#base58_data = "5jRcjdixRUDgzecaLFnPY1aCRHz1J4fkb"
json_output = pump.decode(base58_data)
print(json_output)


encoded_data ="3Bxs4TgLZ72zxZ1y"
decoded_data = system.decode(encoded_data)
print(decoded_data)
