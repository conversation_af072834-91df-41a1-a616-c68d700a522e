[EncodedTransactionWithStatusMeta(
    EncodedTransactionWithStatusMeta {
        transaction: Accounts(
            UiAccountsList {
                signatures: [
                    "RbaUYJNbX1s7T824ZGexfysSK6nDxsg5SHZnYEftMYssU95dg9fwndw2hJTFSNZe8yyYSRaQCbG1KYCM9p4pCeu",
                ],
                account_keys: [
                    ParsedAccount {
                        pubkey: "DB27eZhCif8t7ScPqUzruYYk3o1SjAcfdS2sCYJhemcn",
                        writable: true,
                        signer: true,
                        source: Some(
                            Transaction,
                        ),
                    },
                    ParsedAccount {
                        pubkey: "5opkhP6STtA4QGh7zP9rgQ1p4cDRSC361tiewYWvBSVv",
                        writable: true,
                        signer: false,
                        source: Some(
                            Transaction,
                        ),
                    },
                    ParsedAccount {
                        pubkey: "HbdGBNyBkFWsZJKWXK51tRNJrKpPAFgNQSbQ63EGkr2F",
                        writable: true,
                        signer: false,
                        source: Some(
                            Transaction,
                        ),
                    },
                    ParsedAccount {
                        pubkey: "11111111111111111111111111111111",
                        writable: false,
                        signer: false,
                        source: Some(
                            Transaction,
                        ),
                    },
                    ParsedAccount {
                        pubkey: "ComputeBudget111111111111111111111111111111",
                        writable: false,
                        signer: false,
                        source: Some(
                            Transaction,
                        ),
                    },
                    ParsedAccount {
                        pubkey: "JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4",
                        writable: false,
                        signer: false,
                        source: Some(
                            Transaction,
                        ),
                    },
                    ParsedAccount {
                        pubkey: "So11111111111111111111111111111111111111112",
                        writable: false,
                        signer: false,
                        source: Some(
                            Transaction,
                        ),
                    },
                    ParsedAccount {
                        pubkey: "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
                        writable: false,
                        signer: false,
                        source: Some(
                            Transaction,
                        ),
                    },
                    ParsedAccount {
                        pubkey: "zVfpeNDwYVEuEHd5xvQVepEu54CEib2HbyUMtgxpump",
                        writable: false,
                        signer: false,
                        source: Some(
                            Transaction,
                        ),
                    },
                    ParsedAccount {
                        pubkey: "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL",
                        writable: false,
                        signer: false,
                        source: Some(
                            Transaction,
                        ),
                    },
                    ParsedAccount {
                        pubkey: "D8cy77BBepLMngZx6ZukaTff5hCt1HrWyKk3Hnd9oitf",
                        writable: false,
                        signer: false,
                        source: Some(
                            Transaction,
                        ),
                    },
                    ParsedAccount {
                        pubkey: "9x4BKtsrRwYDxr5rCyiPgYZqNEbfJDgCfnvYmJ3PkR5n",
                        writable: true,
                        signer: false,
                        source: Some(
                            LookupTable,
                        ),
                    },
                    ParsedAccount {
                        pubkey: "DukcUPJT8zhn2v21GXyDV5jfNXEPHvLYPQ66N651ycMT",
                        writable: true,
                        signer: false,
                        source: Some(
                            LookupTable,
                        ),
                    },
                    ParsedAccount {
                        pubkey: "HZw1kBU1DTxgiaVsm11FCk6kM4KnBjVC2tjArL5gBNSh",
                        writable: true,
                        signer: false,
                        source: Some(
                            LookupTable,
                        ),
                    },
                    ParsedAccount {
                        pubkey: "5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1",
                        writable: false,
                        signer: false,
                        source: Some(
                            LookupTable,
                        ),
                    },
                    ParsedAccount {
                        pubkey: "675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8",
                        writable: false,
                        signer: false,
                        source: Some(
                            LookupTable,
                        ),
                    },
                ],
            },
        ),
        meta: Some(
            UiTransactionStatusMeta {
                err: None,
                status: Ok(
                    (),
                ),
                fee: 24919,
                pre_balances: [
                    **********,
                    0,
                    2039280,
                    1,
                    1,
                    1141440,
                    ************,
                    *********,
                    1461600,
                    *********,
                    0,
                    2039280,
                    6124800,
                    ***********,
                    **********,
                    1141440,
                ],
                post_balances: [
                    6240253191,
                    0,
                    2039280,
                    1,
                    1,
                    1141440,
                    ************,
                    *********,
                    1461600,
                    *********,
                    0,
                    2039280,
                    6124800,
                    ***********,
                    **********,
                    1141440,
                ],
                inner_instructions: None,
                log_messages: None,
                pre_token_balances: Some(
                    [
                        UiTransactionTokenBalance {
                            account_index: 2,
                            mint: "zVfpeNDwYVEuEHd5xvQVepEu54CEib2HbyUMtgxpump",
                            ui_token_amount: UiTokenAmount {
                                ui_amount: None,
                                decimals: 6,
                                amount: "0",
                                ui_amount_string: "0",
                            },
                            owner: Some(
                                "DB27eZhCif8t7ScPqUzruYYk3o1SjAcfdS2sCYJhemcn",
                            ),
                            program_id: Some(
                                "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
                            ),
                        },
                        UiTransactionTokenBalance {
                            account_index: 11,
                            mint: "zVfpeNDwYVEuEHd5xvQVepEu54CEib2HbyUMtgxpump",
                            ui_token_amount: UiTokenAmount {
                                ui_amount: Some(
                                    *********.406213,
                                ),
                                decimals: 6,
                                amount: "*********406213",
                                ui_amount_string: "*********.406213",
                            },
                            owner: Some(
                                "5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1",
                            ),
                            program_id: Some(
                                "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
                            ),
                        },
                        UiTransactionTokenBalance {
                            account_index: 13,
                            mint: "So11111111111111111111111111111111111111112",
                            ui_token_amount: UiTokenAmount {
                                ui_amount: Some(
                                    80.*********,
                                ),
                                decimals: 9,
                                amount: "80*********",
                                ui_amount_string: "80.*********",
                            },
                            owner: Some(
                                "5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1",
                            ),
                            program_id: Some(
                                "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
                            ),
                        },
                    ],
                ),
                post_token_balances: Some(
                    [
                        UiTransactionTokenBalance {
                            account_index: 2,
                            mint: "zVfpeNDwYVEuEHd5xvQVepEu54CEib2HbyUMtgxpump",
                            ui_token_amount: UiTokenAmount {
                                ui_amount: Some(
                                    1399778.884216,
                                ),
                                decimals: 6,
                                amount: "*************",
                                ui_amount_string: "1399778.884216",
                            },
                            owner: Some(
                                "DB27eZhCif8t7ScPqUzruYYk3o1SjAcfdS2sCYJhemcn",
                            ),
                            program_id: Some(
                                "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
                            ),
                        },
                        UiTransactionTokenBalance {
                            account_index: 11,
                            mint: "zVfpeNDwYVEuEHd5xvQVepEu54CEib2HbyUMtgxpump",
                            ui_token_amount: UiTokenAmount {
                                ui_amount: Some(
                                    *********.521997,
                                ),
                                decimals: 6,
                                amount: "*********521997",
                                ui_amount_string: "*********.521997",
                            },
                            owner: Some(
                                "5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1",
                            ),
                            program_id: Some(
                                "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
                            ),
                        },
                        UiTransactionTokenBalance {
                            account_index: 13,
                            mint: "So11111111111111111111111111111111111111112",
                            ui_token_amount: UiTokenAmount {
                                ui_amount: Some(
                                    80.*********,
                                ),
                                decimals: 9,
                                amount: "80*********",
                                ui_amount_string: "80.*********",
                            },
                            owner: Some(
                                "5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1",
                            ),
                            program_id: Some(
                                "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
                            ),
                        },
                    ],
                ),
                rewards: None,
                loaded_addresses: Skip,
                return_data: Skip,
                compute_units_consumed: Skip,
            },
        ),
        version: Some(
            Number(
                0,
            ),
        ),
    },
)]