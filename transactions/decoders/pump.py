import base58
import json
import asyncio
from pathlib import Path
from solders.pubkey import Pubkey
from anchorpy import Idl, Program, Provider, Wallet
from solders.keypair import Keypair
from solana.rpc.async_api import AsyncClient

async def get_provider():
    # Load keypair from the file
    keypair = Keypair.from_json("[59,157,241,74,142,149,142,207,144,169,161,131,233,92,56,57,78,182,66,185,137,171,138,47,222,244,145,131,147,15,249,197,21,183,241,74,58,175,32,115,116,252,183,22,96,62,115,5,246,239,169,46,244,118,65,80,121,39,211,51,163,50,78,227]")
    wallet = Wallet(keypair)

    # Provide connection to a local cluster or actual Solana cluster
    client = AsyncClient("https://api.devnet.solana.com")
    provider = Provider(client, wallet)
    return provider

def to_serializable(obj):
    if isinstance(obj, dict):
        return {k: to_serializable(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [to_serializable(v) for v in obj]
    elif hasattr(obj, "__dict__"):
        return to_serializable(obj.__dict__)
    else:
        return obj

class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, Pubkey):
            return str(obj)
        if hasattr(obj, "__dict__"):
            return obj.__dict__
        return super().default(obj)

async def decode_base58_data(base58_data):
    # Read the generated IDL.
    module_dir = Path(__file__).resolve().parent
    idl_path = module_dir / "pump-fun.json"
    with idl_path.open() as f:
        raw_idl = f.read()
    idl = Idl.from_json(raw_idl)

    # Address of the deployed program.
    program_id = Pubkey.from_string("6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P")

    # Initialize the provider and program
    provider = await get_provider()
    program = Program(idl, program_id, provider)

    buffer0 = base58.b58decode(base58_data)
    buffer = buffer0[8:]

    decoded_instruction = program.coder.events.parse(buffer)
    if not decoded_instruction:
        decoded_instruction = program.coder.instruction.parse(buffer0)
    json_output = json.loads(json.dumps(to_serializable(decoded_instruction), cls=CustomJSONEncoder, indent=4))

    # Close the provider connection
    await provider.connection.close()

    return json_output

def decode(base58_data):
    return asyncio.run(decode_base58_data(base58_data))
