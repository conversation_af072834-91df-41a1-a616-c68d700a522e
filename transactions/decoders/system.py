#!/usr/bin/env python3
import base58,json
from construct import Int32ul, Int64ul, Pass, Struct as cStruct, Switch
from enum import IntEnum
from solders.pubkey import Pubkey

# Placeholder layouts for PUBLIC_KEY_LAYOUT and RUST_STRING_LAYOUT
PUBLIC_KEY_LAYOUT = cStruct("public_key" / Int64ul)  # Replace with actual layout
RUST_STRING_LAYOUT = cStruct("length" / Int32ul, "string" / Int32ul)  # Replace with actual layout

# Define the InstructionType enum
class InstructionType(IntEnum):
    CREATE_ACCOUNT = 0
    ASSIGN = 1
    TRANSFER = 2
    CREATE_ACCOUNT_WITH_SEED = 3
    ADVANCE_NONCE_ACCOUNT = 4
    WITHDRAW_NONCE_ACCOUNT = 5
    INITIALIZE_NONCE_ACCOUNT = 6
    AUTHORIZE_NONCE_ACCOUNT = 7
    ALLOCATE = 8
    ALLOCATE_WITH_SEED = 9
    ASSIGN_WITH_SEED = 10
    TRANSFER_WITH_SEED = 11

# Define the layout structures
_CREATE_ACCOUNT_LAYOUT = cStruct(
    "lamports" / Int64ul,
    "space" / Int64ul,
    "program_id" / PUBLIC_KEY_LAYOUT,
)

_ASSIGN_LAYOUT = cStruct("program_id" / PUBLIC_KEY_LAYOUT)

_TRANSFER_LAYOUT = cStruct("lamports" / Int64ul)

_CREATE_ACCOUNT_WITH_SEED_LAYOUT = cStruct(
    "base" / PUBLIC_KEY_LAYOUT,
    "seed" / RUST_STRING_LAYOUT,
    "lamports" / Int64ul,
    "space" / Int64ul,
    "program_id" / PUBLIC_KEY_LAYOUT,
)

_WITHDRAW_NONCE_ACCOUNT_LAYOUT = cStruct("lamports" / Int64ul)

_INITIALIZE_NONCE_ACCOUNT_LAYOUT = cStruct("authorized" / PUBLIC_KEY_LAYOUT)

_AUTHORIZE_NONCE_ACCOUNT_LAYOUT = cStruct("authorized" / PUBLIC_KEY_LAYOUT)

_ALLOCATE_LAYOUT = cStruct("space" / Int64ul)

_ALLOCATE_WITH_SEED_LAYOUT = cStruct(
    "base" / PUBLIC_KEY_LAYOUT, "seed" / RUST_STRING_LAYOUT, "space" / Int64ul, "program_id" / PUBLIC_KEY_LAYOUT
)

_ASSIGN_WITH_SEED_LAYOUT = cStruct(
    "base" / PUBLIC_KEY_LAYOUT, "seed" / RUST_STRING_LAYOUT, "program_id" / PUBLIC_KEY_LAYOUT
)

_TRANSFER_WITH_SEED_LAYOUT = cStruct(
    "lamports" / Int64ul,
    "from_seed" / RUST_STRING_LAYOUT,
    "from_owner" / PUBLIC_KEY_LAYOUT,
)

SYSTEM_INSTRUCTIONS_LAYOUT = cStruct(
    "instruction_type" / Int32ul,
    "args"
    / Switch(
        lambda this: this.instruction_type,
        {
            InstructionType.CREATE_ACCOUNT: _CREATE_ACCOUNT_LAYOUT,
            InstructionType.ASSIGN: _ASSIGN_LAYOUT,
            InstructionType.TRANSFER: _TRANSFER_LAYOUT,
            InstructionType.CREATE_ACCOUNT_WITH_SEED: _CREATE_ACCOUNT_WITH_SEED_LAYOUT,
            InstructionType.ADVANCE_NONCE_ACCOUNT: Pass,  # No args
            InstructionType.WITHDRAW_NONCE_ACCOUNT: _WITHDRAW_NONCE_ACCOUNT_LAYOUT,
            InstructionType.INITIALIZE_NONCE_ACCOUNT: _INITIALIZE_NONCE_ACCOUNT_LAYOUT,
            InstructionType.AUTHORIZE_NONCE_ACCOUNT: _AUTHORIZE_NONCE_ACCOUNT_LAYOUT,
            InstructionType.ALLOCATE: _ALLOCATE_LAYOUT,
            InstructionType.ALLOCATE_WITH_SEED: _ALLOCATE_WITH_SEED_LAYOUT,
            InstructionType.ASSIGN_WITH_SEED: _ASSIGN_WITH_SEED_LAYOUT,
            InstructionType.TRANSFER_WITH_SEED: _TRANSFER_WITH_SEED_LAYOUT,
        },
    ),
)

def to_serializable(obj):
    if isinstance(obj, dict):
        return {k: to_serializable(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [to_serializable(v) for v in obj]
    elif hasattr(obj, "__dict__"):
        return to_serializable(obj.__dict__)
    else:
        return obj

class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, Pubkey):
            return str(obj)
        if hasattr(obj, "__dict__"):
            return obj.__dict__
        return super().default(obj)
    
def decode_system_program_instruction(encoded_data):
    # Base58 decode the data
    decoded_bytes = base58.b58decode(encoded_data)
    
    # Parse the decoded bytes using the SYSTEM_INSTRUCTIONS_LAYOUT
    parsed_data = SYSTEM_INSTRUCTIONS_LAYOUT.parse(decoded_bytes)
    
    return parsed_data

def process_parsed_data(parsed_data):
    instruction_type = parsed_data.instruction_type
    args = parsed_data.args

    print(f"Instruction Type: {instruction_type}")
    
    # Handle different instruction types
    print(instruction_type)
    if instruction_type == InstructionType.TRANSFER:
        lamports = args.lamports
        print(f"Transfer Lamports: {lamports}")
    elif instruction_type == InstructionType.CREATE_ACCOUNT:
        lamports = args.lamports
        space = args.space
        program_id = args.program_id.public_key  # Adjust based on actual PUBLIC_KEY_LAYOUT
        print(f"Create Account Lamports: {lamports}")
        print(f"Space: {space}")
        print(f"Program ID: {program_id}")
    # Add more cases as needed for other instruction types

def decode1(encoded_data):
    parsed_data = decode_system_program_instruction(encoded_data)
    return parsed_data

def decode(encoded_data):
    parsed_data = decode_system_program_instruction(encoded_data)
    json_output = json.loads(json.dumps(to_serializable(parsed_data), cls=CustomJSONEncoder, indent=4))
    
    return json_output