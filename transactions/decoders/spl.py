#!/usr/bin/env python3
import base58
import json
from construct import Bytes, Int8ul, Int32ul, Int64ul, Pass, Struct as cStruct, Switch
from enum import IntEnum

# Define the InstructionType enum for SPL Token instructions
class InstructionType(IntEnum):
    INITIALIZE_MINT = 0
    INITIALIZE_ACCOUNT = 1
    INITIALIZE_MULTISIG = 2
    TRANSFER = 3
    APPROVE = 4
    REVOKE = 5
    SET_AUTHORITY = 6
    MINT_TO = 7
    BURN = 8
    CLOSE_ACCOUNT = 9
    FREEZE_ACCOUNT = 10
    THAW_ACCOUNT = 11
    TRANSFER2 = 12
    APPROVE2 = 13
    MINT_TO2 = 14
    BURN2 = 15
    SYNC_NATIVE = 17

# Define the layout structures
PUBLIC_KEY_LAYOUT = Bytes(32)

_INITIALIZE_MINT_LAYOUT = cStruct(
    "decimals" / Int8ul,
    "mint_authority" / PUBLIC_KEY_LAYOUT,
    "freeze_authority_option" / Int8ul,
    "freeze_authority" / PUBLIC_KEY_LAYOUT,
)

_INITIALIZE_MULTISIG_LAYOUT = cStruct("m" / Int8ul)

_AMOUNT_LAYOUT = cStruct("amount" / Int64ul)

_SET_AUTHORITY_LAYOUT = cStruct(
    "authority_type" / Int8ul,
    "new_authority_option" / Int8ul,
    "new_authority" / PUBLIC_KEY_LAYOUT,
)

_AMOUNT2_LAYOUT = cStruct("amount" / Int64ul, "decimals" / Int8ul)

INSTRUCTIONS_LAYOUT = cStruct(
    "instruction_type" / Int8ul,
    "args"
    / Switch(
        lambda this: this.instruction_type,
        {
            InstructionType.INITIALIZE_MINT: _INITIALIZE_MINT_LAYOUT,
            InstructionType.INITIALIZE_ACCOUNT: Pass,
            InstructionType.INITIALIZE_MULTISIG: _INITIALIZE_MULTISIG_LAYOUT,
            InstructionType.TRANSFER: _AMOUNT_LAYOUT,
            InstructionType.APPROVE: _AMOUNT_LAYOUT,
            InstructionType.REVOKE: Pass,
            InstructionType.SET_AUTHORITY: _SET_AUTHORITY_LAYOUT,
            InstructionType.MINT_TO: _AMOUNT_LAYOUT,
            InstructionType.BURN: _AMOUNT_LAYOUT,
            InstructionType.CLOSE_ACCOUNT: Pass,
            InstructionType.FREEZE_ACCOUNT: Pass,
            InstructionType.THAW_ACCOUNT: Pass,
            InstructionType.TRANSFER2: _AMOUNT2_LAYOUT,
            InstructionType.APPROVE2: _AMOUNT2_LAYOUT,
            InstructionType.MINT_TO2: _AMOUNT2_LAYOUT,
            InstructionType.BURN2: _AMOUNT2_LAYOUT,
        },
    ),
)

def to_serializable(obj):
    if isinstance(obj, dict):
        return {k: to_serializable(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [to_serializable(v) for v in obj]
    elif hasattr(obj, "__dict__"):
        return to_serializable(obj.__dict__)
    else:
        return obj

class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, bytes):
            return obj.hex()
        if hasattr(obj, "__dict__"):
            return obj.__dict__
        return super().default(obj)
    
def decode_spl_token_instruction(encoded_data):
    # Base58 decode the data
    decoded_bytes = base58.b58decode(encoded_data)
    
    # Parse the decoded bytes using the INSTRUCTIONS_LAYOUT
    parsed_data = INSTRUCTIONS_LAYOUT.parse(decoded_bytes)
    
    return parsed_data

def process_parsed_data(parsed_data):
    instruction_type = parsed_data.instruction_type
    args = parsed_data.args

    print(f"Instruction Type: {instruction_type}")
    
    # Handle different instruction types
    if instruction_type == InstructionType.TRANSFER:
        amount = args.amount
        print(f"Transfer Amount: {amount}")
    elif instruction_type == InstructionType.INITIALIZE_MINT:
        decimals = args.decimals
        mint_authority = args.mint_authority
        freeze_authority_option = args.freeze_authority_option
        freeze_authority = args.freeze_authority
        print(f"Initialize Mint Decimals: {decimals}")
        print(f"Mint Authority: {mint_authority.hex()}")
        print(f"Freeze Authority Option: {freeze_authority_option}")
        print(f"Freeze Authority: {freeze_authority.hex()}")
    # Add more cases as needed for other instruction types

def decode1(encoded_data):
    parsed_data = decode_spl_token_instruction(encoded_data)
    return parsed_data

def decode(encoded_data):
    parsed_data = decode_spl_token_instruction(encoded_data)
    json_output = json.loads(json.dumps(to_serializable(parsed_data), cls=CustomJSONEncoder, indent=4))
    
    return json_output
