#!/usr/bin/env python3
import json
import threading
import time
import requests
import sys,json

def getTokenAccounts(pump_address):
    rpc_url ='https://mainnet.helius-rpc.com/?api-key=094cf3fa-bf7c-46cd-9b40-df49beb9b6c3'

    request = {
        "jsonrpc": "2.0",
        "id": "1",
        "method": "getProgramAccounts",
                "params": [
        "675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8",
        {
            "encoding": "jsonParsed",
            "commitment": "recent",
            "filters": [
                {
                    "memcmp": {
                        "offset": 44,
                        "bytes": "aa"
                    }
                }
            ]
        }
    ],

        }


    request = requests.post(rpc_url, json=request)
    data = request
    print(data.text)


#main rcp call  __main__

if __name__ == "__main__":
    args = sys.argv
    getTokenAccounts(args[1])
