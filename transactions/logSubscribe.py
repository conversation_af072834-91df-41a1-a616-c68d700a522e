#!/usr/bin/env python3
import asyncio
import websockets
import json

async def subscribe_to_logs():
    uri = "wss://little-sleek-mansion.solana-mainnet.quiknode.pro/4b69dc2b49ac07ae787d9c65b8a33ccee7a904d2"  # Replace with the correct WebSocket URL for Solana

    async with websockets.connect(uri) as websocket:
        # First logsSubscribe request with mentions
        request_mention = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "logsSubscribe",
            "params": [
                {
                    "mentions": ["CWJikWDsVCfyosmz5LgDMY3ArLDEN1xyCbsVcZ69X4oy"]
                },
                {
                    "commitment": "confirmed"
                }
            ]
        }

        # Second logsSubscribe request for all logs
        request_all = {
            "jsonrpc": "2.0",
            "id": 2,
            "method": "logsSubscribe",
            "params": ["all"]
        }

        # Send the requests
        await websocket.send(json.dumps(request_mention))
        #await websocket.send(json.dumps(request_all))

        while True:
            response = await websocket.recv()

            if "params" in response:

                # Uncomment the following line to print the signature of the first transaction in the logs
                data=json.loads(response)
                print(data["params"]["result"]["value"]["signature"])

# Run the asyncio event loop
asyncio.get_event_loop().run_until_complete(subscribe_to_logs())

