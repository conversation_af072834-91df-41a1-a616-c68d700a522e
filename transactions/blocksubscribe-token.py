#!/usr/bin/env python3
import asyncio
from datetime import datetime, timedelta
from solana.rpc.async_api import AsyncClient
from solana.rpc.websocket_api import connect
from solana.rpc.websocket_api import RpcBlockSubscribeFilterMentions
from solders.pubkey import Pubkey
from solders.transaction_status import TransactionDetails
import logging
import pika

import sys

#print(metadata.get_token_info("M4tCEjM9B7k8kNMVBtAfktmRJShMjVrdPWZDZboXfQG"))


logger = logging.getLogger('newtokens')
logger.setLevel(logging.INFO)

# Create console handler and set level to info
ch = logging.StreamHandler()
ch.setLevel(logging.INFO)

# Create formatter and add it to the handler
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
ch.setFormatter(formatter)

# Add the handler to the logger
logger.addHandler(ch)

# Suppress logging from other modules
logging.getLogger('asyncio').setLevel(logging.WARNING)
logging.getLogger('pika').setLevel(logging.WARNING)
logging.getLogger('requests').setLevel(logging.WARNING)
logging.getLogger('solana').setLevel(logging.WARNING)
logging.getLogger('solders').setLevel(logging.WARNING)



pool = None

# Replace with your specific account address

ACCOUNT_ADDRESS = "zVfpeNDwYVEuEHd5xvQVepEu54CEib2HbyUMtgxpump"
#ACCOUNT_ADDRESS = "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P"

# Define the WebSocket link as a variable
WS_URL = "wss://go.getblock.io/7fd33c9eaa0e45bfa986ee41b9d216e2"
#WS_URL = "wss://endpoints.omniatech.io/v1/ws/sol/mainnet/b6efe195db51499ca3719ad3eb838aa6"
#WS_URL = "wss://little-sleek-mansion.solana-mainnet.quiknode.pro/4b69dc2b49ac07ae787d9c65b8a33ccee7a904d2/"
RPC_URL = "https://api.mainnet-beta.solana.com"
RPC_URL = "https://little-sleek-mansion.solana-mainnet.quiknode.pro/4b69dc2b49ac07ae787d9c65b8a33ccee7a904d2/" 


TOKEN_ACCOUNT_LIST=[]
TOKEN_ACCOUNT_LIST_DATE=[]
MIN_TOKEN_AMOUNT=3
MAX_TOKEN_LOGGING=600
RABBITMQ_SERVER="***************"


async def send_message(message,queue="token"):
    credentials = pika.PlainCredentials('guest', 'guest')
    parameters = pika.ConnectionParameters(RABBITMQ_SERVER, 5672, '/', credentials)
    connection = pika.BlockingConnection(parameters)
    channel = connection.channel()

    # Create a queue named 'task_queue'
    channel.queue_declare(queue=queue, durable=True)

    # Publish the message to the queue
    channel.basic_publish(
        exchange='',
        routing_key=queue,
        body=message,
        properties=pika.BasicProperties(
            delivery_mode=2,  # Make message persistent
        ))

    connection.close()

def find_account(account_list, account_to_find):
    for account in account_list:
        if account['account'] == account_to_find:
            return account
    return None


async def handle_block_notifications(account_address):
    while True:
        try:
            async with connect(WS_URL) as websocket:
                async with AsyncClient(RPC_URL) as client:
                    await websocket.block_subscribe(transaction_details=TransactionDetails.Full,filter_=RpcBlockSubscribeFilterMentions(Pubkey.from_string(ACCOUNT_ADDRESS)),commitment="confirmed",encoding="jsonParsed",max_supported_transaction_version=0 )
                    logger.info("Connected")
                    response = await websocket.recv()
                    while True:
                        response = await websocket.recv()
                        await fetch_and_filter_transactions(response)
        except Exception as e:
                    print(e)
                    await asyncio.sleep(1)  # Wait before reconnecting

def find_transactions_in_block_by_address(block,token):
    found=0
    solsum=0
    for tx in block.result.value.block.transactions:
        for addr in tx.transaction.account_keys:
            if str(token) == str(addr.pubkey):
                solsum=solsum+abs(tx.meta.post_balances[0]-tx.meta.pre_balances[0])
                found=found+1
                
    return {'amount':solsum/**********,"found":found}

async def fetch_and_filter_transactions(blocks):

    for block in blocks:
        print("============block====================")
        #print(block.result.value.block.transactions)
        for tx in block.result.value.block.transactions:
            if tx.meta.err == None:
                print(tx)
                continue
                #accounts=tx.transaction.account_keys[0].pubkey
                pre_balances = tx.meta.pre_balances
                post_balances = tx.meta.post_balances

                if hasattr(tx, 'transaction') and hasattr(tx.transaction, 'account_keys'):
                    from_account=tx.transaction.account_keys[0].pubkey
                else:
                    logger.info(f"Failed: transaction cannot find account_keys")
                    return

            
        
                #if tx.meta.post_token_balances[0].mint == Pubkey.from_string(token):
                amount_token=0
                amount_sol=0
                #try:
                if True:
                    if len(tx.meta.pre_token_balances) == 0 and len(tx.meta.post_token_balances) > 0:
                        if tx.meta.post_token_balances[0].owner == from_account:
                            to_account=tx.meta.post_token_balances[1].owner
                        else:
                            to_account=tx.meta.post_token_balances[0].owner
                        for post_token_balance in tx.meta.post_token_balances:
                            if post_token_balance.owner == from_account:
                                amount_token=abs(post_token_balance.ui_token_amount.ui_amount)
                        
                        amount_sol=abs(post_balances[0]-pre_balances[0])/**********

                        print(amount_sol)
    
                #except:
                #    logger.info(tx)
            else:
                pass
                #logger.info("Transaction has error")



if __name__ == "__main__":
    
    asyncio.get_event_loop().run_until_complete(handle_block_notifications(ACCOUNT_ADDRESS))


