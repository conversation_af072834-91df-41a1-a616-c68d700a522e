import asyncio
from solana.rpc.async_api import Async<PERSON><PERSON>
from typing import Optional
from solders.pubkey import Pubkey

SOLANA_RPC_URL = "https://little-sleek-mansion.solana-mainnet.quiknode.pro/4b69dc2b49ac07ae787d9c65b8a33ccee7a904d2"  # Example RPC URL, adjust as necessary

async def get_oldest_transaction_signature(address: str) -> Optional[str]:
    async with <PERSON>ync<PERSON><PERSON>(SOLANA_RPC_URL) as client:
        # Start with None to get the most recent signatures first
        before = None
        oldest_signature = None

        address = Pubkey.from_string(address)

        while True:
            # Fetch a batch of signatures
            signatures_response = await client.get_signatures_for_address(
                address, before=before, limit=3
            )
            signatures = signatures_response.value
            if not signatures:
                break  # No more signatures found
            # Update the oldest signature
            oldest_signature = signatures[-1].signature
            # Set the 'before' parameter to the oldest signature in the current batch
            before = oldest_signature
            print(before)
    if oldest_signature:
        return str(oldest_signature)
    else:
        return None

# Usage example
async def main():
    address = "EaYz4CuA2LcNU5hCZrRi67VzFWeegJ44w6JvnPSHpump"  # Replace with the actual address
    oldest_signature = await get_oldest_transaction_signature(address)
    print(f"Oldest Transaction Signature: {oldest_signature}")

if __name__ == "__main__":
    asyncio.run(main())