#!/usr/bin/env python3
import requests
import json
import sys,os
import  decoders.pump as pump
import decoders.system as system
import decoders.spl as spl
import os
import pika
import requests
import threading
import time
import logging
import json
import mysql.connector
from mysql.connector import errorcode
from datetime import datetime
import aiomysql
import asyncio



# base58_data = "2K7nL28PxCW8ejnyCeuMpbW4z2r7nbUrm3t5K5TQraAvKZkA9YEwPu6UKKDicLH2s3GvUb21ngfbNVRaWqVNwDdTheebEyhab5rZLxGK9nTsPSg39QhxDDgHMkmvfKkN5Swd7zN26w4rQgVvHUxwhWQ5W5XrDm3mAJk1Jx2mzgu4TnYb36oi4tn2LFaf"
# base58_data = "5jRcjdixRUDgzecaLFnPY1aCRHz1J4fkb"
# json_output = pump.decode(base58_data)
# print(json_output)


# encoded_data ="3Bxs4TgLZ72zxZ1y"
# decoded_data = system.decode(encoded_data)
# print(decoded_data)



# Define the JSON-RPC endpoint
url = "https://little-sleek-mansion.solana-mainnet.quiknode.pro/4b69dc2b49ac07ae787d9c65b8a33ccee7a904d2/"
url="https://api.mainnet-beta.solana.com"

url = "https://endpoints.omniatech.io/v1/sol/mainnet/b6efe195db51499ca3719ad3eb838aa6"  # Example RPC URL, adjust as necessary

batch_request1 = [
  
        {
        "jsonrpc": "2.0",
        "id": 1,
        "method": "getTransaction",
        "params": ["2YKscqKdRmgG4aSNGMqf3oQP6uZN5nkPytvgMoPWkR9jxfDfbQVexY4LWahLJUp1fE5AgF1w8HmLm1ZSTpmSQVWV", {"maxSupportedTransactionVersion":0,"encoding": "json"}]
    }  
    
    # Add more transactions as needed
]


batch_request = [
    {
        "jsonrpc": "2.0",
        "id": 1,
        "method": "getTransaction",
        "params": ["hsgzkYcR4F2WwcTypisLChc6o1429C8QwW5eg1uCFfY6yP1HRnfzD9UbwZzSJmExeNDZvtUt43wztGUs3QaC9Kt", {"maxSupportedTransactionVersion":0,"encoding": "json"}]
    },
        {
        "jsonrpc": "2.0",
        "id": 2,
        "method": "getTransaction",
        "params": ["677tutkSyeV9YE8W7GRDAfGFg43Qb4MQMxg1evWZ5gKhSRAUGonvTWWa7mKLbEctRWoisoDwpFgHPKdygPC8UeFY", {"maxSupportedTransactionVersion":0,"encoding": "json"}]
    },

        {
        "jsonrpc": "2.0",
        "id": 3,
        "method": "getTransaction",
        "params": ["2YKscqKdRmgG4aSNGMqf3oQP6uZN5nkPytvgMoPWkR9jxfDfbQVexY4LWahLJUp1fE5AgF1w8HmLm1ZSTpmSQVWV", {"maxSupportedTransactionVersion":0,"encoding": "json"}]
    }  
   ,

        {
        "jsonrpc": "2.0",
        "id": 4,
        "method": "getTransaction",
        "params": ["5Gwv9gVYGeyQs2vBZ73968XqEmoPyLsh2F4ojtWR2u4vj7WfsyXp7awHvzS3Uz7txPmhzmd8Q4n6ZW2K9tjoTwBw", {"maxSupportedTransactionVersion":0,"encoding": "json"}]
    }  
       ,

        {
        "jsonrpc": "2.0",
        "id": 5,
        "method": "getTransaction",
        "params": ["29X24Pn1DiPS6ewj4mGvzpqQx2W1pz3Eo3j99VqNhEz67ZV2g52PaXiHfgAPEy4aEH9aLzJNXjkniX6EqXLKUmAV", {"maxSupportedTransactionVersion":0,"encoding": "json"}]
    }  
    

    # Add more transactions as needed
]

# Convert the request to JSON
batch_request_json = json.dumps(batch_request)

# Send the POST request
response = requests.post(url, headers={"Content-Type": "application/json"}, data=batch_request_json)
#print(response.text)
with open('out.json', 'w') as json_file:
    json.dump(json.loads(response.text), json_file, indent=4)

response=json.loads(response.text)[4]

tx=response['result']

print(f"slot: {tx['slot']}")
print(f"blockTime: {tx['blockTime']}")
SYSTEM_FEE_ACCOUNT="HWEoBxYs7ssKuudEjzjmpfJVX7Dvi7wescFsVx2L5yoY"
PUMP_FEE_ACCOUNT="CebN5WGQ4jvEPvsVU4EoHEpgzq1VV7AbicfhtW4xC9iM"
JITO_FEE = [
    "96gYZGLnJYVFmbjzopPSU6QiEV5fGqZNyN9nmNhvrZU5",
    "HFqU5x63VTqvQss8hp11i4wVV8bD44PvwucfZ2bU7gRe",
    "Cw8CFyM9FkoMi7K7Crf6HNQqf4uEMzpKw6QNghXLvLkY",
    "ADaUMid9yfUytqMBgopwjb2DTLSokTSzL1zt6iGPaS49",
    "DfXygSm4jCyNCybVYYK6DwvWqjKee8pbDmJGcLWNDXjh",
    "ADuUkR4vqLUMWXxW9gh6D6L8pMSawimctcNZ5pGwDcEt",
    "DttWaMuVvTiduZRnguLF7jNxTgiMBZ1hyAumKUiL2KRL",
    "3AVi9Tg9Uo68tJfuvoKvqKNWKkC5wPdSSdeBnizKZ6jT"
]

FEE_ACCOUNTS=[SYSTEM_FEE_ACCOUNT,PUMP_FEE_ACCOUNT]+JITO_FEE
SYSTEM_ACCOUNT="11111111111111111111111111111111"
TOKENPROGRAM_ACCOUNT="TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"
PUMP_PROGRAM="6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P"

ACCOUNT_KEYS=tx['transaction']['message']['accountKeys']+tx['meta']['loadedAddresses']['writable']+tx['meta']['loadedAddresses']['readonly']

try:
    PUMP_INDEX=ACCOUNT_KEYS.index(PUMP_PROGRAM)
except:
    PUMP_INDEX=None
try:    
    SYSTEM_INDEX=ACCOUNT_KEYS.index(SYSTEM_ACCOUNT)
except:
    SYSTEM_INDEX=None
try:
    TOKEN_INDEX=ACCOUNT_KEYS.index(TOKENPROGRAM_ACCOUNT)
except:
    TOKEN_INDEX=None
found=False
ixcount=0

for i in tx['transaction']['message']['instructions']:
    if i["programIdIndex"] == PUMP_INDEX:
        pump_decode=pump.decode(i['data'])
        if isinstance(pump_decode, list) and pump_decode[0] == 'TradeEvent':
            if pump_decode[1]['is_buy']:
                trade="buy"
            else:
                trade="sell"
            BONDING_CURVE_ADDRESS=ACCOUNT_KEYS[i['accounts'][3]]
            print(f"TRADE: {trade}  User:{pump_decode[1]['user']}  TADDRESS: {BONDING_CURVE_ADDRESS}   Token:{pump_decode[1]['token_amount']}  Sol:{pump_decode[1]['sol_amount']}")

    if i["programIdIndex"] == SYSTEM_INDEX:
        sys_decode=system.decode(i['data'])
        if sys_decode['instruction_type'] == 2 and not ( ACCOUNT_KEYS[i['accounts'][1]] in FEE_ACCOUNTS):
            print(f"TRANSFER from    : {ACCOUNT_KEYS[i['accounts'][0]]}  to {ACCOUNT_KEYS[i['accounts'][1]]}  SOL: {sys_decode['args']['lamports']/**********:.4f}")   

    if i["programIdIndex"] == TOKEN_INDEX:
        sys_decode=spl.decode(i['data'])
        if sys_decode['instruction_type'] == 3 and not ( ACCOUNT_KEYS[i['accounts'][1]] in FEE_ACCOUNTS):
            print(f"TRANSFER SPL from: {ACCOUNT_KEYS[i['accounts'][0]]}  to {ACCOUNT_KEYS[i['accounts'][1]]}  SOL: {sys_decode['args']['amount']}")   


    for ii in tx['meta']['innerInstructions']:
        if ii['index'] == ixcount:
            for iii in ii['instructions']:
                if iii["programIdIndex"] == PUMP_INDEX:
                    pump_decode=pump.decode(iii['data'])
                    if isinstance(pump_decode, list) and pump_decode[0] == 'TradeEvent':
                        if pump_decode[1]['is_buy']:
                            trade="buy"
                        else:
                            trade="sell"
                        BONDING_CURVE_ADDRESS=ACCOUNT_KEYS[i['accounts'][3]]
                        print(f"TRADE: {trade}    User:{pump_decode[1]['user']}  TADDRESS: {BONDING_CURVE_ADDRESS}   Token:{pump_decode[1]['token_amount']}  Sol:{pump_decode[1]['sol_amount']}")
                if iii["programIdIndex"] == SYSTEM_INDEX:
                    sys_decode=system.decode(iii['data'])
                    if sys_decode['instruction_type'] == 2 and not ( ACCOUNT_KEYS[iii['accounts'][1]] in FEE_ACCOUNTS):
                        print(f"TRANSFER from    : {ACCOUNT_KEYS[iii['accounts'][0]]}  to {ACCOUNT_KEYS[iii['accounts'][1]]}  SOL: {sys_decode['args']['lamports']/**********:.4f}")   
                if iii["programIdIndex"] == TOKEN_INDEX:
                    sys_decode=spl.decode(iii['data'])
                    if sys_decode['instruction_type'] == 3 and not ( ACCOUNT_KEYS[iii['accounts'][1]] in FEE_ACCOUNTS):
                        print(f"TRANSFER SPL from: {ACCOUNT_KEYS[iii['accounts'][0]]}  to {ACCOUNT_KEYS[iii['accounts'][1]]}  SOL: {sys_decode['args']['amount']}")   
     

    ixcount=ixcount+1




sys.exit()

ixcount=0
for i in tx['transaction']['message']['instructions']:
    if i["programIdIndex"] == PUMP_INDEX and len(i['accounts'])==12:
        found=True
        mint=ACCOUNT_KEYS[i['accounts'][2]]
        owner_account=ACCOUNT_KEYS[i['accounts'][6]]
        token_account=ACCOUNT_KEYS[i['accounts'][3]]
        preTokenAmount=0
        postTokenAmount=0
        for preTokenBalance in tx['meta']['preTokenBalances']:
            if preTokenBalance['owner'] == owner_account:
                preTokenAmount=preTokenBalance['uiTokenAmount']['uiAmountString']
        for postTokenBalance in tx['meta']['postTokenBalances']:
            if postTokenBalance['owner'] == owner_account:
                postTokenAmount=postTokenBalance['uiTokenAmount']['uiAmountString']

        preBalance=tx['meta']['preBalances'][ACCOUNT_KEYS.index(owner_account)]  
        postBalance=tx['meta']['postBalances'][ACCOUNT_KEYS.index(owner_account)]                   
        print(mint,owner_account,token_account,float(preTokenAmount),float(postTokenAmount),preBalance,postBalance)

    for ii in tx['meta']['innerInstructions']:
        if ii['index'] == ixcount:
            for i in ii['instructions']:
                if i["programIdIndex"] == PUMP_INDEX and len(i['accounts'])==12:
                    found=True
                    mint=ACCOUNT_KEYS[i['accounts'][2]]
                    owner_account=ACCOUNT_KEYS[i['accounts'][6]]
                    token_account=ACCOUNT_KEYS[i['accounts'][3]]
                    preTokenAmount=0
                    postTokenAmount=0
                    for preTokenBalance in tx['meta']['preTokenBalances']:
                        if preTokenBalance['owner'] == owner_account:
                            preTokenAmount=preTokenBalance['uiTokenAmount']['uiAmountString']
                    for postTokenBalance in tx['meta']['postTokenBalances']:
                        if postTokenBalance['owner'] == owner_account:
                            postTokenAmount=postTokenBalance['uiTokenAmount']['uiAmountString']
                    preBalance=tx['meta']['preBalances'][ACCOUNT_KEYS.index(owner_account)]  
                    postBalance=tx['meta']['postBalances'][ACCOUNT_KEYS.index(owner_account)]                   
                    print(mint,owner_account,token_account,float(preTokenAmount),float(postTokenAmount),preBalance,postBalance)
    ixcount=ixcount+1


sys.exit()
TX_ADDRESSES=[]
for ptb in tx['meta']['postTokenBalances']:
    if ptb['mint'] != "So11111111111111111111111111111111111111112":
        TX_ADDRESSES.append(ptb['owner'])

print(TX_ADDRESSES)
# token program transfer : TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA 
#" if rydiumprogram: 675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8 index:2 rydium acc,  16(last)
# if pumpfun program: 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P 
                        #store_transaction(mint, from_address, to_address, send_type, send_amount, receive_type, receive_amount)

    # mint VARCHAR(255),
    # signature VARCHAR(255),
    # from_address VARCHAR(255),
    # to_address VARCHAR(255),
    # currency VARCHAR(50),
    # amount DECIMAL(30, 20),
    # block INTEGER NOT NULL,
    # block_time TIMESTAMP ,  ???
    # transaction_date TIMESTAMP




# if len(tx.meta.preTokenBalances) == 0 and len(tx.meta.post_token_balances) > 0:
#     if tx.meta.post_token_balances[0].owner == from_account:
#         to_account=tx.meta.post_token_balances[1].owner
#     else:
#         to_account=tx.meta.post_token_balances[0].owner
#     for post_token_balance in tx.meta.post_token_balances:
#         if post_token_balance.owner == from_account:
#             amount_token=abs(post_token_balance.ui_token_amount.ui_amount)
    
#     amount_sol=abs(post_balances[0]-pre_balances[0])/**********

#     #tx_in_same_block=find_transactions_in_block_by_address(block,str(to_account))
#     tx_in_same_block=find_transactions_in_block_by_address(block,str(to_account))
#     if tx_in_same_block['found'] > 1:
#         total_amount_in_same_block=tx_in_same_block['amount']+amount_sol
#     else:
#         total_amount_in_same_block=amount_sol
#     if total_amount_in_same_block> MIN_TOKEN_AMOUNT:
#         logger.info(f"Adding to logging: {str(to_account)}")
#         #post token with rabbitmq (tx.meta.post_token_balances[0].mint)
#         logger.info(f"MINT: {tx.meta.post_token_balances[0].mint} Budget: {total_amount_in_same_block}  Adding to RABBIT")
#         await send_message(str(tx.meta.post_token_balances[0].mint),queue="token")
#     #logger.info(f"NEW token:{tx.meta.post_token_balances[0].mint} from:{from_account}   to:{to_account}  token:{amount_token}  sol:{amount_sol} total(sol): {total_amount_in_same_block} total(tx): {tx_in_same_block['found']} ")



