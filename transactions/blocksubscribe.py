#!/usr/bin/env python3
import asyncio
from datetime import datetime, timedelta
from solana.rpc.async_api import AsyncClient
from solana.rpc.websocket_api import connect
from solana.rpc.websocket_api import RpcBlockSubscribeFilterMentions
from solders.pubkey import Pubkey
from solders.transaction_status import TransactionDetails
import logging
import pika

logger = logging.getLogger('newtokens')
logger.setLevel(logging.INFO)

# Create console handler and set level to info
ch = logging.StreamHandler()
ch.setLevel(logging.INFO)

# Create formatter and add it to the handler
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
ch.setFormatter(formatter)

# Add the handler to the logger
logger.addHandler(ch)

# Suppress logging from other modules
logging.getLogger('asyncio').setLevel(logging.WARNING)
logging.getLogger('pika').setLevel(logging.WARNING)
logging.getLogger('requests').setLevel(logging.WARNING)
logging.getLogger('solana').setLevel(logging.WARNING)
logging.getLogger('solders').setLevel(logging.WARNING)



pool = None

# Replace with your specific account address
token="4g5anAZZRYqLXKXeCN1Ti3kAxa3BXrQRdQQ23CkLpump" 
ACCOUNT_ADDRESS = "CNJRnF6oUFNYUWG1A6dkNaCwmX6ka2JYvzLWqaMNpump"
ACCOUNT_ADDRESS = "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P"

# Define the WebSocket link as a variable
WS_URL = "wss://endpoints.omniatech.io/v1/ws/sol/mainnet/438ffec158c5401e9b00d2de309a86a5"
WS_URL = "wss://little-sleek-mansion.solana-mainnet.quiknode.pro/4b69dc2b49ac07ae787d9c65b8a33ccee7a904d2/"
RPC_URL = "https://api.mainnet-beta.solana.com"
RPC_URL ="https://little-sleek-mansion.solana-mainnet.quiknode.pro/4b69dc2b49ac07ae787d9c65b8a33ccee7a904d2/" 

TOKEN_ACCOUNT_LIST=[]
TOKEN_ACCOUNT_LIST_DATE=[]
MIN_TOKEN_AMOUNT=3
MAX_TOKEN_LOGGING=600
RABBITMQ_SERVER="***************"
increment=0

async def send_message(message,queue="token"):
    credentials = pika.PlainCredentials('guest', 'guest')
    parameters = pika.ConnectionParameters(RABBITMQ_SERVER, 5672, '/', credentials)
    connection = pika.BlockingConnection(parameters)
    channel = connection.channel()

    # Create a queue named 'task_queue'
    channel.queue_declare(queue=queue, durable=True)

    # Publish the message to the queue
    channel.basic_publish(
        exchange='',
        routing_key=queue,
        body=message,
        properties=pika.BasicProperties(
            delivery_mode=2,  # Make message persistent
        ))

    connection.close()

def find_account(account_list, account_to_find):
    for account in account_list:
        if account['account'] == account_to_find:
            return account
    return None


async def handle_block_notifications(account_address):
    while True:
        try:
            async with connect(WS_URL) as websocket:
                async with AsyncClient(RPC_URL) as client:
                    await websocket.block_subscribe(transaction_details=TransactionDetails.Accounts,filter_=RpcBlockSubscribeFilterMentions(Pubkey.from_string(ACCOUNT_ADDRESS)),commitment="confirmed",encoding="json",max_supported_transaction_version=0 )
                    logger.info("Connected")
                    response = await websocket.recv()
                    while True:
                        response = await websocket.recv()
                        await fetch_and_filter_transactions(response)
        except websocket.exceptions.ConnectionClosed as e:
                    logger.info(f"Connection closed with code {e.code}: {e.reason}")
                    await asyncio.sleep(1)  # Wait before reconnecting

def find_transactions_in_block_by_address(block,token):
    found=0
    solsum=0
    for tx in block.result.value.block.transactions:
        for addr in tx.transaction.account_keys:
            if str(token) == str(addr.pubkey):
                solsum=solsum+abs(tx.meta.post_balances[0]-tx.meta.pre_balances[0])
                found=found+1
                
    return {'amount':solsum/**********,"found":found}

async def fetch_and_filter_transactions(blocks):

    for block in blocks:
        #logger.info(f"Block: {increment}")
        #increment=increment+1
        for tx in block.result.value.block.transactions:
            if tx.meta.err == None:
                #accounts=tx.transaction.account_keys[0].pubkey
                pre_balances = tx.meta.pre_balances
                post_balances = tx.meta.post_balances

                if hasattr(tx, 'transaction') and hasattr(tx.transaction, 'account_keys'):
                    from_account=tx.transaction.account_keys[0].pubkey
                else:
                    logger.info(f"Failed: transaction cannot find account_keys")
                    return

            
        
                #if tx.meta.post_token_balances[0].mint == Pubkey.from_string(token):
                amount_token=0
                amount_sol=0
                #try:
                if True:
                    if len(tx.meta.pre_token_balances) == 0 and len(tx.meta.post_token_balances) > 0:
                        if tx.meta.post_token_balances[0].owner == from_account:
                            to_account=tx.meta.post_token_balances[1].owner
                        else:
                            to_account=tx.meta.post_token_balances[0].owner
                        for post_token_balance in tx.meta.post_token_balances:
                            if post_token_balance.owner == from_account:
                                amount_token=abs(post_token_balance.ui_token_amount.ui_amount)
                        
                        amount_sol=abs(post_balances[0]-pre_balances[0])/**********

                        #tx_in_same_block=find_transactions_in_block_by_address(block,str(to_account))
                        tx_in_same_block=find_transactions_in_block_by_address(block,str(to_account))
                        if tx_in_same_block['found'] > 1:
                            total_amount_in_same_block=tx_in_same_block['amount']+amount_sol
                        else:
                            total_amount_in_same_block=amount_sol
                        if total_amount_in_same_block> MIN_TOKEN_AMOUNT:
                            logger.info(f"Adding to logging: {str(to_account)}")
                            #post token with rabbitmq (tx.meta.post_token_balances[0].mint)
                            logger.info(f"MINT: {tx.meta.post_token_balances[0].mint} Budget: {total_amount_in_same_block}  Adding to RABBIT")
                            await send_message(str(tx.meta.post_token_balances[0].mint),queue="token")
                        #logger.info(f"NEW token:{tx.meta.post_token_balances[0].mint} from:{from_account}   to:{to_account}  token:{amount_token}  sol:{amount_sol} total(sol): {total_amount_in_same_block} total(tx): {tx_in_same_block['found']} ")
            
    
                #except:
                #    logger.info(tx)
            else:
                pass
                #logger.info("Transaction has error")



if __name__ == "__main__":
    
    asyncio.get_event_loop().run_until_complete(handle_block_notifications(ACCOUNT_ADDRESS))


