#!/usr/bin/env python3
import os
import pika
import requests
import threading
import time
import logging
import json
import mysql.connector
from mysql.connector import errorcode
from datetime import datetime
import aiomysql
import asyncio
import  decoders.pump as pump
import decoders.system as system
import decoders.spl as spl
from mysql.connector import pooling


PROCESS_DELAY=20
#RPC_URL = "https://go.getblock.io/4153ec34e2804e6593df8d9c94181e24"
#RPC_URL ="https://little-sleek-mansion.solana-mainnet.quiknode.pro/4b69dc2b49ac07ae787d9c65b8a33ccee7a904d2/" 
#RPC_URL2 = 'https://solana-mainnet.api.syndica.io/api-key/4TMsFG1pLWMP4zpdF895C1kYrML8t6AXKQjFB3NzRh1xMjfcMAFPTwCmpRJWa1i72SKTFxjyxBMz7uAkfJoRfR5ZX4kQURk2wwc'
#RPC_URL = "https://api.mainnet-beta.solana.com"   
#RPC_URL = 'https://solana-mainnet.g.alchemy.com/v2/********************************'

RPC_URL2 = "https://solana-mainnet.g.alchemy.com/v2/********************************"
RPC_URL = "https://endpoints.omniatech.io/v1/sol/mainnet/4fd8f6aa33f443ebb14c52e25551890a"
BULLX_URL="https://bullx.io/terminal?chainId=**********&address="
SYSTEM_FEE_ACCOUNT="HWEoBxYs7ssKuudEjzjmpfJVX7Dvi7wescFsVx2L5yoY"
PUMP_FEE_ACCOUNT="CebN5WGQ4jvEPvsVU4EoHEpgzq1VV7AbicfhtW4xC9iM"
JITO_FEE = [
    "96gYZGLnJYVFmbjzopPSU6QiEV5fGqZNyN9nmNhvrZU5",
    "HFqU5x63VTqvQss8hp11i4wVV8bD44PvwucfZ2bU7gRe",
    "Cw8CFyM9FkoMi7K7Crf6HNQqf4uEMzpKw6QNghXLvLkY",
    "ADaUMid9yfUytqMBgopwjb2DTLSokTSzL1zt6iGPaS49",
    "DfXygSm4jCyNCybVYYK6DwvWqjKee8pbDmJGcLWNDXjh",
    "ADuUkR4vqLUMWXxW9gh6D6L8pMSawimctcNZ5pGwDcEt",
    "DttWaMuVvTiduZRnguLF7jNxTgiMBZ1hyAumKUiL2KRL",
    "3AVi9Tg9Uo68tJfuvoKvqKNWKkC5wPdSSdeBnizKZ6jT"
]

FEE_ACCOUNTS=[SYSTEM_FEE_ACCOUNT,PUMP_FEE_ACCOUNT]+JITO_FEE

SYSTEM_ACCOUNT="11111111111111111111111111111111"
PUMP_PROGRAM="6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P"
TOKENPROGRAM_ACCOUNT="TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"



logger = logging.getLogger('newtokens')
logger.setLevel(logging.INFO)

# Create console handler and set level to info
ch = logging.StreamHandler()
ch.setLevel(logging.INFO)

# Create formatter and add it to the handler
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
ch.setFormatter(formatter)

# Add the handler to the logger
logger.addHandler(ch)

# Suppress logging from other modules
logging.getLogger('asyncio').setLevel(logging.WARNING)
logging.getLogger('pika').setLevel(logging.WARNING)
logging.getLogger('requests').setLevel(logging.WARNING)
logging.getLogger('solana').setLevel(logging.WARNING)
logging.getLogger('solders').setLevel(logging.WARNING)




# Database connection parameters
db_config = {
    'user': 'pump2',
    'password': 'pump2',
    'host': '***************',
    'db': 'pump'
}

pool = pooling.MySQLConnectionPool(pool_name="mypool", pool_size=10,user='pump2',password='pump2',host='***************',db='pump')


# Collection interval and duration
COLLECTION_INTERVAL = 5  # in seconds
COLLECTION_DURATION = 600  # in seconds

async def create_pool():
    global pool
    pool = await aiomysql.create_pool(
        **db_config,
        minsize=1,
        maxsize=10,
        autocommit=True,
    )

def batch_tx_request(signatures):
    # Form the batch request using the "signature" key values
    batch_request = []
    for i, sig in enumerate(signatures):
        batch_request.append({
            "jsonrpc": "2.0",
            "id": i + 1,
            "method": "getTransaction",
            "params": [sig['signature'], {"commitment":"confirmed","maxSupportedTransactionVersion": 0, "encoding": "json"}]
        })
    
    # Convert the request to JSON
    batch_request_json = json.dumps(batch_request)
    
    # Send the POST request
    response = requests.post(RPC_URL, headers={"Content-Type": "application/json"}, data=batch_request_json)
    
    # Check if the response is successful
    if response.status_code == 200:
        return response.json()
    else:
        response.raise_for_status()




def get_signatures_for_address(address, limit=1, retries=3, delay=5):
    headers = {
        "Content-Type": "application/json"
    }
    payload = {
        "jsonrpc": "2.0",
        "id": 1,
        "method": "getSignaturesForAddress",
        "params": [
            address,
            {
                "commitment":"confirmed",
                "limit": 1000
            }
        ]
    }

    for attempt in range(retries):
        try:
            response = requests.post(RPC_URL2, headers=headers, data=json.dumps(payload))
            
            # Check if the response is successful

            if response.status_code == 200:
                return response.text
            else:
                response.raise_for_status()
        
        except requests.exceptions.RequestException as e:
            print(f"Attempt {attempt + 1} failed: {e}")
            
            # If this is the last attempt, raise the exception
            if attempt + 1 == retries:
                raise
            else:
                # Wait before retrying
                time.sleep(delay)

def store_transaction(mint, tx_type, from_address, to_address, amount, block, block_time, signature,price):
    #print(mint, tx_type, from_address, to_address, amount, block, block_time, signature)
    global pool
    conn = pool.get_connection()
    try:
        with conn.cursor() as cursor:
            query = """
            INSERT INTO transactions 
            (mint, tx_type, from_address, to_address, amount, block, block_time, signature,price) 
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s,%s)
            """
            transaction_date = datetime.now()
            cursor.execute(query, (mint, tx_type, from_address, to_address, amount, block, block_time, signature,price))
            conn.commit()
    finally:
        conn.close()

# Function to collect and store token prices
def process_transaction(mint,test):
    #logger.info(f"Processing: {mint}")
    time.sleep(PROCESS_DELAY)
    data=json.loads(get_signatures_for_address(mint))
    if "result" in data:
        if len(data['result'])>0:
            total_tx=len(data['result'])
            transactions=batch_tx_request(data['result'])
            count=len(transactions)
            counter=0
            trade_counter=0
            transfer_counter=0
            failed_counter=0
            turnaround_amount=0

            for tx in transactions:
                if "result" in tx:
                    tx=tx['result']
                    sig=tx['transaction']['signatures'][0]
                    slot=tx['slot']
                    block_time=tx['blockTime']

                    if not tx['meta']['err']:
                        #print(f"SUCCESS: mint: {mint} slot: ({counter}/{count}) {tx['slot']}  tx: {tx['transaction']['signatures'][0]}")

                        #print(f"block:{slot}  blockTime: {block_time}  sig: {sig} ")
                        ACCOUNT_KEYS=tx['transaction']['message']['accountKeys']+tx['meta']['loadedAddresses']['writable']+tx['meta']['loadedAddresses']['readonly']

                        try:
                            PUMP_INDEX=ACCOUNT_KEYS.index(PUMP_PROGRAM)
                        except:
                            PUMP_INDEX=None
                        try:    
                            SYSTEM_INDEX=ACCOUNT_KEYS.index(SYSTEM_ACCOUNT)
                        except:
                            SYSTEM_INDEX=None
                        try:
                            TOKEN_INDEX=ACCOUNT_KEYS.index(TOKENPROGRAM_ACCOUNT)
                        except:
                            TOKEN_INDEX=None

                        found=False
                        ixcount=0

                        for i in tx['transaction']['message']['instructions']:
                            if i["programIdIndex"] == PUMP_INDEX:
                                pump_decode=pump.decode(i['data'])
                                if isinstance(pump_decode, list) and pump_decode[0] == 'TradeEvent':
                                    if pump_decode[1]['is_buy']:
                                        trade="buy"
                                    else:
                                        trade="sell"
                                    BONDING_CURVE_ADDRESS=ACCOUNT_KEYS[i['accounts'][3]]
                                    #print(f"TRADE: {trade}  User:{pump_decode[1]['user']}  TADDRESS: {BONDING_CURVE_ADDRESS}   Token:{pump_decode[1]['token_amount']}  Sol:{pump_decode[1]['sol_amount']/**********:.4f}")
                                    price=pump_decode[1]['sol_amount']/pump_decode[1]['token_amount']
                                    store_transaction(mint, trade,pump_decode[1]['user'], BONDING_CURVE_ADDRESS, f"{pump_decode[1]['token_amount']/1000000:.0f}",slot,block_time,sig,price)
                                    trade_counter=trade_counter+1
                            if i["programIdIndex"] == SYSTEM_INDEX:
                                sys_decode=system.decode(i['data'])
                                if sys_decode['instruction_type'] == 2 and not ( ACCOUNT_KEYS[i['accounts'][1]] in FEE_ACCOUNTS):
                                    #print(f"TRANSFER from: {ACCOUNT_KEYS[i['accounts'][0]]}  to {ACCOUNT_KEYS[i['accounts'][1]]}  SOL: {sys_decode['args']['lamports']/**********:.4f}")   
                                    price=0
                                    store_transaction(mint, "transfer",ACCOUNT_KEYS[i['accounts'][0]], ACCOUNT_KEYS[i['accounts'][1]], f"{sys_decode['args']['lamports']/**********:.4f}",slot,block_time,sig,price)
                                    transfer_counter=transfer_counter+1
                                    turnaround_amount=turnaround_amount+sys_decode['args']['lamports']/**********
                            # if i["programIdIndex"] == TOKEN_INDEX:
                            #     sys_decode=spl.decode(i['data'])
                            #     if sys_decode['instruction_type'] == 3 and not ( ACCOUNT_KEYS[i['accounts'][1]] in FEE_ACCOUNTS):
                            #         print(f"TRANSFER SPL from: {ACCOUNT_KEYS[i['accounts'][0]]}  to {ACCOUNT_KEYS[i['accounts'][1]]}  SOL: {sys_decode['args']['amount']}")   



                            for ii in tx['meta']['innerInstructions']:
                                if ii['index'] == ixcount:
                                    for iii in ii['instructions']:
                                        if iii["programIdIndex"] == PUMP_INDEX:
                                            pump_decode=pump.decode(iii['data'])
                                            if isinstance(pump_decode, list) and pump_decode[0] == 'TradeEvent':
                                                if pump_decode[1]['is_buy']:
                                                    trade="buy"
                                                else:
                                                    trade="sell"
                                                BONDING_CURVE_ADDRESS=ACCOUNT_KEYS[i['accounts'][3]]
                                                #print(f"TRADE: {trade}  User:{pump_decode[1]['user']}  TADDRESS: {BONDING_CURVE_ADDRESS}   Token:{pump_decode[1]['token_amount']}  Sol:{pump_decode[1]['sol_amount']/**********:.4f}")
                                                price=pump_decode[1]['sol_amount']/pump_decode[1]['token_amount']
                                                store_transaction(mint, trade,pump_decode[1]['user'], BONDING_CURVE_ADDRESS, f"{pump_decode[1]['token_amount']/1000000:.0f}",slot,block_time,sig,price)                                            
                                                trade_counter=trade_counter+1
                                        if iii["programIdIndex"] == SYSTEM_INDEX:
                                            sys_decode=system.decode(iii['data'])
                                            if sys_decode['instruction_type'] == 2 and not ( ACCOUNT_KEYS[iii['accounts'][1]] in FEE_ACCOUNTS):
                                                #print(f"TRANSFER from: {ACCOUNT_KEYS[iii['accounts'][0]]}  to {ACCOUNT_KEYS[iii['accounts'][1]]}  SOL: {sys_decode['args']['lamports']/**********:.4f}")   
                                                price=0
                                                store_transaction(mint, "transfer",ACCOUNT_KEYS[iii['accounts'][0]], ACCOUNT_KEYS[iii['accounts'][1]], f"{sys_decode['args']['lamports']/**********:.4f}",slot,block_time,sig,price)
                                                transfer_counter=transfer_counter+1
                                                turnaround_amount=turnaround_amount+sys_decode['args']['lamports']/**********
                                        # if iii["programIdIndex"] == TOKEN_INDEX:
                                        #     sys_decode=spl.decode(iii['data'])
                                        #     if sys_decode['instruction_type'] == 3 and not ( ACCOUNT_KEYS[iii['accounts'][1]] in FEE_ACCOUNTS):
                                        #         print(f"TRANSFER SPL from: {ACCOUNT_KEYS[iii['accounts'][0]]}  to {ACCOUNT_KEYS[iii['accounts'][1]]}  SOL: {sys_decode['args']['amount']}")   
                            
                            ixcount=ixcount+1




                        #store_transaction(type, from_address, to_address, amount, slot, block_time, signature)


                    else:
                        failed_counter=failed_counter+1
                        pass
                        #print(f" FAILED: mint: {mint} slot: ({counter}/{count}) {tx['slot']}  tx: {tx['transaction']['signatures'][0]}")
                else:
                    print(tx)    
                counter=counter+1

            logger.info(f"Processed: {mint} total tx:{total_tx} processed:{counter} failed: {failed_counter} trades:{trade_counter}  transfers:{transfer_counter} sol_tx_sum:{turnaround_amount:0.2f}  bullx: {BULLX_URL}{mint}")



# Function to process messages from RabbitMQ
def on_message(ch, method, properties, body):
    try:
        token_data = body.decode('utf-8')
        token = token_data.strip()  # Assuming the message is just the token
        token_name = "token_name_placeholder"  # Modify as needed
        threading.Thread(target=process_transaction, args=(token, token_name)).start()
    except Exception as e:
        logger.info(f"Failed to process message: {e}")

# Set up RabbitMQ connection and channel
async def setup_rabbitmq():
    try:
        global pool
        #await create_pool()
        rabbitmq_host = os.getenv('RABBITMQ_HOST', '***************')
        connection = pika.BlockingConnection(pika.ConnectionParameters(rabbitmq_host))
        channel = connection.channel()
        channel.queue_declare(queue='token', durable=True)  # Declare the queue as durable
        channel.basic_consume(queue='token', on_message_callback=on_message, auto_ack=True)
        logger.info('Waiting for messages. To exit press CTRL+C')
        channel.start_consuming()
    except Exception as e:
        logger.info(f"Unexpected error: {e}")

if __name__ == '__main__':
    
    asyncio.get_event_loop().run_until_complete(setup_rabbitmq())

