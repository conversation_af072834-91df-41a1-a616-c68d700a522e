from flask import Flask, render_template, request, redirect, url_for, jsonify, Response
import os
import json
from datetime import datetime
from functools import wraps
import uuid  # Import uuid to create unique identifiers

app = Flask(__name__)

# Define admin credentials
ADMIN_USERNAME = 'admin'
ADMIN_PASSWORD = 'animal'

def check_auth(username, password):
    """Check if the provided username and password match the admin credentials."""
    return username == ADMIN_USERNAME and password == ADMIN_PASSWORD

def authenticate():
    """Sends a 401 response that enables basic auth"""
    return Response(
        'Could not verify your access level for that URL.\n'
        'You have to login with proper credentials', 401,
        {'WWW-Authenticate': 'Basic realm="Login Required"'})

def requires_auth(f):
    """Decorator to prompt for login before accessing the page."""
    @wraps(f)
    def decorated(*args, **kwargs):
        auth = request.authorization
        if not auth or not check_auth(auth.username, auth.password):
            return authenticate()
        return f(*args, **kwargs)
    return decorated

# Helper function to load articles
def load_articles():
    articles = []
    articles_dir = 'content/articles'
    for filename in os.listdir(articles_dir):
        if filename.endswith('.json'):
            with open(os.path.join(articles_dir, filename), 'r') as f:
                article = json.load(f)
                article['id'] = filename.split('_')[1].replace('.json', '')  # Extract identifier
                articles.append(article)
    articles.sort(key=lambda x: datetime.strptime(x['date'], '%Y-%m-%d'), reverse=True)
    return articles

@app.route('/')
def home():
    articles = load_articles()
    latest_article = articles[0] if articles else None
    other_articles = articles[1:]
    return render_template('home.html', latest=latest_article, articles=other_articles)

@app.route('/news')
def news():
    articles = load_articles()
    return render_template('news.html', articles=articles)

@app.route('/contacts', methods=['GET', 'POST'])
def contacts():
    if request.method == 'POST':
        return redirect(url_for('contacts'))
    return render_template('contacts.html')

@app.route('/about')
def about():
    return render_template('about.html')

# Admin routes with password protection
@app.route('/admin')
@requires_auth
def admin():
    articles = load_articles()
    return render_template('admin/dashboard.html', articles=articles)

@app.route('/admin/new', methods=['GET', 'POST'])
@requires_auth
def new_article():
    if request.method == 'POST':
        # Generate a unique identifier for the article
        article_id = str(uuid.uuid4())
        
        # Collect form data
        title = request.form['title']
        content = request.form['content']
        preview = request.form['preview']
        date = datetime.now().strftime('%Y-%m-%d')
        image = request.form['image']

        article = {
            'id': article_id,
            'title': title,
            'date': date,
            'content': content,
            'preview': preview,
            'image': image
        }

        # Save the article as a JSON file with the identifier in the filename
        filename = f"article_{article_id}.json"
        with open(os.path.join('content/articles', filename), 'w') as f:
            json.dump(article, f)
        
        return redirect(url_for('admin'))
    
    return render_template('admin/new_article.html')
@app.route('/article/<article_id>')
def article(article_id):
    """Display an individual article."""
    article_file = os.path.join('content/articles', f"article_{article_id}.json")
    
    # Check if the article file exists
    if not os.path.exists(article_file):
        return "Article not found", 404
    
    # Load the article data
    with open(article_file, 'r') as f:
        article = json.load(f)
    
    return render_template('article.html', article=article)

@app.route('/admin/edit/<article_id>', methods=['GET', 'POST'])
@requires_auth
def edit_article(article_id):
    """Edit an existing article."""
    article_file = os.path.join('content/articles', f"article_{article_id}.json")
    
    # Check if the article file exists
    if not os.path.exists(article_file):
        return "Article not found", 404
    
    if request.method == 'POST':
        # Update the article with form data
        title = request.form['title']
        content = request.form['content']
        preview = request.form['preview']
        image = request.form['image']
        date = datetime.now().strftime('%Y-%m-%d')  # Update date to the current date

        updated_article = {
            'id': article_id,
            'title': title,
            'date': date,
            'content': content,
            'preview': preview,
            'image': image
        }

        # Save the updated article back to the JSON file
        with open(article_file, 'w') as f:
            json.dump(updated_article, f)
        
        return redirect(url_for('admin'))
    
    # Load the article data for GET request (edit form)
    with open(article_file, 'r') as f:
        article = json.load(f)

    return render_template('admin/edit_article.html', article=article, article_id=article_id)

if __name__ == '__main__':
    app.run(debug=True,host="0.0.0.0",port=6600)
