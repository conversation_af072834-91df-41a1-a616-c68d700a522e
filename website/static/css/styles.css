/* Basic Reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    line-height: 1.6;
    color: #333;
}

/* Container */
.container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
}

/* Header */
.header {
    background-color: #002d5e;
    color: white;
    padding: 20px 0;
}

.header .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.header .logo img {
    max-height: 60px;
}

.header .navbar a {
    color: white;
    margin: 0 15px;
    text-decoration: none;
    font-weight: bold;
}

.header .navbar a:hover {
    text-decoration: underline;
}

/* Breadcrumbs */
.breadcrumbs {
    background-color: #f1f1f1;
    padding: 10px 0;
    font-size: 14px;
}

.breadcrumbs a {
    color: #002d5e;
    text-decoration: none;
}

.breadcrumbs span {
    color: #666;
}

/* Main Content Layout */
.main-content {
    display: flex;
    margin-top: 20px;
}

.content {
    flex: 2;
    margin-right: 20px;
}

.sidebar {
    flex: 1;
}

.sidebar h3 {
    font-size: 1.2em;
    color: #002d5e;
    margin-bottom: 15px;
}

.sidebar-links li {
    list-style: none;
    margin-bottom: 10px;
}

.sidebar-links a {
    color: #002d5e;
    text-decoration: none;
}

.sidebar-links a:hover {
    text-decoration: underline;
}

/* Article Styles */
h1 {
    font-size: 2em;
    color: #002d5e;
    margin-bottom: 10px;
}

.article-meta {
    color: #666;
    font-size: 0.9em;
    margin-bottom: 20px;
}

.video {
    margin: 20px 0;
    text-align: center;
}

.video iframe {
    width: 100%;
    max-width: 560px;
    height: 315px;
}

/* Footer */
footer {
    background-color: #002d5e;
    color: white;
    text-align: center;
    padding: 20px 0;
    margin-top: 40px;
}

footer p {
    margin: 0;
}
