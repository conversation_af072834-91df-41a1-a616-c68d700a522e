#!/usr/bin/env python3


from datetime import datetime, timedelta
from mysql.connector import pooling
from modules import db,transfer,utils
import time
import pandas as pd
from neo4j import GraphDatabase
from modules.neo import Neo4jClient
from modules.config import *
import pika,json,os
import modules.config



MYSQL_SERVER="pumpfun.mooo.com"
MYSQL_PORT=3306
MYSQL_USER="pump2"
MYSQL_PASSWORD="pump2"
MYSQL_DB="pump"
MYSQL_POOL="mypool"
MYSQL_POOL_SIZE="10"


pool = pooling.MySQLConnectionPool(
    pool_name=MYSQL_POOL, 
    user=MYSQL_USER,
    password=MYSQL_PASSWORD,
    host=MYSQL_SERVER,
    db=MYSQL_DB)

modules.config.pool=pool

MIN_BALANCE_CHANGE=50
SCAN_TYPE="follow"

import modules.utils as utils
log=utils.get_logger()

if __name__ == "__main__":

    log.info("start balace updates")


    while True:


        # 1. update balances
        accounts, current_balances, _ = db.get_all_accounts_by_type(pool, SCAN_TYPE)

        log.info(f"Processing {len(accounts)} accounts")

        BALANCE_UPDATE_BATCH = 1000
        BALANCE_SLEEP=5

        balances_df = pd.DataFrame()  # Initialize an empty DataFrame to store balances

        # Process accounts in batches of 100
        for start in range(0, len(accounts), BALANCE_UPDATE_BATCH):
            end = start + BALANCE_UPDATE_BATCH
            account_batch = accounts[start:end]
            current_balance_batch = current_balances[start:end]
            
            # Get account balances for the current batch
            batch_balances_df = transfer.get_account_balances(account_batch, current_balance_batch,int(MIN_BALANCE_CHANGE))
            
            # Append the batch balances to the main DataFrame
            balances_df = pd.concat([balances_df, batch_balances_df], ignore_index=True)

        # Process the DataFrame in batches of 100 for updating

        db.update_account_balances_many(pool, balances_df,SCAN_TYPE)
        log.info("All accounts updated. Waiting for the next cycle.")
        

        #########################check gransactions ############################

        #  get list of account with "update"
        log.info("Load account,tokens")
        accounts,tokens=db.load_account_address_list_from_db(pool)
        _,_,last_sigs=db.get_all_accounts_by_type(pool,SCAN_TYPE,"update")
        
        # for each account with new balance
        for sig_address in last_sigs:
            log.info(f"prosess transactins for address: {sig_address['address']}")
            addr=sig_address["address"]


            #get transactions above 50 SOL
            txs,last_sig=transfer.scan_wallet(sig_address["address"],sig_address["last_sig"])
            print(json.dumps(txs))
            try:
                if len(txs)>0:
                    log.info("process transactions")
                    transfer.process_transactions_follow(pool,txs,addr,last_sig)
            finally:
                db.update_account_last_sig(pool,str(sig_address["address"]),last_sig)
            log.info("Transaction finished. waiting for next ")        
     

        time.sleep(5)
            