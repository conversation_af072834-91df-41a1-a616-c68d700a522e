<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Symbol Search</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        /* Styling to make the results look like a table */
        .table {
            display: table;
            width: 100%;
            border-collapse: collapse;
        }
        .table-row {
            display: table-row;
            border-bottom: 1px solid #ddd;
            padding: 10px;
        }
        .table-cell {
            display: table-cell;
            padding: 10px;
            vertical-align: top;
        }
        .table-header {
            font-weight: bold;
            background-color: #f2f2f2;
        }
        img {
            max-width: 50px;
            max-height: 50px;
        }
    </style>
</head>
<body>
    <h1>Token Discovery Search</h1>
    <input type="text" id="search-input" placeholder="Type a symbol...">
    <div id="results"></div>

    <script>
        $(document).ready(function() {
            $('#search-input').on('input', function() {
                const symbol = $(this).val();
                
                if (symbol) {
                    $.ajax({
                        url: '/api/filter',
                        type: 'GET',
                        data: { symbol },
                        success: function(data) {
                            $('#results').empty();

                            // Table header
                            $('#results').append(`
                                <div class="table">
                                    <div class="table-row table-header">
                                        <div class="table-cell">Image</div>
                                        <div class="table-cell">Symbol</div>
                                        <div class="table-cell">Name</div>
                                        <div class="table-cell">Description</div>
                                        <div class="table-cell">Website</div>
                                        <div class="table-cell">Twitter</div>
                                        <div class="table-cell">Telegram</div>
                                    </div>
                                </div>
                            `);

                            // Append each result as a table row
                            data.forEach(item => {
                                const imageUrl = item.image_local_uri ? `${item.image_local_uri}` : 'placeholder.jpg'; // Use a placeholder if no image
                                $('#results .table').append(`
                                    <div class="table-row">
                                        <div class="table-cell"><img src="${imageUrl}" alt="Token Image"></div>
                                        <div class="table-cell">${item.symbol}</div>
                                        <div class="table-cell">${item.name}</div>
                                        <div class="table-cell">${item.description}</div>
                                        <div class="table-cell">${item.website || 'N/A'}</div>
                                        <div class="table-cell">${item.twitter || 'N/A'}</div>
                                        <div class="table-cell">${item.telegram || 'N/A'}</div>
                                    </div>
                                `);
                            });
                        }
                    });
                } else {
                    $('#results').empty();
                }
            });
        });
    </script>
</body>
</html>
