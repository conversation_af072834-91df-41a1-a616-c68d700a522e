from flask import Flask, request, jsonify, render_template, send_from_directory
import mysql.connector
import os

app = Flask(__name__)

# MySQL database configuration
db_config = {
    'user': 'pump2',
    'password': 'pump2',
    'host': 'pumpfun.mooo.com',
    'database': 'pump',
    'port': 3306,
    'pool_name': 'mypool',
    'pool_size': 10
}

# Directory for images
IMAGE_FOLDER = 'images'  # Ensure this folder exists and contains images

# Route to render the frontend
@app.route('/')
def index():
    return render_template('index.html')

# API route to filter by symbol
@app.route('/api/filter', methods=['GET'])
def filter_symbols():
    symbol = request.args.get('symbol', '')
    
    connection = mysql.connector.connect(**db_config)
    cursor = connection.cursor(dictionary=True)
    query = "SELECT * FROM token_discoveries2 WHERE symbol LIKE %s or website LIKE %s or twitter LIKE %s or telegram LIKE %s" 
    cursor.execute(query, (f"%{symbol}%","%{symbol}%","%{symbol}%","%{symbol}%"))
    results = cursor.fetchall()
    cursor.close()
    connection.close()
    
    return jsonify(results)

# Route to serve images
@app.route('/image/<filename>')
def image(filename):
    # Use send_from_directory to serve images from the 'images' folder
    return send_from_directory(IMAGE_FOLDER, filename)

if __name__ == '__main__':
    # Create the images directory if it doesn't exist
    if not os.path.exists(IMAGE_FOLDER):
        os.makedirs(IMAGE_FOLDER)
    
    app.run(debug=True,host="0.0.0.0", port=5100)
