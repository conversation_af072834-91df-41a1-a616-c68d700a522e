[package]
name = "helius_webhook"
version = "0.1.0"
edition = "2021"

[dependencies]
anyhow = "1.0"
actix-web = "4"
actix-rt = "2"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
sqlx = { version = "0.6", features = ["runtime-actix-native-tls", "mysql"] }
reqwest = { version = "0.11", features = ["json", "blocking"] }
tokio = { version = "1", features = ["macros"] }
once_cell = "1.17"
