use actix_web::{
    web, App, HttpResponse, HttpServer, Responder, post
};
use serde_json::{json, Value};
use sqlx::{mysql::MySqlPoolOptions, MySql, Pool};

static MYSQL_SERVER: &str = "pumpfun.mooo.com";
static MYSQL_PORT: u16 = 3306;
static MYSQL_USER: &str = "pump2";
static MYSQL_PASSWORD: &str = "pump2";
static MYSQL_DB: &str = "pump";

static DISCORD_WEBHOOK_SCANNER: &str = "https://discord.com/api/webhooks/1328648852110180393/ZXxCLkZcPtD5Ul92vbmcO0hw8xTVxW1WoExacGJFRsJupmW1n9Iu2IcoYRwo93lK8vGV";

#[actix_web::main] // Actix's own Tokio runtime
async fn main() -> std::io::Result<()> {
    // Build the connection string for MySQL
    let connection_string = format!(
        "mysql://{}:{}@{}:{}/{}",
        MYSQL_USER, MYSQL_PASSWORD, MYSQL_SERVER, MYSQL_PORT, MYSQL_DB
    );

    // Create a connection pool with sqlx, asynchronously
    let pool = MySqlPoolOptions::new()
        .max_connections(10)
        .connect(&connection_string)
        .await
        .expect("Failed to create pool.");

    // Start the Actix HTTP server, passing the pool to each worker
    HttpServer::new(move || {
        App::new()
            .app_data(web::Data::new(pool.clone())) // share pool
            .service(helius_webhook)
    })
    .bind(("0.0.0.0", 6005))?
    .run()
    .await
}

#[post("/webhook")]
async fn helius_webhook(
    pool: web::Data<Pool<MySql>>,
    body: web::Json<Value>,
) -> impl Responder {
    // We spawn a task to process the body asynchronously
    let pool_clone = pool.clone();
    let data_clone = body.into_inner();

    actix_rt::spawn(async move {
        if let Err(e) = process_webhook(pool_clone, data_clone).await {
            eprintln!("Error processing webhook data: {}", e);
        }
    });

    HttpResponse::Ok().json(json!({"status": "success"}))
}

async fn process_webhook(
    pool: web::Data<Pool<MySql>>,
    data: Value,
) -> anyhow::Result<()> {

    // Safely parse the "instructions" array from the JSON
    let instructions = match data[0]["instructions"].as_array() {
        Some(arr) => arr,
        None => {
            // No instructions => just return
            return Ok(());
        }
    };

    // Safely handle the case if we have fewer than 3 instructions
    if instructions.len() <= 2 {
        return Ok(());
    }

    let program_id = instructions[2]["programId"].as_str().unwrap_or("");
    let accounts = instructions[2]["accounts"]
        .as_array()
        .map(|arr| arr.as_slice())
        .unwrap_or(&[]);

    // Check for a specific program ID and account length
    if program_id == "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P" && accounts.len() == 14 {
        let signature = data[0]["signature"].as_str().unwrap_or("unknown_signature");
        let file_path = format!("tmp/{}.json", signature);
        std::fs::write(&file_path, serde_json::to_string_pretty(&data)?)?;

        let mint = accounts[0].as_str().unwrap_or("unknown_mint");
        let owner = data[0]["feePayer"].as_str().unwrap_or("unknown_owner");

        // Check if this user has an account in the DB
        let account_info = get_trader_account(&pool, owner).await?;
        if account_info.is_some() {
            println!("----- MINTED: {} --------------", owner);
            let message = format!(
                "mints [{}](https://neo.bullx.io/terminal?chainId=**********&address={})",
                mint, mint
            );
            discord_webhook_signals(DISCORD_WEBHOOK_SCANNER, &message).await?;
        }
    }

    Ok(())
}

async fn get_trader_account(
    pool: &Pool<MySql>,
    address: &str,
) -> anyhow::Result<Option<String>> {
    // Example SELECT. Adjust as needed
    let row = sqlx::query_scalar::<_, String>(
        "SELECT address FROM accounts WHERE address = ?"
    )
    .bind(address)
    .fetch_optional(pool)
    .await?;

    Ok(row)
}

async fn discord_webhook_signals(
    webhook_url: &str,
    content: &str,
) -> anyhow::Result<()> {
    let client = reqwest::Client::new();
    let payload = json!({ "content": content });

    let res = client
        .post(webhook_url)
        .json(&payload)
        .send()
        .await?;

    if !res.status().is_success() {
        eprintln!("Failed to send Discord message. Status: {}", res.status());
    }

    Ok(())
}
