#!/usr/bin/env python
from flask import Flask, request, jsonify
import json
import pika
import requests

from mysql.connector import pooling
import modules.utils as utils
from modules.config import *
import modules.db as db
import modules.bot as bot
import modules.image as image
import modules.config



MYSQL_SERVER="pumpfun.mooo.com"
MYSQL_PORT=3306
MYSQL_USER="pump2"
MYSQL_PASSWORD="pump2"
MYSQL_DB="pump"
MYSQL_POOL="mypool"
MYSQL_POOL_SIZE="10"


pool = pooling.MySQLConnectionPool(
    pool_name=MYSQL_POOL, 
    user=MYSQL_USER,
    password=MYSQL_PASSWORD,
    host=MYSQL_SERVER,
    db=MYSQL_DB)

modules.config.pool=pool

RABBITMQ_SERVER="pumpfun.mooo.com"
increment=0



app = Flask(__name__)

def get_coin_data(token):
    url = f"https://frontend-api.pump.fun/coins/{token}"
    headers = {
        "accept": "*/*"
    }
    
    response = requests.get(url, headers=headers)
    
    if response.status_code == 200:
        data = response.json()
      
        return data  # Return the JSON response if the request was successful
    else:
        print(f"Failed to fetch data. Status code: {response.text}")
        return None
    


def send_message(message,queue="token_discovery"):


    credentials = pika.PlainCredentials('pump', 'pump2pump')
    parameters = pika.ConnectionParameters(RABBITMQ_SERVER, 5672, '/', credentials)
    connection = pika.BlockingConnection(parameters)
    channel = connection.channel()

    # Create a queue named 'task_queue'
    channel.queue_declare(queue=queue, durable=True)

    # Publish the message to the queue
    channel.basic_publish(
        exchange='',
        routing_key=queue,
        body=message,
        properties=pika.BasicProperties(
            delivery_mode=2,  # Make message persistent
        ))
    print(" [x] Sent %r" % message)
    

def add_mint_to_db(owner,mint,signature,bock_time, slot):
    send_message(json.dumps({"owner":owner,"mint":mint,"signature":signature,"block_time":bock_time,"slot":slot}))
    db.add_mint_to_db(pool,owner,mint,signature,bock_time, slot)
    

# Webhook endpoint
@app.route('/webhook', methods=['POST'])
def helius_webhook():
    try:
        # Get JSON data from the incoming request
        data = request.json

        if "minted 1 tokens" in data[0]["description"]:
            if "setAuthority" in data[0]["events"]:
                description = data[0]["description"]
                signature = data[0]["signature"]  
                slot = data[0]["slot"]  
                type=data[0]["type"]
                timestamp=data[0]["timestamp"]
                owner = data[0]["feePayer"]
                block_time = data[0]["timestamp"]
                owner = data[0]["feePayer"]
                account_info=db.get_account_name(pool,owner)
                account_name=""
                if account_info:
                    if len(account_info)>0:
                        account_name=account_info[0]
                mint=data[0]["events"]["setAuthority"][0]["account"]
                swap=data[0]["nativeTransfers"][6]['amount']
                print(f"====={swap} lamports ({round(swap/1e9)})=======")
                #yogurt , orangie
                minters = [
                    "DKwybycDSWidrHfpMjaahUsT1Yid3kig86ncXPAGe7AU",
                    "4zq1iLpmepj2Rj7W6A3XQMRQA1HyjYqVpZiBzM6aPyH7"
                ]

                

                data_send={"description":description,"signature":signature,"type":type,"timestamp":timestamp,"slot":slot,"owner":owner,"mint":mint,"account_name":account_name,"swap":swap}
                #if swap in [***********,***********,***********,***********,***********]:
                if  owner in  minters:
                    utils.notify_account(data_send)
               
                #with open(f"tmp/{owner}.json", 'a') as json_file:
                #    json.dump(data_send, json_file, indent=4)

                
                add_mint_to_db(owner,mint,signature,block_time, slot)

                #pumpdot=get_coin_data(mint)
                #print(f">>>> {pumpdot['symbol']} {pumpdot['name']} {pumpdot['description']} {pumpdot['image_uri']} {pumpdot['twitter']} {pumpdot['telegram']} {pumpdot['website']} {pumpdot['creator']} {pumpdot['created_timestamp']}  {pumpdot['bonding_curve']} {pumpdot['associated_bonding_curve']}")
                
                #img=image.save_image(pumpdot['image_uri'])
                #print(img)
                #for key, value in pumpdot.items():
                #    globals()[key] = value
                #print(f"mint: {mint} symbol: {symbol} name: {name} description: {description} image_uri: {image_uri} twitter: {twitter} telegram: {telegram} website: {website} creator: {creator} created_timestamp: {created_timestamp} image_md5: {image_md5} image_local_uri: {image_local_uri} bonding_curve: {bonding_curve} associated_bonding_curve: {associated_bonding_curve}")
                #db.add_mint_to_db_full(pool,mint,symbol,name,description,image_uri,twitter,telegram,website,creator,created_timestamp,image_md5,image_local_uri,bonding_curve,associated_bonding_curve)



            else:
                for token in data[0]["tokenTransfers"]:
                    if token["fromTokenAccount"] != "" and token["mint"] != "So11111111111111111111111111111111111111112":
                        print(json.dumps(token))

            

        # Respond to Helius to acknowledge the receipt of the webhook
        return jsonify({"status": "success"}), 200
    except Exception as e:
        print("Error processing webhook:", e)
        return jsonify({"status": "error", "message": str(e)}), 500

if __name__ == '__main__':
    # Start the Flask web server
    app.run(host='0.0.0.0', port=6000)