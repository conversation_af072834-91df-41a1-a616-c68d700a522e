from solana.rpc.api import Client
from solders.keypair import Keypair #type: ignore

PRIV_KEY = "5Qx2JU7vTx23pk3XhFZL4MUKq3zDRSsCCjFe6LFE3GwgXUurCY7QGveBFGDTxWBs3SBBmUtkpxskyAyFxkXJztu1"
#RPC = "https://endpoints.omniatech.io/v1/sol/mainnet/bf8f530c3d624f1889e1d0c720901fb1"
RPC= "https://api.mainnet-beta.solana.com"
#RPC="https://little-sleek-mansion.solana-mainnet.quiknode.pro/4b69dc2b49ac07ae787d9c65b8a33ccee7a904d2"
client = Client(RPC)
payer_keypair = Keypair.from_base58_string(PRIV_KEY)
