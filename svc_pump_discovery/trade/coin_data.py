from solders.pubkey import Pubkey  # type: ignore
from spl.token.instructions import get_associated_token_address
from construct import Padding, Struct, Int64ul, Flag
from trade.config import client
from trade.constants import PUMP_FUN_PROGRAM
import time

def get_virtual_reserves(bonding_curve: Pubkey):
    bonding_curve_struct = Struct(
        Padding(8),
        "virtualTokenReserves" / Int64ul,
        "virtualSolReserves" / Int64ul,
        "realTokenReserves" / Int64ul,
        "realSolReserves" / Int64ul,
        "tokenTotalSupply" / Int64ul,
        "complete" / Flag
    )
    
    retries = 0
    max_retries= 10
    print(bonding_curve)
    while retries < max_retries:
        try:
            
            account_info = client.get_account_info(bonding_curve)
            if account_info is not None and account_info.value is not None:
                data = account_info.value.data
                parsed_data = bonding_curve_struct.parse(data)
                return parsed_data
            else:
                print(f"Attempt {retries + 1}/{max_retries}: No account info returned, retrying...")
        except Exception as e:
            print(f"Attempt {retries + 1}/{max_retries}: Error occurred: {e}, retrying...")
        retries += 1
        time.sleep(1)

    print("Max retries reached, returning None.")
    return None

def derive_bonding_curve_accounts(mint_str: str):
    try:
        mint = Pubkey.from_string(mint_str)
        bonding_curve, _ = Pubkey.find_program_address(
            ["bonding-curve".encode(), bytes(mint)],
            PUMP_FUN_PROGRAM
        )
        associated_bonding_curve = get_associated_token_address(bonding_curve, mint)
        return bonding_curve, associated_bonding_curve
    except Exception:
        return None, None

def get_coin_data(mint_str: str):
    bonding_curve, associated_bonding_curve = derive_bonding_curve_accounts(mint_str)
    if bonding_curve is None or associated_bonding_curve is None:
        return None

    virtual_reserves = get_virtual_reserves(bonding_curve)
    if virtual_reserves is None:
        return None

    try:
        virtual_token_reserves = int(virtual_reserves.virtualTokenReserves)
        virtual_sol_reserves = int(virtual_reserves.virtualSolReserves)
        token_total_supply = int(virtual_reserves.tokenTotalSupply)
        complete = bool(virtual_reserves.complete)
        
        return {
            "mint": mint_str,
            "bonding_curve": str(bonding_curve),
            "associated_bonding_curve": str(associated_bonding_curve),
            "virtual_token_reserves": virtual_token_reserves,
            "virtual_sol_reserves": virtual_sol_reserves,
            "token_total_supply": token_total_supply,
            "complete": complete
        }
    except Exception:
        return None
