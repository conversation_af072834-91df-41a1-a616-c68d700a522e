#!/usr/bin/env python3
import os
import pika
import requests
import threading
import time
import logging
import json
import mysql.connector
from mysql.connector import errorcode
from datetime import datetime
import aiomysql
import asyncio
import  modules.decoders.pump as pump
import modules.decoders.system as system
import modules.decoders.spl as spl
from mysql.connector import pooling
import modules.db as db 
import modules.transfer
import modules.token
import modules.token_past
import redis
import modules.config
import modules.utils as utils
from modules.config import *
from decimal import Decimal

from trade.pump_fun import buy
from trade.pump_fun import sell
from trade.utils import get_token_balance
from trade.coin_data import get_coin_data
import time as tt




log = logging.getLogger('token_discovery')

MYSQL_SERVER="pumpfun.mooo.com"
MYSQL_PORT=3306
MYSQL_USER="pump2"
MYSQL_PASSWORD="pump2"
MYSQL_DB="pump"
MYSQL_POOL="mypool"
MYSQL_POOL_SIZE="20"

DATAGATHER=120

pool = pooling.MySQLConnectionPool(
    pool_name=MYSQL_POOL, 
    user=MYSQL_USER,
    password=MYSQL_PASSWORD,
    host=MYSQL_SERVER,
    db=MYSQL_DB)

modules.config.pool=pool
modules.config.redis_connection = redis.Redis(host='**************', port=6379, db=0,password="pump2pump")



## get data from rabbit make thread wait 30min collect data see exampl log_transactij or new token
#while waiting for 30 collect owner data , transaction age, balacne
#1. also update token info name, symbol picture etc, 
#2. update owner account data
#3. update transaction data
#4. 

#{"owner":owner,"mint":mint,"signature":signature,"block_time":bock_time,"slot":slot}

def trade(mint_str):
    # Buy Example
    coin_data = get_coin_data(mint_str)
    print(coin_data)
    buy(mint_str=mint_str, sol_in=25, slippage=5)
    print("seep start")
    tt.sleep(2.0)
    print("seep end")
    token_balance = get_token_balance(mint_str)
    sell(mint_str=mint_str, token_balance=token_balance, slippage=25, close_token_account=True)
    sell(mint_str=mint_str, token_balance=token_balance, slippage=25, close_token_account=True)



def process_transaction(owner,mint,signature,block_time, slot):
    global pool
    #print(owner,mint,signature,block_time, slot)
    # get goken info: name, symbol, logo
    # get swaps for ming: address,trade,sol amount,token amount,price,mcap,etc
    # get owner info: balance, age, txs  | save transaction to transactoins


    #trade(mint)

    token_info=modules.transfer.get_token_info(mint)

    token_name=token_info["metadata"]["name"]
    token_symbol=token_info["metadata"]["symbol"]    
    img_url=""
    if "image" in token_info["links"]:
        img_url=token_info["links"]["image"]


    #time.sleep(5)
    #rugcheck=modules.token.get_token_report(mint)
    #score=rugcheck["score"]
    #freeze_auth=rugcheck["freezeAuthority"]
    #mint_authority=rugcheck["mintAuthority"]
    #risks=rugcheck["risks"]
    #mutable=rugcheck["tokenMeta"]["mutable"]
    score=0
    freeze_auth=""
    mint_authority=""
    risks=""
    mutable=""


    #print(token_name,token_symbol,img_url,score,freeze_auth,mint_authority,mutable) 
    db.update_token_metadata(pool,mint,token_name,token_symbol,img_url,score,freeze_auth,mint_authority,json.dumps(risks),mutable)
   
    # get owner balance
    balance=modules.transfer.get_multiple_accounts([owner])
    if len(balance)>0:
        balance=balance[0]
    else:
        balance=0 

    # store account info
    #db.update_account(pool,owner,owner_balance[0]["lamports"],owner_balance[0]["owner"],owner_balance[0]["rentEpoch"],owner_balance[0]["token"],owner_balance[0]["executable"],owner_balance[0]["state"],owner_balance[0]["lamports"],owner_balance[0]["owner"],owner_balance[0]["rentEpoch"],owner_balance[0]["token"],owner_balance[0]["executable"],owner_balance[0]["state"],block_time)

    # get owner account info * and transactions
    # print paramenters for add_account
    db.add_account(modules.config.pool, owner, balance, "regular", "system", datetime.fromtimestamp(block_time), None, None, "","new",role="regular",scan=1,distance=1)

   
    time.sleep(DATAGATHER)
    #print(token_name,token_symbol,img_url,score,freeze_auth,mint_authority,mutable)

    modules.token_past.batchTransactions(mint,mint)
    uniq_addresses, total_tx, total_buys,total_sells,last_price,mint_count,mint_list = db.get_tx_stats_for_period(modules.config.pool,mint)
    if last_price == None:
        return
    last_price = Decimal(last_price)
    mcap=int(float(last_price)*float(solana_price)*1000000)
    
    if mcap>5:
        print(f"Emailed: Name:{token_name}, Owner Bal: {round(balance/1e9, 2)} SOL  MCap: ${mcap}K   buys:{total_buys}  sells: {total_sells}")
        discord_data={
            "mint": mint,
            "token_name": token_name,
            "owner":  owner,
            "balance": balance/1e9, 
            "uniq_addresses": uniq_addresses,
            "total_tx": total_tx,
            "DATAGATHER": DATAGATHER,
            "total_buys": total_buys,
            "total_sells": total_sells,
            "mcap": mcap,  
            "mint_count": mint_count,
            "mint_list": mint_list
        }
        utils.discord_webhook(discord_data)
        #utils.notify_token(mint, token_name, owner, balance/1e9, uniq_addresses, total_tx, DATAGATHER,total_buys,total_sells,mcap,mint_count,mint_list) # + sells, buys last price
    else:
        print(f"Name:{token_name}, Owner Bal: {round(balance/1e9, 2)} SOL  MCap: ${mcap}K   buys:{total_buys}  sells: {total_sells} url: https://bullx.io/terminal?chainId=**********&address={mint}")
    #accounts=db.get_mint_unique_holders(modules.config.pool,mint,40,0.3)
    #if accounts != None and len(accounts)>0:
    #    print(f" >>>>>>>>>>>FOUND BIG MINT: {mint} <<<<<<<<<<<<<<")
    #    uniq_addresses, total_tx, total_buys,total_sells,last_price = db.get_tx_stats_for_period(modules.config.pool,mint)

    #    utils.notify_token(mint, token_name, owner, balance/1e9, uniq_addresses, total_tx, DATAGATHER,total_buys,total_sells,last_price) # + sells, buys last price
        # temporary remove now focusing on existing stats
        #for account in accounts:
        #    db.add_account(pool,account,0,"regular","system",datetime.fromtimestamp(block_time),None,None,"","new",role="holder",scan=1,distance=1)
    
    # sleep 10 sec    
    # get all swaps for mint
    # store in db
    # for each account
    #    get balance
    #    get all signatures
    #    if signature less then 1000
    #        new - get all native transactions
    #        old - get all native transactions  

def on_message(ch, method, properties, body):
    try:
        token = json.loads(body)
        owner=token["owner"]
        mint=token["mint"]
        signature=token["signature"]
        block_time=token["block_time"]
        slot=token["slot"]

        threading.Thread(target=process_transaction, args=(owner,mint,signature,block_time,slot)).start()
    except Exception as e:
        log.info(f"Failed to process message: {e}")


async def setup_rabbitmq():
    try:
        global pool
        #await create_pool()
        #auth to rabbit
        rabbitmq_host = os.getenv('RABBITMQ_HOST', 'pumpfun.mooo.com')
        credentials = pika.PlainCredentials('pump', 'pump2pump')
        parameters = pika.ConnectionParameters(rabbitmq_host, 5672, '/', credentials)
        connection = pika.BlockingConnection(parameters)

        channel = connection.channel()
        channel.queue_declare(queue='token_discovery', durable=True)  # Declare the queue as durable
        channel.basic_consume(queue='token_discovery', on_message_callback=on_message, auto_ack=True)
        log.info('Waiting for messages. To exit press CTRL+C')
        channel.start_consuming()
    except Exception as e:
        log.info(f"Unexpected error: {e}")

if __name__ == '__main__':
    
    asyncio.get_event_loop().run_until_complete(setup_rabbitmq())