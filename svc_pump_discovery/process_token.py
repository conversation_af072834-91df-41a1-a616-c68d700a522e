import pika
import json
import time
import modules.bot as bot
import modules.image as image
import requests
from mysql.connector import pooling
import modules.utils as utils
from modules.config import *
import modules.db as db
import modules.bot as bot
import modules.image as image
import modules.config
import requests
import os
import hashlib
import filetype
from PIL import Image
from io import BytesIO
import requests
import time

proxy_url = f"http://{proxy_user}:{proxy_pass}@{proxy_host}:{proxy_port}"
proxies = {
    'http': proxy_url,
    'https': proxy_url
}


MYSQL_SERVER="pumpfun.mooo.com"
MYSQL_PORT=3306
MYSQL_USER="pump2"
MYSQL_PASSWORD="pump2"
MYSQL_DB="pump"
MYSQL_POOL="mypool"
MYSQL_POOL_SIZE="10"


pool = pooling.MySQLConnectionPool(
    pool_name=MYSQL_POOL, 
    user=MYSQL_USER,
    password=MYSQL_PASSWORD,
    host=MYSQL_SERVER,
    db=MYSQL_DB)

modules.config.pool=pool

RABBITMQ_SERVER="pumpfun.mooo.com"
increment=0



# RabbitMQ server configuration
RABBITMQ_SERVER = "pumpfun.mooo.com"
QUEUE_NAME = "token_discovery"




def save_image(image_url):
    folder_path = "images"
    api_path="image"
    image_name = image_url.split('/')[-1]  # Get image name from URL
    
    # Create the folder if it doesn't exist
    os.makedirs(folder_path, exist_ok=True)

    try:
        # Download the image with a 1-second timeout
        response = requests.get(image_url, timeout=1)
        if response.status_code == 200:
            # Calculate the MD5 hash of the image content
            md5_hash = hashlib.md5(response.content).hexdigest()

            # Detect file type and add the appropriate extension
            kind = filetype.guess(BytesIO(response.content))
            if kind:
                image_extension = kind.extension
                image_name = f"{image_name}.{image_extension}" if '.' not in image_name else image_name
            else:
                print("Unknown file type. Could not determine extension.")
                return None

            # Open the image for cropping and resizing
            image = Image.open(BytesIO(response.content))

            # Crop the image to a square (centered)
            width, height = image.size
            min_dimension = min(width, height)
            left = (width - min_dimension) / 2
            top = (height - min_dimension) / 2
            right = (width + min_dimension) / 2
            bottom = (height + min_dimension) / 2
            image = image.crop((left, top, right, bottom))

            # Resize the cropped image to 60x60 pixels
            image.thumbnail((60, 60))

            # Save the final thumbnail to disk
            thumbnail_path = os.path.join(folder_path, image_name)
            image.save(thumbnail_path)
            return {"path": f"{api_path}/{image_name}", "md5": md5_hash}
        else:
            print("Failed to download image")
            return None
    except requests.exceptions.Timeout:
        print("Image download timed out")
        return None



def get_coin_data(token):
    # Verify if the token ends with "pump"
    if not token.lower().endswith("pump"):
        return None
    
    url = f"https://frontend-api.pump.fun/coins/{token}"
    headers = {
        "accept": "*/*"
    }
    
    retries = 3
    delay = 0.5  # 0.5-second delay between retries
    
    for attempt in range(retries):
        response = requests.get(url, headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            return data  # Return the JSON response if the request was successful
        else:
            print(f"Attempt {attempt + 1} failed for token {token}. Retrying...")
            time.sleep(delay)  # Wait before retrying
    
    print(f"Failed to fetch data after {retries} attempts for token {token}.")
    return None


def send_message(message,queue="token_discovery"):

    print("============Sending message to RabbitMQ====",message)
    credentials = pika.PlainCredentials('pump', 'pump2pump')
    parameters = pika.ConnectionParameters(RABBITMQ_SERVER, 5672, '/', credentials)
    connection = pika.BlockingConnection(parameters)
    channel = connection.channel()

    # Create a queue named 'task_queue'
    channel.queue_declare(queue=queue, durable=True)

    # Publish the message to the queue
    channel.basic_publish(
        exchange='',
        routing_key=queue,
        body=message,
        properties=pika.BasicProperties(
            delivery_mode=2,  # Make message persistent
        ))
    print(" [x] Sent %r" % message)

def process_mint(mint):
    """
    Placeholder function for processing the mint value.
    Modify this function to perform the desired task with the mint.
    """
    #print(f"Processing mint: {mint}")
    try:
        pumpdot=get_coin_data(mint)
        if pumpdot is not None:
            print(f">>>> {pumpdot['symbol']} {pumpdot['name']} {pumpdot['description']} {pumpdot['image_uri']} {pumpdot['twitter']} {pumpdot['telegram']} {pumpdot['website']} {pumpdot['creator']} {pumpdot['created_timestamp']}  {pumpdot['bonding_curve']} {pumpdot['associated_bonding_curve']}")
        if pumpdot['image_uri']:
            img=save_image(pumpdot['image_uri'])
            image_md5=img['md5']
            image_local_uri=img['path']
            print(f"Image saved to {image_local_uri}")
            db.add_mint_to_db_full(pool,mint,pumpdot['symbol'],pumpdot['name'],pumpdot['description'],pumpdot['image_uri'],pumpdot['twitter'],pumpdot['telegram'],pumpdot['website'],pumpdot['creator'],pumpdot['created_timestamp'],image_md5,image_local_uri,pumpdot['bonding_curve'],pumpdot['associated_bonding_curve'])

            send_message(json.dumps({"mint":mint}),queue="uniq_website")
    except:
        print(f"Error:{pumpdot}")     


 
   

def callback(ch, method, properties, body):
    message = json.loads(body)
    mint = message.get("mint")
    
    if mint:
        process_mint(mint)
    else:
        print("Mint value not found in message")
    
    # Acknowledge message after processing
    ch.basic_ack(delivery_tag=method.delivery_tag)

def main():
    # Connect to RabbitMQ server
    credentials = pika.PlainCredentials("pump", "pump2pump")
    parameters = pika.ConnectionParameters(RABBITMQ_SERVER, 5672, "/", credentials)
    connection = pika.BlockingConnection(parameters)
    channel = connection.channel()

    # Declare the queue to ensure it exists
    channel.queue_declare(queue=QUEUE_NAME, durable=True)
    print(f"Connected to RabbitMQ. Waiting for messages in '{QUEUE_NAME}' queue...")

    # Set up subscription on the queue with callback
    channel.basic_qos(prefetch_count=1)
    channel.basic_consume(queue=QUEUE_NAME, on_message_callback=callback)

    # Start consuming messages
    channel.start_consuming()

if __name__ == "__main__":
    main()
