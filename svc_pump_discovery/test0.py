from trade.pump_fun import buy
from trade.pump_fun import sell
from trade.utils import get_token_balance
from trade.coin_data import get_coin_data
import sys
import time




#print cli argument
print(sys.argv[1])
print(get_coin_data(sys.argv[1]))
sys.exit(0)
# Buy Example
print("buying")
mint_str = "Ep1fRciRvzzgsPqAC7x6JckUXKNPh6q8kQt59LNENhtv"
buy(mint_str=mint_str, sol_in=.05, slippage=50)
time.sleep(3)
print("selling")
token_balance = get_token_balance(mint_str)
sell(mint_str=mint_str, token_balance=token_balance, slippage=25, close_token_account=True)


