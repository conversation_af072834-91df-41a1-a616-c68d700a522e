from flask import Flask, request, jsonify
import threading
import modules.utils as utils
from modules.config import *
import modules.db as db
from mysql.connector import pooling
import modules.config
import modules.bot
import logging,json
logging.getLogger('werkzeug').setLevel(logging.ERROR)

MYSQL_SERVER = "pumpfun.mooo.com"
MYSQL_PORT = 3306
MYSQL_USER = "pump2"
MYSQL_PASSWORD = "pump2"
MYSQL_DB = "pump"
MYSQL_POOL = "mypool"
MYSQL_POOL_SIZE = "10"

pool = pooling.MySQLConnectionPool(
    pool_name=MYSQL_POOL,
    user=MYSQL_USER,
    password=MYSQL_PASSWORD,
    host=MYSQL_SERVER,
    db=MYSQL_DB
)

modules.config.pool = pool

app = Flask(__name__)

def process_webhook(data):
    try:


        if len(data[0]["instructions"]) > 2 and data[0]["instructions"][2]["programId"] == "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P" and len(data[0]["instructions"][2]["accounts"]) == 14:
            with open(f"tmp/{data[0]["signature"]}.json", 'w') as f:
                json.dump(data, f)
            mint = data[0]["instructions"][2]["accounts"][0]
            owner = data[0]["feePayer"]
            account_info = db.get_trader_accounts(pool, owner) 
            if account_info is not None:
                print(f"----- MINTED: {owner} --------------")
                message = f"mints [{mint}](https://neo.bullx.io/terminal?chainId=**********&address={mint})"
                utils.discord_webhook_signals(discord_webhook_scanner, message)
    except Exception as e:
        print("Error processing webhook data:", e)

@app.route('/webhook', methods=['POST'])
def helius_webhook():
    try:
        data = request.json
        # Start a background thread to process the webhook
        threading.Thread(target=process_webhook, args=(data,)).start()
        # Respond immediately to Helius to acknowledge the receipt of the webhook
        return jsonify({"status": "success"}), 200
    except Exception as e:
        print("Error receiving webhook:", e)
        return jsonify({"status": "error", "message": str(e)}), 500

if __name__ == '__main__':
    # Start the Flask web server
    app.run(host='0.0.0.0', port=6005)
