#!/usr/bin/env python3
"""Generate ledger transactions from solana transactions.

Usage:
  process-txs.py [options]

Options:
  -h, --help               Show this screen.
  -y YEAR, --year YEAR     Limit output to transactions executed in the given year
  -t TZ, --timezone TZ     Timezone for output dates [default: Europe/Berlin]
  --signers CSV            CSV with list of public keys which can sign and the owner of them [default: signers.csv]
  --initial-assets CSV     CSV file containing asset state of user accounts at the start of the processing interval
  --final-assets CSV       Save final asset state at the end of the processing interval to CSV file
  --track-events CSV       Save account tracking events to CSV file
  --manual-ledger LEDGER   Ledger file containing entries mapped manually [default: manual.ledger]
  --manual-aliases CSV     CSV file with pubkey,name mapping solana publickeys to ledger account names [default: manual-aliases.csv]
  --no-map-tokens          Do not map tokens to their symbols, emit raw amounts and mint addresses instead
"""
import base64
import dataclasses
import string
import json
import re
import sqlite3
import sys
import traceback
from collections import defaultdict
from dataclasses import dataclass
from datetime import datetime, timezone
from typing import Any

import based58
import pandas as pd
from dateutil.tz import gettz
from docopt import docopt

import constants
import ix_parser
import solana.message
import solana.publickey
import solana.transaction
from lib import (SolRpc, amount_to_string, fetch_rewards_for_epoch,
                 first_epoch_slot, format_amount, load_rewards,
                 read_solana_tokens, slot_to_epoch)


@dataclass(frozen=True, order=True)
class TxId:
    slot: int
    index: int


@dataclass
class TransactionRecord:
    signature: str
    tx: solana.transaction.Transaction
    meta: dict
    timestamp: datetime
    txid: TxId

    def token_changes(self):
        pre_token_by_account = {
            amt["accountIndex"]: amt for amt in self.meta["preTokenBalances"]
        }
        post_token_by_account = {
            amt["accountIndex"]: amt for amt in self.meta["postTokenBalances"]
        }
        token_deltas = []
        for ix in set(pre_token_by_account) | set(post_token_by_account):
            pre = pre_token_by_account.get(ix, {})
            post = post_token_by_account.get(ix, {})

            if pre.get("mint", None) == post.get("mint", None):
                delta = int(post["uiTokenAmount"]["amount"]) - int(
                    pre["uiTokenAmount"]["amount"]
                )
                token_deltas += [(ix, pre.get("mint"), delta)]
                continue

            if pre.get("uiTokenAmount", {}).get("amount", "0") != "0":
                token_deltas += [
                    (ix, pre.get("mint"), -int(pre["uiTokenAmount"]["amount"]))
                ]

            if post.get("uiTokenAmount", {}).get("amount", "0") != "0":
                token_deltas += [
                    (ix, post.get("mint"), int(post["uiTokenAmount"]["amount"]))
                ]

        msg = self.tx.compile_message()

        out = []
        for idx, mint, delta in token_deltas:
            if not delta:
                continue
            out += [(msg.account_keys[idx], idx, mint, delta)]
        return out

    def sol_changes(self):
        native_sol_deltas = [
            post - pre
            for pre, post in zip(self.meta["preBalances"], self.meta["postBalances"])
        ]
        msg = self.tx.compile_message()

        return list(zip(msg.account_keys, native_sol_deltas))

    def toplevel_instructions(self):
        msg = self.tx.compile_message()
        inner_by_idx = {
            inner["index"]: len(inner["instructions"])
            for inner in self.meta["innerInstructions"]
        }
        linear_idx = 0
        for idx, ix in enumerate(msg.instructions):
            inner = inner_by_idx.pop(idx, 0)
            yield {
                "program_id_index": ix.program_id_index,
                "accounts": ix.accounts,
                "data": ix.data,
                "inner": False,
                "num_inner": inner,
                "linear_idx": linear_idx,
                "next_linear_idx": linear_idx + 1 + inner,
            }
            linear_idx += 1 + inner

    def all_instructions(self):
        inner_by_idx = {
            inner["index"]: inner["instructions"]
            for inner in self.meta["innerInstructions"]
        }
        msg = self.tx.compile_message()

        for idx, ix in enumerate(msg.instructions):
            yield {
                "program_id_index": ix.program_id_index,
                "accounts": ix.accounts,
                "data": ix.data,
                "inner": False,
                "toplevel_idx": idx,
            }
            for inner in inner_by_idx.pop(idx, []):
                yield {
                    "program_id_index": inner["programIdIndex"],
                    "accounts": bytes(inner["accounts"]),
                    "data": based58.b58decode(inner["data"].encode()),
                    "inner": True,
                    "toplevel_idx": idx,
                }

    def successful(self):
        return 'Ok' in self.meta['status']


@dataclass
class TokenBalance:
    mint: str
    owner: str | None
    amount: int | None

    @staticmethod
    def deserialize(data):
        return TokenBalance(**json.loads(data))

    def serialize(self):
        return json.dumps(dataclasses.asdict(self))

@dataclass
class StakeAccount:
    owner: str
    validator: str | None = None
    delegated_amount: int | None = None
    activation_epoch: int | None = None
    deactivation_epoch: int | None = None
    lockup_authority: str | None = None
    lockup_timestamp: int | None = None
    lockup_epoch: int | None = None

    @staticmethod
    def deserialize(data):
        return StakeAccount(**json.loads(data))

    def serialize(self):
        return json.dumps(dataclasses.asdict(self))

    def is_lockup_active(self, slot, timestamp):
        active_epoch = self.lockup_epoch is not None and slot < first_epoch_slot(slot)
        active_timestamp = self.lockup_timestamp is not None and timestamp < self.lockup_timestamp
        return active_epoch or active_timestamp


    def is_delegated(self, epoch):
        if self.deactivation_epoch is not None and epoch > self.deactivation_epoch:
            return False

        return self.activation_epoch is not None and epoch > self.activation_epoch

@dataclass
class PublicAccount:
    asset_delta: dict[str, int] = dataclasses.field(default_factory=dict)

    @staticmethod
    def deserialize(data):
        return PublicAccount(**json.loads(data))

    def serialize(self):
        return json.dumps(dataclasses.asdict(self))


TRACKED_ACCOUNT_STATE_TYPE = {
    'token': TokenBalance,
    'stake': StakeAccount,
    'public': PublicAccount,
}


@dataclass
class TrackedAccount:
    pubkey: str
    kind: str
    native_balance: int | None = None
    state: Any = None
    external: bool = False

    def to_dict(self):
        return {
            'pubkey': self.pubkey,
            'kind': self.kind,
            'external': self.external,
            'native_balance': self.native_balance,
            'state': self.state.serialize() if self.state else '',
        }

    def copy(self):
        args = self.to_dict()
        args['state'] = '' if not args['state'] else TRACKED_ACCOUNT_STATE_TYPE[args['kind']].deserialize(args['state'])
        return TrackedAccount(**args)


ASSETS_SCHEMA = {
    'pubkey': str,
    'kind': str,
    'external': bool,
    'native_balance': 'Int64',
    'state': str,
}

@dataclass
class TrackEvent:
    txid: TxId
    event: str
    data: dict[str, Any]

    def to_dict(self):
        return dict(
            {
                'tx_slot': self.txid.slot,
                'tx_index': self.txid.index,
                'event': self.event,
            },
            **self.data
        )


class AccountTracker:
    signers: dict[str, str]
    assets: dict[str, TrackedAccount]
    events: list[TrackEvent]
    last_txid: TxId
    have_error: bool

    def __init__(self, signers):
        self.signers = signers
        self.assets = { pk: TrackedAccount(pk, 'signer') for pk, owner in self.signers.items() if owner == 'me' }
        self.events = []
        self.last_txid = TxId(-1, 0)
        self.have_error = False

    def save_assets(self, filename):
        records = [
            acc.to_dict() for acc in self.assets.values()
        ]
        pd.DataFrame(records).astype(ASSETS_SCHEMA).to_csv(filename, index=False)

    def load_assets(self, filename):
        df: pd.DataFrame = pd.read_csv(filename, dtype=ASSETS_SCHEMA) # type: ignore
        for _, record in df.iterrows():
            tracked = TrackedAccount(
                pubkey=record.pubkey,
                kind=record.kind,
                native_balance=int(record.native_balance) if not pd.isna(record.native_balance) else None,
                state=TRACKED_ACCOUNT_STATE_TYPE[record.kind].deserialize(record.state) if not pd.isna(record.state) else None,
                external=record.external,
            )
            if tracked.kind == 'signer' and tracked.pubkey not in self.signers:
                raise RuntimeError(f'asset {tracked} claims to be a signer, but public key is not in list of known signers')

            existing = self.assets.get(tracked.pubkey, None)
            if existing is not None and existing.kind != 'signer':
                raise RuntimeError(f'conflicting initial assets: {existing} vs {tracked}')

            self.assets[tracked.pubkey] = tracked

    def is_own_signer(self, pk):
        return self.signers.get(str(pk), '') == 'me'

    def is_own_asset(self, pk):
        asset = self.assets.get(pk)
        return self.is_own_signer(pk) or (asset and not asset.external)

    def get_stake_pubkeys(self):
        return [acc.pubkey for acc in self.assets.values() if acc.kind == 'stake' and not acc.external]

    def get_public_account(self):
        self.assets.setdefault('public', TrackedAccount('public', kind='public', state=PublicAccount(), external=True))
        return self.assets['public']

    def tx_preprocess(self, details: 'TransactionRecordDetails'):
        assert self.last_txid is None or details.record.txid >= self.last_txid
        self.last_txid = details.record.txid

        if self.signers.get(str(details.record.tx.fee_payer), '') == 'public':
            public = self.get_public_account()
            public.state.asset_delta.setdefault('SOL', 0)
            public.state.asset_delta['SOL'] -= min(details.record.meta['fee'], public.state.asset_delta['SOL'])

        for idx, pubkey in enumerate(details.msg.account_keys):
            pubkey = str(pubkey)
            tracked = self.assets.get(pubkey, None)
            if tracked is None: continue

            if tracked.native_balance is None:
                tracked.native_balance = details.native_balances[0][idx]

            if tracked.native_balance != details.native_balances[0][idx] and not tracked.external:
                mismatch = { 'attr': 'native_balance', 'chain': details.native_balances[0][idx], 'tracked': tracked.native_balance }
                self.have_error = True
                self.events += [TrackEvent(details.record.txid, 'state-mismatch', dict(mismatch, **tracked.to_dict()))]

            if tracked.kind == 'token':
                pre = details.token_balances[0][idx]

                if tracked.state is None and pre is not None:
                    tracked.state = dataclasses.replace(pre)

                if pre is None and tracked.state is not None:
                    pre = dataclasses.replace(tracked.state)

                if pre is None and tracked.state is None: continue
                assert pre is not None and tracked.state is not None

                if tracked.state.owner is None and pre.owner is not None:
                    tracked.state.owner = pre.owner

                if pre.owner is None and tracked.state.owner is not None:
                    pre.owner = tracked.state.owner

                if tracked.state.amount is None and pre.amount is not None:
                    tracked.state.amount = pre.amount

                if pre.owner != tracked.state.owner and not tracked.external:
                    mismatch = { 'attr': 'owner', 'chain': pre.owner, 'tracked': tracked.state.owner }
                    self.have_error = True
                    self.events += [TrackEvent(details.record.txid, 'state-mismatch', dict(mismatch, **tracked.to_dict()))]

                if pre.mint != tracked.state.mint and not tracked.external:
                    mismatch = { 'attr': 'mint', 'chain': pre.mint, 'tracked': tracked.state.mint }
                    self.have_error = True
                    self.events += [TrackEvent(details.record.txid, 'state-mismatch', dict(mismatch, **tracked.to_dict()))]

                if pre.amount != tracked.state.amount and not tracked.external:
                    mismatch = { 'attr': 'amount', 'chain': pre.amount, 'tracked': tracked.state.amount }
                    self.events += [TrackEvent(details.record.txid, 'state-mismatch', dict(mismatch, **tracked.to_dict()))]

                if not tracked.external:
                    details.token_balances[0][idx] = pre

    def tx_postprocess(self, details: 'TransactionRecordDetails'):
        for idx, pubkey in enumerate(details.msg.account_keys):
            pubkey = str(pubkey)
            token = details.token_balances[-1][idx]

            tracked = self.assets.get(pubkey, None)
            if tracked is not None and token is not None and tracked.kind == 'token' and tracked.state.mint != token.mint:
                self.untrack(details.record.txid, tracked)

            if (tracked is None or tracked.kind == 'signer') and token is not None and token.owner is not None and self.is_own_signer(token.owner):
                tracked = TrackedAccount(pubkey=pubkey, kind='token', native_balance=details.native_balances[-1][idx], state=token)
                self.track(details.record.txid, tracked)
                if token.amount is None:
                    data = dict({ 'attr': 'token-balance' }, **tracked.to_dict())
                    self.have_error = True
                    self.events += [TrackEvent(details.record.txid, 'state-missing', data)]

            if tracked is None: continue

            tracked.native_balance = details.native_balances[-1][idx]
            if tracked.kind == 'token':
                if token is None:
                    assert tracked.native_balance == 0
                tracked.state = token
                tracked.external = not token or not self.is_own_signer(token.owner)
            if tracked.kind != 'signer' and tracked.native_balance == 0:
                assert token is None
                self.untrack(details.record.txid, tracked)


    def process_reward(self, reward):
        txid = TxId(reward['effective_slot'], -1)
        assert self.last_txid is None or txid >= self.last_txid
        self.last_txid = txid

        tracked = self.assets.get(reward['pubkey'], None)
        if tracked is None: return

        chain_balance = reward['post_balance'] - reward['amount']
        if tracked.native_balance is None or tracked.external:
            tracked.native_balance = chain_balance
        if tracked.native_balance != chain_balance:
            mismatch = { 'attr': 'native_balance', 'chain': chain_balance, 'tracked': tracked.native_balance }
            self.events += [TrackEvent(txid, 'state-mismatch', dict(mismatch, **tracked.to_dict()))]
            self.have_error = True
        if tracked.kind =='stake' and tracked.state.delegated_amount is not None:
            tracked.state.delegated_amount += reward['amount']

        tracked.native_balance = reward['post_balance']

    def track(self, txid: TxId, tracked: TrackedAccount):
        self.events += [TrackEvent(txid, 'track-start', tracked.to_dict())]

        existing = self.assets.get(tracked.pubkey, None)
        if existing is not None and existing.kind != 'signer' and not existing.external:
            mismatch = { 'attr': 'is-empty', 'chain': True, 'tracked': False }
            self.have_error = True
            self.events += [TrackEvent(txid, 'state-mismatch', dict(mismatch, **tracked.to_dict()))]

        self.assets[tracked.pubkey] = tracked

    def untrack(self, txid: TxId, tracked: TrackedAccount):
        if tracked.pubkey not in self.assets:
            return

        self.events += [TrackEvent(txid, 'track-stop', tracked.to_dict())]
        if self.is_own_signer(tracked.pubkey):
            tracked.kind = 'signer'
            tracked.state = None
        else:
            self.assets.pop(tracked.pubkey)


class TransactionRecordDetails:
    record: TransactionRecord
    msg: solana.message.Message
    all_instructions: list[dict[str, Any]]
    native_balances: list[list[int | None]]
    token_balances: list[list[TokenBalance  | None]]
    token_balance_is_delta: list[bool]

    def __init__(self, tracker: AccountTracker, record: TransactionRecord):
        self.record = record
        num_accounts = len(record.tx.compile_message().account_keys)

        self.all_instructions = list(record.all_instructions())
        self.msg = record.tx.compile_message()

        self.native_balances = [ [None] * num_accounts for _ in self.all_instructions ]
        self.token_balances = [ [None] * num_accounts for _ in self.all_instructions ]
        self.token_balance_is_delta = [False] * num_accounts

        self.native_balances[0] = list(record.meta['preBalances'])
        for balance in record.meta['preTokenBalances']:
            amount = int(balance['uiTokenAmount']['amount'])
            idx = balance['accountIndex']
            balance = TokenBalance(mint=balance['mint'], owner=balance.get('owner', None), amount=amount)
            if str(self.msg.account_keys[idx]) == '2hLHK6YTkwC5oUKn1mbmv5SwdcJVWQNyyutH1k1AuPp4':
                balance.owner = '6ZRCB7AAqGre6c72PRz3MHLC73VMYvJ8bi9KHf1HFpNk'
            self.token_balances[0][idx] = balance

        self.native_balances.append(list(record.meta['postBalances']))
        self.token_balances.append([None] * num_accounts)
        for balance in record.meta['postTokenBalances']:
            amount = int(balance['uiTokenAmount']['amount'])
            idx = balance['accountIndex']
            balance = TokenBalance(mint=balance['mint'], owner=balance.get('owner', None), amount=amount)
            self.token_balances[-1][idx] = balance


        tracker.tx_preprocess(self)

        self.native_balances[0][0] -= record.meta['fee']
        while True:
            while True:
                changed = False
                for i in range(len(self.all_instructions)):
                    changed = self.propagate(i) or changed
                if not changed: break

                changed = False
                for i in reversed(range(len(self.all_instructions))):
                    changed = self.propagate(i) or changed
                if not changed: break

            changed = False
            for idx, balance in enumerate(self.token_balances[0]):
                if balance is None: continue
                if balance.amount is None:
                    self.token_balance_is_delta[i] = True
                    balance.amount = 0
                    changed = True
            if not changed: break


    def token_changes(self):
        if self.record.meta['postTokenBalances'] or self.record.meta['preTokenBalances']:
            return self.record.token_changes()

        out = []
        for idx, (pre, post) in enumerate(zip(self.token_balances[0], self.token_balances[-1])):
            if pre == None and post == None: continue

            if pre is not None and post is not None and pre.mint == post.mint:
                assert post.amount != None, 'token post amount should be known'
                assert pre.amount != None, 'token pre amount should be known'
                out += [(self.msg.account_keys[idx], idx, pre.mint, post.amount - pre.amount)]
                continue

            if pre is not None:
                assert pre.amount != None, 'token pre amount should be known'
                out += [(self.msg.account_keys[idx], idx, pre.mint, -pre.amount)]

            if post is not None:
                assert post.amount != None, 'token post amount should be known'
                out += [(self.msg.account_keys[idx], idx, post.mint, post.amount)]

        return out

    def token_burns_and_mints(self):
        account_keys = list(range(len(self.native_balances[0])))
        for idx, ix in enumerate(self.all_instructions):
            invoked_prog = str(self.msg.account_keys[ix["program_id_index"]])
            if invoked_prog != constants.SPL_TOKEN_ID: continue

            parsed = ix_parser.parse_spl_token(account_keys, ix['accounts'], ix['data'])
            if parsed['instruction'] not in { 'InitializeAccount', 'CloseAccount', 'SyncNative', 'MintTo', 'Burn' }: continue

            if parsed['instruction'] == 'Burn':
                pre = self.token_balances[idx][parsed['account']]
                owner = pre and pre.owner
                yield (str(self.msg.account_keys[parsed['mint']]), str(self.msg.account_keys[parsed['account']]), owner, -parsed['amount'])
                continue

            if parsed['instruction'] == 'MintTo':
                post = self.token_balances[idx + 1][parsed['account']]
                owner = post and post.owner
                yield (str(self.msg.account_keys[parsed['mint']]), str(self.msg.account_keys[parsed['account']]), owner, parsed['amount'])
                continue

            pre = self.token_balances[idx][parsed['account']]
            post = self.token_balances[idx + 1][parsed['account']]
            mint = (pre and pre.mint) or (post and post.mint) or ('mint' in parsed and str(self.msg.account_keys[parsed['mint']]))
            if mint != constants.WSOL_MINT_ID: continue

            if parsed['instruction'] == 'SyncNative':
                if pre is None or post is None or pre.amount is None or post.amount is None:
                    yield (mint, str(self.msg.account_keys[parsed['account']]), None, None)
                else:
                    yield (mint, str(self.msg.account_keys[parsed['account']]), post.owner, post.amount - pre.amount)
                continue

            if parsed['instruction'] == 'InitializeAccount':
                if post is None or post.amount is None:
                    yield (mint, str(self.msg.account_keys[parsed['account']]), None, None)
                else:
                    yield (mint, str(self.msg.account_keys[parsed['account']]), post.owner, post.amount)
                continue

            if parsed['instruction'] == 'CloseAccount':
                if pre is None or pre.amount is None:
                    yield (mint, str(self.msg.account_keys[parsed['account']]), None, None)
                else:
                    yield (mint, str(self.msg.account_keys[parsed['account']]), pre.owner, -pre.amount)
                continue


    def propagate(self, ix_idx):
        changed = False
        ix = self.all_instructions[ix_idx]
        invoked_prog = str(self.msg.account_keys[ix["program_id_index"]])

        def handle_token_initialize_account(parsed):
            acc_idx = parsed['account']
            pre_native = self.native_balances[ix_idx][acc_idx]

            assert self.token_balances[ix_idx][acc_idx] is None, 'only uninitialized accounts can be created'
            assert pre_native is None or pre_native >= constants.TOKEN_RENT_EXEMPT_AMOUNT, 'must be rent-exempt'

            mint = parsed['mint']
            mint = str(self.msg.account_keys[mint] if isinstance(mint, int) else mint)

            owner = parsed['owner']
            owner = str(self.msg.account_keys[owner] if isinstance(owner, int) else owner)

            balance = TokenBalance(mint=mint, owner=owner, amount=None)
            if balance.mint == constants.WSOL_MINT_ID:
                lamports = self.native_balances[ix_idx][acc_idx]
                balance.amount = lamports - constants.TOKEN_RENT_EXEMPT_AMOUNT if lamports is not None else None
            else:
                balance.amount = 0

            changed = self.token_balances[ix_idx + 1][acc_idx] != balance
            self.token_balances[ix_idx + 1][acc_idx] = balance
            changed_native = any(list(handle_native_delta(i) for i in ix['accounts']))
            changed_token = any(list(handle_token_delta(i) for i in ix['accounts'] if i != acc_idx))
            return changed or changed_native or changed_token

        def handle_token_close_account(parsed):
            acc_idx = parsed['account']
            changed = False

            post = self.token_balances[ix_idx + 1][acc_idx]
            assert post is None, 'closed account must be uninit afterwards'

            pre = self.token_balances[ix_idx][acc_idx]
            if pre is None: return False

            pre_native = self.native_balances[ix_idx][acc_idx]
            post_native = self.native_balances[ix_idx + 1][acc_idx]
            if pre.mint != constants.WSOL_MINT_ID:
                if pre.amount is None:
                    pre.amount = 0
                    changed = True
                assert pre.amount == 0, 'non-native token account must have zero balance for closing'

                if pre_native is None:
                    pre_native = constants.TOKEN_RENT_EXEMPT_AMOUNT
                    changed = True
                assert pre_native == constants.TOKEN_RENT_EXEMPT_AMOUNT, 'non-native token account must have rent-exempt balance'

            if post_native is None:
                post_native = 0
                changed = True
            assert post_native == 0, 'token account must have zero balance after closing'

            for i in ix['accounts']:
                if i == acc_idx: continue

                if i == parsed['destination']:
                    if pre_native is not None and post_native is not None:
                        changed = handle_native_delta(i, pre_native - post_native) or changed
                else:
                    changed = handle_native_delta(i) or changed

                changed = handle_token_delta(i) or changed

            return changed

        def handle_token_sync_native(parsed):
            changed = False
            acc_idx = parsed['account']

            pre = self.token_balances[ix_idx][acc_idx]
            post = self.token_balances[ix_idx + 1][acc_idx]
            pre_native = self.native_balances[ix_idx][acc_idx]
            post_native = self.native_balances[ix_idx + 1][acc_idx]

            if pre is None:
                pre = TokenBalance(mint=constants.WSOL_MINT_ID, owner=None, amount=None)
                changed = True

            if post is None:
                post = TokenBalance(mint=constants.WSOL_MINT_ID, owner=None, amount=None)
                changed = True

            assert post.mint == pre.mint, 'mint cannot change'

            if pre.owner is None and post.owner is not None:
                pre.owner = post.owner
                changed = True

            if post.owner is None and pre.owner is not None:
                post.owner = pre.owner
                changed = True

            assert pre.owner == post.owner, 'owner cannot change'

            if post_native is None and pre_native is not None:
                post_native = pre_native
                changed = True

            if pre_native is None and post_native is not None:
                pre_native = post_native
                changed = True

            assert pre_native == post_native, 'native balances must match'

            if post.amount is None and post_native is not None:
                post.amount = post_native - constants.TOKEN_RENT_EXEMPT_AMOUNT
                changed = True

            if post_native is None and post.amount is not None:
                post_native = post.amount + constants.TOKEN_RENT_EXEMPT_AMOUNT
                changed = True

            if post_native is not None and post.amount is not None:
                assert post_native - constants.TOKEN_RENT_EXEMPT_AMOUNT == post.amount, 'balance must be sync'

            for i in ix['accounts']:
                if i == acc_idx: continue
                changed = handle_native_delta(i, 0) or changed
                changed = handle_token_delta(i, 0) or changed

            self.token_balances[ix_idx][acc_idx] = pre
            self.token_balances[ix_idx + 1][acc_idx] = post
            self.native_balances[ix_idx][acc_idx] = pre_native
            self.native_balances[ix_idx + 1][acc_idx] = post_native

            return changed


        def handle_token_mint_or_burn(parsed):
            factor = -1 if parsed['instruction'] == 'Burn' else 1

            deltas = defaultdict(int)
            deltas[parsed['account']] = factor * parsed['amount']

            changed = any(list(handle_native_delta(i) for i in ix['accounts']))
            return any(list(handle_token_delta(i, deltas[i]) for i in ix['accounts'])) or changed

        def handle_token_transfer(parsed):
            src_idx = parsed['source']
            dst_idx = parsed['destination']

            deltas = defaultdict(int)
            deltas[src_idx] += -parsed['amount']
            deltas[dst_idx] += parsed['amount']

            native_deltas = defaultdict(int)
            all_accs = (
                self.token_balances[i][acc_idx]
                for i in (ix_idx, ix_idx + 1)
                for acc_idx in (src_idx, dst_idx)
            )
            mint = next((acc.mint for acc in all_accs if acc is not None), None)
            if mint == constants.WSOL_MINT_ID:
                native_deltas[src_idx] = -parsed['amount']
                native_deltas[dst_idx] = parsed['amount']

            if mint is not None:
                for i in (ix_idx, ix_idx + 1):
                    for acc_idx in (src_idx, dst_idx):
                        acc = self.token_balances[i][acc_idx]
                        if acc is not None: continue

                        self.token_balances[i][acc_idx] = TokenBalance(mint=mint, owner=None, amount=None)
                        changed = True

            ignored_accs = {src_idx, dst_idx} if mint is None else set()
            changed = any(list(handle_native_delta(i, native_deltas[i]) for i in ix['accounts'] if i not in ignored_accs))
            changed = any(list(handle_token_delta(i, deltas[i]) for i in ix['accounts'])) or changed
            return changed

        def handle_token_approve_or_revoke(parsed):
            src_idx = parsed['source']
            authority = parsed['authority']
            authority = str(self.msg.account_keys[authority] if isinstance(authority, int) else authority)

            changed = any(list(handle_native_delta(i) for i in ix['accounts']))
            changed = any(list(handle_token_delta(i) for i in ix['accounts'])) or changed

            for i in (ix_idx, ix_idx + 1):
                acc = self.token_balances[i][src_idx]
                if acc is not None and acc.owner is None:
                    acc.owner = authority
                    changed = True
                assert acc is None or acc.owner == authority, 'authority must sign for approve/revoke'
                self.token_balances[i][src_idx] = acc
            return changed

        def handle_token_set_authority(parsed):
            if parsed['authority_type'] != 'AccountOwner':
                return any(handle_token_delta(i) for i in ix['accounts'])

            changed = False

            acc_idx = parsed['account']
            new_authority = parsed['new_authority']
            if isinstance(new_authority, int):
                new_authority = self.msg.account_keys[new_authority]
            new_authority = str(new_authority)
            authority = parsed['authority']
            authority = str(self.msg.account_keys[authority] if isinstance(authority, int) else authority)

            pre = self.token_balances[ix_idx][acc_idx]
            post = self.token_balances[ix_idx + 1][acc_idx]
            any_acc = pre or post
            if not any_acc: return False

            if pre is None:
                pre = dataclasses.replace(post, owner=authority)
                changed = True

            if post is None:
                post = dataclasses.replace(pre, owner=new_authority)
                changed = True

            assert pre is not None and post is not None, 'either pre or post must exist at this point'

            if pre.amount is None:
                pre.amount = post.amount
                changed = True

            if post.amount is None:
                post.amount = pre.amount
                changed = True

            if pre.owner is None:
                pre.owner = authority
                changed = True

            if post.owner is None:
                post.owner = new_authority
                changed = True

            assert pre.mint == post.mint, 'mint cannot change'
            assert pre.amount == post.amount, 'amount cannot change'
            assert pre.owner == authority, 'authority must be previous owner'
            assert post.owner == new_authority, 'new_authority must be new owner'

            self.token_balances[ix_idx][acc_idx] = pre
            self.token_balances[ix_idx + 1][acc_idx] = post

            changed = any(list(handle_native_delta(i) for i in ix['accounts'])) or changed
            return any(list(handle_token_delta(i) for i in ix['accounts'] if i != acc_idx)) or changed

        def handle_token_delta(acc_idx, delta=0):
            changed = False

            pre = self.token_balances[ix_idx][acc_idx]
            post = self.token_balances[ix_idx + 1][acc_idx]
            any_acc = pre or post
            if not any_acc: return False

            if pre is None:
                pre = dataclasses.replace(post, amount=None)
                changed = True

            if post is None:
                post = dataclasses.replace(pre, amount=None)
                changed = True

            assert pre is not None and post is not None

            if pre.amount is None and post.amount is not None:
                pre.amount = post.amount - delta
                changed = True

            if post.amount is None and pre.amount is not None:
                post.amount = pre.amount + delta
                changed = True

            if pre.owner is None and post.owner is not None:
                pre.owner = post.owner
                changed = True

            if post.owner is None and pre.owner is not None:
                post.owner = pre.owner
                changed = True

            assert pre.mint == post.mint, 'mint cannot change'
            assert pre.owner == post.owner, 'owner cannot change'
            if pre.amount is not None and post.amount is not None:
                assert pre.amount + delta == post.amount, 'amount must match'
            else:
                assert pre.amount is None and post.amount is None

            self.token_balances[ix_idx][acc_idx] = pre
            self.token_balances[ix_idx + 1][acc_idx] = post

            return changed

        def handle_native_delta(acc_idx, delta=0):
            changed = False

            pre = self.native_balances[ix_idx][acc_idx]
            post = self.native_balances[ix_idx + 1][acc_idx]
            if pre is None and post is None: return False

            if pre is None:
                assert post is not None
                pre = post - delta
                changed = True

            if post is None:
                assert pre is not None
                post = pre + delta
                changed = True

            assert pre + delta == post, 'amount must match'
            self.native_balances[ix_idx][acc_idx] = pre
            self.native_balances[ix_idx + 1][acc_idx] = post

            return changed

        def handle_system_create_account(parsed):
            changed = False
            dest_pre = self.native_balances[ix_idx][parsed['destination']]
            if dest_pre is None:
                dest_pre = 0
                changed = True
            assert dest_pre == 0, 'create account destination must have zero balance'
            self.native_balances[ix_idx][parsed['destination']] = dest_pre

            native_deltas = defaultdict(int)
            native_deltas[parsed['source']] = -parsed['amount']
            native_deltas[parsed['destination']] = parsed['amount']

            native_changed = any(list(handle_native_delta(i, native_deltas[i]) for i in ix['accounts']))
            return changed or native_changed

        def handle_system_transfer(parsed):
            native_deltas = defaultdict(int)
            native_deltas[parsed['source']] = -parsed['amount']
            native_deltas[parsed['destination']] = parsed['amount']
            return any(list(handle_native_delta(i, native_deltas[i]) for i in ix['accounts']))

        def handle_system_withdraw_nonce_account(parsed):
            native_deltas = defaultdict(int)
            native_deltas[parsed['account']] = -parsed['amount']
            native_deltas[parsed['destination']] = parsed['amount']
            return any(list(handle_native_delta(i, native_deltas[i]) for i in ix['accounts']))

        def handle_not_implemented(*args, **_kwargs):
            raise NotImplemented

        token_handlers = {
            'MintTo': handle_token_mint_or_burn,
            'Burn': handle_token_mint_or_burn,
            'InitializeAccount': handle_token_initialize_account,
            'CloseAccount': handle_token_close_account,
            'Transfer': handle_token_transfer,
            'SetAuthority': handle_token_set_authority,
            'SyncNative': handle_token_sync_native,
            'Approve': handle_token_approve_or_revoke,
            'Revoke': handle_token_approve_or_revoke,
        }

        system_handlers = {
            'CreateAccount': handle_system_create_account,
            'Transfer': handle_system_transfer,
            'CreateAccountWithSeed': handle_not_implemented,
            'TransferWithSeed': handle_not_implemented,
            'WithdrawNonceAccount': handle_system_withdraw_nonce_account,
        }


        # propagate balances of readonly accounts and accounts not used by current instruction
        used_accs = set(ix['accounts'])
        for acc_idx in range(len(self.native_balances[ix_idx])):
            is_used = acc_idx in used_accs
            is_writable = self.msg.is_account_writable(acc_idx)
            known_nonmut_progs = { constants.SPL_ATA_ID }
            if is_used and is_writable and invoked_prog not in known_nonmut_progs: continue

            changed = handle_native_delta(acc_idx, 0) or changed
            changed = handle_token_delta(acc_idx, 0) or changed

        # propagate token balances of accounts if invoked program is not the token program
        account_keys = list(range(len(self.native_balances[ix_idx])))
        if invoked_prog != constants.SPL_TOKEN_ID:
            for acc_idx in ix['accounts']:
                changed = handle_token_delta(acc_idx, 0) or changed
        else:
            parsed = ix_parser.parse_spl_token(account_keys, ix['accounts'], ix['data'])
            handler = token_handlers.get(parsed['instruction'], None)
            if handler is not None:
                changed = handler(parsed) or changed
            else:
                for acc_idx in ix['accounts']:
                    changed = handle_token_delta(acc_idx, 0) or changed
                    changed = handle_native_delta(acc_idx, 0) or changed

        if invoked_prog == constants.SYSTEM_PROGRAM_ID:
            parsed = ix_parser.parse_system(account_keys, ix['accounts'], ix['data'])
            handler = system_handlers.get(parsed['instruction'], None)
            if handler is not None:
                changed = handler(parsed) or changed
            else:
                for acc_idx in ix['accounts']:
                    changed = handle_token_delta(acc_idx, 0) or changed
                    changed = handle_native_delta(acc_idx, 0) or changed


        return changed


RE_LEDGER_POSTING = re.compile(r'^[ \t]+(?P<account>"(?:[^"\\]|\\.)*"|(?:[ \t]?[^ \t]+)*)(?:[ \t]{2,}(?P<amount_and_trailer>.*))?$')
RE_AMOUNT_TOKEN = re.compile(r'^[ \t]*("(?:[^"\\]|\\.)*"|-|\+|[^ \t-.,0-9;=@"]+|;|=|@|[,.]*[0-9][0-9,.]*(?:[eE][+-]?[0-9]*)?|[eE][+-]?[0-9]*)')
RE_AMOUNT_VALUE = re.compile(r'^(?P<value>[,.]*[0-9][0-9,.]*)?(?:[eE](?P<exponent>[+-]?[0-9]*))?$')
RE_AMOUNT_CURRENCY = re.compile(r'^"((?:[^"\\]|\\.)*)"|([^ \t-.,0-9;=@"]+)$')

@dataclass
class LedgerPosting:
    account: str
    amount: int | float
    currency: str
    trailer: str

    @staticmethod
    def from_line(line):
        if not line or line[0] not in " \t":
            raise ValueError('posting line must be indented, but is not: ' + line)


        match = RE_LEDGER_POSTING.match(line)
        if not match:
            raise ValueError('invalid ledger posting: ' + line)

        account = match.group('account')
        rest = match.group('amount_and_trailer')

        amount_abs = float('nan')
        sign = None
        trailer = ''
        currency = ''
        while rest:
            token_match = RE_AMOUNT_TOKEN.match(rest)
            if not token_match:
                raise ValueError(f'invalid posting at {rest}: {line}')

            rest = rest[token_match.end():]
            token = token_match.group(1)

            if token in '@=;':
                trailer = token + rest
                break

            if token in {'+', '-'}:
                if sign:
                    raise ValueError(f'invalid amount, duplicate sign: {line}')
                sign = token
                continue

            value_match = RE_AMOUNT_VALUE.match(token)
            if value_match:
                value = value_match.group('value')
                if ',,' in value:
                    raise ValueError(f'invalid amount, duplicate digit separator: {line}')

                value = value.replace(',','')
                if '.' in value:
                    amount_abs = float(value)
                elif value:
                    amount_abs = int(value)
                else:
                    amount_abs = 0

                e = value_match.group('exponent')
                if e:
                    amount_abs = amount_abs * pow(10, int(e))
                continue

            currency_match = RE_AMOUNT_CURRENCY.match(token)
            if currency_match:
                currency = currency_match.group(1)
                if currency is None:
                    currency = currency_match.group(2)
                continue

            raise AssertionError(f'token should be handled: {repr(token)}')

        if sign is not None and pd.isna(amount_abs):
            raise ValueError(f'invalid amount, sign without value in posting: {line}')

        if currency and pd.isna(amount_abs):
            raise ValueError(f'invalid amount, currency without value in posting: {line}')

        amount = -amount_abs if sign == '-' else amount_abs
        return LedgerPosting(account, amount, currency, trailer)

LEDGER_ENTRY_HEADER = re.compile(r'^(?P<date>[0-9/\-.]+)[ \t](?P<description>.*)$')
LEDGER_ENTRY_COMMENT = re.compile(r'^[ \t]*[;*#](.*)$')
LEDGER_TAG = re.compile(r'(?P<name>[^ ,\n]+):(?P<value>[^,\n]*)')

@dataclass
class LedgerEntry:
    date_str: str
    description: str
    comment: str = ''
    postings: list[LedgerPosting] = dataclasses.field(default_factory=list)
    tags: set = dataclasses.field(default_factory=set)

    @staticmethod
    def new_for_timestamp(tz, timestamp, description):
        date_str = timestamp.astimezone(tz).strftime("%Y-%m-%d")
        return LedgerEntry(date_str, description, comment=f'timestamp:{timestamp}\n')

    @staticmethod
    def from_lines(ls):
        if not ls: raise ValueError('empty ledger entry, need at least one line')

        header_match = LEDGER_ENTRY_HEADER.match(ls[0])
        if not header_match:
            raise ValueError(f'first line of ledger entry is not a valid header: {ls[0]}')

        ls = ls[1:]
        comment = ''
        while ls:
            m = LEDGER_ENTRY_COMMENT.match(ls[0])
            if not m: break
            comment += m.group(1) + '\n'
            ls = ls[1:]

        tags = set()
        for tag in LEDGER_TAG.finditer(comment):
            name = tag.group('name')
            value = tag.group('value')
            if value:
                tags.add(name + ':' + value)
            else:
                tags.add(name)

        postings = [LedgerPosting.from_line(l) for l in ls]
        date = header_match.group('date')
        desc = header_match.group('description')
        return LedgerEntry(date, desc, comment, postings, tags)

    def add_posting(self, account, amount, currency, trailer=''):
        self.postings.append(LedgerPosting(account, amount, currency, trailer))

    def balance_for(self, currency):
        return sum(posting.amount for posting in self.postings if posting.currency == currency)

    def merge_postings(self):
        total_delta: dict[tuple[str,str], int|float] = defaultdict(int)
        for posting in self.postings:
            if posting.trailer.strip(): continue
            total_delta[(posting.account, posting.currency)] += posting.amount

        new_postings = []
        for posting in self.postings:
            if posting.trailer.strip():
                new_postings += [posting]
                continue

            delta = total_delta.pop((posting.account, posting.currency), 0)
            if not delta: continue

            new_postings += [LedgerPosting(posting.account, delta, posting.currency, "")]

        self.postings = new_postings

    def print(self, tokens, raw):
        print(f'{self.date_str} {self.description}')
        for line in self.comment.strip().split('\n'):
            if line.strip():
                print(f'    ; {line.strip()}')
        tag_comment = ''
        for tag in self.tags:
            tag = tag + ':' if ':' not in tag else tag
            if tag in self.comment: continue
            tag_comment += tag + ','
        if tag_comment:
            print(f'    ; {tag_comment}')

        for posting in self.postings:
            if not posting.amount: continue
            trailer = '' if not posting.trailer.strip() else ' ' + posting.trailer.strip()

            if pd.isna(posting.amount):
                if trailer:
                    trailer = ' ' + trailer
                print(f'    {posting.account}{trailer}')
            else:
                amount = amount_to_string(tokens, posting.amount, posting.currency, raw)
                print(f'    {posting.account}  {amount}{trailer}')


def parse_ledger_file(f):
    line = next(f, None)
    while line is not None:
        line = line.rstrip('\n')
        if not line.strip() or LEDGER_ENTRY_COMMENT.match(line):
            line = next(f, None)
            continue

        entry_lines = [line]
        while True:
            line = next(f, None)
            if not line or not line.strip(): break
            line = line.rstrip('\n')
            if line[0] not in ' \t': break
            entry_lines += [line]

        yield LedgerEntry.from_lines(entry_lines)


def account_pubkey(account_keys, pk_or_idx):
    if pk_or_idx is None: return None
    if isinstance(pk_or_idx, int):
        return str(account_keys[pk_or_idx])
    return str(pk_or_idx)


class Processor:
    def __init__(self, tz, rpc: SolRpc, account_tracker: AccountTracker, rewards: pd.DataFrame, begin: int, end: int|float, manual_entries: dict[str, LedgerEntry], no_map_tokens: bool):
        epoch_info = rpc.epochInfo()
        self.tokens = read_solana_tokens()
        self.transfer_mapping = pd.read_csv('transfer-mapping.csv', dtype=str, keep_default_na=False).set_index('pubkey').to_dict('index')
        self.tz = tz
        self.rpc = rpc
        self.account_tracker = account_tracker
        self.last_epoch = epoch_info['epoch']
        self.last_slot = epoch_info['absoluteSlot']
        self.rewards = rewards
        self.begin = begin
        self.end = end
        self.next_reward_epoch = 0
        self.remaining_epoch_rewards = []
        self.manual_entries = manual_entries
        self.no_map_tokens = no_map_tokens
        self.aliases = {}

        self.current_slot = -1
        self.current_timestamp = -1
        self.ix_processed = set()

    def entry_for_tx_generic(self, details: TransactionRecordDetails) -> LedgerEntry:
        record = details.record
        account_keys = details.msg.account_keys
        fee_payer = account_keys[0]

        entry = LedgerEntry.new_for_timestamp(self.tz, details.record.timestamp, f'{fee_payer} | unknown')
        entry.comment += f'signature:{details.record.signature}\nhttps://solscan.io/tx/{details.record.signature}\n'
        entry.comment += f'fee_payer:{details.record.tx.fee_payer}\n'
        for signer in details.msg.account_keys[:details.msg.header.num_required_signatures]:
            entry.comment += f'signer:{signer}\n'

        for acc, delta in record.sol_changes():
            entry.add_posting(f'raw:solana:{acc}', delta, 'SOL')

        for acc, _, mint, delta in details.token_changes():
            if not delta:
                continue
            entry.add_posting(f'raw:solana:{acc}', delta, mint)
            if str(mint) == constants.WSOL_MINT_ID:
                entry.add_posting(f'raw:solana:{acc}', -delta, 'SOL')

        unknown_mint_or_burn_amount = set()
        for mint, acc, owner, amount in details.token_burns_and_mints():
            if amount is None:
                unknown_mint_or_burn_amount.add(mint)
                continue
            if mint == constants.WSOL_MINT_ID:
                acc = 'equity:conversion:solana:wsol' if self.account_tracker.is_own_signer(owner) else 'external:solana:conversion:wsol'
                entry.add_posting(acc, -amount, mint)
                entry.add_posting(acc, amount, 'SOL')
                continue
            entry.add_posting(f'equity:solana:mint:{mint}', -amount, mint)

        is_scam = any('scam' in self.tokens.get(posting.currency, {}).get('tags', []) for posting in entry.postings)
        is_scam = is_scam or any('scam' in self.tokens.get(str(key), {}).get('tags', []) for key in account_keys)

        if not self.account_tracker.is_own_signer(fee_payer):
            entry.tags.add('external')
        if is_scam:
            entry.tags.add('scam')

        if record.successful():
            wsol_balance = entry.balance_for(constants.WSOL_MINT_ID)
            if wsol_balance != 0:
                print(f'warning: corrected wSOL balance for {details.record.signature}', file=sys.stderr)
                assert constants.WSOL_MINT_ID in unknown_mint_or_burn_amount
                entry.add_posting(f'equity:solana:mint:{constants.WSOL_MINT_ID}', -wsol_balance, constants.WSOL_MINT_ID)

            for mint in unknown_mint_or_burn_amount:
                if mint == constants.WSOL_MINT_ID: continue
                raise RuntimeError(f'mint or burn amount for mint {mint} should be known, as it is not a wsol mint')

        else:
            # a failed transaction does not modify anything except fees
            entry.postings = [ posting for posting in entry.postings if posting.currency == 'SOL' ]
            entry.tags.add('failed')

        if self.account_tracker.signers.get(str(account_keys[0]), '') in { 'me', 'public' }:
            entry.add_posting(f"expenses:solana:fees:network:{account_keys[0]}", record.meta['fee'], 'SOL')
        else:
            entry.add_posting(f"external:solana:fees", record.meta['fee'], 'SOL')
            entry.add_posting(f"external:solana:{account_keys[0]}", -record.meta['fee'], 'SOL')
            entry.add_posting(f"raw:solana:{account_keys[0]}", record.meta['fee'], 'SOL')

        balance = entry.balance_for('SOL')
        if balance != 0:
            rent_one_epoch_zero_size = 2439
            assert balance < 0 and (
                (-balance) % rent_one_epoch_zero_size == 0
                or -balance in record.meta["preBalances"]
            )
            entry.add_posting(f'expenses:solana:fees:rent', -balance, 'SOL')

        entry.merge_postings()
        return entry

    def entry_for_inflation_reward(self, reward) -> LedgerEntry:
        self.account_tracker.process_reward(reward)
        desc = f"{reward['pubkey']} | staking rewards for epoch {reward['epoch']}"
        entry = LedgerEntry.new_for_timestamp(self.tz, reward['timestamp'].to_pydatetime(), desc)
        entry.tags.add('kind:stake-reward')

        acc = self.stake_pubkey_delegated_name(reward['pubkey'], reward['epoch'])
        rent = 0 if ':delegated:' not in acc else constants.STAKE_RENT_EXEMPT_AMOUNT
        assertion = f"= {format_amount(reward['post_balance'] - rent, 9, ',' if self.no_map_tokens else '.')} SOL"

        tracked = self.account_tracker.assets.get(reward['pubkey'])
        if ':delegated:' in acc and tracked and tracked.state.deactivation_epoch == reward['epoch']:
            assertion = f'= 0 SOL'

        entry.add_posting(f"{acc}", reward['amount'], 'SOL', assertion)
        if tracked and tracked.external:
            entry.add_posting(f'external:solana:inflation', -reward['amount'], 'SOL')
        else:
            assert tracked.state.validator is not None
            entry.add_posting(f'income:misc:staking:solana:{tracked.state.validator}', -reward["amount"], "SOL"),
        return entry

    def process_vote_withdraws(self, details: TransactionRecordDetails, entry: LedgerEntry):
        account_keys = details.record.tx.compile_message().account_keys
        if constants.VOTE_PROGRAM_ID not in { str(x) for x in account_keys }:
            return

        for idx, ix in enumerate(details.record.toplevel_instructions()):
            prog = str(account_keys[ix['program_id_index']])
            if prog != constants.VOTE_PROGRAM_ID: continue

            parsed = ix_parser.parse_vote(account_keys, ix['accounts'], ix['data'])
            if parsed['instruction'] == 'Withdraw':
                self.ix_processed.add(idx)
                yield parsed

    def process_stake(self, details: TransactionRecordDetails, entry: LedgerEntry):
        record = details.record
        account_keys = record.tx.compile_message().account_keys
        if constants.STAKE_PROGRAM_ID not in { str(x) for x in account_keys }:
            return

        for idx, ix in enumerate(record.all_instructions()):
            prog = str(account_keys[ix['program_id_index']])
            if prog != constants.STAKE_PROGRAM_ID: continue

            parsed = ix_parser.parse_stake(range(len(account_keys)), ix['accounts'], ix['data'])

            if parsed['instruction'] == 'Initialize':
                withdrawer = account_pubkey(account_keys, parsed['authorized']['withdrawer'])
                external = not self.account_tracker.is_own_signer(withdrawer)
                pk = str(account_keys[parsed['account']])
                lockup = parsed['lockup']
                state = StakeAccount(withdrawer, lockup_authority=str(lockup['custodian']), lockup_timestamp=lockup['unix_timestamp'], lockup_epoch=lockup['epoch'])
                tracked = TrackedAccount(pk, 'stake', record.meta['postBalances'][parsed['account']], state, external=external)

                payers = {}
                if not ix['inner']:
                    self.ix_processed.add(ix['toplevel_idx'])

                    # find create account instruction for initialize, if in same tx
                    for create_idx, create_ix in enumerate(record.toplevel_instructions()):
                        if create_idx >= ix['toplevel_idx']: break
                        if parsed['account'] not in create_ix['accounts']: continue
                        if str(account_keys[create_ix['program_id_index']]) != constants.SYSTEM_PROGRAM_ID: continue

                        system = ix_parser.parse_system(range(len(account_keys)), create_ix['accounts'], create_ix['data'])
                        acc = system.get('account', system.get('destination', None))

                        if acc == parsed['account'] and system['instruction'] in {'CreateAccount', 'Transfer', 'Assign', 'Allocate'}:
                            self.ix_processed.add(create_idx)
                        if 'source' in system:
                            payers[str(account_keys[system['source']])] = system['amount']

                    yield 'new', pk, payers

                if state.lockup_authority or not tracked.external:
                    self.account_tracker.track(details.record.txid, tracked)

            if parsed['instruction'] == 'Authorize' and parsed['kind'] == 'Staker':
                if not ix['inner']:
                    self.ix_processed.add(ix['toplevel_idx'])

            if parsed['instruction'] == 'Authorize' and parsed['kind'] == 'Withdrawer':
                pk = str(account_keys[parsed['account']])
                tracked = self.account_tracker.assets.get(pk)
                new_authority = account_pubkey(account_keys, parsed['new_authority'])
                external = not self.account_tracker.is_own_signer(new_authority)

                if not ix['inner']:
                    self.ix_processed.add(ix['toplevel_idx'])

                if not tracked:
                    if external: continue

                    if parsed['custodian'] is not None:
                        data = {
                            'pubkey': pk,
                            'kind': 'stake',
                            'external': False,
                        }
                        self.account_tracker.events += [TrackEvent(details.record.txid, 'state-missing', data)]
                        self.account_tracker.have_error = True

                    state = StakeAccount(new_authority)
                    tracked = TrackedAccount(pk, 'stake', record.meta['postBalances'][parsed['account']], state, external=False)
                    self.account_tracker.track(details.record.txid, tracked)
                    yield 'new', pk, {}
                else:
                    assert tracked.kind == 'stake'

                    before = tracked.copy()
                    tracked.external = external
                    tracked.state.owner = new_authority
                    yield 'changed', before

                    if tracked.state.lockup_authority is None and tracked.external:
                        self.account_tracker.untrack(details.record.txid, tracked)
                    else:
                        self.account_tracker.events += [TrackEvent(details.record.txid, 'track-change', tracked.to_dict())]

            if parsed['instruction'] == 'Split':
                src = str(account_keys[parsed['source']])
                dest = str(account_keys[parsed['destination']])

                if not ix['inner']:
                    self.ix_processed.add(ix['toplevel_idx'])
                    # find create account instruction for initialize, if in same tx
                    for create_idx, create_ix in enumerate(record.toplevel_instructions()):
                        if create_idx >= ix['toplevel_idx']: break
                        if parsed['destination'] not in create_ix['accounts']: continue
                        if str(account_keys[create_ix['program_id_index']]) != constants.SYSTEM_PROGRAM_ID: continue

                        system = ix_parser.parse_system(range(len(account_keys)), create_ix['accounts'], create_ix['data'])
                        acc = system.get('account', system.get('destination', None))

                        if acc == parsed['destination'] and system['instruction'] in {'CreateAccount', 'Transfer', 'Assign', 'Allocate'}:
                            self.ix_processed.add(create_idx)

                tracked_src = self.account_tracker.assets.get(src)
                authority = str(account_keys[parsed['authority']])
                if tracked_src is None:
                    state = StakeAccount(authority)
                    tracked_src = TrackedAccount(src, 'stake', external=not self.account_tracker.is_own_signer(state.owner), state=state)
                    if not tracked_src.external:
                        data = {
                            'pubkey': src,
                            'kind': 'stake',
                            'external': False,
                        }
                        self.account_tracker.events += [TrackEvent(details.record.txid, 'state-missing', data)]
                        self.account_tracker.have_error = True

                assert tracked_src.kind == 'stake'
                state = StakeAccount.deserialize(tracked_src.state.serialize())
                tracked_dst = TrackedAccount(dest, 'stake', record.meta['postBalances'][parsed['destination']], state, external=tracked_src.external)
                if not tracked_dst.external:
                    self.account_tracker.track(details.record.txid, tracked_dst)

                yield 'split', tracked_src.copy(), tracked_dst.copy()
                if not tracked_src.external:
                    if tracked_src.state.is_delegated(slot_to_epoch(self.current_slot)):
                        pre_balance = details.native_balances[idx][parsed['destination']] or 0
                        split_staked = parsed['amount'] - pre_balance
                        stake_delta_src = min(parsed['amount'], tracked_src.state.delegated_amount)

                        tracked_src.state.delegated_amount -= stake_delta_src
                        tracked_dst.state.delegated_amount = split_staked

            if parsed['instruction'] == 'Merge':
                src = str(account_keys[parsed['source']])
                dst = str(account_keys[parsed['destination']])

                tracked_src = self.account_tracker.assets.get(src)
                tracked_dst = self.account_tracker.assets.get(dst)

                if not ix['inner']:
                    self.ix_processed.add(ix['toplevel_idx'])

                if tracked_src is None and tracked_dst is not None and not tracked_dst.external:
                    data = {
                        'pubkey': src,
                        'kind': 'stake',
                        'external': False,
                    }
                    self.account_tracker.events += [TrackEvent(details.record.txid, 'state-missing', data)]
                    self.account_tracker.have_error = True

                if tracked_dst is None and tracked_src is not None and not tracked_src.external:
                    data = {
                        'pubkey': dst,
                        'kind': 'stake',
                        'external': False,
                    }
                    self.account_tracker.events += [TrackEvent(details.record.txid, 'state-missing', data)]
                    self.account_tracker.have_error = True

                if tracked_src is None or tracked_dst is None or not record.successful(): continue

                yield 'merge', tracked_src.copy(), tracked_dst.copy()
                assert tracked_src.external == tracked_dst.external
                if tracked_src.external or not record.successful(): continue

                if tracked_dst.state.is_delegated(slot_to_epoch(self.current_slot)):
                    tracked_dst.state.delegated_amount += tracked_src.state.delegated_amount


            if parsed['instruction'] == 'SetLockup':
                if not ix['inner']:
                    self.ix_processed.add(ix['toplevel_idx'])

                pk = str(account_keys[parsed['account']])
                tracked = self.account_tracker.assets.get(pk)
                custodian_or_withdrawer = account_pubkey(account_keys, parsed['signer'])
                if not tracked or not record.successful():
                    continue

                assert tracked.kind == 'stake'

                if tracked.state.owner != custodian_or_withdrawer and not tracked.state.lockup_authority:
                    tracked.state.lockup_authority = custodian_or_withdrawer

                new_authority = account_pubkey(account_keys, parsed['lockup']['custodian'])
                if new_authority:
                    tracked.state.lockup_authority = new_authority

                if parsed['lockup']['unix_timestamp'] is not None:
                    tracked.state.lockup_timestamp = parsed['lockup']['unix_timestamp']

                if parsed['lockup']['epoch'] is not None:
                    tracked.state.lockup_epoch = parsed['lockup']['epoch']

                self.account_tracker.events += [TrackEvent(details.record.txid, 'track-change', tracked.to_dict())]

            if parsed['instruction'] == 'DelegateStake':
                if not ix['inner']:
                    self.ix_processed.add(ix['toplevel_idx'])

                pk = str(account_keys[parsed['account']])
                vote = account_pubkey(account_keys, parsed['vote'])
                tracked = self.account_tracker.assets.get(pk)
                if not tracked or (tracked.external and tracked.kind != 'stake') or not record.successful():
                    continue

                assert tracked.kind == 'stake'
                if tracked.state.validator == vote and tracked.state.activation_epoch == slot_to_epoch(self.current_slot): continue

                yield 'delegate', tracked.copy()
                tracked.state.validator = vote
                tracked.state.deactivation_epoch = None
                tracked.state.activation_epoch = slot_to_epoch(self.current_slot)
                tracked.state.delegated_amount = tracked.native_balance - constants.STAKE_RENT_EXEMPT_AMOUNT

                self.account_tracker.events += [TrackEvent(details.record.txid, 'track-change', tracked.to_dict())]

            if parsed['instruction'] == 'Deactivate':
                if not ix['inner']:
                    self.ix_processed.add(ix['toplevel_idx'])

                pk = str(account_keys[parsed['account']])
                tracked = self.account_tracker.assets.get(pk)
                if not tracked or (tracked.external and tracked.kind != 'stake') or not record.successful():
                    continue

                assert tracked.kind == 'stake'
                if tracked.state.deactivation_epoch == slot_to_epoch(self.current_slot): continue

                yield 'deactivate', tracked.copy()
                tracked.state.deactivation_epoch = slot_to_epoch(self.current_slot)

                self.account_tracker.events += [TrackEvent(details.record.txid, 'track-change', tracked.to_dict())]

            if parsed['instruction'] == 'Withdraw':
                if not ix['inner']:
                    self.ix_processed.add(ix['toplevel_idx'])
                if record.successful():
                    yield 'withdraw', str(account_keys[parsed['account']]), str(account_keys[parsed['destination']])

    def process_system_transfers(self, details: TransactionRecordDetails):
        record = details.record
        account_keys = record.tx.compile_message().account_keys
        for idx, ix in enumerate(record.toplevel_instructions()):
            prog = str(account_keys[ix['program_id_index']])
            if prog == constants.SYSTEM_PROGRAM_ID:
                parsed = ix_parser.parse_system(account_keys, ix['accounts'], ix['data'])
                if parsed['instruction'] == 'Transfer' and idx not in self.ix_processed:
                    yield parsed
                    self.ix_processed.add(idx)
                if parsed['instruction'] == 'AdvanceNonceAccount' and idx not in self.ix_processed:
                    self.ix_processed.add(idx)

    def process_token_transfers(self, details: TransactionRecordDetails):
        record = details.record
        account_keys = record.tx.compile_message().account_keys
        toplevel = record.toplevel_instructions()
        for idx, ix in enumerate(toplevel):
            prog = str(account_keys[ix['program_id_index']])
            if prog != constants.SPL_TOKEN_ID: continue

            parsed = ix_parser.parse_spl_token(account_keys, ix['accounts'], ix['data'])
            if parsed['instruction'] == 'Transfer' and idx not in self.ix_processed:
                yield parsed
                self.ix_processed.add(idx)
            if parsed['instruction'] == 'Authorize' and idx not in self.ix_processed:
                yield parsed
                self.ix_processed.add(idx)

    def process_ata_transfers(self, details: TransactionRecordDetails):
        record = details.record
        account_keys = record.tx.compile_message().account_keys
        toplevel = record.toplevel_instructions()
        for idx, ix in enumerate(toplevel):
            prog = str(account_keys[ix['program_id_index']])
            if prog != constants.SPL_ATA_ID: continue

            parsed = ix_parser.parse_spl_ata(account_keys, ix['accounts'], ix['data'])
            if parsed['instruction'] == 'Create':
                self.ix_processed.add(idx)
                yield str(parsed['account']), str(parsed['source'])


    def handle_stake_activation(self, epoch):
        epoch_timestamp = pd.NaT
        for acc in self.account_tracker.assets.values():
            if acc.kind != 'stake' or acc.external: continue

            if acc.state.deactivation_epoch == epoch and acc.state.activation_epoch == epoch:
                continue

            if acc.state.deactivation_epoch == epoch:
                epoch_rewards = self.rewards.loc[epoch, acc.pubkey].iloc[0]
                epoch_timestamp = epoch_rewards.timestamp
                assert not pd.isna(epoch_timestamp)
                entry = LedgerEntry.new_for_timestamp(self.tz, epoch_timestamp.to_pydatetime(), f'{acc.pubkey} | unstaked from {acc.state.validator}')
                entry.comment=f'stake:{acc.pubkey}, validator:{acc.state.validator}, timestamp:{epoch_timestamp}, epoch:{epoch+1}'

                # include last epoch rewards in unstake, so that income reporting shows from which stake the rewards came
                amount = epoch_rewards.amount + acc.state.delegated_amount
                entry.add_posting(self.stake_pubkey_delegated_name(acc.pubkey, epoch), -amount, 'SOL')
                entry.add_posting(self.stake_pubkey_delegated_name(acc.pubkey, epoch + 1), amount, 'SOL')

                yield entry
                continue

            if acc.state.activation_epoch == epoch:
                epoch_timestamp = self.rewards.loc[epoch].timestamp.min() if pd.isna(epoch_timestamp) else epoch_timestamp
                assert not pd.isna(epoch_timestamp)
                entry = LedgerEntry.new_for_timestamp(self.tz, epoch_timestamp.to_pydatetime(), f'{acc.pubkey} | staked to {acc.state.validator}')
                entry.comment=f'stake:{acc.pubkey}, validator:{acc.state.validator}, timestamp:{epoch_timestamp}, epoch:{epoch+1}'

                entry.add_posting(self.stake_pubkey_delegated_name(acc.pubkey, epoch), -acc.state.delegated_amount, 'SOL')
                entry.add_posting(self.stake_pubkey_delegated_name(acc.pubkey, epoch + 1), acc.state.delegated_amount, 'SOL')

                yield entry
                continue


    def process_finished_epoch(self, epoch):
        stakes = self.account_tracker.get_stake_pubkeys()
        self.rewards = fetch_rewards_for_epoch(self.rpc, 'rewards.csv', self.rewards, epoch, stakes)

        yield from self.handle_stake_activation(epoch)

        epoch_rewards = self.rewards.loc[epoch].sort_values('effective_slot')
        self.remaining_epoch_rewards += [reward for _, reward in epoch_rewards[~epoch_rewards['effective_slot'].isna()].iterrows()]

    def process_rewards_until_slot(self, end_slot):
        to_epoch = slot_to_epoch(end_slot)
        if self.next_reward_epoch <= to_epoch:
            for finished_epoch in range(max(0, self.next_reward_epoch - 1), to_epoch):
                yield from self.process_finished_epoch(finished_epoch)

            self.next_reward_epoch = to_epoch + 1

        while self.remaining_epoch_rewards and self.remaining_epoch_rewards[0]['effective_slot'] <= end_slot:
            reward = self.remaining_epoch_rewards.pop(0)
            if reward['timestamp'].timestamp() < self.begin: continue
            if reward['timestamp'].timestamp() >= self.end: break

            self.current_slot = reward['effective_slot']
            self.current_timestamp = reward['timestamp'].timestamp()

            tracked = self.account_tracker.assets.get(reward['pubkey'])
            if tracked is None or tracked.external: continue

            entry = self.entry_for_inflation_reward(reward)
            yield entry

    def stake_pubkey_delegated_name(self, pk, epoch):
        tracked = self.account_tracker.assets.get(pk, None)
        return self.stake_account_delegated_name(tracked, epoch, pk=pk)

    def stake_account_delegated_name(self, tracked, epoch, pk=None):
        if pk is None:
            pk = tracked.pubkey
        if tracked is None or tracked.kind != 'stake' or tracked.external:
            return f'raw:solana:{pk}'

        if tracked.state.is_delegated(epoch):
            return f'assets:solana:stake:{pk}:delegated:{tracked.state.validator}'

        return f'assets:solana:stake:{pk}'

    def handle_stake_op(self, details, entry: LedgerEntry, stake):
        if not stake: return

        if not any(self.account_tracker.is_own_signer(str(details.msg.account_keys[i])) for i in range(details.msg.header.num_required_signatures)):
            return

        action0 = stake[0]
        if action0[0] == 'withdraw' and len(stake) == 1:
            _, acc, _dest = action0
            entry.description = f'{acc} | withdraw from stake account'
            entry.tags.add('kind:stake-op')
            return True

        if action0[0] == 'delegate' and len(stake) == 1:
            _, tracked = action0
            tracked = self.account_tracker.assets.get(tracked.pubkey)
            assert tracked is not None
            entry.description = f'{tracked.pubkey} | initiate stake to {tracked.state.validator}'
            entry.tags.add('kind:stake-op')
            return True

        if action0[0] == 'deactivate' and len(stake) == 1:
            _, tracked = action0
            entry.description = f'{tracked.pubkey} | initiate unstake from {tracked.state.validator}'
            entry.tags.add('kind:stake-op')
            return True

        if action0[0] == 'new' and len(stake) == 1:
            entry.description = f'{action0[1]} | initialize stake account'
            entry.tags.add('kind:stake-op')
            return True

        if action0[0] == 'merge' and len(stake) == 1:
            _, src, dst = action0
            if src.external == dst.external:
                entry.description = f'{dst.pubkey} | merge stake {src.pubkey}'
                entry.tags.add('kind:stake-op')
                return True

        if action0[0] == 'split' and len(stake) == 1:
            _, src, dst = action0
            if src.external == dst.external:
                entry.description = f'{src.pubkey} | split stake to {dst.pubkey}'
                entry.tags.add('kind:stake-op')
                return True

        if action0[0] == 'changed' and len(stake) == 1:
            before = action0[1]
            after = self.account_tracker.assets.get(before.pubkey)

            if before and after and before.external == after.external:
                entry.description = f'{before.pubkey} | update stake account authority'
                entry.tags.add('kind:stake-op')
                return True

    def handle_transfer_op(self, details, entry: LedgerEntry, system, token, stake, atas):
        system_src = set(str(parsed['source']) for parsed in system)
        system_dst = set(str(parsed['destination']) for parsed in system)

        self_funded_atas = {}
        for pk, payer in atas:
            if not self.account_tracker.is_own_signer(payer):
                system_src.add(payer)
            else:
                self_funded_atas[pk] = payer

        token_src = defaultdict(set)
        token_dst = defaultdict(set)
        signer = None
        if len(details.record.tx.signatures) == 1:
            signer = str(details.record.tx.fee_payer)

        for idx, (pre, post) in enumerate(zip(details.token_balances[0], details.token_balances[-1])):
            if pre and post and pre.mint == post.mint and pre.owner == post.owner:
                if pre.amount == post.amount: continue

                if pre.amount < post.amount:
                    if not post.owner: return
                    token_dst[pre.owner].add(str(details.msg.account_keys[idx]))
                else:
                    owner = pre.owner or signer
                    if not owner: return
                    token_src[owner].add(str(details.msg.account_keys[idx]))
                continue

            if pre:
                owner = pre.owner or signer
                if not owner: return
                token_src[owner].add(str(details.msg.account_keys[idx]))

            if post:
                if not post.owner: return
                token_dst[post.owner].add(str(details.msg.account_keys[idx]))

        stake_src = defaultdict(set)
        stake_dst = defaultdict(set)
        stake_transfers = []

        for op in stake:
            if op[0] in { 'delegate', 'deactivate' }: return
            if op[0] == 'new':
                system_src = system_src | set(op[2])
                tracked = self.account_tracker.assets.get(op[1])
                if tracked and tracked.kind == 'stake':
                    if not tracked.state.owner: return
                    stake_dst[tracked.state.owner].add(tracked.pubkey)

            if op[0] == 'changed':
                before = op[1]
                after = self.account_tracker.assets.get(before.pubkey)
                if after is None:
                    if before.owner is None: return
                    stake_src[before.state.owner].add(before.pubkey)
                    continue

                if before.state.owner != after.state.owner:
                    stake_src[before.state.owner].add(before.pubkey)
                    stake_dst[after.state.owner].add(before.pubkey)
                    stake_transfers += [(before, after)]
                    continue

                if before.native_balance < after.native_balance:
                    stake_dst[before.state.owner].add(before.pubkey)

                if before.native_balance > after.native_balance:
                    stake_src[before.state.owner].add(before.pubkey)

            if op[0] == 'split':
                _, src, dst = op
                dst = self.account_tracker.assets.get(dst.pubkey)
                if dst is None:
                    stake_src[src.state.owner].add(src.pubkey)
                    continue

                if src.state.owner != dst.state.owner:
                    stake_src[src.state.owner].add(src.pubkey)
                    stake_dst[dst.state.owner].add(dst.pubkey)

        srcs = system_src | set(token_src) | set(stake_src)
        dsts = system_dst | set(token_dst) | set(stake_dst)

        src_is_own = all(self.account_tracker.is_own_asset(s) for s in srcs)
        dst_is_own = all(self.account_tracker.is_own_asset(d) for d in dsts)

        src_is_public = all(self.account_tracker.signers.get(s, '') == 'public' for s in srcs)
        dst_is_public = all(self.account_tracker.signers.get(d, '') == 'public' for d in dsts)

        # if str(details.record.signature) == 'tSP38dp5s7qsJUzZDw5idFZdWV3ZBcjALFZmBj1PMgv78HJQwHfbcyXYeK92hAxnrFLLFREPhKEtFLwWxQrtmKP':
        #     import IPython
        #     IPython.embed()

        if dst_is_own and src_is_own:
            entry.description = 'transfer between own wallets'
            entry.tags.add('kind:transfer')

            return True

        if src_is_own and dst_is_public:
            entry.description = 'transfer to public wallet'
            entry.tags.add('kind:transfer')
            entry.tags.add('transfer:public')

            all_dst_accs = system_dst | set(
                acc
                for d in [token_dst, stake_dst]
                for accs in d.values()
                for acc in accs
            )

            for posting in list(entry.postings):
                if not posting.amount: continue

                pk = posting.account.split(':')[-1]
                if pk not in all_dst_accs or posting.account != f'raw:solana:{pk}': continue

                assert posting.amount > 0, 'transfer destination posting must have positive amount'
                tracked = self.account_tracker.get_public_account()

                tracked.state.asset_delta.setdefault(posting.currency, 0)
                tracked.state.asset_delta[posting.currency] += posting.amount


            for before, after in stake_transfers:
                assert before.owner in stake_src
                assert after.owner in stake_dst
                idx = [str(k) for k in details.msg.account_keys].index(before.pubkey)

                # only need to handle this if no posting was generated
                if details.record.meta['preBalances'][idx] != details.record.meta['postBalances'][idx]:
                    continue

                amount = details.record.meta['preBalances'][idx]
                tracked = self.account_tracker.get_public_account()

                tracked.state.asset_delta.setdefault('SOL', 0)
                tracked.state.asset_delta['SOL'] += amount

            return True

        if src_is_public and dst_is_own and srcs:
            src = next(iter(srcs))

            entry.description = 'transfer from public wallet'
            entry.tags.add('kind:transfer')
            entry.tags.add('transfer:public')

            mapping = self.transfer_mapping.get(src, {}) if len(srcs) == 1 else {}

            all_src_accs = system_src | set(
                acc
                for d in [token_src, stake_src]
                for accs in d.values()
                for acc in accs
            )

            for posting in list(entry.postings):
                if not posting.amount: continue

                pk = posting.account.split(':')[-1]
                if pk not in all_src_accs or posting.account != f'raw:solana:{pk}': continue

                assert posting.amount < 0, 'transfer source posting must have positive amount'

                tracked = self.account_tracker.get_public_account()
                tracked.state.asset_delta.setdefault(posting.currency, 0)
                tracked.state.asset_delta[posting.currency] -= -posting.amount

                if pk == str(details.record.tx.fee_payer) and posting.currency == 'SOL':
                    tracked.state.asset_delta[posting.currency] += details.record.meta['fee']

                excess = tracked.state.asset_delta[posting.currency]
                if excess < 0:
                    tracked.state.asset_delta[posting.currency] = 0
                    if len(srcs) != 1: return
                    if not mapping.get('source'):
                        entry.tags.add(f'transfer-source:{src}')
                        print(f'warning: missing transfer source mapping for {src} for {excess} {posting.currency}', file=sys.stderr)
                        return

                    entry.add_posting(mapping['source'], excess, posting.currency)

            for before, after in stake_transfers:
                assert before.owner in srcs
                idx = [str(k) for k in details.msg.account_keys].index(before.pubkey)

                # only need to handle this if no posting was generated
                if details.record.meta['preBalances'][idx] != details.record.meta['postBalances'][idx]: continue

                amount = details.record.meta['preBalances'][idx]
                tracked = self.account_tracker.get_public_account()
                tracked.state.asset_detla.setdefault('SOL', 0)
                tracked.state.asset_delta['SOL'] -= -amount

                excess = tracked.state.asset_delta['SOL']
                if excess < 0:
                    tracked.state.asset_delta['SOL'] = 0
                    if len(srcs) != 1: return
                    if not mapping.get('source'):
                        entry.tags.add(f'transfer-source:{src}')
                        print(f'warning: missing transfer source mapping for {src}', file=sys.stderr)
                        return
                    entry.add_posting(mapping['source'], -amount, 'SOL')

            return True

        src_external = not any(self.account_tracker.is_own_asset(s) for s in srcs)
        dst_external = not any(self.account_tracker.is_own_asset(d) for d in dsts)

        if src_external and dst_external:
            entry.description = 'transfer between external wallets'
            entry.tags.add('kind:transfer')
            entry.tags.add('transfer:external')

            all_asset_accs = system_src | system_dst | set(
                acc
                for d in [token_src, stake_src, token_dst, stake_src, stake_dst]
                for accs in d.values()
                for acc in accs
            )

            for posting in list(entry.postings):
                if not posting.amount: continue

                pk = posting.account.split(':')[-1]
                if pk not in all_asset_accs or posting.account != f'raw:solana:{pk}': continue

                entry.add_posting(f'raw:solana:{pk}', -posting.amount, posting.currency)
                entry.add_posting(f'external:solana:{pk}', posting.amount, posting.currency)

            return True

        if len(srcs) == 1 and dst_is_own:
            src = next(iter(srcs))
            mapping = self.transfer_mapping.get(src, {})
            if not mapping.get('source'):
                entry.tags.add(f'transfer-source:{src}')
                print(f'warning: missing transfer source mapping for {src}', file=sys.stderr)
                return

            entry.description = mapping['source_description']
            entry.tags.add('kind:transfer')
            entry.tags.add('transfer:incoming')
            to_rewrite = system_src | stake_src[src] | token_src[src]

            for posting in list(entry.postings):
                if not posting.amount: continue

                pk = posting.account.split(':')[-1]
                if pk not in to_rewrite or posting.account != f'raw:solana:{pk}': continue

                assert posting.amount < 0, 'transfer source posting should have negative amount'

                entry.add_posting(f'raw:solana:{pk}', -posting.amount, posting.currency)
                entry.add_posting(f'{mapping["source"]}', posting.amount, posting.currency)

            for before, after in stake_transfers:
                assert before.owner == src
                idx = [str(k) for k in details.msg.account_keys].index(before.pubkey)

                # only need to handle this if no posting was generated
                if details.record.meta['preBalances'][idx] != details.record.meta['postBalances'][idx]: continue

                amount = details.record.meta['preBalances'][idx]
                entry.add_posting('raw:solana:' + before.pubkey, amount, 'SOL')
                entry.add_posting(mapping['source'], -amount, 'SOL')

            return True

        if len(dsts) == 1 and src_is_own:
            dst = next(iter(dsts))
            mapping = self.transfer_mapping.get(dst, {})
            if not mapping.get('destination'):
                entry.tags.add(f'transfer-destination:{dst}')
                print(f'warning: missing transfer destination mapping for {dst}', file=sys.stderr)
                return

            entry.description = mapping['destination_description']
            entry.tags.add('kind:transfer')
            entry.tags.add('transfer:outgoing')
            to_rewrite = system_dst | stake_dst[dst] | token_dst[dst]

            for posting in list(entry.postings):
                if not posting.amount: continue

                pk = posting.account.split(':')[-1]
                if pk not in to_rewrite or posting.account != f'raw:solana:{pk}': continue

                if pk in self_funded_atas and posting.currency == 'SOL': continue

                assert posting.amount > 0, 'transfer destination posting should have positive amount'

                entry.add_posting(f'raw:solana:{pk}', -posting.amount, posting.currency)
                entry.add_posting(f'{mapping["destination"]}', posting.amount, posting.currency)

            for pk, payer in self_funded_atas.items():
                entry.add_posting(f'raw:solana:{pk}', -constants.TOKEN_RENT_EXEMPT_AMOUNT, 'SOL')
                entry.add_posting(f'expenses:donations:solana-ata-rent', constants.TOKEN_RENT_EXEMPT_AMOUNT, 'SOL')

            for before, after in stake_transfers:
                assert not before.external
                idx = [str(k) for k in details.msg.account_keys].index(before.pubkey)

                # only need to handle this if no posting was generated
                if details.record.meta['preBalances'][idx] != details.record.meta['postBalances'][idx]: continue

                amount = details.record.meta['preBalances'][idx]
                entry.add_posting(f'raw:solana:{before.pubkey}', -amount, 'SOL')
                entry.add_posting(mapping['destination'], amount, 'SOL')

            return True

    def handle_vote_withdraw_op(self, details: TransactionRecordDetails, entry: LedgerEntry, withdraws):
        handled = True
        total_amount_for_dest = defaultdict(int)
        for withdraw in withdraws:
            src = str(withdraw['account'])
            dst = str(withdraw['destination'])
            entry.tags.add('kind:stake-reward')

            total_amount_for_dest[dst] += withdraw['amount']

            if not self.account_tracker.is_own_asset(withdraw['destination']):
                entry.add_posting(f'raw:solana:{dst}', -withdraw['amount'], 'SOL')
                entry.add_posting(f'external:solana:{dst}', withdraw['amount'], 'SOL')

                entry.add_posting(f'raw:solana:{src}', withdraw['amount'], 'SOL')
                entry.add_posting(f'external:solana:inflation', -withdraw['amount'], 'SOL')
                continue

            mapping = self.transfer_mapping.get(src, {})
            if not mapping.get('source'):
                print(f'warning: missing transfer source mapping for {src}', file=sys.stderr)
                handled = False
                continue

            entry.add_posting(f'raw:solana:{src}', withdraw['amount'], 'SOL')
            entry.add_posting(f'income:misc:staking:solana:{src}', -withdraw['amount'], 'SOL')
            for asset in self.account_tracker.assets.values():
                if asset.kind != 'stake' or asset.state is None: continue
                if asset.state.validator is None or asset.state.validator != src: continue

                entry.tags.add(f'stake:{asset.pubkey}')

        for dst, amount in total_amount_for_dest.items():
            idx = [str(acc) for acc in details.msg.account_keys].index(dst)
            delta = details.native_balances[-1][idx] - details.native_balances[0][idx]
            if delta != amount:
                rent_one_epoch_zero_size = 2439
                diff = delta - amount
                assert diff < 0
                assert (
                    details.native_balances[0][idx] == -diff or
                    (-diff) % rent_one_epoch_zero_size == 0
                )
                if not self.account_tracker.is_own_asset(dst):
                    entry.add_posting(f'expenses:solana:fees:rent', diff, 'SOL')
                    entry.add_posting(f'external:solana:fees:rent', -diff, 'SOL')
                    entry.add_posting(f'raw:solana:{dst}', -diff, 'SOL')
                    entry.add_posting(f'external:solana:{dst}', diff, 'SOL')

        if handled:
            entry.description = f'collect staking rewards'

        return handled


    def process_token_ops(self, details: TransactionRecordDetails):
        for idx, ix in enumerate(details.record.toplevel_instructions()):
            prog = str(details.msg.account_keys[ix['program_id_index']])
            if prog == constants.SPL_ATA_ID:
                parsed = ix_parser.parse_spl_ata(details.msg.account_keys, ix['accounts'], ix['data'])
                if parsed['instruction'] == 'Create':
                    self.ix_processed.add(idx)
                    yield 'InitializeAccount'

                continue

            if prog == constants.SPL_TOKEN_ID:
                parsed = ix_parser.parse_spl_token(details.msg.account_keys, ix['accounts'], ix['data'])

                if parsed['instruction'] == 'InitializeAccount':
                    if not self.account_tracker.is_own_signer(str(parsed['owner'])): continue
                    self.ix_processed.add(idx)

                    yield 'InitializeAccount'

                if parsed['instruction'] == 'CloseAccount':
                    if not self.account_tracker.is_own_signer(str(parsed['authority'])): continue
                    self.ix_processed.add(idx)
                    yield 'CloseAccount'

                if parsed['instruction'] == 'SyncNative':
                    self.ix_processed.add(idx)

                if parsed['instruction'] == 'Transfer':
                    source_idx = [str(k) for k in details.msg.account_keys].index(str(parsed['source']))
                    dest_idx = [str(k) for k in details.msg.account_keys].index(str(parsed['source']))

                    source = details.token_balances[ix['linear_idx']][source_idx]
                    dest = details.token_balances[ix['linear_idx']][dest_idx]

                    if source is None or dest is None or source.owner is None or dest.owner is None: continue
                    if not self.account_tracker.is_own_signer(source.owner): continue
                    if not self.account_tracker.is_own_signer(dest.owner): continue

                    self.ix_processed.add(idx)
                    yield 'Transfer'

                continue

            if prog == constants.SYSTEM_PROGRAM_ID:
                parsed = ix_parser.parse_system(details.msg.account_keys, ix['accounts'], ix['data'])

                if parsed['instruction'] == 'CreateAccount':
                    if not self.account_tracker.is_own_signer(str(parsed['source'])): continue
                    if str(parsed['owner']) != constants.SPL_TOKEN_ID: continue


                    self.ix_processed.add(idx)
                    if parsed['amount'] != constants.TOKEN_RENT_EXEMPT_AMOUNT:
                        yield 'Transfer'
                    else:
                        yield 'InitializeAccount'

                if parsed['instruction'] == 'Transfer':
                    if not self.account_tracker.is_own_signer(str(parsed['source'])): continue

                    self.ix_processed.add(idx)
                    yield 'Transfer'


    def process_loader_ops(self, details: TransactionRecordDetails, entry: LedgerEntry):
        for idx, ix in enumerate(details.record.toplevel_instructions()):
            prog = str(details.msg.account_keys[ix['program_id_index']])

            if prog == constants.BPF_LOADER_ID:
                if not self.account_tracker.is_own_signer(str(details.record.tx.fee_payer)): continue
                self.ix_processed.add(idx)
                parsed = ix_parser.parse_loader(details.msg.account_keys, ix['accounts'], ix['data'])
                yield {
                    'Finalize': 'deploy',
                    'Write': 'write',
                }[parsed['instruction']]

            if prog == constants.BPF_UPGRADEABLE_LOADER_ID:
                if not self.account_tracker.is_own_signer(str(details.record.tx.fee_payer)): continue
                self.ix_processed.add(idx)
                parsed = ix_parser.parse_upgradeable_loader(range(len(details.msg.account_keys)), ix['accounts'], ix['data'])
                if 'authority' in parsed and not self.account_tracker.is_own_signer(account_pubkey(details.msg.account_keys, parsed['authority'])):
                    continue
                if parsed['instruction'] == 'DeployWithMaxDataLen':
                    prog_pk = account_pubkey(details.msg.account_keys, parsed['program'])
                    amount = details.native_balances[-1][parsed['program']]
                    assert amount is not None
                    entry.add_posting(f'expenses:solana:fees:rent-exempt', amount, 'SOL')
                    entry.add_posting(f'raw:solana:{prog_pk}', -amount, 'SOL')

                    progdata_pk = account_pubkey(details.msg.account_keys, parsed['programdata'])
                    amount = details.native_balances[-1][parsed['programdata']]
                    assert amount is not None
                    entry.add_posting(f'assets:solana:program-buffers:rent', amount, 'SOL')
                    entry.add_posting(f'raw:solana:{progdata_pk}', -amount, 'SOL')

                    buffer_pk = account_pubkey(details.msg.account_keys, parsed['buffer'])
                    amount = details.native_balances[0][parsed['buffer']]
                    assert amount is not None
                    entry.add_posting(f'assets:solana:program-buffers:rent', -amount, 'SOL')
                    entry.add_posting(f'raw:solana:{buffer_pk}', amount, 'SOL')
                if parsed['instruction'] == 'InitializeBuffer':
                    account_pk = account_pubkey(details.msg.account_keys, parsed['account'])
                    amount = details.native_balances[-1][parsed['account']]
                    assert amount is not None
                    entry.add_posting(f'assets:solana:program-buffers:rent', amount, 'SOL')
                    entry.add_posting(f'raw:solana:{account_pk}', -amount, 'SOL')
                if parsed['instruction'] == 'Close':
                    account_pk = account_pubkey(details.msg.account_keys, parsed['account'])
                    amount = details.native_balances[0][parsed['account']]
                    assert amount is not None
                    entry.add_posting(f'assets:solana:program-buffers:rent', -amount, 'SOL')
                    entry.add_posting(f'raw:solana:{account_pk}', amount, 'SOL')

                yield {
                    'Write': 'write',
                    'DeployWithMaxDataLen': 'deploy',
                    'InitializeBuffer': 'initialize',
                    'Upgrade': 'upgrade',
                    'SetAuthority': 'set-authority',
                    'Close': 'close-program' if parsed.get('program') is not None else 'close-buffer',
                }[parsed['instruction']]


            if prog == constants.SYSTEM_PROGRAM_ID:
                parsed = ix_parser.parse_system(details.msg.account_keys, ix['accounts'], ix['data'])

                if parsed['instruction'] == 'CreateAccount':
                    if not self.account_tracker.is_own_signer(str(parsed['source'])): continue
                    if str(parsed['owner']) not in { constants.BPF_LOADER_ID, constants.BPF_UPGRADEABLE_LOADER_ID }:
                        continue

                    if str(parsed['owner']) == constants.BPF_LOADER_ID:
                        entry.add_posting(f'raw:solana:{parsed["destination"]}', -parsed['amount'], 'SOL')

                        if self.account_tracker.is_own_signer(str(parsed['source'])):
                            entry.add_posting(f'expenses:solana:fees:rent-exempt', parsed['amount'], 'SOL')
                        else:
                            entry.add_posting(f'external:solana:fees:rent-exempt', parsed['amount'], 'SOL')

                    self.ix_processed.add(idx)
                    yield 'initialize'


    def alias(self, pk, new, raw=False):
        if pk in self.aliases: return

        self.aliases[pk] = new
        if raw:
            print(f"alias raw:solana:{pk} = {new}")
        print(f"alias equity:solana:mint:{pk} = {new}")
        print(f"alias external:solana:{pk} = {new}")


    def process_swaps(self, details: TransactionRecordDetails, entry: LedgerEntry):
        if not ({ constants.JUP_ID, constants.RAYDIUM_V4_ID } & { str(x) for x in details.msg.account_keys }):
            return

        for idx, ix in enumerate(details.record.toplevel_instructions()):
            prog = str(details.msg.account_keys[ix['program_id_index']])
            if prog == constants.JUP_ID:
                parsed = ix_parser.parse_jup(range(len(details.msg.account_keys)), ix['accounts'], ix['data'])
                if parsed['instruction'] == 'set_token_ledger':
                    self.ix_processed.add(idx)
                    continue

                swap_params = {
                    'token_swap': {
                        'name': 'orca',
                        'authority': 'user_transfer_authority',
                        'source': 'source',
                        'destination': 'destination',
                        'pool_id': 'swap',
                        'pool_accounts': ['swap_source', 'swap_destination'],
                        'pool_fee': 'pool_fee',
                    },
                    'raydium_swap_v2': {
                        'name': 'raydium',
                        'authority': 'user_source_owner',
                        'source': 'user_source_token_account',
                        'destination': 'user_destination_token_account',
                        'pool_id': 'amm_id',
                        'pool_accounts': ['pool_coin_token_account', 'pool_pc_token_account'],
                    },
                    'saber_exchange': {
                        'name': 'saber',
                        'authority': 'user_authority',
                        'source': 'input_user_account',
                        'destination': 'output_user_account',
                        'pool_id': 'swap',
                        'pool_accounts': ['input_token_account', 'output_token_account'],
                        'pool_fee': 'fees_token_account',
                    },
                    'lifinity_token_swap': {
                        'name': 'lifinity',
                        'authority': 'user_transfer_authority',
                        'source': 'source_info',
                        'destination': 'destination_info',
                        'pool_id': 'amm',
                        'pool_accounts': ['swap_source', 'swap_destination'],
                        'pool_fee': 'fee_account',
                    },
                    'crema_token_swap': {
                        'name': 'crema',
                        'authority': 'wallet_authority',
                        'source': 'user_source_token_account',
                        'destination': 'user_destination_token_account',
                        'pool_id': 'pool',
                        'pool_accounts': ['pool_source_token_account', 'pool_destination_token_account'],
                    }
                }

                if parsed['instruction'] in swap_params:
                    params = swap_params[parsed['instruction']]
                    user_authority = account_pubkey(details.msg.account_keys, parsed[params['authority']])
                    if not self.account_tracker.is_own_signer(user_authority): continue

                    source_before = details.token_balances[ix['linear_idx']][parsed[params['source']]]
                    source_after = details.token_balances[ix['next_linear_idx']][parsed[params['source']]]
                    assert source_before and source_after and source_before.amount is not None and source_after.amount is not None
                    amount_in = source_before.amount - source_after.amount

                    destination_before = details.token_balances[ix['linear_idx']][parsed[params['destination']]]
                    destination_after = details.token_balances[ix['next_linear_idx']][parsed[params['destination']]]
                    assert destination_before and destination_after and destination_before.amount is not None and destination_after.amount is not None
                    amount_out = destination_after.amount - destination_before.amount

                    pool_accounts = [parsed[k] for k in params['pool_accounts']]
                    amm_pk = account_pubkey(details.msg.account_keys, parsed[params['pool_id']])
                    for pool_acc in pool_accounts:
                        bal = details.token_balances[ix['linear_idx']][pool_acc]
                        assert bal is not None
                        pk = account_pubkey(details.msg.account_keys, pool_acc)
                        if bal.mint == source_before.mint:
                            entry.add_posting(f'raw:solana:{pk}', -amount_in, bal.mint)
                            entry.add_posting(f'equity:conversion:solana:{params["name"]}:{amm_pk}', amount_in, bal.mint)
                        else:
                            assert bal.mint == destination_before.mint
                            entry.add_posting(f'raw:solana:{pk}', amount_out, bal.mint)
                            entry.add_posting(f'equity:conversion:solana:{params["name"]}:{amm_pk}', -amount_out, bal.mint)

                    if 'pool_fee' in params:
                        fee_before = details.token_balances[ix['linear_idx']][parsed[params['pool_fee']]]
                        fee_after = details.token_balances[ix['next_linear_idx']][parsed[params['pool_fee']]]
                        pool_fee = account_pubkey(details.msg.account_keys, parsed[params['pool_fee']])
                        assert fee_before and fee_after and fee_before.amount is not None and fee_after.amount is not None
                        fee = fee_after.amount - fee_before.amount

                        entry.add_posting(f'raw:solana:{pool_fee}', -fee, fee_before.mint)
                        entry.add_posting(f'external:solana:fees:{params["name"]}:{amm_pk}', fee, fee_before.mint)

                    self.ix_processed.add(idx)
                    yield params['name'], source_before.mint, destination_before.mint
                    continue

            if prog == constants.RAYDIUM_V4_ID and ix['data'][0] in {9, 11}: # swap base in/out
                accounts = ix['accounts']
                user_authority = account_pubkey(details.msg.account_keys, accounts[17])
                if not self.account_tracker.is_own_signer(user_authority): continue

                source_before = details.token_balances[ix['linear_idx']][accounts[15]]
                source_after = details.token_balances[ix['next_linear_idx']][accounts[15]]
                assert source_before and source_after and source_before.amount is not None and source_after.amount is not None
                amount_in = source_before.amount - source_after.amount

                destination_before = details.token_balances[ix['linear_idx']][accounts[16]]
                destination_after = details.token_balances[ix['next_linear_idx']][accounts[16]]
                assert destination_before and destination_after and destination_before.amount is not None and destination_after.amount is not None
                amount_out = destination_after.amount - destination_before.amount

                pool_accounts = [accounts[i] for i in [5, 6]]
                amm_pk = account_pubkey(details.msg.account_keys, ix['accounts'][1])
                for pool_acc in pool_accounts:
                    bal = details.token_balances[ix['linear_idx']][pool_acc]
                    assert bal is not None
                    pk = account_pubkey(details.msg.account_keys, pool_acc)
                    if bal.mint == source_before.mint:
                        entry.add_posting(f'raw:solana:{pk}', -amount_in, bal.mint)
                        entry.add_posting(f'equity:conversion:solana:raydium:{amm_pk}', amount_in, bal.mint)
                    else:
                        assert bal.mint == destination_before.mint
                        entry.add_posting(f'raw:solana:{pk}', amount_out, bal.mint)
                        entry.add_posting(f'equity:conversion:solana:raydium:{amm_pk}', -amount_out, bal.mint)

                self.ix_processed.add(idx)
                yield 'raydium', source_before.mint, destination_before.mint


    def __call__(self, record: TransactionRecord):
        entries = list(self.process_rewards_until_slot(record.txid.slot))

        self.current_slot = record.txid.slot
        self.current_timestamp = record.timestamp.timestamp()
        self.ix_processed = set()

        all_toplevel_ixs = set(range(len(list(record.toplevel_instructions()))))

        details = TransactionRecordDetails(self.account_tracker, record)
        entry = self.entry_for_tx_generic(details)

        assets_before = {}
        for acc in details.msg.account_keys:
            asset = self.account_tracker.assets.get(str(acc), None)
            if asset is None: continue

            assets_before[str(acc)] = asset.copy()

        if record.signature in self.manual_entries:
            manual_entry = self.manual_entries.pop(record.signature)
            manual_entry.postings = entry.postings + manual_entry.postings
            manual_entry.tags |= entry.tags
            entries += [manual_entry]
        else:
            for _ in range(1):
                # process "transfers" and staking operations
                stake = list(self.process_stake(details, entry))
                if self.ix_processed == all_toplevel_ixs and self.handle_stake_op(details, entry, stake): break

                system = list(self.process_system_transfers(details))
                token = list(self.process_token_transfers(details))
                ata = list(self.process_ata_transfers(details))

                for idx, ix in enumerate(record.toplevel_instructions()):
                    prog = str(details.msg.account_keys[ix['program_id_index']])
                    if prog in { constants.SCAM_NOOP_PROGRAM_ID }:
                        self.ix_processed.add(idx)

                if self.ix_processed == all_toplevel_ixs and self.handle_transfer_op(details, entry, system, token, stake, ata): break

                self.ix_processed = set()
                loader_ops = set(self.process_loader_ops(details, entry))

                if loader_ops == {'initialize', 'deploy'}:
                    loader_ops = {'deploy'}

                if self.ix_processed == all_toplevel_ixs and len(loader_ops) == 1:
                    op = next(iter(loader_ops))
                    entry.description = f'program deployment operation: {op}'
                    entry.tags.add('kind:loader-op')
                    entry.tags.add(f'loader:{op}')
                    break

                self.ix_processed = set()
                vote_withdraws = list(self.process_vote_withdraws(details, entry))
                if self.ix_processed == all_toplevel_ixs and self.handle_vote_withdraw_op(details, entry, vote_withdraws):
                    break

                self.ix_processed = set()
                token_ops = set(self.process_token_ops(details))
                if self.ix_processed == all_toplevel_ixs and token_ops and self.account_tracker.is_own_signer(record.tx.fee_payer):
                    entry.description = ''
                    for op in sorted(token_ops):
                        if entry.description:
                            entry.description += ', '
                        entry.description += {
                            'InitializeAccount': 'initialize new account',
                            'CloseAccount': 'close own account',
                            'Transfer': 'transfer between own wallets',
                        }[op]
                        tag = {
                            'InitializeAccount': 'initialize',
                            'CloseAccount': 'close',
                            'Transfer': 'transfer',
                        }[op]
                        entry.tags.add(f'token-op:{tag}')
                    entry.description = f'{record.tx.fee_payer} | token operations: {entry.description}'
                    entry.tags.add('kind:token-op')
                    break

                jup = list(self.process_swaps(details, entry))
                if self.ix_processed == all_toplevel_ixs:
                    entry.tags.add('kind:swap')
                    entry.description = f'{record.tx.fee_payer} | swap:'
                    for swap, mint_from, mint_to in jup:
                        entry.tags.add(f'swap:{swap}')
                        symbol_from = self.tokens[mint_from]['symbol']
                        symbol_to = self.tokens[mint_to]['symbol']
                        entry.description += f' {symbol_from} -> {symbol_to} ({swap}),'
                    entry.description = entry.description.rstrip(',')

            entries += [entry]

        self.account_tracker.tx_postprocess(details)

        # generate offsetting posting for changed assets
        epoch = slot_to_epoch(self.current_slot)
        for acc in details.msg.account_keys:
            before = assets_before.get(str(acc), None)
            after = self.account_tracker.assets.get(str(acc), None)
            if (before is None or before.external) and (after is None or after.external): continue
            if before == after: continue

            if before and before.kind == 'stake':
                if before.state.is_delegated(epoch):
                    entries[-1].add_posting(self.stake_account_delegated_name(before, epoch), -before.state.delegated_amount, 'SOL')
                    entries[-1].add_posting(f'assets:solana:stake:{before.pubkey}', before.state.delegated_amount - before.native_balance, 'SOL')
                    entries[-1].add_posting(f'raw:solana:{before.pubkey}', before.native_balance, 'SOL')
                else:
                    entries[-1].add_posting(f'assets:solana:stake:{before.pubkey}', -before.native_balance, 'SOL')
                    entries[-1].add_posting(f'raw:solana:{before.pubkey}', before.native_balance, 'SOL')

            if after and after.kind == 'stake':
                if after.state.is_delegated(epoch):
                    entries[-1].add_posting(self.stake_account_delegated_name(after, epoch), after.state.delegated_amount, 'SOL')
                    entries[-1].add_posting(f'assets:solana:stake:{after.pubkey}', -after.state.delegated_amount + after.native_balance, 'SOL')
                    entries[-1].add_posting(f'raw:solana:{after.pubkey}', -after.native_balance, 'SOL')
                else:
                    if after.external:
                        entries[-1].add_posting(f'raw:solana:{after.pubkey}', after.native_balance, 'SOL')
                    else:
                        entries[-1].add_posting(f'assets:solana:stake:{after.pubkey}', after.native_balance, 'SOL')
                    entries[-1].add_posting(f'raw:solana:{after.pubkey}', -after.native_balance, 'SOL')

            if before and before.kind == 'token' and not before.external:
                native_wsol = 0 if before.state.mint != constants.WSOL_MINT_ID else before.state.amount
                native_balance = before.native_balance - native_wsol
                entries[-1].add_posting(f'assets:solana:wallet:{before.state.owner}:{before.pubkey}', -before.state.amount, before.state.mint)
                entries[-1].add_posting(f'assets:solana:wallet:{before.state.owner}:{before.pubkey}', -native_balance, 'SOL')
                entries[-1].add_posting(f'raw:solana:{before.pubkey}', before.state.amount, before.state.mint)
                entries[-1].add_posting(f'raw:solana:{before.pubkey}', native_balance, 'SOL')

            if after and after.kind == 'token' and not after.external:
                native_wsol = 0 if after.state.mint != constants.WSOL_MINT_ID else after.state.amount
                native_balance = after.native_balance - native_wsol
                entries[-1].add_posting(f'assets:solana:wallet:{after.state.owner}:{after.pubkey}', after.state.amount, after.state.mint)
                entries[-1].add_posting(f'assets:solana:wallet:{after.state.owner}:{after.pubkey}', native_balance, 'SOL')
                entries[-1].add_posting(f'raw:solana:{after.pubkey}', -after.state.amount, after.state.mint)
                entries[-1].add_posting(f'raw:solana:{after.pubkey}', -native_balance, 'SOL')

        for idx, entry in enumerate(entries):
            entry.merge_postings()
            is_record_entry = idx == len(entries) - 1

            for posting in list(entry.postings):
                if not posting.account.startswith('raw:solana:'): continue
                suffix = posting.account[len('raw:solana:'):]
                parts = suffix.split(':', 1)
                pk = parts[0]
                suffix = '' if len(parts) < 2 or not parts[1] else ':' + parts[1]

                is_own_asset = self.account_tracker.is_own_asset(pk)
                is_public_asset = self.account_tracker.signers.get(pk, '') == 'public'

                if self.account_tracker.is_own_signer(pk):
                    posting.account = f'assets:solana:wallet:{pk}{suffix}'
                    continue

                if is_public_asset:
                    posting.account = f'assets:solana:public:{pk}{suffix}'
                    continue

                # if asset is not None and asset.kind == 'token' and not asset.external:
                #     assert asset.state.owner is not None
                #     posting.account = f'assets:solana:wallet:{asset.state.owner}:{pk}{suffix}'

            entry.print(self.tokens, self.no_map_tokens)
            print("")

    def finalize(self):
        self.process_rewards_until_slot(self.last_slot)


def main():
    assert __doc__ is not None
    args = docopt(__doc__)

    db = sqlite3.connect("tx.db")
    tz = gettz(args['--timezone'])
    rpc = SolRpc("https://api.mainnet-beta.solana.com")
    rewards = load_rewards('rewards.csv')

    select_condition = '1'
    begin = 0
    end = float('inf')
    if args['--year'] is not None:
        begin = int(datetime(year=int(args['--year'], 10), month=1, day=1, tzinfo=tz).timestamp())
        end = datetime(year=int(args['--year'], 10) + 1, month=1, day=1, tzinfo=tz).timestamp()
        select_condition = f'timestamp >= {begin} AND timestamp < {end}'

    signers: pd.DataFrame = pd.read_csv(args['--signers'], index_col='pubkey') # type: ignore
    account_tracker = AccountTracker(signers['owner'].to_dict())

    if args['--initial-assets'] is not None:
        account_tracker.load_assets(args['--initial-assets'])

    manual_entries = {}
    with open(args['--manual-ledger']) as f:
        for entry in parse_ledger_file(f):
            signature = None
            for tag in entry.tags:
                if not tag.startswith('signature:'): continue
                tag_sig = tag[len('signature:'):]
                if signature is not None:
                    raise RuntimeError(f'duplicate signature tag for entry: {entry}')
                signature = tag_sig
            if signature is None:
                raise RuntimeError(f'manual entry without TX signature: {entry}')
            if signature in manual_entries:
                raise RuntimeError(f'more than one manual entry for signature {signature}')
            manual_entries[signature] = entry

    processor = Processor(tz, rpc, account_tracker, rewards, begin, end, manual_entries, bool(args['--no-map-tokens']))
    have_error = False

    if processor.no_map_tokens:
        print('decimal-mark ,')
    else:
        print('decimal-mark .')

    if args['--manual-aliases']:
        aliases = pd.read_csv(args['--manual-aliases'], dtype={'pubkey': str, 'name': str, 'raw': bool}, keep_default_na=False)
        for _, alias in aliases.iterrows():
            processor.alias(alias.pubkey, alias['name'], alias.raw)

    for sig, timestamp, slot, index, data_str in db.execute(
        f"select * from transactions where {select_condition} order by timestamp, slot, \"index\" asc"
    ):
        data = json.loads(data_str)
        tx = solana.transaction.Transaction.deserialize(
            base64.b64decode(data["transaction"][0])
        )
        timestamp = datetime.fromtimestamp(timestamp, tz=timezone.utc)
        record = TransactionRecord(
            signature=sig,
            tx=tx,
            meta=data["meta"],
            timestamp=timestamp,
            txid=TxId(slot, index),
        )

        try:
            processor(record)
        except Exception as e:
            have_error = True
            print("", file=sys.stderr)
            print(f"error processing {sig}", file=sys.stderr)
            traceback.print_exception(e)

    processor.finalize()

    if args['--track-events']:
        pd.DataFrame([event.to_dict() for event in account_tracker.events]).to_csv(args['--track-events'], index=False)

    if args['--final-assets']:
        account_tracker.save_assets(args['--final-assets'])

    if have_error or account_tracker.have_error:
        sys.exit(1)


if __name__ == "__main__":
    main()
