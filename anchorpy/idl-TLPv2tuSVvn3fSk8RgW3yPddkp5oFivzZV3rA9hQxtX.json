{"version": "0.1.0", "name": "vaults", "instructions": [{"name": "initialize", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "management", "isMut": true, "isSigner": false}, {"name": "serum<PERSON>rade<PERSON><PERSON>unt", "isMut": true, "isSigner": false}, {"name": "serumTradePda", "isMut": false, "isSigner": false}, {"name": "serumRebateAccount", "isMut": false, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}], "args": []}, {"name": "newVault", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": false, "isSigner": false}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "underlyingMint", "isMut": true, "isSigner": false}, {"name": "underlyingDepositQueue", "isMut": false, "isSigner": false}, {"name": "underlyingCompoundQueue", "isMut": true, "isSigner": false}, {"name": "underlyingWithdrawQueue", "isMut": true, "isSigner": false}, {"name": "sharesMint", "isMut": true, "isSigner": false}, {"name": "serum<PERSON>rade<PERSON><PERSON>unt", "isMut": true, "isSigner": false}, {"name": "serumTradeOpenOrdersAccount", "isMut": true, "isSigner": false}, {"name": "serumTradePda", "isMut": true, "isSigner": false}, {"name": "serumTradeMarket", "isMut": false, "isSigner": false}, {"name": "serumDexProgram", "isMut": false, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}], "args": [{"name": "args", "type": {"defined": "NewVaultArgs"}}]}, {"name": "createSerumTradeFeeRecipient", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "serum<PERSON>rade<PERSON><PERSON>unt", "isMut": false, "isSigner": false}, {"name": "serumTradePda", "isMut": true, "isSigner": false}, {"name": "serumFeeRecipient", "isMut": true, "isSigner": false}, {"name": "feeTokenMint", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}], "args": []}, {"name": "createSerumTradeOpenOrders", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "serumMarket", "isMut": false, "isSigner": false}, {"name": "serum<PERSON>rade<PERSON><PERSON>unt", "isMut": false, "isSigner": false}, {"name": "serumTradePda", "isMut": true, "isSigner": false}, {"name": "serumTradeOpenOrdersAccount", "isMut": true, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}, {"name": "serumDexProgram", "isMut": false, "isSigner": false}], "args": []}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "management", "isMut": true, "isSigner": false}, {"name": "whitelistAuthority", "isMut": false, "isSigner": false}], "args": [{"name": "args", "type": {"defined": "ModifyWhiteListArgs"}}]}, {"name": "registerDepositTrackingAccount", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "vault", "isMut": false, "isSigner": false}, {"name": "depositTrackingAccount", "isMut": true, "isSigner": false}, {"name": "depositTrackingQueueAccount", "isMut": true, "isSigner": false}, {"name": "depositTrackingHoldAccount", "isMut": true, "isSigner": false}, {"name": "sharesMint", "isMut": true, "isSigner": false}, {"name": "depositTrackingPda", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}], "args": [{"name": "farmType", "type": {"array": ["u64", 2]}}]}, {"name": "issueShares", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "depositTracking", "isMut": true, "isSigner": false}, {"name": "depositTrackingPda", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": false, "isSigner": false}, {"name": "vaultUnderlyingAccount", "isMut": true, "isSigner": false}, {"name": "sharesMint", "isMut": true, "isSigner": false}, {"name": "receivingSharesAccount", "isMut": true, "isSigner": false}, {"name": "depositingUnderlyingAccount", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}], "args": [{"name": "args", "type": {"defined": "SharesArgs"}}]}, {"name": "permissionedIssueShares", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "vaultPda", "isMut": false, "isSigner": false}, {"name": "vaultUnderlyingAccount", "isMut": true, "isSigner": false}, {"name": "sharesMint", "isMut": true, "isSigner": false}, {"name": "receivingSharesAccount", "isMut": true, "isSigner": false}, {"name": "depositingUnderlyingAccount", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}], "args": [{"name": "args", "type": {"defined": "SharesArgs"}}]}, {"name": "configureRaydiumVault", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": true, "isSigner": false}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "userStakeInfoAccount", "isMut": true, "isSigner": false}, {"name": "raydiumStakeProgram", "isMut": false, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}, {"name": "poolId", "isMut": true, "isSigner": false}, {"name": "associatedStakeAccount", "isMut": true, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}], "args": [{"name": "args", "type": {"defined": "ConfigureRaydiumVaultArgs"}}]}, {"name": "configureLendingOptimizer", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": false, "isSigner": false}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "startingPlatformInformation", "isMut": false, "isSigner": false}], "args": []}, {"name": "configureMultiDepositOptimizer", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": false, "isSigner": false}, {"name": "stateTransitionAccount", "isMut": true, "isSigner": false}, {"name": "stateTransitionUnderlying", "isMut": false, "isSigner": false}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}], "args": [{"name": "args", "type": {"defined": "ConfigureMultiDepositOptimizerArgs"}}]}, {"name": "configure<PERSON><PERSON><PERSON>ault", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": true, "isSigner": false}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "userFarm", "isMut": true, "isSigner": false}, {"name": "globalFarm", "isMut": false, "isSigner": false}, {"name": "globalRewardTokenVault", "isMut": false, "isSigner": false}, {"name": "globalBaseTokenVault", "isMut": false, "isSigner": false}, {"name": "poolSwapAccount", "isMut": false, "isSigner": false}, {"name": "vaultFarmTokenAccount", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Account", "isMut": false, "isSigner": false}, {"name": "vaultSwapTokenAccountA", "isMut": false, "isSigner": false}, {"name": "vaultSwapTokenAccountB", "isMut": false, "isSigner": false}, {"name": "poolSwapTokenA", "isMut": false, "isSigner": false}, {"name": "poolSwapTokenB", "isMut": false, "isSigner": false}, {"name": "farmTokenMint", "isMut": true, "isSigner": false}, {"name": "swapTokenMint", "isMut": true, "isSigner": false}, {"name": "rewardTokenMint", "isMut": true, "isSigner": false}, {"name": "tokenMintA", "isMut": true, "isSigner": false}, {"name": "tokenMintB", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "aquaFarmProgram", "isMut": false, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}], "args": [{"name": "args", "type": {"defined": "Configure<PERSON>rgs"}}]}, {"name": "configureOrcaVaultDd", "accounts": [{"name": "ddCompound<PERSON><PERSON>ue", "isMut": true, "isSigner": false}, {"name": "ddWithdrawQ<PERSON>ue", "isMut": true, "isSigner": false}, {"name": "ddCompoundQueueMint", "isMut": true, "isSigner": false}, {"name": "configData", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": true, "isSigner": false}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "userFarm", "isMut": true, "isSigner": false}, {"name": "globalFarm", "isMut": false, "isSigner": false}, {"name": "globalRewardTokenVault", "isMut": false, "isSigner": false}, {"name": "globalBaseTokenVault", "isMut": false, "isSigner": false}, {"name": "poolSwapAccount", "isMut": false, "isSigner": false}, {"name": "vaultFarmTokenAccount", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Account", "isMut": false, "isSigner": false}, {"name": "vaultSwapTokenAccountA", "isMut": false, "isSigner": false}, {"name": "vaultSwapTokenAccountB", "isMut": false, "isSigner": false}, {"name": "poolSwapTokenA", "isMut": false, "isSigner": false}, {"name": "poolSwapTokenB", "isMut": false, "isSigner": false}, {"name": "farmTokenMint", "isMut": true, "isSigner": false}, {"name": "swapTokenMint", "isMut": true, "isSigner": false}, {"name": "rewardTokenMint", "isMut": true, "isSigner": false}, {"name": "tokenMintA", "isMut": true, "isSigner": false}, {"name": "tokenMintB", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "aquaFarmProgram", "isMut": false, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}]}, {"name": "rent", "isMut": false, "isSigner": false}], "args": [{"name": "args", "type": {"defined": "Configure<PERSON>rgs"}}]}, {"name": "lendingOptimizerAddPlatform", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": false, "isSigner": false}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "clock", "isMut": false, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "platformInformation", "isMut": true, "isSigner": false}, {"name": "platformConfigData", "isMut": true, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}], "args": [{"name": "args", "type": {"defined": "AddLendingPlatformArgs"}}]}, {"name": "sweepRaydiumVault", "accounts": [{"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": true, "isSigner": false}, {"name": "vaultStakeInfoAccount", "isMut": true, "isSigner": false}, {"name": "poolId", "isMut": true, "isSigner": false}, {"name": "poolAuthority", "isMut": true, "isSigner": false}, {"name": "underlyingDepositQueue", "isMut": true, "isSigner": false}, {"name": "underlyingCompoundQueue", "isMut": true, "isSigner": false}, {"name": "poolLpTokenAccount", "isMut": true, "isSigner": false}, {"name": "vault<PERSON><PERSON><PERSON><PERSON><PERSON>Account", "isMut": true, "isSigner": false}, {"name": "poolRewardATokenAccount", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>okenAccount", "isMut": true, "isSigner": false}, {"name": "poolRewardBTokenAccount", "isMut": true, "isSigner": false}, {"name": "clock", "isMut": false, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "raydiumStakeProgram", "isMut": false, "isSigner": false}, {"name": "authority", "isMut": false, "isSigner": true}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "sharesMint", "isMut": false, "isSigner": false}], "args": [{"name": "harvest", "type": "bool"}]}, {"name": "sweepLendingOptimizerVault", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": false, "isSigner": false}, {"name": "platformInformation", "isMut": false, "isSigner": false}, {"name": "platformConfigData", "isMut": false, "isSigner": false}, {"name": "lendingProgram", "isMut": false, "isSigner": false}, {"name": "underlyingDepositQueue", "isMut": true, "isSigner": false}, {"name": "underlyingDestination", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "clock", "isMut": false, "isSigner": false}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "sharesMint", "isMut": false, "isSigner": false}], "args": []}, {"name": "withdrawDepositTracking", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "clock", "isMut": false, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "depositTrackingAccount", "isMut": true, "isSigner": false}, {"name": "depositTrackingPda", "isMut": true, "isSigner": false}, {"name": "depositTrackingHoldAccount", "isMut": true, "isSigner": false}, {"name": "receivingSharesAccount", "isMut": true, "isSigner": false}, {"name": "sharesMint", "isMut": false, "isSigner": false}], "args": [{"name": "amount", "type": "u64"}, {"name": "farmType", "type": {"array": ["u64", 2]}}]}, {"name": "withdrawRaydiumVault", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": true, "isSigner": false}, {"name": "vaultStakeInfoAccount", "isMut": true, "isSigner": false}, {"name": "poolId", "isMut": true, "isSigner": false}, {"name": "poolAuthority", "isMut": true, "isSigner": false}, {"name": "underlyingWithdrawQueue", "isMut": true, "isSigner": false}, {"name": "poolLpTokenAccount", "isMut": true, "isSigner": false}, {"name": "vault<PERSON><PERSON><PERSON><PERSON><PERSON>Account", "isMut": true, "isSigner": false}, {"name": "poolRewardATokenAccount", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>okenAccount", "isMut": true, "isSigner": false}, {"name": "poolRewardBTokenAccount", "isMut": true, "isSigner": false}, {"name": "burningSharesTokenAccount", "isMut": true, "isSigner": false}, {"name": "receivingUnderlyingTokenAccount", "isMut": true, "isSigner": false}, {"name": "sharesMint", "isMut": true, "isSigner": false}, {"name": "clock", "isMut": false, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "raydiumStakeProgram", "isMut": false, "isSigner": false}], "args": [{"name": "amount", "type": "u64"}]}, {"name": "withdrawLendingOptimizerVault", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": false, "isSigner": false}, {"name": "platformInformation", "isMut": false, "isSigner": false}, {"name": "platformConfigData", "isMut": false, "isSigner": false}, {"name": "lendingProgram", "isMut": false, "isSigner": false}, {"name": "burningSharesTokenAccount", "isMut": true, "isSigner": false}, {"name": "receivingUnderlyingTokenAccount", "isMut": true, "isSigner": false}, {"name": "underlyingWithdrawQueue", "isMut": true, "isSigner": false}, {"name": "sharesMint", "isMut": true, "isSigner": false}, {"name": "clock", "isMut": false, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}], "args": [{"name": "amount", "type": "u64"}]}, {"name": "withdrawMultiDepositOptimizerVault", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "multiDeposit", "isMut": true, "isSigner": false}, {"name": "multiDepositPda", "isMut": false, "isSigner": false}, {"name": "withdraw<PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "withdrawVaultPda", "isMut": false, "isSigner": false}, {"name": "platformInformation", "isMut": false, "isSigner": false}, {"name": "platformConfigData", "isMut": false, "isSigner": false}, {"name": "lendingProgram", "isMut": false, "isSigner": false}, {"name": "multiBurningSharesTokenAccount", "isMut": true, "isSigner": false}, {"name": "withdrawBurningSharesTokenAccount", "isMut": true, "isSigner": false}, {"name": "receivingUnderlyingTokenAccount", "isMut": true, "isSigner": false}, {"name": "multiUnderlyingWithdrawQueue", "isMut": true, "isSigner": false}, {"name": "multiSharesMint", "isMut": true, "isSigner": false}, {"name": "withdrawSharesMint", "isMut": true, "isSigner": false}, {"name": "clock", "isMut": false, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "withdrawVaultUnderlyingDepositQueue", "isMut": false, "isSigner": false}], "args": [{"name": "amount", "type": "u64"}]}, {"name": "newSerumSwap", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "vaultAccount", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": false, "isSigner": false}, {"name": "pcMint", "isMut": false, "isSigner": false}, {"name": "coinMint", "isMut": false, "isSigner": false}, {"name": "pcW<PERSON>t", "isMut": true, "isSigner": false}, {"name": "market", "accounts": [{"name": "market", "isMut": true, "isSigner": false}, {"name": "openOrders", "isMut": true, "isSigner": false}, {"name": "requestQueue", "isMut": true, "isSigner": false}, {"name": "eventQueue", "isMut": true, "isSigner": false}, {"name": "bids", "isMut": true, "isSigner": false}, {"name": "asks", "isMut": true, "isSigner": false}, {"name": "order<PERSON>ayer<PERSON>okenAccount", "isMut": true, "isSigner": false}, {"name": "coinVault", "isMut": true, "isSigner": false}, {"name": "p<PERSON><PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "vault<PERSON><PERSON><PERSON>", "isMut": false, "isSigner": false}, {"name": "coinWallet", "isMut": true, "isSigner": false}]}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}, {"name": "dexProgram", "isMut": false, "isSigner": false}, {"name": "serum<PERSON>rade<PERSON><PERSON>unt", "isMut": false, "isSigner": false}, {"name": "serumTradePda", "isMut": false, "isSigner": false}, {"name": "serumFeeRecipient", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "sourceTokenAccount", "isMut": true, "isSigner": false}], "args": [{"name": "args", "type": {"defined": "NewSerumSwapArgs"}}]}, {"name": "newRaydiumSwap", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": true, "isSigner": false}, {"name": "swapOrLiquidityProgram", "isMut": false, "isSigner": false}, {"name": "ammId", "isMut": true, "isSigner": false}, {"name": "ammAuthority", "isMut": true, "isSigner": false}, {"name": "ammOpenOrders", "isMut": true, "isSigner": false}, {"name": "ammQuantitiesOrTargetOrders", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Account", "isMut": true, "isSigner": false}, {"name": "poolPcTokenAccount", "isMut": true, "isSigner": false}, {"name": "serumDexProgram", "isMut": false, "isSigner": false}, {"name": "serumMarket", "isMut": true, "isSigner": false}, {"name": "serumBids", "isMut": true, "isSigner": false}, {"name": "serumAsks", "isMut": true, "isSigner": false}, {"name": "serumEventQueue", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>Account", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}], "args": [{"name": "args", "type": {"defined": "NewRaydiumSwapArgs"}}]}, {"name": "<PERSON><PERSON><PERSON>", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "vault", "isMut": true, "isSigner": false}], "args": [{"name": "farmType", "type": {"array": ["u64", 2]}}]}, {"name": "closeDepositTracking", "accounts": [{"name": "authority", "isMut": true, "isSigner": true}, {"name": "vault", "isMut": false, "isSigner": false}, {"name": "depositTrackingAccount", "isMut": true, "isSigner": false}, {"name": "depositTrackingHold", "isMut": true, "isSigner": false}, {"name": "depositTrackingQueue", "isMut": true, "isSigner": false}, {"name": "authorityShares", "isMut": true, "isSigner": false}, {"name": "depositTrackingPda", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "management", "isMut": false, "isSigner": false}], "args": []}, {"name": "addRaydiumLiquidity", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "liquidityProgramId", "isMut": false, "isSigner": false}, {"name": "ammId", "isMut": true, "isSigner": false}, {"name": "ammAuthority", "isMut": true, "isSigner": false}, {"name": "ammOpenOrders", "isMut": true, "isSigner": false}, {"name": "ammQuantitiesOrTargetOrders", "isMut": true, "isSigner": false}, {"name": "lpMintAddress", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Account", "isMut": true, "isSigner": false}, {"name": "poolPcTokenAccount", "isMut": true, "isSigner": false}, {"name": "serumMarket", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>Account", "isMut": true, "isSigner": false}, {"name": "vaultUnderlyingAccount", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": true, "isSigner": false}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "tokenProgramId", "isMut": false, "isSigner": false}, {"name": "management", "isMut": false, "isSigner": false}], "args": []}, {"name": "updateRaydiumVault", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "management", "isMut": false, "isSigner": false}], "args": [{"name": "args", "type": {"defined": "ConfigureRaydiumVaultArgs"}}]}, {"name": "updateLendingOptimizerVaultPlatformSettings", "accounts": [{"name": "update", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": false, "isSigner": false}, {"name": "management", "isMut": false, "isSigner": false}]}, {"name": "platformInformation", "isMut": true, "isSigner": false}, {"name": "platformConfigData", "isMut": true, "isSigner": false}], "args": [{"name": "args", "type": {"defined": "UpdateArgs"}}]}, {"name": "rebalanceLendingOptimizerVault", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": false, "isSigner": false}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "sourcePlatformInformation", "isMut": false, "isSigner": false}, {"name": "destinationPlatformInformation", "isMut": false, "isSigner": false}, {"name": "sourceLendingProgram", "isMut": false, "isSigner": false}, {"name": "destinationLendingProgram", "isMut": false, "isSigner": false}, {"name": "underlyingDepositQueue", "isMut": true, "isSigner": false}, {"name": "underlyingDestination", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "clock", "isMut": false, "isSigner": false}, {"name": "sharesMint", "isMut": false, "isSigner": false}], "args": []}, {"name": "rebalanceMultiDepositOptimizerVault", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": false, "isSigner": false}, {"name": "stateTransition", "isMut": true, "isSigner": false}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "multiVaultSharesMint", "isMut": true, "isSigner": false}], "args": [{"name": "calldata", "type": "bytes"}]}, {"name": "forceRebalanceMultiDepositOptimizerVault", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": false, "isSigner": false}, {"name": "stateTransition", "isMut": true, "isSigner": false}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "multiVaultSharesMint", "isMut": true, "isSigner": false}], "args": [{"name": "calldata", "type": "bytes"}]}, {"name": "resetStateTransitionAccount", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": false, "isSigner": false}, {"name": "stateTransition", "isMut": true, "isSigner": false}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "multiVaultSharesMint", "isMut": true, "isSigner": false}], "args": [{"name": "resetPausable", "type": "bool"}]}, {"name": "startMultiDepositOptimizerRebalance", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": false, "isSigner": false}, {"name": "stateTransition", "isMut": true, "isSigner": false}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "multiVaultSharesMint", "isMut": true, "isSigner": false}], "args": [{"name": "args", "type": {"defined": "StartMultiDepositRebalanceArgs"}}]}, {"name": "removeVaultMultiDepositOptimizerRebalance", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": false, "isSigner": false}, {"name": "stateTransition", "isMut": true, "isSigner": false}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "multiVaultSharesMint", "isMut": true, "isSigner": false}], "args": []}, {"name": "supplyVaultMultiDepositOptimizerRebalance", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": false, "isSigner": false}, {"name": "stateTransition", "isMut": true, "isSigner": false}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "multiVaultSharesMint", "isMut": true, "isSigner": false}], "args": []}, {"name": "rebaseLendingOptimizerVault", "accounts": [{"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": false, "isSigner": false}, {"name": "platformInformation", "isMut": false, "isSigner": false}, {"name": "platformConfigData", "isMut": false, "isSigner": false}, {"name": "lendingProgram", "isMut": false, "isSigner": false}, {"name": "clock", "isMut": false, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "underlyingDepositQueue", "isMut": false, "isSigner": false}, {"name": "feeReceiver", "isMut": true, "isSigner": false}, {"name": "authority", "isMut": false, "isSigner": true}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "sharesMint", "isMut": false, "isSigner": false}], "args": []}, {"name": "forceRebaseLendingOptimizerVault", "accounts": [{"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": false, "isSigner": false}, {"name": "platformInformation", "isMut": false, "isSigner": false}, {"name": "platformConfigData", "isMut": false, "isSigner": false}, {"name": "lendingProgram", "isMut": false, "isSigner": false}, {"name": "clock", "isMut": false, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "underlyingDepositQueue", "isMut": false, "isSigner": false}, {"name": "feeReceiver", "isMut": true, "isSigner": false}, {"name": "authority", "isMut": false, "isSigner": true}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "sharesMint", "isMut": false, "isSigner": false}], "args": []}, {"name": "rebaseMultiDepositOptimizerVault", "accounts": [{"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": false, "isSigner": false}, {"name": "underlyingDepositQueue", "isMut": false, "isSigner": false}, {"name": "authority", "isMut": false, "isSigner": true}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "sharesMint", "isMut": false, "isSigner": false}], "args": []}, {"name": "forceRebaseMultiDepositOptimizerVault", "accounts": [{"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": false, "isSigner": false}, {"name": "underlyingDepositQueue", "isMut": false, "isSigner": false}, {"name": "authority", "isMut": false, "isSigner": true}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "sharesMint", "isMut": false, "isSigner": false}], "args": []}, {"name": "sweepMultiDepositOptimizer", "accounts": [{"name": "multiDepositOptimizer", "isMut": true, "isSigner": false}, {"name": "multiOptimizerPda", "isMut": false, "isSigner": false}, {"name": "standaloneVault", "isMut": true, "isSigner": false}, {"name": "standalonePda", "isMut": false, "isSigner": false}, {"name": "underlyingDepositQueue", "isMut": true, "isSigner": false}, {"name": "underlyingDestination", "isMut": true, "isSigner": false}, {"name": "standaloneSharesMint", "isMut": true, "isSigner": false}, {"name": "multiOptimizerSharesAccount", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "authority", "isMut": false, "isSigner": true}, {"name": "management", "isMut": false, "isSigner": false}], "args": []}, {"name": "changeMultiDepositOptimizerTarget", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "multiDepositOptimizer", "isMut": true, "isSigner": false}, {"name": "newTarget", "isMut": true, "isSigner": false}], "args": []}, {"name": "updateLendingOptimizerVault", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": false, "isSigner": false}, {"name": "management", "isMut": false, "isSigner": false}], "args": [{"name": "args", "type": {"defined": "UpdateArgs"}}]}, {"name": "updateMultiDepositOptimizerVault", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": false, "isSigner": false}, {"name": "management", "isMut": false, "isSigner": false}], "args": [{"name": "args", "type": {"defined": "UpdateArgs"}}]}, {"name": "orcaAddLiqIssueShares", "accounts": [{"name": "issueShares", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "depositTracking", "isMut": true, "isSigner": false}, {"name": "depositTrackingPda", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": false, "isSigner": false}, {"name": "vaultUnderlyingAccount", "isMut": true, "isSigner": false}, {"name": "sharesMint", "isMut": true, "isSigner": false}, {"name": "receivingSharesAccount", "isMut": true, "isSigner": false}, {"name": "depositingUnderlyingAccount", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}]}, {"name": "aquaFarmProgram", "isMut": false, "isSigner": false}, {"name": "addLiq", "accounts": [{"name": "fundingTokenAccountA", "isMut": true, "isSigner": false}, {"name": "fundingTokenAccountB", "isMut": true, "isSigner": false}, {"name": "poolTokenA", "isMut": true, "isSigner": false}, {"name": "poolTokenB", "isMut": true, "isSigner": false}, {"name": "swapProgram", "isMut": false, "isSigner": false}, {"name": "swapAccount", "isMut": true, "isSigner": false}, {"name": "swapAuthority", "isMut": false, "isSigner": false}, {"name": "swapPoolTokenMint", "isMut": true, "isSigner": false}]}], "args": [{"name": "args", "type": {"defined": "OrcaAddLiqIssueSharesArgs"}}]}, {"name": "sweepOrcaVault", "accounts": [{"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": true, "isSigner": false}, {"name": "underlyingDepositQueue", "isMut": true, "isSigner": false}, {"name": "underlyingCompoundQueue", "isMut": true, "isSigner": false}, {"name": "vaultFarmTokenAccount", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Account", "isMut": true, "isSigner": false}, {"name": "globalBaseTokenVault", "isMut": true, "isSigner": false}, {"name": "globalRewardTokenVault", "isMut": true, "isSigner": false}, {"name": "farmTokenMint", "isMut": true, "isSigner": false}, {"name": "globalFarm", "isMut": true, "isSigner": false}, {"name": "userFarm", "isMut": true, "isSigner": false}, {"name": "convertAuthority", "isMut": false, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "aquafarmProgram", "isMut": false, "isSigner": false}, {"name": "authority", "isMut": false, "isSigner": true}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "sharesMint", "isMut": false, "isSigner": false}], "args": [{"name": "harvest", "type": "bool"}, {"name": "doubleDip", "type": "bool"}]}, {"name": "sweepOrcaVaultDd", "accounts": [{"name": "configData", "accounts": [{"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": true, "isSigner": false}, {"name": "underlyingDepositQueue", "isMut": true, "isSigner": false}, {"name": "underlyingCompoundQueue", "isMut": true, "isSigner": false}, {"name": "vaultFarmTokenAccount", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Account", "isMut": true, "isSigner": false}, {"name": "globalBaseTokenVault", "isMut": true, "isSigner": false}, {"name": "globalRewardTokenVault", "isMut": true, "isSigner": false}, {"name": "farmTokenMint", "isMut": true, "isSigner": false}, {"name": "globalFarm", "isMut": true, "isSigner": false}, {"name": "userFarm", "isMut": true, "isSigner": false}, {"name": "convertAuthority", "isMut": false, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "aquafarmProgram", "isMut": false, "isSigner": false}, {"name": "authority", "isMut": false, "isSigner": true}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "sharesMint", "isMut": false, "isSigner": false}]}], "args": [{"name": "harvest", "type": "bool"}]}, {"name": "withdrawOrcaVault", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": true, "isSigner": false}, {"name": "burningSharesTokenAccount", "isMut": true, "isSigner": false}, {"name": "receivingUnderlyingTokenAccount", "isMut": true, "isSigner": false}, {"name": "vaultFarmTokenAccount", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Account", "isMut": true, "isSigner": false}, {"name": "vaultSwapTokenAccount", "isMut": true, "isSigner": false}, {"name": "globalRewardTokenVault", "isMut": true, "isSigner": false}, {"name": "globalBaseTokenVault", "isMut": true, "isSigner": false}, {"name": "poolTokenA", "isMut": true, "isSigner": false}, {"name": "poolTokenB", "isMut": true, "isSigner": false}, {"name": "globalFarm", "isMut": true, "isSigner": false}, {"name": "userFarm", "isMut": true, "isSigner": false}, {"name": "convertAuthority", "isMut": false, "isSigner": false}, {"name": "swapAccount", "isMut": true, "isSigner": false}, {"name": "swapAuthority", "isMut": false, "isSigner": false}, {"name": "swapPoolTokenMint", "isMut": true, "isSigner": false}, {"name": "farmTokenMint", "isMut": true, "isSigner": false}, {"name": "sharesMint", "isMut": true, "isSigner": false}, {"name": "swapPoolFee", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "swapProgram", "isMut": false, "isSigner": false}, {"name": "aquafarmProgram", "isMut": false, "isSigner": false}], "args": [{"name": "args", "type": {"defined": "WithdrawOrcaVaultArgs"}}]}, {"name": "withdrawOrcaVaultDdStageOne", "accounts": [{"name": "configData", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": true, "isSigner": false}, {"name": "burningSharesTokenAccount", "isMut": true, "isSigner": false}, {"name": "receivingUnderlyingTokenAccount", "isMut": true, "isSigner": false}, {"name": "vaultFarmTokenAccount", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Account", "isMut": true, "isSigner": false}, {"name": "vaultSwapTokenAccount", "isMut": true, "isSigner": false}, {"name": "globalRewardTokenVault", "isMut": true, "isSigner": false}, {"name": "globalBaseTokenVault", "isMut": true, "isSigner": false}, {"name": "poolTokenA", "isMut": true, "isSigner": false}, {"name": "poolTokenB", "isMut": true, "isSigner": false}, {"name": "globalFarm", "isMut": true, "isSigner": false}, {"name": "userFarm", "isMut": true, "isSigner": false}, {"name": "convertAuthority", "isMut": false, "isSigner": false}, {"name": "swapAccount", "isMut": true, "isSigner": false}, {"name": "swapAuthority", "isMut": false, "isSigner": false}, {"name": "swapPoolTokenMint", "isMut": true, "isSigner": false}, {"name": "farmTokenMint", "isMut": true, "isSigner": false}, {"name": "sharesMint", "isMut": true, "isSigner": false}, {"name": "swapPoolFee", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "swapProgram", "isMut": false, "isSigner": false}, {"name": "aquafarmProgram", "isMut": false, "isSigner": false}]}], "args": [{"name": "args", "type": {"defined": "WithdrawOrcaVaultArgs"}}]}, {"name": "withdrawOrcaVaultDdStageTwo", "accounts": [{"name": "configData", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": true, "isSigner": false}, {"name": "burningSharesTokenAccount", "isMut": true, "isSigner": false}, {"name": "receivingUnderlyingTokenAccount", "isMut": true, "isSigner": false}, {"name": "vaultFarmTokenAccount", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Account", "isMut": true, "isSigner": false}, {"name": "vaultSwapTokenAccount", "isMut": true, "isSigner": false}, {"name": "globalRewardTokenVault", "isMut": true, "isSigner": false}, {"name": "globalBaseTokenVault", "isMut": true, "isSigner": false}, {"name": "poolTokenA", "isMut": true, "isSigner": false}, {"name": "poolTokenB", "isMut": true, "isSigner": false}, {"name": "globalFarm", "isMut": true, "isSigner": false}, {"name": "userFarm", "isMut": true, "isSigner": false}, {"name": "convertAuthority", "isMut": false, "isSigner": false}, {"name": "swapAccount", "isMut": true, "isSigner": false}, {"name": "swapAuthority", "isMut": false, "isSigner": false}, {"name": "swapPoolTokenMint", "isMut": true, "isSigner": false}, {"name": "farmTokenMint", "isMut": true, "isSigner": false}, {"name": "sharesMint", "isMut": true, "isSigner": false}, {"name": "swapPoolFee", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "swapProgram", "isMut": false, "isSigner": false}, {"name": "aquafarmProgram", "isMut": false, "isSigner": false}]}, {"name": "ephemeralTrackingAccount", "isMut": true, "isSigner": false}], "args": []}, {"name": "withdrawOrcaVaultRemoveLiq", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": false, "isSigner": false}, {"name": "burningUnderlyingAccount", "isMut": true, "isSigner": false}, {"name": "removeLiq", "accounts": [{"name": "fundingTokenAccountA", "isMut": true, "isSigner": false}, {"name": "fundingTokenAccountB", "isMut": true, "isSigner": false}, {"name": "poolTokenA", "isMut": true, "isSigner": false}, {"name": "poolTokenB", "isMut": true, "isSigner": false}, {"name": "swapProgram", "isMut": false, "isSigner": false}, {"name": "swapAccount", "isMut": true, "isSigner": false}, {"name": "swapAuthority", "isMut": false, "isSigner": false}, {"name": "swapPoolTokenMint", "isMut": true, "isSigner": false}]}, {"name": "swapFeeAccount", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "ephemeralTrackingAccount", "isMut": true, "isSigner": false}, {"name": "sharesMint", "isMut": false, "isSigner": false}], "args": [{"name": "doubleDip", "type": "bool"}]}, {"name": "newOrcaSwap", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": true, "isSigner": false}, {"name": "swapAccount", "isMut": true, "isSigner": false}, {"name": "swapAuthority", "isMut": false, "isSigner": false}, {"name": "sourceTokenAccount", "isMut": true, "isSigner": false}, {"name": "destinationTokenAccount", "isMut": true, "isSigner": false}, {"name": "swapSourceTokenAccount", "isMut": true, "isSigner": false}, {"name": "swapDestinationTokenAccount", "isMut": true, "isSigner": false}, {"name": "poolTokenMint", "isMut": true, "isSigner": false}, {"name": "poolFeeAccount", "isMut": true, "isSigner": false}, {"name": "swapProgram", "isMut": false, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}], "args": [{"name": "args", "type": {"defined": "OrcaSwapArgs"}}]}, {"name": "addOrcaLiquidity", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": true, "isSigner": false}, {"name": "underlyingCompoundQueue", "isMut": true, "isSigner": false}, {"name": "addLiq", "accounts": [{"name": "fundingTokenAccountA", "isMut": true, "isSigner": false}, {"name": "fundingTokenAccountB", "isMut": true, "isSigner": false}, {"name": "poolTokenA", "isMut": true, "isSigner": false}, {"name": "poolTokenB", "isMut": true, "isSigner": false}, {"name": "swapProgram", "isMut": false, "isSigner": false}, {"name": "swapAccount", "isMut": true, "isSigner": false}, {"name": "swapAuthority", "isMut": false, "isSigner": false}, {"name": "swapPoolTokenMint", "isMut": true, "isSigner": false}]}, {"name": "tokenProgram", "isMut": false, "isSigner": false}], "args": [{"name": "doubleDip", "type": "bool"}]}, {"name": "addOrcaLiquidityAndConvertDd", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": true, "isSigner": false}, {"name": "addLiq", "accounts": [{"name": "fundingTokenAccountA", "isMut": true, "isSigner": false}, {"name": "fundingTokenAccountB", "isMut": true, "isSigner": false}, {"name": "poolTokenA", "isMut": true, "isSigner": false}, {"name": "poolTokenB", "isMut": true, "isSigner": false}, {"name": "swapProgram", "isMut": false, "isSigner": false}, {"name": "swapAccount", "isMut": true, "isSigner": false}, {"name": "swapAuthority", "isMut": false, "isSigner": false}, {"name": "swapPoolTokenMint", "isMut": true, "isSigner": false}]}, {"name": "underlyingCompoundQueue", "isMut": true, "isSigner": false}, {"name": "ddCompound<PERSON><PERSON>ue", "isMut": true, "isSigner": false}, {"name": "vaultFarmTokenAccount", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Account", "isMut": true, "isSigner": false}, {"name": "globalBaseTokenVault", "isMut": true, "isSigner": false}, {"name": "globalRewardTokenVault", "isMut": true, "isSigner": false}, {"name": "farmTokenMint", "isMut": true, "isSigner": false}, {"name": "globalFarm", "isMut": true, "isSigner": false}, {"name": "userFarm", "isMut": true, "isSigner": false}, {"name": "convertAuthority", "isMut": false, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "aquafarmProgram", "isMut": false, "isSigner": false}, {"name": "sharesMint", "isMut": false, "isSigner": false}], "args": []}, {"name": "updateOrcaDdCompoundQueueStageOne", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": false, "isSigner": false}, {"name": "ddCompound<PERSON><PERSON>ue", "isMut": true, "isSigner": false}, {"name": "farmTokenMint", "isMut": true, "isSigner": false}, {"name": "yourReceivingFarmToken", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}], "args": []}, {"name": "updateOrcaDdCompoundQueueStageTwo", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": false, "isSigner": false}, {"name": "ddCompound<PERSON><PERSON>ue", "isMut": true, "isSigner": false}, {"name": "farmTokenMint", "isMut": true, "isSigner": false}, {"name": "yourReceivingFarmToken", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}], "args": [{"name": "updateOnly", "type": "bool"}]}, {"name": "letMeSpeakToYourManager", "accounts": [{"name": "authority", "isMut": true, "isSigner": true}, {"name": "management", "isMut": true, "isSigner": false}, {"name": "serumTrade", "isMut": true, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}], "args": []}, {"name": "updateDepositCap", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "vault", "isMut": true, "isSigner": false}], "args": [{"name": "args", "type": {"defined": "UpdateDepositCapArgs"}}]}, {"name": "updateVaultFees", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "vault", "isMut": true, "isSigner": false}], "args": [{"name": "args", "type": {"defined": "UpdateVaultFeesArgs"}}]}, {"name": "createAssociatedLedgerAccount", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": true, "isSigner": false}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "userStakeInfoAccount", "isMut": true, "isSigner": false}, {"name": "raydiumStakeProgram", "isMut": false, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}, {"name": "poolId", "isMut": true, "isSigner": false}, {"name": "associatedStakeAccount", "isMut": true, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}], "args": []}, {"name": "emitVersionInfo", "accounts": [{"name": "authority", "isMut": false, "isSigner": false}], "args": []}, {"name": "manageVaultPausable", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "vault", "isMut": true, "isSigner": false}], "args": [{"name": "args", "type": {"defined": "ManageVaultPausableArgs"}}]}, {"name": "multiDepositOptimizerAddStandalone", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": false, "isSigner": false}, {"name": "stateTransitionAccount", "isMut": true, "isSigner": false}, {"name": "stateTransitionUnderlying", "isMut": false, "isSigner": false}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}], "args": [{"name": "args", "type": {"defined": "ConfigureMultiDepositOptimizerArgs"}}, {"name": "update", "type": "u8"}]}, {"name": "collectSerumTradeFees", "accounts": [{"name": "vaultFees", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": true, "isSigner": false}, {"name": "feeRecipient", "isMut": true, "isSigner": false}, {"name": "underlyingWithdrawQueue", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}]}, {"name": "serumTrade", "isMut": false, "isSigner": false}, {"name": "serumTradePda", "isMut": false, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isMut": true, "isSigner": false}], "args": [{"name": "<PERSON><PERSON><PERSON>", "type": {"array": ["u64", 2]}}]}, {"name": "override<PERSON><PERSON><PERSON><PERSON><PERSON>", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "vault", "isMut": true, "isSigner": false}], "args": [{"name": "<PERSON><PERSON><PERSON>", "type": {"array": ["u64", 2]}}, {"name": "field", "type": "string"}, {"name": "value", "type": "string"}]}, {"name": "updateOrcaSwapMarkets", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": true, "isSigner": false}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "userFarm", "isMut": true, "isSigner": false}, {"name": "globalFarm", "isMut": false, "isSigner": false}, {"name": "globalRewardTokenVault", "isMut": false, "isSigner": false}, {"name": "globalBaseTokenVault", "isMut": false, "isSigner": false}, {"name": "poolSwapAccount", "isMut": false, "isSigner": false}, {"name": "vaultFarmTokenAccount", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Account", "isMut": false, "isSigner": false}, {"name": "vaultSwapTokenAccountA", "isMut": false, "isSigner": false}, {"name": "vaultSwapTokenAccountB", "isMut": false, "isSigner": false}, {"name": "poolSwapTokenA", "isMut": false, "isSigner": false}, {"name": "poolSwapTokenB", "isMut": false, "isSigner": false}, {"name": "farmTokenMint", "isMut": true, "isSigner": false}, {"name": "swapTokenMint", "isMut": true, "isSigner": false}, {"name": "rewardTokenMint", "isMut": true, "isSigner": false}, {"name": "tokenMintA", "isMut": true, "isSigner": false}, {"name": "tokenMintB", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "aquaFarmProgram", "isMut": false, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}], "args": [{"name": "args", "type": {"defined": "Configure<PERSON>rgs"}}]}, {"name": "updateOrcaDdSwapMarkets", "accounts": [{"name": "ddCompound<PERSON><PERSON>ue", "isMut": true, "isSigner": false}, {"name": "ddWithdrawQ<PERSON>ue", "isMut": true, "isSigner": false}, {"name": "ddCompoundQueueMint", "isMut": true, "isSigner": false}, {"name": "configData", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": true, "isSigner": false}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "userFarm", "isMut": true, "isSigner": false}, {"name": "globalFarm", "isMut": false, "isSigner": false}, {"name": "globalRewardTokenVault", "isMut": false, "isSigner": false}, {"name": "globalBaseTokenVault", "isMut": false, "isSigner": false}, {"name": "poolSwapAccount", "isMut": false, "isSigner": false}, {"name": "vaultFarmTokenAccount", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Account", "isMut": false, "isSigner": false}, {"name": "vaultSwapTokenAccountA", "isMut": false, "isSigner": false}, {"name": "vaultSwapTokenAccountB", "isMut": false, "isSigner": false}, {"name": "poolSwapTokenA", "isMut": false, "isSigner": false}, {"name": "poolSwapTokenB", "isMut": false, "isSigner": false}, {"name": "farmTokenMint", "isMut": true, "isSigner": false}, {"name": "swapTokenMint", "isMut": true, "isSigner": false}, {"name": "rewardTokenMint", "isMut": true, "isSigner": false}, {"name": "tokenMintA", "isMut": true, "isSigner": false}, {"name": "tokenMintB", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "aquaFarmProgram", "isMut": false, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}]}, {"name": "rent", "isMut": false, "isSigner": false}], "args": [{"name": "args", "type": {"defined": "Configure<PERSON>rgs"}}]}, {"name": "configureQuarryVault", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Account", "isMut": false, "isSigner": false}, {"name": "rewardTokenMint", "isMut": false, "isSigner": false}, {"name": "miner", "isMut": true, "isSigner": false}, {"name": "miner<PERSON><PERSON><PERSON><PERSON>unt", "isMut": false, "isSigner": false}, {"name": "miner<PERSON><PERSON><PERSON><PERSON>", "isMut": false, "isSigner": false}, {"name": "mintWrapper", "isMut": false, "isSigner": false}, {"name": "minter", "isMut": false, "isSigner": false}, {"name": "quarry", "isMut": true, "isSigner": false}, {"name": "rewarder", "isMut": false, "isSigner": false}, {"name": "mineProgram", "isMut": false, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}], "args": [{"name": "args", "type": {"defined": "ConfigureQuarryArgs"}}]}, {"name": "sweepQuarryVault", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": true, "isSigner": false}, {"name": "underlyingDepositQueue", "isMut": true, "isSigner": false}, {"name": "underlyingCompoundQueue", "isMut": true, "isSigner": false}, {"name": "miner", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "quarry", "isMut": true, "isSigner": false}, {"name": "rewarder", "isMut": false, "isSigner": false}, {"name": "quarryMineProgram", "isMut": false, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "sharesMint", "isMut": false, "isSigner": false}], "args": [{"name": "compound", "type": "bool"}]}, {"name": "harvestQuarryVault", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": true, "isSigner": false}, {"name": "miner", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "quarry", "isMut": true, "isSigner": false}, {"name": "mintWrapper", "isMut": true, "isSigner": false}, {"name": "minter", "isMut": true, "isSigner": false}, {"name": "rewardsTokenMint", "isMut": true, "isSigner": false}, {"name": "claimFeeTokenAccount", "isMut": true, "isSigner": false}, {"name": "rewarder", "isMut": false, "isSigner": false}, {"name": "mintWrapperProgram", "isMut": false, "isSigner": false}, {"name": "quarryMineProgram", "isMut": false, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}], "args": []}, {"name": "redeemSaberIouMint", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>Account", "isMut": true, "isSigner": false}, {"name": "vaultRedemptionTokenAccount", "isMut": true, "isSigner": false}, {"name": "iouTokenMint", "isMut": true, "isSigner": false}, {"name": "redemptionMint", "isMut": true, "isSigner": false}, {"name": "redemptionVault", "isMut": true, "isSigner": false}, {"name": "redeemer", "isMut": false, "isSigner": false}, {"name": "mintProxyState", "isMut": false, "isSigner": false}, {"name": "proxyMintAuthority", "isMut": false, "isSigner": false}, {"name": "minterInfo", "isMut": true, "isSigner": false}, {"name": "feeCollectorTokenAccount", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "mintProxyProgram", "isMut": false, "isSigner": false}, {"name": "redemptionProgram", "isMut": false, "isSigner": false}], "args": []}, {"name": "saberAddLiquidity", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": true, "isSigner": false}, {"name": "vaultTokenAAccount", "isMut": true, "isSigner": false}, {"name": "vaultT<PERSON>BAccount", "isMut": true, "isSigner": false}, {"name": "vaultPoolTokenAccount", "isMut": true, "isSigner": false}, {"name": "swapAccount", "isMut": true, "isSigner": false}, {"name": "swapAuthority", "isMut": false, "isSigner": false}, {"name": "swapTokenAAccount", "isMut": true, "isSigner": false}, {"name": "swapTokenBAccount", "isMut": true, "isSigner": false}, {"name": "poolTokenMint", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "stableSwapProgram", "isMut": false, "isSigner": false}, {"name": "clock", "isMut": false, "isSigner": false}], "args": []}, {"name": "initializeQuarrySaberConfigDataAccount", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": true, "isSigner": false}, {"name": "config<PERSON><PERSON><PERSON><PERSON>unt", "isMut": true, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}], "args": [{"name": "args", "type": {"defined": "InitializeSaberConfigurationData"}}]}, {"name": "saberDecimalWrapAsset", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "config<PERSON><PERSON><PERSON><PERSON>unt", "isMut": false, "isSigner": false}, {"name": "vaultPda", "isMut": false, "isSigner": false}, {"name": "wrapper", "isMut": false, "isSigner": false}, {"name": "wrapperMint", "isMut": true, "isSigner": false}, {"name": "wrapperUnderlyingTokens", "isMut": true, "isSigner": false}, {"name": "vaultUnwrappedTokenAccount", "isMut": true, "isSigner": false}, {"name": "vaultWrappedTokenAccount", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "decimalWrapProgram", "isMut": false, "isSigner": false}], "args": [{"name": "wrappingA", "type": "u8"}]}, {"name": "initializeSunnyVaultStageOne", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": true, "isSigner": false}, {"name": "tvaultSun<PERSON><PERSON>aultAccount", "isMut": true, "isSigner": false}, {"name": "miner", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "quarry", "isMut": true, "isSigner": false}, {"name": "sunnyMiner", "isMut": true, "isSigner": false}, {"name": "sunnyM<PERSON>TokenAccount", "isMut": false, "isSigner": false}, {"name": "sunnyPool", "isMut": false, "isSigner": false}, {"name": "sunnyQuarry", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isMut": false, "isSigner": false}, {"name": "sunnyRewardTokenMint", "isMut": false, "isSigner": false}, {"name": "tvaultSunnyRewardTokenAccount", "isMut": false, "isSigner": false}, {"name": "sunnyInternalTokenMint", "isMut": false, "isSigner": false}, {"name": "tvaultSunnyInternalTokenAccount", "isMut": false, "isSigner": false}, {"name": "underlyingTokenMint", "isMut": false, "isSigner": false}, {"name": "quarryRewardTokenMint", "isMut": false, "isSigner": false}, {"name": "quarryMintWrapper", "isMut": false, "isSigner": false}, {"name": "quarryMinter", "isMut": false, "isSigner": false}, {"name": "quarryRewarder", "isMut": false, "isSigner": false}, {"name": "sunnyQuarryProgram", "isMut": false, "isSigner": false}, {"name": "mineProgram", "isMut": false, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}], "args": []}, {"name": "initializeSunnyVaultStageTwo", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": true, "isSigner": false}, {"name": "tvaultSun<PERSON><PERSON>aultAccount", "isMut": true, "isSigner": false}, {"name": "config<PERSON><PERSON><PERSON><PERSON>unt", "isMut": true, "isSigner": false}, {"name": "miner", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "quarry", "isMut": true, "isSigner": false}, {"name": "sunnyMiner", "isMut": false, "isSigner": false}, {"name": "sunnyM<PERSON>TokenAccount", "isMut": false, "isSigner": false}, {"name": "sunnyPool", "isMut": false, "isSigner": false}, {"name": "sunnyQuarry", "isMut": false, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isMut": false, "isSigner": false}, {"name": "sunnyInternalTokenMint", "isMut": false, "isSigner": false}, {"name": "tvaultSunnyInternalTokenAccount", "isMut": false, "isSigner": false}, {"name": "tvaultSunnyRewardTokenAccount", "isMut": false, "isSigner": false}, {"name": "underlyingTokenMint", "isMut": false, "isSigner": false}, {"name": "quarryRewardTokenMint", "isMut": false, "isSigner": false}, {"name": "quarryMintWrapper", "isMut": false, "isSigner": false}, {"name": "quarryMinter", "isMut": false, "isSigner": false}, {"name": "quarryRewarder", "isMut": false, "isSigner": false}, {"name": "sunnyRewardTokenMint", "isMut": false, "isSigner": false}, {"name": "sunnyQuarryProgram", "isMut": false, "isSigner": false}, {"name": "mineProgram", "isMut": false, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}], "args": [{"name": "args", "type": {"defined": "InitializeSaberConfigurationData"}}]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "accounts": [{"name": "sweepData", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": true, "isSigner": false}, {"name": "underlyingDepositQueue", "isMut": true, "isSigner": false}, {"name": "underlyingCompoundQueue", "isMut": true, "isSigner": false}, {"name": "miner", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "quarry", "isMut": true, "isSigner": false}, {"name": "rewarder", "isMut": false, "isSigner": false}, {"name": "quarryMineProgram", "isMut": false, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "sharesMint", "isMut": false, "isSigner": false}]}, {"name": "configData", "isMut": false, "isSigner": false}, {"name": "tvaultVendorTokenAccount", "isMut": true, "isSigner": false}, {"name": "sunnyQuarry", "isMut": true, "isSigner": false}, {"name": "sunnyPool", "isMut": true, "isSigner": false}, {"name": "sunny<PERSON>vault", "isMut": true, "isSigner": false}, {"name": "sunnyInternalMint", "isMut": true, "isSigner": false}, {"name": "tvaultSunnyInternalTokenAccount", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isMut": false, "isSigner": false}, {"name": "sunnyMiner", "isMut": true, "isSigner": false}, {"name": "sunnyM<PERSON>TokenAccount", "isMut": true, "isSigner": false}, {"name": "sunnyQuarryProgram", "isMut": false, "isSigner": false}, {"name": "clock", "isMut": false, "isSigner": false}], "args": [{"name": "compound", "type": "bool"}]}, {"name": "harvestSunnyVault", "accounts": [{"name": "harvestData", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": true, "isSigner": false}, {"name": "miner", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "quarry", "isMut": true, "isSigner": false}, {"name": "mintWrapper", "isMut": true, "isSigner": false}, {"name": "minter", "isMut": true, "isSigner": false}, {"name": "rewardsTokenMint", "isMut": true, "isSigner": false}, {"name": "claimFeeTokenAccount", "isMut": true, "isSigner": false}, {"name": "rewarder", "isMut": false, "isSigner": false}, {"name": "mintWrapperProgram", "isMut": false, "isSigner": false}, {"name": "quarryMineProgram", "isMut": false, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}]}, {"name": "configData", "isMut": false, "isSigner": false}, {"name": "tvaultSaberRewardTokenAccount", "isMut": true, "isSigner": false}, {"name": "tvaultSunnyRewardTokenAccount", "isMut": true, "isSigner": false}, {"name": "tvaultVendorTokenAccount", "isMut": true, "isSigner": false}, {"name": "tvaultSunnyInternalTokenAccount", "isMut": true, "isSigner": false}, {"name": "sunnyRewardTokenMint", "isMut": true, "isSigner": false}, {"name": "sunnyPool", "isMut": true, "isSigner": false}, {"name": "sunny<PERSON>vault", "isMut": true, "isSigner": false}, {"name": "sunnyQuarryProgram", "isMut": false, "isSigner": false}, {"name": "sunnyMintWrapper", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Account", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isMut": false, "isSigner": false}, {"name": "sunnyQuarry", "isMut": true, "isSigner": false}, {"name": "sunnyMiner", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "clock", "isMut": false, "isSigner": false}], "args": [{"name": "harvestQuarry", "type": "bool"}]}, {"name": "withdrawSunnyVaultRewards", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": true, "isSigner": false}, {"name": "configData", "isMut": false, "isSigner": false}, {"name": "sunnyPool", "isMut": false, "isSigner": false}, {"name": "sunny<PERSON>vault", "isMut": true, "isSigner": false}, {"name": "tvaultSaberRewardTokenAccount", "isMut": true, "isSigner": false}, {"name": "tvaultSunnyRewardTokenAccount", "isMut": true, "isSigner": false}, {"name": "vault<PERSON>aber<PERSON><PERSON>ardTokenAccount", "isMut": true, "isSigner": false}, {"name": "vaultS<PERSON>ny<PERSON><PERSON><PERSON><PERSON>okenAccount", "isMut": true, "isSigner": false}, {"name": "saberClaimFeeTokenAccount", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Account", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "sunnyQuarryProgram", "isMut": false, "isSigner": false}], "args": []}, {"name": "redeemSunnyVaultRewards", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": true, "isSigner": false}, {"name": "saberRewardTokenMint", "isMut": true, "isSigner": false}, {"name": "sunnyRewardTokenMint", "isMut": true, "isSigner": false}, {"name": "vault<PERSON>aber<PERSON><PERSON>ardTokenAccount", "isMut": true, "isSigner": false}, {"name": "vaultS<PERSON>ny<PERSON><PERSON><PERSON><PERSON>okenAccount", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Account", "isMut": true, "isSigner": false}, {"name": "vault<PERSON><PERSON><PERSON><PERSON><PERSON>Account", "isMut": true, "isSigner": false}, {"name": "saberTokenMint", "isMut": true, "isSigner": false}, {"name": "sunnyTokenMint", "isMut": true, "isSigner": false}, {"name": "saberRedemptionVault", "isMut": true, "isSigner": false}, {"name": "sunnyRedemptionVault", "isMut": true, "isSigner": false}, {"name": "<PERSON>ber<PERSON><PERSON><PERSON><PERSON>", "isMut": false, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isMut": false, "isSigner": false}, {"name": "saberMinterInfo", "isMut": true, "isSigner": false}, {"name": "sunnyMinterInfo", "isMut": true, "isSigner": false}, {"name": "sunnyMintWrapper", "isMut": false, "isSigner": false}, {"name": "saberMintProxyState", "isMut": false, "isSigner": false}, {"name": "saberProxyMintAuthority", "isMut": false, "isSigner": false}, {"name": "feeCollectorSaberTokenAccount", "isMut": true, "isSigner": false}, {"name": "feeCollectorSunnyTokenAccount", "isMut": true, "isSigner": false}, {"name": "sunnyMinterProgram", "isMut": false, "isSigner": false}, {"name": "saberMintProxyProgram", "isMut": false, "isSigner": false}, {"name": "sunnyRedemptionProgram", "isMut": false, "isSigner": false}, {"name": "saberRedemptionProgram", "isMut": false, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}], "args": []}, {"name": "withdraw<PERSON><PERSON><PERSON><PERSON><PERSON>", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": true, "isSigner": false}, {"name": "configData", "isMut": false, "isSigner": false}, {"name": "sunnyInternalMint", "isMut": true, "isSigner": false}, {"name": "sunnyTvaultVendorTokenAccount", "isMut": true, "isSigner": false}, {"name": "sunnyTvaultInternalTokenAccount", "isMut": true, "isSigner": false}, {"name": "sunnyPool", "isMut": true, "isSigner": false}, {"name": "sunny<PERSON>vault", "isMut": true, "isSigner": false}, {"name": "sunnyQuarry", "isMut": true, "isSigner": false}, {"name": "sunnyMiner", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isMut": false, "isSigner": false}, {"name": "rewarder", "isMut": false, "isSigner": false}, {"name": "quarry", "isMut": true, "isSigner": false}, {"name": "miner", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "sharesMint", "isMut": true, "isSigner": false}, {"name": "burningSharesTokenAccount", "isMut": true, "isSigner": false}, {"name": "vaultWithdraw<PERSON><PERSON>ue", "isMut": true, "isSigner": false}, {"name": "receivingUnderlyingTokenAccount", "isMut": true, "isSigner": false}, {"name": "feeDestination", "isMut": true, "isSigner": false}, {"name": "mineProgram", "isMut": false, "isSigner": false}, {"name": "sunnyQuarryProgram", "isMut": false, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "clock", "isMut": false, "isSigner": false}], "args": [{"name": "amount", "type": "u64"}]}, {"name": "withdrawQuarryVault", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": true, "isSigner": false}, {"name": "rewarder", "isMut": false, "isSigner": false}, {"name": "quarry", "isMut": true, "isSigner": false}, {"name": "miner", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "sharesMint", "isMut": true, "isSigner": false}, {"name": "burningSharesTokenAccount", "isMut": true, "isSigner": false}, {"name": "vaultWithdraw<PERSON><PERSON>ue", "isMut": true, "isSigner": false}, {"name": "receivingUnderlyingTokenAccount", "isMut": true, "isSigner": false}, {"name": "feeDestination", "isMut": true, "isSigner": false}, {"name": "mineProgram", "isMut": false, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "clock", "isMut": false, "isSigner": false}], "args": [{"name": "amount", "type": "u64"}]}, {"name": "configureAtrixVault", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": true, "isSigner": false}, {"name": "vault<PERSON><PERSON>x<PERSON><PERSON><PERSON><PERSON>unt", "isMut": true, "isSigner": false}, {"name": "vault<PERSON><PERSON><PERSON><PERSON><PERSON>vesterA<PERSON>unt", "isMut": true, "isSigner": false}, {"name": "cropAccount", "isMut": false, "isSigner": false}, {"name": "farmAccount", "isMut": false, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}, {"name": "atrixFarmProgram", "isMut": false, "isSigner": false}], "args": [{"name": "dualCrop", "type": "u8"}]}, {"name": "sweepAtrixVault", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": true, "isSigner": false}, {"name": "vaultStakerAccount", "isMut": true, "isSigner": false}, {"name": "farmAccount", "isMut": false, "isSigner": false}, {"name": "farmStakeTokenAccount", "isMut": true, "isSigner": false}, {"name": "cropAccount", "isMut": true, "isSigner": false}, {"name": "cropRewardTokenAccount", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Account", "isMut": true, "isSigner": false}, {"name": "underlyingDepositQueue", "isMut": true, "isSigner": false}, {"name": "underlyingCompoundQueue", "isMut": true, "isSigner": false}, {"name": "sharesMint", "isMut": false, "isSigner": false}, {"name": "atrixFarmProgram", "isMut": false, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "clock", "isMut": false, "isSigner": false}], "args": [{"name": "compound", "type": "u8"}]}, {"name": "withdrawAtrixVault", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": true, "isSigner": false}, {"name": "vaultStakerAccount", "isMut": true, "isSigner": false}, {"name": "farmAccount", "isMut": false, "isSigner": false}, {"name": "farmStakeTokenAccount", "isMut": true, "isSigner": false}, {"name": "cropAccount", "isMut": true, "isSigner": false}, {"name": "cropRewardTokenAccount", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Account", "isMut": true, "isSigner": false}, {"name": "underlyingWithdrawQueue", "isMut": true, "isSigner": false}, {"name": "burningSharesTokenAccount", "isMut": true, "isSigner": false}, {"name": "sharesMint", "isMut": true, "isSigner": false}, {"name": "receivingUnderlyingTokenAccount", "isMut": true, "isSigner": false}, {"name": "atrixFarmProgram", "isMut": false, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "clock", "isMut": false, "isSigner": false}], "args": [{"name": "amount", "type": "u64"}]}, {"name": "harvestAtrixVault", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": true, "isSigner": false}, {"name": "vaultStakerAccount", "isMut": true, "isSigner": false}, {"name": "farmAccount", "isMut": false, "isSigner": false}, {"name": "farmStakeTokenAccount", "isMut": true, "isSigner": false}, {"name": "cropAccount", "isMut": true, "isSigner": false}, {"name": "cropRewardTokenAccount", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Account", "isMut": true, "isSigner": false}, {"name": "underlyingCompoundQueue", "isMut": true, "isSigner": false}, {"name": "feeCollectorTokenAccount", "isMut": true, "isSigner": false}, {"name": "sharesMint", "isMut": false, "isSigner": false}, {"name": "atrixFarmProgram", "isMut": false, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "clock", "isMut": false, "isSigner": false}], "args": []}, {"name": "addAtrixLiquidity", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": true, "isSigner": false}, {"name": "protocolAccount", "isMut": true, "isSigner": false}, {"name": "poolAccount", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Account", "isMut": true, "isSigner": false}, {"name": "poolPcTokenAccount", "isMut": true, "isSigner": false}, {"name": "poolLpTokenAccount", "isMut": true, "isSigner": false}, {"name": "poolLpTokenMint", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>Account", "isMut": true, "isSigner": false}, {"name": "underlyingCompoundQueue", "isMut": true, "isSigner": false}, {"name": "serumMarket", "isMut": true, "isSigner": false}, {"name": "atrixOpenOrders", "isMut": true, "isSigner": false}, {"name": "serumRequestQueue", "isMut": true, "isSigner": false}, {"name": "serumEventQueue", "isMut": true, "isSigner": false}, {"name": "serumMarketBids", "isMut": true, "isSigner": false}, {"name": "serumMarketAsks", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isMut": false, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "atrixPoolProgram", "isMut": false, "isSigner": false}, {"name": "serumDexProgram", "isMut": false, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}], "args": [{"name": "action", "type": "u64"}]}, {"name": "initializeStrategyAccount", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "management", "isMut": true, "isSigner": false}, {"name": "strategyAccount", "isMut": true, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}], "args": [{"name": "strategyType", "type": "u64"}]}, {"name": "registerStrategyAccount", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "strategyAccount", "isMut": true, "isSigner": false}], "args": [{"name": "strategyType", "type": "u64"}, {"name": "data", "type": "bytes"}]}, {"name": "executeStrategy", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "strategyAccount", "isMut": true, "isSigner": false}], "args": [{"name": "args", "type": {"defined": "StrategyExecutionArgs"}}, {"name": "data", "type": "bytes"}]}, {"name": "registerStrategyBalanceAccount", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "strategyAccount", "isMut": true, "isSigner": false}, {"name": "strategyBalanceAccount", "isMut": true, "isSigner": false}, {"name": "strategyBalanceTokenAccount", "isMut": true, "isSigner": false}, {"name": "vault", "isMut": false, "isSigner": false}, {"name": "vaultPda", "isMut": true, "isSigner": false}, {"name": "underlyingTokenMint", "isMut": false, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "ataProgram", "isMut": false, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}], "args": [{"name": "strategyType", "type": "u64"}, {"name": "farmType", "type": {"array": ["u64", 2]}}]}, {"name": "vaultDepositStrategyAccount", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "vaultStrategyBalanceAccount", "isMut": true, "isSigner": false}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": false, "isSigner": false}, {"name": "strategyAccount", "isMut": true, "isSigner": false}, {"name": "vaultStrategyBalanceTokenAccount", "isMut": true, "isSigner": false}, {"name": "strategyTokenAccount", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}], "args": [{"name": "strategyType", "type": "u64"}, {"name": "farmType", "type": {"array": ["u64", 2]}}, {"name": "amount", "type": "u64"}]}, {"name": "vaultWithdrawStrategyAccount", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "vaultStrategyBalanceAccount", "isMut": true, "isSigner": false}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultPda", "isMut": false, "isSigner": false}, {"name": "strategyAccount", "isMut": true, "isSigner": false}, {"name": "strategyPda", "isMut": false, "isSigner": false}, {"name": "vaultStrategyBalanceTokenAccount", "isMut": true, "isSigner": false}, {"name": "strategyTokenAccount", "isMut": true, "isSigner": false}, {"name": "underlyingDepositQueue", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}], "args": [{"name": "strategyType", "type": "u64"}, {"name": "farmType", "type": {"array": ["u64", 2]}}, {"name": "amount", "type": "u64"}]}, {"name": "withdrawMultiDepositOptimizerVaultToStrategy", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "multiDeposit", "isMut": true, "isSigner": false}, {"name": "multiDepositPda", "isMut": false, "isSigner": false}, {"name": "withdraw<PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "withdrawVaultPda", "isMut": false, "isSigner": false}, {"name": "platformInformation", "isMut": false, "isSigner": false}, {"name": "platformConfigData", "isMut": false, "isSigner": false}, {"name": "lendingProgram", "isMut": false, "isSigner": false}, {"name": "withdrawBurningSharesTokenAccount", "isMut": true, "isSigner": false}, {"name": "strategyBalanceTokenAccount", "isMut": true, "isSigner": false}, {"name": "multiUnderlyingWithdrawQueue", "isMut": true, "isSigner": false}, {"name": "multiSharesMint", "isMut": true, "isSigner": false}, {"name": "withdrawSharesMint", "isMut": true, "isSigner": false}, {"name": "clock", "isMut": false, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "withdrawVaultUnderlyingDepositQueue", "isMut": false, "isSigner": false}, {"name": "management", "isMut": false, "isSigner": false}], "args": [{"name": "amount", "type": "u64"}]}, {"name": "adjustSerumTradeSlippage", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "management", "isMut": false, "isSigner": false}, {"name": "serum<PERSON>rade<PERSON><PERSON>unt", "isMut": true, "isSigner": false}], "args": [{"name": "slippage", "type": "u64"}]}], "accounts": [{"name": "SerumTradeAccount", "type": {"kind": "struct", "fields": [{"name": "serumRebateAccount", "type": "public<PERSON>ey"}, {"name": "pda", "type": "public<PERSON>ey"}, {"name": "pdaNonce", "type": "u8"}, {"name": "slippage", "type": "u64"}, {"name": "buffer", "type": {"array": ["u8", 919]}}]}}, {"name": "BorrowLend", "type": {"kind": "struct", "fields": [{"name": "accountAddress", "type": "public<PERSON>ey"}, {"name": "pdaAddress", "type": "public<PERSON>ey"}, {"name": "totalDepositedBalance", "type": "u64"}, {"name": "totalIssuedShares", "type": "u64"}, {"name": "totalDepositedBalanceInUse", "type": "u64"}, {"name": "currentIteration", "type": "u64"}, {"name": "maxIterations", "type": "u64"}, {"name": "configAccountIndex", "type": "u64"}, {"name": "amountBorrowed", "type": "u64"}, {"name": "pnlGains", "type": "u64"}, {"name": "pnlLosses", "type": "u64"}, {"name": "balanceAfterLastExecution", "type": "u64"}, {"name": "balanceBeforeLastExecution", "type": "u64"}, {"name": "addressCounter", "type": "u64"}, {"name": "pdaNonce", "type": "u8"}, {"name": "sourcePlatform", "type": "u8"}, {"name": "destinationPlatform", "type": "u8"}, {"name": "enabled", "type": "bool"}, {"name": "pendingWithdraws", "type": "bool"}, {"name": "buffer", "type": {"array": ["u8", 512]}}]}}, {"name": "StrategyBalanceAccount", "type": {"kind": "struct", "fields": [{"name": "totalDepositedBalance", "type": "u64"}, {"name": "totalIssuedShares", "type": "u64"}, {"name": "strategyAccount", "type": "public<PERSON>ey"}, {"name": "authority", "type": "public<PERSON>ey"}, {"name": "pnlGains", "type": "u64"}, {"name": "pnlLosses", "type": "u64"}, {"name": "buffer", "type": {"array": ["u8", 32]}}]}}, {"name": "AtrixVaultV1", "type": {"kind": "struct", "fields": [{"name": "base", "type": {"defined": "VaultBaseV1"}}, {"name": "atrixFarmAccount", "type": "public<PERSON>ey"}, {"name": "vaultStakerAccount", "type": "public<PERSON>ey"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "public<PERSON>ey"}, {"name": "dualCrop", "type": "u8"}, {"name": "buffer", "type": {"array": ["u8", 519]}}]}}, {"name": "LendingOptimizerV1", "type": {"kind": "struct", "fields": [{"name": "base", "type": {"defined": "VaultBaseV1"}}, {"name": "currentFarmProgram", "type": "public<PERSON>ey"}, {"name": "currentPlatformInformation", "type": "public<PERSON>ey"}, {"name": "currentPlatformCount", "type": "u64"}, {"name": "lastRebaseSlot", "type": "u64"}, {"name": "buffer", "type": {"array": ["u8", 1000]}}]}}, {"name": "LendingPlatformV1", "type": {"kind": "struct", "fields": [{"name": "vault", "type": "public<PERSON>ey"}, {"name": "programAddress", "type": "public<PERSON>ey"}, {"name": "programType", "type": {"defined": "ProgramType"}}, {"name": "config<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "public<PERSON>ey"}, {"name": "index", "type": "u64"}, {"name": "nonce", "type": "u8"}, {"name": "buffer", "type": {"array": ["u8", 256]}}]}}, {"name": "SplLendingConfig", "type": {"kind": "struct", "fields": [{"name": "collateralMint", "type": "public<PERSON>ey"}, {"name": "reserveAccount", "type": "public<PERSON>ey"}, {"name": "reserveLiquidityAccount", "type": "public<PERSON>ey"}, {"name": "lendingMarketAccount", "type": "public<PERSON>ey"}, {"name": "derivedLendingMarketAuthority", "type": "public<PERSON>ey"}, {"name": "collateralTokenAccount", "type": "public<PERSON>ey"}, {"name": "oracleKeys", "type": {"array": ["public<PERSON>ey", 3]}}, {"name": "oraclePrograms", "type": {"array": ["public<PERSON>ey", 3]}}, {"name": "informationAccount", "type": "public<PERSON>ey"}, {"name": "nonce", "type": "u8"}, {"name": "obligationAccount", "type": "public<PERSON>ey"}, {"name": "buffer", "type": {"array": ["u8", 224]}}]}}, {"name": "MangoV3Config", "type": {"kind": "struct", "fields": [{"name": "mangoGroupAccount", "type": "public<PERSON>ey"}, {"name": "mangoAccount", "type": "public<PERSON>ey"}, {"name": "mangoCache", "type": "public<PERSON>ey"}, {"name": "mangoRootBank", "type": "public<PERSON>ey"}, {"name": "mangoNodeBank", "type": "public<PERSON>ey"}, {"name": "mangoGroupTokenAccount", "type": "public<PERSON>ey"}, {"name": "informationAccount", "type": "public<PERSON>ey"}, {"name": "nonce", "type": "u8"}, {"name": "buffer", "type": {"array": ["u8", 256]}}]}}, {"name": "MultiDepositOptimizerV1", "type": {"kind": "struct", "fields": [{"name": "base", "type": {"defined": "VaultBaseV1"}}, {"name": "lastRebaseSlot", "type": "u64"}, {"name": "standaloneVaults", "type": {"array": [{"defined": "StandaloneVaultCacheV1"}, 6]}}, {"name": "targetVault", "type": "public<PERSON>ey"}, {"name": "stateTransitionAccount", "type": "public<PERSON>ey"}, {"name": "minimumRebalanceAmount", "type": "u64"}, {"name": "balanceAvailableForStrategy", "type": "u64"}, {"name": "balanceInUsedByStrategy", "type": "u64"}, {"name": "buffer", "type": {"array": ["u8", 256]}}]}}, {"name": "RebalanceStateTransitionV1", "type": {"kind": "struct", "fields": [{"name": "optimizerVault", "type": "public<PERSON>ey"}, {"name": "vaultRemovalAmountA", "type": "u64"}, {"name": "vaultSupplyAmountB", "type": "u64"}, {"name": "unusedTwo", "type": "u64"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "u64"}, {"name": "vaultAddressA", "type": "public<PERSON>ey"}, {"name": "vaultAddressB", "type": "public<PERSON>ey"}, {"name": "vaultAProgramType", "type": {"defined": "ProgramType"}}, {"name": "vaultBProgramType", "type": {"defined": "ProgramType"}}, {"name": "state", "type": {"defined": "RebalanceStates"}}, {"name": "lastCompletionTs", "type": "i64"}, {"name": "buffer", "type": {"array": ["u8", 256]}}]}}, {"name": "OrcaVaultV1", "type": {"kind": "struct", "fields": [{"name": "base", "type": {"defined": "VaultBaseV1"}}, {"name": "farmData", "type": {"defined": "OrcaVaultDataV1"}}, {"name": "buffer", "type": {"array": ["u8", 135]}}]}}, {"name": "OrcaDoubleDipVaultV1", "type": {"kind": "struct", "fields": [{"name": "base", "type": {"defined": "VaultBaseV1"}}, {"name": "farmData", "type": {"defined": "OrcaVaultDataV1"}}, {"name": "ddFarmData", "type": {"defined": "OrcaVaultDataV1"}}, {"name": "ddCompound<PERSON><PERSON>ue", "type": "public<PERSON>ey"}, {"name": "ddCompoundQueueNonce", "type": "u8"}, {"name": "ddConfigured", "type": "u8"}, {"name": "ddWithdrawQ<PERSON>ue", "type": "public<PERSON>ey"}, {"name": "ddWithdrawQueueNonce", "type": "u8"}, {"name": "buffer", "type": {"array": ["u8", 35]}}]}}, {"name": "QuarryVaultV1", "type": {"kind": "struct", "fields": [{"name": "base", "type": {"defined": "VaultBaseV1"}}, {"name": "miner", "type": "public<PERSON>ey"}, {"name": "miner<PERSON><PERSON><PERSON><PERSON>unt", "type": "public<PERSON>ey"}, {"name": "mintWrapper", "type": "public<PERSON>ey"}, {"name": "minter", "type": "public<PERSON>ey"}, {"name": "quarry", "type": "public<PERSON>ey"}, {"name": "rewarder", "type": "public<PERSON>ey"}, {"name": "rewardTokenMint", "type": "public<PERSON>ey"}, {"name": "rewardTokenAccount", "type": "public<PERSON>ey"}, {"name": "swapMarkets", "type": {"array": ["public<PERSON>ey", 3]}}, {"name": "variant", "type": {"defined": "QuarryVariant"}}, {"name": "configData", "type": "public<PERSON>ey"}, {"name": "configDataInitialized", "type": "u8"}, {"name": "extraDataAccount", "type": "public<PERSON>ey"}, {"name": "buffer", "type": {"array": ["u8", 263]}}]}}, {"name": "SaberConfigurationDataV1", "type": {"kind": "struct", "fields": [{"name": "vault", "type": "public<PERSON>ey"}, {"name": "isDecimalWrappedA", "type": "u8"}, {"name": "isDecimalWrappedB", "type": "u8"}, {"name": "wrapperMintA", "type": "public<PERSON>ey"}, {"name": "wrapperAccountA", "type": "public<PERSON>ey"}, {"name": "wrapperAccountB", "type": "public<PERSON>ey"}, {"name": "wrapperMintB", "type": "public<PERSON>ey"}, {"name": "extraDataAccount", "type": "public<PERSON>ey"}, {"name": "buffer", "type": {"array": ["u8", 256]}}]}}, {"name": "SunnyConfigurationDataV1", "type": {"kind": "struct", "fields": [{"name": "vault", "type": "public<PERSON>ey"}, {"name": "isDecimalWrappedA", "type": "u8"}, {"name": "isDecimalWrappedB", "type": "u8"}, {"name": "wrapperMintA", "type": "public<PERSON>ey"}, {"name": "wrapperAccountA", "type": "public<PERSON>ey"}, {"name": "wrapperAccountB", "type": "public<PERSON>ey"}, {"name": "wrapperMintB", "type": "public<PERSON>ey"}, {"name": "sunnyPool", "type": "public<PERSON>ey"}, {"name": "sunny<PERSON>vault", "type": "public<PERSON>ey"}, {"name": "sunnyMiner", "type": "public<PERSON>ey"}, {"name": "sunnyM<PERSON>TokenAccount", "type": "public<PERSON>ey"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "public<PERSON>ey"}, {"name": "extraDataAccount", "type": "public<PERSON>ey"}, {"name": "buffer", "type": {"array": ["u8", 128]}}]}}, {"name": "RaydiumVaultV1", "type": {"kind": "struct", "fields": [{"name": "base", "type": {"defined": "VaultBaseV1"}}, {"name": "raydiumLpMintAddress", "type": "public<PERSON>ey"}, {"name": "raydiumAmmId", "type": "public<PERSON>ey"}, {"name": "raydiumAmmAuthority", "type": "public<PERSON>ey"}, {"name": "raydiumAmmOpenOrders", "type": "public<PERSON>ey"}, {"name": "raydiumAmmQuantitiesOrTargetOrders", "type": "public<PERSON>ey"}, {"name": "raydiumStakeProgram", "type": "public<PERSON>ey"}, {"name": "raydiumLiquidityProgram", "type": "public<PERSON>ey"}, {"name": "raydiumCoinTokenAccount", "type": "public<PERSON>ey"}, {"name": "raydiumPcTokenAccount", "type": "public<PERSON>ey"}, {"name": "raydiumPoolTempTokenAccount", "type": "public<PERSON>ey"}, {"name": "raydiumPoolLpTokenAccount", "type": "public<PERSON>ey"}, {"name": "raydiumPoolWithdrawQueue", "type": "public<PERSON>ey"}, {"name": "raydiumPoolId", "type": "public<PERSON>ey"}, {"name": "raydiumPoolAuthority", "type": "public<PERSON>ey"}, {"name": "raydiumPoolRewardATokenAccount", "type": "public<PERSON>ey"}, {"name": "raydiumPoolRewardBTokenAccount", "type": "public<PERSON>ey"}, {"name": "dualRewards", "type": "u8"}, {"name": "vault<PERSON><PERSON><PERSON><PERSON><PERSON>Account", "type": "public<PERSON>ey"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>okenAccount", "type": "public<PERSON>ey"}, {"name": "vaultStakeInfoAccount", "type": "public<PERSON>ey"}, {"name": "associatedStakeInfoAddress", "type": "public<PERSON>ey"}, {"name": "coinMint", "type": "public<PERSON>ey"}, {"name": "pcMint", "type": "public<PERSON>ey"}, {"name": "serumMarket", "type": "public<PERSON>ey"}, {"name": "buffer", "type": {"array": ["u8", 407]}}]}}, {"name": "Management", "type": {"kind": "struct", "fields": [{"name": "authority", "type": "public<PERSON>ey"}, {"name": "whitelistedEntries", "type": {"array": [{"defined": "WhitelistEntry"}, 10]}}, {"name": "strategyCounterOne", "type": "u8"}, {"name": "specialDepositorSetOne", "type": {"array": ["public<PERSON>ey", 3]}}, {"name": "buffer", "type": {"array": ["u8", 698]}}]}}, {"name": "DepositTrackingV1", "type": {"kind": "struct", "fields": [{"name": "owner", "type": "public<PERSON>ey"}, {"name": "vault", "type": "public<PERSON>ey"}, {"name": "pdaNonce", "type": "u8"}, {"name": "queueNonce", "type": "u8"}, {"name": "alignment", "type": {"array": ["u8", 6]}}, {"name": "shares", "type": "u64"}, {"name": "depositedBalance", "type": "u64"}, {"name": "lastDepositTime", "type": "i64"}, {"name": "pendingWithdrawAmount", "type": "u64"}, {"name": "totalDepositedUnderlying", "type": "u64"}, {"name": "totalWithdrawnUnderlying", "type": "u64"}, {"name": "lastPendingReward", "type": "u64"}, {"name": "rewardPerSharePaid", "type": "u128"}, {"name": "extraDataAccount", "type": "public<PERSON>ey"}, {"name": "buffer", "type": {"array": ["u8", 256]}}]}}, {"name": "EphemeralTrackingV1", "type": {"kind": "struct", "fields": [{"name": "authority", "type": "public<PERSON>ey"}, {"name": "availableForWithdraw", "type": "u64"}, {"name": "liqToRemove", "type": "u64"}, {"name": "configured", "type": "u8"}, {"name": "canWithdraw", "type": "u8"}, {"name": "metadataOne", "type": {"array": ["u8", 64]}}, {"name": "metadataTwo", "type": {"array": ["u8", 64]}}, {"name": "buffer", "type": {"array": ["u8", 254]}}]}}], "types": [{"name": "InitSerumTradeAccountArgs", "type": {"kind": "struct", "fields": [{"name": "serumRebateAccount", "type": "public<PERSON>ey"}, {"name": "pda", "type": "public<PERSON>ey"}, {"name": "pdaNonce", "type": "u8"}, {"name": "slippage", "type": "u64"}]}}, {"name": "ConfigureAtrixVaultArgs", "type": {"kind": "struct", "fields": [{"name": "atrixFarmAccount", "type": "public<PERSON>ey"}, {"name": "vaultStakerAccount", "type": "public<PERSON>ey"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "public<PERSON>ey"}, {"name": "dualCrop", "type": "u8"}]}}, {"name": "StandaloneVaultCacheV1", "type": {"kind": "struct", "fields": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "public<PERSON>ey"}, {"name": "depositedBalance", "type": "u64"}, {"name": "programType", "type": {"defined": "ProgramType"}}, {"name": "programAddress", "type": "public<PERSON>ey"}, {"name": "sharesMint", "type": "public<PERSON>ey"}, {"name": "sharesAccount", "type": "public<PERSON>ey"}, {"name": "alignment", "type": {"array": ["u8", 7]}}, {"name": "buffer", "type": {"array": ["u64", 6]}}]}}, {"name": "OrcaVaultDataV1", "type": {"kind": "struct", "fields": [{"name": "userFarmAddr", "type": "public<PERSON>ey"}, {"name": "userFarmNonce", "type": "u8"}, {"name": "vaultSwapTokenA", "type": "public<PERSON>ey"}, {"name": "vaultSwapTokenB", "type": "public<PERSON>ey"}, {"name": "poolSwapTokenA", "type": "public<PERSON>ey"}, {"name": "poolSwapTokenB", "type": "public<PERSON>ey"}, {"name": "poolSwapAccount", "type": "public<PERSON>ey"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Account", "type": "public<PERSON>ey"}, {"name": "vaultFarmTokenAccount", "type": "public<PERSON>ey"}, {"name": "vaultSwapTokenAccount", "type": "public<PERSON>ey"}, {"name": "globalBaseTokenVault", "type": "public<PERSON>ey"}, {"name": "globalRewardTokenVault", "type": "public<PERSON>ey"}, {"name": "globalFarm", "type": "public<PERSON>ey"}, {"name": "farmTokenMint", "type": "public<PERSON>ey"}, {"name": "rewardTokenMint", "type": "public<PERSON>ey"}, {"name": "swapPoolMint", "type": "public<PERSON>ey"}, {"name": "tokenAMint", "type": "public<PERSON>ey"}, {"name": "tokenBMint", "type": "public<PERSON>ey"}, {"name": "swapMarkets", "type": {"array": ["public<PERSON>ey", 3]}}, {"name": "buffer", "type": {"array": ["u8", 32]}}]}}, {"name": "ConfigureOrcaVaultArgs", "type": {"kind": "struct", "fields": [{"name": "userFarmAddr", "type": "public<PERSON>ey"}, {"name": "userFarmNonce", "type": "u8"}, {"name": "vaultSwapTokenA", "type": "public<PERSON>ey"}, {"name": "vaultSwapTokenB", "type": "public<PERSON>ey"}, {"name": "poolSwapTokenA", "type": "public<PERSON>ey"}, {"name": "poolSwapTokenB", "type": "public<PERSON>ey"}, {"name": "poolSwapAccount", "type": "public<PERSON>ey"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Account", "type": "public<PERSON>ey"}, {"name": "vaultFarmTokenAccount", "type": "public<PERSON>ey"}, {"name": "vaultSwapTokenAccount", "type": "public<PERSON>ey"}, {"name": "globalFarm", "type": "public<PERSON>ey"}, {"name": "globalBaseTokenVault", "type": "public<PERSON>ey"}, {"name": "globalRewardTokenVault", "type": "public<PERSON>ey"}, {"name": "farmTokenMint", "type": "public<PERSON>ey"}, {"name": "rewardTokenMint", "type": "public<PERSON>ey"}, {"name": "swapPoolMint", "type": "public<PERSON>ey"}, {"name": "tokenAMint", "type": "public<PERSON>ey"}, {"name": "tokenBMint", "type": "public<PERSON>ey"}, {"name": "swapMarkets", "type": {"array": ["public<PERSON>ey", 3]}}]}}, {"name": "ConfigureQuarryVaultArgs", "type": {"kind": "struct", "fields": [{"name": "miner", "type": "public<PERSON>ey"}, {"name": "miner<PERSON><PERSON><PERSON><PERSON>unt", "type": "public<PERSON>ey"}, {"name": "quarry", "type": "public<PERSON>ey"}, {"name": "rewarder", "type": "public<PERSON>ey"}, {"name": "rewardTokenMint", "type": "public<PERSON>ey"}, {"name": "mintWrapper", "type": "public<PERSON>ey"}, {"name": "minter", "type": "public<PERSON>ey"}, {"name": "variant", "type": {"defined": "QuarryVariant"}}, {"name": "requiresConfigDataAccount", "type": "bool"}, {"name": "swapMarkets", "type": {"array": ["public<PERSON>ey", 3]}}]}}, {"name": "ConfigureSunny<PERSON>ault<PERSON><PERSON>s", "type": {"kind": "struct", "fields": [{"name": "sunny<PERSON>vault", "type": "public<PERSON>ey"}, {"name": "sunnyPool", "type": "public<PERSON>ey"}, {"name": "sunnyRewardTokenMint", "type": "public<PERSON>ey"}, {"name": "tvaultSunnyRewardTokenAccount", "type": "public<PERSON>ey"}, {"name": "sunnyInternalTokenMint", "type": "public<PERSON>ey"}, {"name": "tvaultSunnyInternalTokenAccount", "type": "public<PERSON>ey"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "public<PERSON>ey"}, {"name": "sunnyQuarry", "type": "public<PERSON>ey"}, {"name": "sunnyMiner", "type": "public<PERSON>ey"}, {"name": "sunnyM<PERSON>TokenAccount", "type": "public<PERSON>ey"}, {"name": "isDecimalWrappedA", "type": "u8"}, {"name": "isDecimalWrappedB", "type": "u8"}, {"name": "wrapperMintA", "type": "public<PERSON>ey"}, {"name": "wrapperAccountA", "type": "public<PERSON>ey"}, {"name": "wrapperAccountB", "type": "public<PERSON>ey"}, {"name": "wrapperMintB", "type": "public<PERSON>ey"}]}}, {"name": "InitializeSaberConfigurationData", "type": {"kind": "struct", "fields": [{"name": "isDecimalWrappedA", "type": "u8"}, {"name": "isDecimalWrappedB", "type": "u8"}, {"name": "wrapperMintA", "type": "public<PERSON>ey"}, {"name": "wrapperAccountA", "type": "public<PERSON>ey"}, {"name": "wrapperAccountB", "type": "public<PERSON>ey"}, {"name": "wrapperMintB", "type": "public<PERSON>ey"}]}}, {"name": "ConfigureRaydiumVaultArgs", "type": {"kind": "struct", "fields": [{"name": "raydiumPoolId", "type": "public<PERSON>ey"}, {"name": "raydiumPoolAuthority", "type": "public<PERSON>ey"}, {"name": "raydiumLpMintAddress", "type": "public<PERSON>ey"}, {"name": "raydiumAmmId", "type": "public<PERSON>ey"}, {"name": "raydiumAmmAuthority", "type": "public<PERSON>ey"}, {"name": "raydiumAmmOpenOrders", "type": "public<PERSON>ey"}, {"name": "raydiumAmmQuantitiesOrTargetOrders", "type": "public<PERSON>ey"}, {"name": "raydiumLiquidityProgram", "type": "public<PERSON>ey"}, {"name": "raydiumStakeProgram", "type": "public<PERSON>ey"}, {"name": "raydiumCoinTokenAccount", "type": "public<PERSON>ey"}, {"name": "raydiumPcTokenAccount", "type": "public<PERSON>ey"}, {"name": "raydiumPoolTempTokenAccount", "type": "public<PERSON>ey"}, {"name": "raydiumPoolWithdrawQueue", "type": "public<PERSON>ey"}, {"name": "raydiumPoolRewardATokenAccount", "type": "public<PERSON>ey"}, {"name": "raydiumPoolRewardBTokenAccount", "type": "public<PERSON>ey"}, {"name": "raydiumPoolLpTokenAccount", "type": "public<PERSON>ey"}, {"name": "vaultStakeInfoAccount", "type": "public<PERSON>ey"}, {"name": "dualRewards", "type": "bool"}, {"name": "vault<PERSON><PERSON><PERSON><PERSON><PERSON>Account", "type": "public<PERSON>ey"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>okenAccount", "type": "public<PERSON>ey"}, {"name": "coinTokenMint", "type": "public<PERSON>ey"}, {"name": "pcTokenMint", "type": "public<PERSON>ey"}, {"name": "serumMarket", "type": "public<PERSON>ey"}]}}, {"name": "WhitelistEntry", "type": {"kind": "struct", "fields": [{"name": "authority", "type": "public<PERSON>ey"}, {"name": "roles", "type": {"array": [{"defined": "Roles"}, 3]}}]}}, {"name": "FeesV1", "type": {"kind": "struct", "fields": [{"name": "feeMultiplier", "type": "u64"}, {"name": "controller<PERSON>ee", "type": "u64"}, {"name": "platformFee", "type": "u64"}, {"name": "withdrawFee", "type": "u64"}, {"name": "depositFee", "type": "u64"}, {"name": "feeWallet", "type": "public<PERSON>ey"}, {"name": "totalCollectedA", "type": "u64"}, {"name": "totalCollectedB", "type": "u64"}, {"name": "buffer", "type": {"array": ["u64", 6]}}]}}, {"name": "RealizedYield", "type": {"kind": "struct", "fields": [{"name": "gainPerSecond", "type": "u128"}, {"name": "apr", "type": "u128"}, {"name": "buffer", "type": {"array": ["u64", 4]}}]}}, {"name": "VaultBaseV1", "type": {"kind": "struct", "fields": [{"name": "nonce", "type": "u8"}, {"name": "tag", "type": {"array": ["u8", 32]}}, {"name": "pda", "type": "public<PERSON>ey"}, {"name": "pdaNonce", "type": "u8"}, {"name": "pdaAlignment", "type": {"array": ["u8", 6]}}, {"name": "totalDepositedBalance", "type": "u64"}, {"name": "totalShares", "type": "u64"}, {"name": "underlyingMint", "type": "public<PERSON>ey"}, {"name": "underlyingWithdrawQueue", "type": "public<PERSON>ey"}, {"name": "underlyingDepositQueue", "type": "public<PERSON>ey"}, {"name": "underlyingCompoundQueue", "type": "public<PERSON>ey"}, {"name": "sharesMint", "type": "public<PERSON>ey"}, {"name": "withdrawsPaused", "type": "u8"}, {"name": "depositsPaused", "type": "u8"}, {"name": "compoundPaused", "type": "u8"}, {"name": "supportsCompound", "type": "u8"}, {"name": "rebasePaused", "type": "u8"}, {"name": "rebalancePaused", "type": "u8"}, {"name": "stateAlignment", "type": {"array": ["u8", 2]}}, {"name": "precisionFactor", "type": "u64"}, {"name": "lastCompoundTime", "type": "i64"}, {"name": "compoundInterval", "type": "i64"}, {"name": "slippageTolerance", "type": "u8"}, {"name": "slipAlignment", "type": {"array": ["u8", 7]}}, {"name": "fees", "type": {"defined": "FeesV1"}}, {"name": "farm", "type": {"array": ["u64", 2]}}, {"name": "configured", "type": "u8"}, {"name": "configuredAlignment", "type": {"array": ["u8", 7]}}, {"name": "pendingFees", "type": "u64"}, {"name": "totalDepositedBalanceCap", "type": "u64"}, {"name": "realized<PERSON>ield", "type": {"defined": "RealizedYield"}}, {"name": "buffer", "type": {"array": ["u64", 4]}}]}}, {"name": "InitFeeArgsV1", "type": {"kind": "struct", "fields": [{"name": "feeMultiplier", "type": "u64"}, {"name": "controller<PERSON>ee", "type": "u64"}, {"name": "platformFee", "type": "u64"}, {"name": "withdrawFee", "type": "u64"}, {"name": "depositFee", "type": "u64"}, {"name": "feeWallet", "type": "public<PERSON>ey"}]}}, {"name": "ModifyWhiteListArgs", "type": {"kind": "struct", "fields": [{"name": "role", "type": "u8"}, {"name": "action", "type": "u8"}, {"name": "modifySpecialDepositorSet", "type": "bool"}]}}, {"name": "MultiDepositRebalanceWorkloopArgs", "type": {"kind": "struct", "fields": [{"name": "calldata", "type": "bytes"}]}}, {"name": "AddLendingPlatformArgs", "type": {"kind": "struct", "fields": [{"name": "programType", "type": "u64"}, {"name": "oracleKeys", "type": {"vec": "public<PERSON>ey"}}, {"name": "oraclePrograms", "type": {"vec": "public<PERSON>ey"}}]}}, {"name": "ConfigureMultiDepositOptimizerArgs", "type": {"kind": "struct", "fields": [{"name": "vaults", "type": {"vec": {"defined": "StandaloneVaultArgs"}}}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "public<PERSON>ey"}]}}, {"name": "StandaloneVaultArgs", "type": {"kind": "struct", "fields": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "public<PERSON>ey"}, {"name": "programType", "type": "u64"}, {"name": "programAddress", "type": "public<PERSON>ey"}, {"name": "sharesMint", "type": "public<PERSON>ey"}]}}, {"name": "StartMultiDepositRebalanceArgs", "type": {"kind": "struct", "fields": [{"name": "removeAmount", "type": "u64"}]}}, {"name": "OrcaAddLiqIssueSharesArgs", "type": {"kind": "struct", "fields": [{"name": "tokenAmountA", "type": "u64"}, {"name": "tokenAmountB", "type": "u64"}, {"name": "farmType", "type": {"array": ["u64", 2]}}]}}, {"name": "WithdrawOrcaVaultArgs", "type": {"kind": "struct", "fields": [{"name": "doubleDip", "type": "bool"}, {"name": "amount", "type": "u64"}]}}, {"name": "UpdateDepositCapArgs", "type": {"kind": "struct", "fields": [{"name": "farmType", "type": {"array": ["u64", 2]}}, {"name": "newCap", "type": "u64"}]}}, {"name": "UpdateVaultFeesArgs", "type": {"kind": "struct", "fields": [{"name": "farmType", "type": {"array": ["u64", 2]}}, {"name": "feeMultiplier", "type": "u64"}, {"name": "controller<PERSON>ee", "type": "u64"}, {"name": "platformFee", "type": "u64"}, {"name": "withdrawFee", "type": "u64"}, {"name": "depositFee", "type": "u64"}]}}, {"name": "OrcaSwapArgs", "type": {"kind": "struct", "fields": [{"name": "transferInAmount", "type": "u64"}, {"name": "transferIn", "type": "bool"}, {"name": "fullAmount", "type": "bool"}, {"name": "checkSwapSkip", "type": "bool"}, {"name": "doubleDip", "type": "bool"}, {"name": "quarry", "type": "bool"}, {"name": "bypassSafetyChecks", "type": "bool"}]}}, {"name": "NewSerumSwapArgs", "type": {"kind": "struct", "fields": [{"name": "farmType", "type": {"array": ["u64", 2]}}, {"name": "amount", "type": "u64"}, {"name": "side", "type": "u64"}, {"name": "checkSerumSwapSkip", "type": "bool"}, {"name": "fullAmount", "type": "bool"}]}}, {"name": "NewVaultArgs", "type": {"kind": "struct", "fields": [{"name": "farmType", "type": {"array": ["u64", 2]}}, {"name": "compoundInterval", "type": "i64"}, {"name": "tag", "type": {"array": ["u8", 32]}}, {"name": "feeArgs", "type": {"defined": "InitFeeArgsV1"}}]}}, {"name": "ManageVaultPausableArgs", "type": {"kind": "struct", "fields": [{"name": "pause", "type": "bool"}, {"name": "action", "type": "u8"}, {"name": "farmType", "type": {"array": ["u64", 2]}}]}}, {"name": "NewRaydiumSwapArgs", "type": {"kind": "struct", "fields": [{"name": "farmType", "type": {"array": ["u64", 2]}}, {"name": "aToB", "type": "bool"}, {"name": "checkSwapSkip", "type": "bool"}, {"name": "fullAmount", "type": "bool"}]}}, {"name": "Configure<PERSON>rgs", "type": {"kind": "struct", "fields": [{"name": "farmType", "type": {"array": ["u64", 2]}}, {"name": "swapMarkets", "type": {"vec": "public<PERSON>ey"}}]}}, {"name": "ConfigureQuarryArgs", "type": {"kind": "struct", "fields": [{"name": "common", "type": {"defined": "Configure<PERSON>rgs"}}, {"name": "variant", "type": "u64"}, {"name": "requiresConfigDataAccount", "type": "bool"}]}}, {"name": "UpdateArgs", "type": {"kind": "struct", "fields": [{"name": "field", "type": "string"}, {"name": "values", "type": {"vec": "string"}}]}}, {"name": "SharesArgs", "type": {"kind": "struct", "fields": [{"name": "farmType", "type": {"array": ["u64", 2]}}, {"name": "amount", "type": "u64"}]}}, {"name": "StrategyExecutionArgs", "type": {"kind": "struct", "fields": [{"name": "strategyType", "type": "u64"}, {"name": "finalize", "type": "u8"}]}}, {"name": "Atrix", "type": {"kind": "enum", "variants": [{"name": "SOLUSDC"}, {"name": "GMTUSDC"}, {"name": "mSOLUSDC"}, {"name": "BTCUSDC"}, {"name": "USDrUSDC"}, {"name": "PLACEHOLDER_E"}, {"name": "PLACEHOLDER_F"}, {"name": "PLACEHOLDER_G"}, {"name": "PLACEHOLDER_H"}, {"name": "PLACEHOLDER_I"}, {"name": "PLACEHOLDER_J"}, {"name": "PLACEHOLDER_K"}, {"name": "PLACEHOLDER_L"}, {"name": "PLACEHOLDER_M"}, {"name": "PLACEHOLDER_N"}, {"name": "PLACEHOLDER_O"}, {"name": "PLACEHOLDER_P"}, {"name": "PLACEHOLDER_Q"}, {"name": "PLACEHOLDER_R"}, {"name": "PLACEHOLDER_S"}, {"name": "PLACEHOLDER_T"}, {"name": "PLACEHOLDER_U"}, {"name": "PLACEHOLDER_V"}, {"name": "PLACEHOLDER_W"}, {"name": "PLACEHOLDER_X"}, {"name": "PLACEHOLDER_Y"}, {"name": "PLACEHOLDER_Z"}, {"name": "UNKNOWN"}]}}, {"name": "Lending", "type": {"kind": "enum", "variants": [{"name": "TULIP"}, {"name": "SOLEND"}, {"name": "MANGO"}, {"name": "PORT"}, {"name": "LARIX"}, {"name": "PARROT"}, {"name": "USDC"}, {"name": "MULTI_DEPOSIT"}, {"name": "RAY"}, {"name": "USDT"}, {"name": "SOL"}, {"name": "PLACEHOLDER_E"}, {"name": "PLACEHOLDER_F"}, {"name": "PLACEHOLDER_G"}, {"name": "PLACEHOLDER_H"}, {"name": "PLACEHOLDER_I"}, {"name": "PLACEHOLDER_J"}, {"name": "PLACEHOLDER_K"}, {"name": "PLACEHOLDER_L"}, {"name": "PLACEHOLDER_M"}, {"name": "PLACEHOLDER_N"}, {"name": "PLACEHOLDER_O"}, {"name": "PLACEHOLDER_P"}, {"name": "PLACEHOLDER_Q"}, {"name": "PLACEHOLDER_R"}, {"name": "PLACEHOLDER_S"}, {"name": "PLACEHOLDER_T"}, {"name": "PLACEHOLDER_U"}, {"name": "PLACEHOLDER_V"}, {"name": "PLACEHOLDER_W"}, {"name": "PLACEHOLDER_X"}, {"name": "PLACEHOLDER_Y"}, {"name": "PLACEHOLDER_Z"}, {"name": "UNKNOWN"}]}}, {"name": "Orca", "type": {"kind": "enum", "variants": [{"name": "ATLASUSDC"}, {"name": "POLISUSDC"}, {"name": "ORCASOL"}, {"name": "USDTUSDC"}, {"name": "ORCAUSDC"}, {"name": "BASISUSDC"}, {"name": "SAMOUSDC"}, {"name": "SHDWUSDC"}, {"name": "SHDWSOL"}, {"name": "stSOLUSDC"}, {"name": "wUSTUSDC"}, {"name": "CMFIUSDC"}, {"name": "stSOLwUST"}, {"name": "sRLYSOL"}, {"name": "PLACEHOLDER_D"}, {"name": "PLACEHOLDER_E"}, {"name": "PLACEHOLDER_F"}, {"name": "PLACEHOLDER_G"}, {"name": "PLACEHOLDER_H"}, {"name": "PLACEHOLDER_I"}, {"name": "PLACEHOLDER_J"}, {"name": "PLACEHOLDER_K"}, {"name": "PLACEHOLDER_L"}, {"name": "PLACEHOLDER_M"}, {"name": "PLACEHOLDER_N"}, {"name": "PLACEHOLDER_O"}, {"name": "PLACEHOLDER_P"}, {"name": "PLACEHOLDER_Q"}, {"name": "PLACEHOLDER_R"}, {"name": "PLACEHOLDER_S"}, {"name": "PLACEHOLDER_T"}, {"name": "PLACEHOLDER_U"}, {"name": "PLACEHOLDER_V"}, {"name": "PLACEHOLDER_W"}, {"name": "PLACEHOLDER_X"}, {"name": "PLACEHOLDER_Y"}, {"name": "PLACEHOLDER_Z"}, {"name": "UNKNOWN"}]}}, {"name": "Quarry", "type": {"kind": "enum", "variants": [{"name": "VANILLA"}, {"name": "SABER"}, {"name": "SUNNY"}, {"name": "UNKNOWN"}]}}, {"name": "Raydium", "type": {"kind": "enum", "variants": [{"name": "ALEPHUSDC"}, {"name": "BOPRAY"}, {"name": "COPEUSDC"}, {"name": "LIKEUSDC"}, {"name": "PSYUSDC"}, {"name": "MERUSDC"}, {"name": "stSOLUSDC"}, {"name": "RAY"}, {"name": "RAYUSDT"}, {"name": "RAYUSDC"}, {"name": "RAYSRM"}, {"name": "RAYSOL"}, {"name": "RAYETH"}, {"name": "ROPEUSDC"}, {"name": "SAMORAY"}, {"name": "SNYUSDC"}, {"name": "stSOLUSDT"}, {"name": "TULIPUSDC"}, {"name": "ATLASRAY"}, {"name": "POLISRAY"}, {"name": "RAYwhETH"}, {"name": "PLACEHOLDER_B"}, {"name": "PLACEHOLDER_C"}, {"name": "PLACEHOLDER_D"}, {"name": "PLACEHOLDER_E"}, {"name": "PLACEHOLDER_F"}, {"name": "PLACEHOLDER_G"}, {"name": "PLACEHOLDER_H"}, {"name": "PLACEHOLDER_I"}, {"name": "PLACEHOLDER_J"}, {"name": "PLACEHOLDER_K"}, {"name": "PLACEHOLDER_L"}, {"name": "PLACEHOLDER_M"}, {"name": "PLACEHOLDER_N"}, {"name": "PLACEHOLDER_O"}, {"name": "PLACEHOLDER_P"}, {"name": "PLACEHOLDER_Q"}, {"name": "PLACEHOLDER_R"}, {"name": "PLACEHOLDER_S"}, {"name": "PLACEHOLDER_T"}, {"name": "PLACEHOLDER_U"}, {"name": "PLACEHOLDER_V"}, {"name": "PLACEHOLDER_W"}, {"name": "PLACEHOLDER_X"}, {"name": "PLACEHOLDER_Y"}, {"name": "PLACEHOLDER_Z"}, {"name": "UNKNOWN"}]}}, {"name": "Unknown", "type": {"kind": "enum", "variants": [{"name": "Uknown"}]}}, {"name": "Farm", "type": {"kind": "enum", "variants": [{"name": "Raydium", "fields": [{"name": "name", "type": {"defined": "Raydium"}}]}, {"name": "Lending", "fields": [{"name": "name", "type": {"defined": "Lending"}}]}, {"name": "Orca", "fields": [{"name": "name", "type": {"defined": "Orca"}}]}, {"name": "Quarry", "fields": [{"name": "name", "type": {"defined": "Quarry"}}]}, {"name": "Atrix", "fields": [{"name": "name", "type": {"defined": "Atrix"}}]}, {"name": "Unknown", "fields": [{"name": "name", "type": {"defined": "Unknown"}}]}]}}, {"name": "SlippageMethod", "type": {"kind": "enum", "variants": [{"name": "Ignore"}, {"name": "Percentage", "fields": [{"name": "perc", "type": "u64"}, {"name": "coin_amount_pre", "type": "u64"}, {"name": "pc_amount_pre", "type": "u64"}]}]}}, {"name": "SettleMethod", "type": {"kind": "enum", "variants": [{"name": "Exact"}, {"name": "Multiplier", "fields": [{"name": "modif", "type": "i64"}]}]}}, {"name": "StrategyType", "type": {"kind": "enum", "variants": [{"name": "BorrowLend"}]}}, {"name": "ProgramType", "type": {"kind": "enum", "variants": [{"name": "SplUnmodified"}, {"name": "SplModifiedSolend"}, {"name": "MangoV3"}, {"name": "Unknown"}]}}, {"name": "RebalanceStates", "type": {"kind": "enum", "variants": [{"name": "Inactive"}, {"name": "Started"}, {"name": "VaultARemoved"}, {"name": "VaultABRebalanced"}]}}, {"name": "QuarryVariant", "type": {"kind": "enum", "variants": [{"name": "Vanilla"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "UNKNOWN"}]}}, {"name": "Roles", "type": {"kind": "enum", "variants": [{"name": "COMPOUNDER"}, {"name": "FEE"}, {"name": "ADMIN"}, {"name": "VAULT"}, {"name": "SWAP"}, {"name": "SWEEP"}, {"name": "UNKNOWN"}]}}, {"name": "AtrixAddLiquidityAction", "type": {"kind": "enum", "variants": [{"name": "Ix1"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "PlaceIx1"}, {"name": "PlaceIx2"}, {"name": "All"}, {"name": "UNKNOWN"}]}}, {"name": "Side", "type": {"kind": "enum", "variants": [{"name": "Bid"}, {"name": "Ask"}]}}, {"name": "ModifyWhiteListAction", "type": {"kind": "enum", "variants": [{"name": "Add"}, {"name": "Remove"}, {"name": "Revoke"}, {"name": "Unknown"}]}}], "errors": [{"code": 6000, "name": "Unauthorized", "msg": "unauthorized account"}, {"code": 6001, "name": "InsufficientWhitelistSpace", "msg": "insufficient whitelist space"}]}