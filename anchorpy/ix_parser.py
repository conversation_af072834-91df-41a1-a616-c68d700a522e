#!/usr/bin/env python3
import solana.publickey
import constants
import struct
import json
import os
from anchorpy_idl import Idl, InstructionCoder


def parse_spl_token(account_keys, accounts, data):
    tag = data[0]
    if tag == 0 or tag == 20:
        decimals = data[1]
        mint_authority = solana.publickey.PublicKey(data[2:34])
        freeze_authority = solana.publickey.PublicKey(data[35:67]) if data[34] != 0 else None

        return {
            'instruction': 'InitializeMint',
            'decimals': decimals,
            'mint_authority': mint_authority,
            'freeze_authority': freeze_authority,
            'mint': account_keys[accounts[0]],
        }

    if tag == 1 or tag == 16 or tag == 18:
        return {
            'instruction': 'InitializeAccount',
            'account': account_keys[accounts[0]],
            'mint': account_keys[accounts[1]],
            'owner': account_keys[accounts[2]] if tag == 1 else solana.publickey.PublicKey(data[1:]),
        }

    if tag == 2 or tag == 19:
        first_signer_index = 2 if tag == 2 else 1
        return {
            'instruction': 'InitializeMultisig',
            'multisig': account_keys[accounts[0]],
            'm': data[1],
            'signers': [account_keys[i] for i in accounts[first_signer_index:]]
        }

    if tag == 7 or tag == 14:
        amount = struct.unpack("<Q", data[1:9])[0]
        return {
            'instruction': 'MintTo',
            'amount': amount,
            'mint': account_keys[accounts[0]],
            'account':  account_keys[accounts[1]],
            'authority': account_keys[accounts[2]],
            'decimals': None if tag == 7 else data[9],
        }

    if tag == 8 or tag == 15:
        amount = struct.unpack("<Q", data[1:9])[0]
        return {
            'instruction': 'Burn',
            'amount': amount,
            'account':  account_keys[accounts[0]],
            'mint': account_keys[accounts[1]],
            'authority': account_keys[accounts[2]],
            'decimals': None if tag == 8 else data[9],
        }

    if tag == 3 or tag == 12:
        amount = struct.unpack("<Q", data[1:9])[0]
        mint_offset = 1 if tag != 3 else 0
        return {
            'instruction': 'Transfer',
            'amount': amount,
            'source':  account_keys[accounts[0]],
            'mint': account_keys[accounts[1]] if tag != 3 else None,
            'destination':  account_keys[accounts[mint_offset + 1]],
            'authority': account_keys[accounts[mint_offset + 2]],
            'decimals': None if tag == 3 else data[9],
        }

    if tag == 4 or tag == 13:
        amount = struct.unpack("<Q", data[1:9])[0]
        mint_offset = 1 if tag != 4 else 0
        return {
            'instruction': 'Approve',
            'amount': amount,
            'source':  account_keys[accounts[0]],
            'mint': account_keys[accounts[1]] if tag != 4 else None,
            'delegate':  account_keys[accounts[mint_offset + 1]],
            'authority': account_keys[accounts[mint_offset + 2]],
            'decimals': None if tag == 4 else data[9],
        }

    if tag == 5:
        return {
            'instruction': 'Revoke',
            'source':  account_keys[accounts[0]],
            'authority': account_keys[accounts[1]],
        }

    if tag == 6:
        authority_type = ['MintTokens', 'FreezeAccount', 'AccountOwner', 'CloseAccount'][data[1]]
        new_authority = solana.publickey.PublicKey(data[3:35]) if data[2] != 0 else None

        return {
            'instruction': 'SetAuthority',
            'authority_type': authority_type,
            'new_authority': new_authority,
            'account':  account_keys[accounts[0]],
            'authority':  account_keys[accounts[1]],
        }

    if tag == 9:
        return {
            'instruction': 'CloseAccount',
            'account':  account_keys[accounts[0]],
            'destination': account_keys[accounts[1]],
            'authority': account_keys[accounts[2]],
        }

    if tag == 10:
        return {
            'instruction': 'FreezeAccount',
            'account':  account_keys[accounts[0]],
            'mint': account_keys[accounts[1]],
            'authority': account_keys[accounts[2]],
        }

    if tag == 11:
        return {
            'instruction': 'ThawAccount',
            'account':  account_keys[accounts[0]],
            'mint': account_keys[accounts[1]],
            'authority': account_keys[accounts[2]],
        }

    if tag == 17:
        return {
            'instruction': 'SyncNative',
            'account': account_keys[accounts[0]],
        }

    if tag == 21:
        return {
            'instruction': 'GetAccountDataSize',
            'mint': account_keys[accounts[0]],
        }

    if tag == 23:
        amount = struct.unpack("<Q", data[1:9])[0]
        return {
            'instruction': 'AmountToUiAmount',
            'mint': account_keys[accounts[0]],
            'amount': amount,
        }

    if tag == 24:
        ui_amount = data[1:].decode()
        return {
            'instruction': 'UiAmountToAmount',
            'mint': account_keys[accounts[0]],
            'ui_amount': ui_amount,
        }


def parse_stake(account_keys, accounts, data):
    tag = struct.unpack("<I", data[0:4])[0]

    def parse_authorized(data):
        return {
            'staker': solana.publickey.PublicKey(data[:32]),
            'withdrawer': solana.publickey.PublicKey(data[32:64])
        }

    def parse_lockup(data, custodian=None):
        timestamp = struct.unpack("<q", data[:8])[0]
        epoch = struct.unpack("<Q", data[8:16])[0]
        custodian = solana.publickey.PublicKey(data[16:48]) if custodian is None and len(data) == 48 else custodian
        if custodian and str(custodian) == constants.SYSTEM_PROGRAM_ID:
            custodian = None

        return {
            'unix_timestamp': timestamp if timestamp != 0 else None,
            'epoch': epoch if epoch != 0 else None,
            'custodian': custodian,
        }

    def parse_lockup_args(data, custodian=None):
        timestamp = None
        epoch = None

        if data[0]:
            timestamp = struct.unpack("<q", data[1:9])[0]
            data = data[9:]
        else:
            data = data[1:]

        if data[0]:
            epoch = struct.unpack("<Q", data[1:9])[0]
            data = data[9:]
        else:
            data = data[1:]

        if data and data[0] and not custodian:
            custodian = solana.publickey.PublicKey(data[1:])

        return {
            'unix_timestamp': timestamp,
            'epoch': epoch,
            'custodian': custodian,
        }

    if tag == 0:
        return {
            'instruction': 'Initialize',
            'account': account_keys[accounts[0]],
            'authorized': parse_authorized(data[4:68]),
            'lockup': parse_lockup(data[68:])
        }

    if tag == 9:
        return {
            'instruction': 'Initialize',
            'account': account_keys[accounts[0]],
            'authorized': {
                'staker': account_keys[accounts[3]],
                'withdrawer': account_keys[accounts[3]],

            },
            'lockup': {
                'unix_timestamp': None,
                'epoch': None,
                'custodian': None,
            }
        }


    if tag == 1 :
        new_authority = solana.publickey.PublicKey(data[4:36])
        kind = ['Staker', 'Withdrawer'][struct.unpack('<I', data[36:40])[0]]
        return {
            'instruction': 'Authorize',
            'kind': kind,
            'new_authority': new_authority,
            'account': account_keys[accounts[0]],
            'authority': account_keys[accounts[2]],
            'custodian': account_keys[accounts[3]] if len(accounts) == 4 else None,
        }

    if tag == 10:
        kind = ['Staker', 'Withdrawer'][struct.unpack('<I', data[4:8])[0]]
        return {
            'instruction': 'Authorize',
            'kind': kind,
            'account': account_keys[accounts[0]],
            'authority': account_keys[accounts[2]],
            'new_authority': account_keys[accounts[3]],
            'custodian': account_keys[accounts[4]] if len(accounts) == 5 else None,
        }

    if tag == 2:
        return {
            'instruction': 'DelegateStake',
            'account': account_keys[accounts[0]],
            'vote': account_keys[accounts[1]],
            'authority': account_keys[accounts[5]],
        }

    if tag == 3:
        amount = struct.unpack("<Q", data[4:12])[0]
        return {
            'instruction': 'Split',
            'amount': amount,
            'source': account_keys[accounts[0]],
            'destination': account_keys[accounts[1]],
            'authority': account_keys[accounts[2]],
        }

    if tag == 4:
        amount = struct.unpack("<Q", data[4:12])[0]
        return {
            'instruction': 'Withdraw',
            'amount': amount,
            'account': account_keys[accounts[0]],
            'destination': account_keys[accounts[1]],
            'authority': account_keys[accounts[4]],
            'custodian': account_keys[accounts[5]] if len(accounts) == 6 else None,
        }

    if tag == 5:
        return {
            'instruction': 'Deactivate',
            'account': account_keys[accounts[0]],
            'authority': account_keys[accounts[2]],
        }

    if tag == 6:
        return {
            'instruction': 'SetLockup',
            'account': account_keys[accounts[0]],
            'signer': account_keys[accounts[1]],
            'lockup': parse_lockup_args(data[4:]),
        }

    if tag == 12:
        return {
            'instruction': 'SetLockup',
            'account': account_keys[accounts[0]],
            'signer': account_keys[accounts[1]],
            'lockup': parse_lockup_args(data[4:], custodian=account_keys[accounts[2]] if len(accounts) == 3 else None),
        }

    if tag == 7:
        return {
            'instruction': 'Merge',
            'destination': account_keys[accounts[0]],
            'source': account_keys[accounts[1]],
            'authority': account_keys[accounts[4]],
        }

    if tag == 8:
        return {
            'instruction': 'AuthorizeWithSeed',
        }

    if tag == 11:
        return {
            'instruction': 'AuthorizeWithSeed',
        }

    if tag == 13:
        return {
            'instruction': 'GetMinimumDelegation',
        }

    if tag == 14:
        return {
            'instruction': 'DeactivateDelinquent',
            'account': account_keys[accounts[0]],
            'vote': account_keys[accounts[2]],
        }


def parse_system(account_keys, accounts, data):
    tag = struct.unpack("<I", data[0:4])[0]

    if tag == 0:
        amount = struct.unpack("<Q", data[4:12])[0]
        space = struct.unpack("<Q", data[12:20])[0]
        owner = solana.publickey.PublicKey(data[20:])
        return {
            'instruction': 'CreateAccount',
            'amount': amount,
            'space': space,
            'owner': owner,
            'source': account_keys[accounts[0]],
            'destination': account_keys[accounts[1]],
        }

    if tag == 1:
        owner = solana.publickey.PublicKey(data[4:])
        return {
            'instruction': 'Assign',
            'owner': owner,
            'account': account_keys[accounts[0]],
        }

    if tag == 2:
        amount = struct.unpack("<Q", data[4:12])[0]
        return {
            'instruction': 'Transfer',
            'amount': amount,
            'source': account_keys[accounts[0]],
            'destination': account_keys[accounts[1]],
        }

    if tag == 3:
        return {
            'instruction': 'CreateAccountWithSeed',
        }

    if tag == 4:
        return {
            'instruction': 'AdvanceNonceAccount',
            'account': account_keys[accounts[0]],
            'authority': account_keys[accounts[2]],
        }

    if tag == 5:
        amount = struct.unpack("<Q", data[4:12])[0]
        return {
            'instruction': 'WithdrawNonceAccount',
            'amount': amount,
            'account': account_keys[accounts[0]],
            'destination': account_keys[accounts[1]],
            'authority': account_keys[accounts[4]],
        }

    if tag == 6:
        authority = solana.publickey.PublicKey(data[4:])
        return {
            'instruction': 'InitializeNonceAccount',
            'account': account_keys[accounts[0]],
            'authority': authority,
        }

    if tag == 7:
        new_authority = solana.publickey.PublicKey(data[4:])
        return {
            'instruction': 'AuthorizeNonceAccount',
            'account': account_keys[accounts[0]],
            'authority': account_keys[accounts[1]],
            'new_authority': new_authority,
        }

    if tag == 8:
        space = struct.unpack("<Q", data[4:12])[0]
        return {
            'instruction': 'Allocate',
            'space': space,
            'account': account_keys[accounts[0]],
        }

    if tag == 9:
        return {
            'instruction': 'AllocateWithSeed',
            'account': account_keys[accounts[0]],
        }

    if tag == 10:
        return {
            'instruction': 'AssignWithSeed',
            'account': account_keys[accounts[0]],
        }

    if tag == 11:
        return {
            'instruction': 'TransferWithSeed',
            'source': account_keys[accounts[0]],
            'destination': account_keys[accounts[2]],
        }

    if tag == 12:
        return {
            'instruction': 'UpgradeNonceAccount',
            'account': account_keys[accounts[0]],
        }


def parse_vote(account_keys, accounts, data):
    tag = struct.unpack("<I", data[0:4])[0]

    if tag == 3:
        amount = struct.unpack("<Q", data[4:12])[0]
        return {
            'instruction': 'Withdraw',
            'amount': amount,
            'account': account_keys[accounts[0]],
            'destination': account_keys[accounts[1]],
            'authority': account_keys[accounts[2]],
        }

    raise NotImplementedError()

def parse_spl_ata(account_keys, accounts, data):
    tag = 0 if not data else struct.unpack("<I", data[0:4])[0]

    if tag == 0 or tag == 1:
        return {
            'instruction': 'Create',
            'source': account_keys[accounts[0]],
            'account': account_keys[accounts[1]],
            'wallet': account_keys[accounts[2]],
            'mint': account_keys[accounts[3]],
            'idempotent': tag == 1
        }

    if tag == 2:
        return {
            'instruction': 'RecoverNested',
            'nested_account': account_keys[accounts[0]],
            'nested_mint': account_keys[accounts[1]],
            'wallet_ata': account_keys[accounts[2]],
            'owner_ata': account_keys[accounts[3]],
            'owner_mint': account_keys[accounts[4]],
            'wallet': account_keys[accounts[5]],
        }

    raise RuntimeError('unparsable ATA instruction')

def parse_loader(account_keys, accounts, data):
    tag = struct.unpack("<I", data[0:4])[0]

    if tag == 0:
        offset = struct.unpack("<I", data[4:8])[0]
        return {
            'instruction': 'Write',
            'account': account_keys[accounts[0]],
            'offset': offset,
        }

    if tag == 1:
        return {
            'instruction': 'Finalize',
            'account': account_keys[accounts[0]],
        }

    raise RuntimeError('unparsable loader instruction')


def parse_upgradeable_loader(account_keys, accounts, data):
    tag = struct.unpack("<I", data[0:4])[0]

    if tag == 0:
        return {
            'instruction': 'InitializeBuffer',
            'account': account_keys[accounts[0]],
            'authority': account_keys[accounts[1]],
        }

    if tag == 1:
        offset = struct.unpack("<I", data[4:8])[0]
        return {
            'instruction': 'Write',
            'account': account_keys[accounts[0]],
            'authority': account_keys[accounts[1]],
            'offset': offset,
        }

    if tag == 2:
        max_data_len = struct.unpack("<Q", data[4:12])[0]
        return {
            'instruction': 'DeployWithMaxDataLen',
            'payer': account_keys[accounts[0]],
            'programdata': account_keys[accounts[1]],
            'program': account_keys[accounts[2]],
            'buffer': account_keys[accounts[3]],
            'authority': account_keys[accounts[7]],
            'max_data_len': max_data_len,
        }

    if tag == 3:
        return {
            'instruction': 'Upgrade',
            'programdata': account_keys[accounts[0]],
            'program': account_keys[accounts[1]],
            'buffer': account_keys[accounts[2]],
            'spill': account_keys[accounts[3]],
            'authority': account_keys[accounts[6]],
        }

    if tag == 4:
        return {
            'instruction': 'SetAuthority',
            'account': account_keys[accounts[0]],
            'authority': account_keys[accounts[1]],
            'new_authority': account_keys[accounts[2]] if len(accounts) == 3 else None,
        }

    if tag == 5:
        return {
            'instruction': 'Close',
            'account': account_keys[accounts[0]],
            'destination': account_keys[accounts[1]],
            'authority': account_keys[accounts[2]],
            'program': account_keys[accounts[3]] if len(accounts) == 4 else None,
        }

    raise RuntimeError('unparsable upgradeable loader instruction')


class AnchorIxParser():
    def __init__(self, idl_path):
        with open(idl_path) as f:
            self.idl = Idl.from_json(json.load(f))
        self.coder = InstructionCoder(self.idl)

        self.ix_by_name = {}
        for ix in self.idl.instructions:
            if ix.name in self.ix_by_name:
                raise RuntimeError('instruction defined more than once in idl: ' + ix.name)
            self.ix_by_name[ix.name] = ix

    def __call__(self, account_keys, accounts, data) -> dict:
        parsed = self.coder.parse(data)

        out = dict(parsed['data'])
        if '_io' in out:
            out.pop('_io')
        out['instruction'] = parsed['name']
        ix = self.ix_by_name[parsed['name']]
        for acc, acc_idx in zip(ix.accounts, accounts):
            out[acc.name] = account_keys[acc_idx]
        return out

parse_jup = AnchorIxParser(os.path.dirname(__file__) + '/idl-jup.json')
