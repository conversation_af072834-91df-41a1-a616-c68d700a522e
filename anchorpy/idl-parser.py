#!/usr/bin/env python3
"""Adapted from https://github.com/kevinheavey/anchorpy"""
from hashlib import sha256
from typing import Dict, Any, Tuple, cast, TypeVar, Protocol, Mapping
from types import MappingProxyType

from solana import publickey
from construct import Int8ul as U8
from construct import Int32ul as U32
from borsh_construct import (
    CStruct,
    TupleStruct,
    Vec,
    Enum,
    Bool,
    I8,
    U16,
    I16,
    I32,
    F32,
    U64,
    I64,
    F64,
    U128,
    I128,
    Bytes,
    String,
    Option,
)
from construct import Sequence, Bytes
from construct import Construct, Adapter, Switch, Container, IfThenElse, Padding

def sighash(ix_name: str) -> bytes:
    """Not technically sighash, since we don't include the arguments.
    (Because Rust doesn't allow function overloading.)
    Args:
        ix_name: The instruction name.
    Returns:
        The sighash bytes.
    """
    formatted_str = f"global:{ix_name}"
    return sha256(formatted_str.encode()).digest()[:8]



class COption(Adapter):
    _discriminator_key = "discriminator"
    _value_key = "value"

    def __init__(self, subcon: Construct) -> None:
        option_struct = CStruct(
            self._discriminator_key / U8,
            self._value_key
            / IfThenElse(
                lambda this: this[self._discriminator_key] == 0,
                Padding(subcon.sizeof()),
                subcon,
            ),
        )
        super().__init__(option_struct)  # type: ignore

    def _decode(self, obj: Any, context, path) -> Any:
        discriminator = obj[self._discriminator_key]
        return None if discriminator == 0 else obj[self._value_key]

    def _encode(self, obj, context, path) -> dict:
        discriminator = 0 if obj is None else 1
        return {self._discriminator_key: discriminator, self._value_key: obj}



def parse_ix_layout(idl: dict[str,Any]) -> Dict[str, Construct]:
    ix_layout: Dict[str, Construct] = {}
    for ix in idl['instructions']:
        typedefs = idl['accounts'] + idl['types']
        field_layouts = [
            _field_layout(arg, typedefs)
            for arg in ix['args']
        ]
        ix_layout[ix['name']] = ix['name'] / CStruct(*field_layouts)
    return ix_layout

FIELD_TYPE_MAP: Mapping[str, Construct] = MappingProxyType(
    {
        "bool": Bool,
        "u8": U8,
        "i8": I8,
        "u16": U16,
        "i16": I16,
        "u32": U32,
        "i32": I32,
        "f32": F32,
        "u64": U64,
        "i64": I64,
        "f64": F64,
        "u128": U128,
        "i128": I128,
        "bytes": Bytes,
        "string": String,
        "publicKey": BorshPubkey,
    },
)


_enums_cache: dict[tuple[str, str], Enum] = {}


def _handle_enum_variants(idl_enum: str, types: Any, name: str) -> Enum:
    dict_key = (name, str(idl_enum))
    try:
        return _enums_cache[dict_key]
    except KeyError:
        result = _handle_enum_variants_no_cache(idl_enum, types, name)
        _enums_cache[dict_key] = result
        return result


def _handle_enum_variants_no_cache(idl_enum: dict[str, Any], types: dict[str, Any], name: str)) -> Enum:
    variants = []
    dclasses = {}
    for variant in idl_enum['variants']:
        variant_name = variant['name']
        if variant['fields'] is None:
            variants.append(variant_name)
        else:
            variant_fields = variant['fields']
            if isinstance(variant_fields[0], _IdlField):
                fields = []
                named_fields = cast(_IdlEnumFieldsNamed, variant_fields)
                for fld in named_fields:
                    fields.append(_field_layout(fld, types))
                cstruct = CStruct(*fields)
                datacls = _idl_enum_fields_named_to_dataclass_type(
                    named_fields,
                    variant_name,
                )
                dclasses[variant_name] = datacls
                renamed = variant_name / cstruct
            else:
                fields = []
                unnamed_fields = cast(_IdlEnumFieldsTuple, variant_fields)
                for type_ in unnamed_fields:
                    fields.append(_type_layout(type_, types))
                tuple_struct = TupleStruct(*fields)
                renamed = variant_name / tuple_struct
            variants.append(renamed)  # type: ignore
    enum_without_types = Enum(*variants, enum_name=name)
    if dclasses:
        for cname in enum_without_types.enum._sumtype_constructor_names:
            try:
                dclass = dclasses[cname]
            except KeyError:
                continue
            dclass_fields = dc_fields(dclass)
            constructr = getattr(enum_without_types.enum, cname)
            for constructor_field in constructr._sumtype_attribs:
                attrib = constructor_field[1]  # type: ignore
                fld_name = constructor_field[0]  # type: ignore
                dclass_field = [f for f in dclass_fields if f.name == fld_name][0]
                attrib.type = dclass_field.type  # type: ignore
    return enum_without_types


def _typedef_layout_without_field_name(
    typedef: _AccountDefOrTypeDef,
    types: _AccountDefsOrTypeDefs,
) -> Construct:
    typedef_type = typedef.type
    name = typedef.name
    if isinstance(typedef_type, _IdlTypeDefTyStruct):
        field_layouts = [_field_layout(field, types) for field in typedef_type.fields]
        cstruct = CStruct(*field_layouts)
        datacls = _idl_typedef_ty_struct_to_dataclass_type(typedef_type, name)
        return _DataclassStruct(cstruct, datacls=datacls)
    elif isinstance(typedef_type, _IdlTypeDefTyEnum):
        return _handle_enum_variants(typedef_type, types, name)
    unknown_type = typedef_type.kind
    raise ValueError(f"Unknown type {unknown_type}")


def _typedef_layout(
    typedef: _AccountDefOrTypeDef,
    types: list[_IdlTypeDef],
    field_name: str,
) -> Construct:
    """Map an IDL typedef to a `Construct` object.
    Args:
        typedef: The IDL typedef object.
        types: IDL type definitions.
        field_name: The name of the field.
    Raises:
        ValueError: If an unknown type is passed.
    Returns:
        `Construct` object from `borsh-construct`.
    """  # noqa: DAR402
    return field_name / _typedef_layout_without_field_name(typedef, types)


def _type_layout(type_: _IdlType, types: _AccountDefsOrTypeDefs) -> Construct:
    if isinstance(type_, str):
        return FIELD_TYPE_MAP[type_]
    field_type = cast(
        _NonLiteralIdlTypes,
        type_,
    )
    if isinstance(field_type, _IdlTypeVec):
        return Vec(_type_layout(field_type.vec, types))
    elif isinstance(field_type, _IdlTypeOption):
        return Option(_type_layout(field_type.option, types))
    elif isinstance(field_type, _IdlTypeCOption):
        return COption(_type_layout(field_type.coption, types))
    elif isinstance(field_type, _IdlTypeDefined):
        defined = field_type.defined
        if not types:
            raise ValueError("User defined types not provided")
        filtered = [t for t in types if t.name == defined]
        if len(filtered) != 1:
            raise ValueError(f"Type not found {defined}")
        return _typedef_layout_without_field_name(filtered[0], types)
    elif isinstance(field_type, _IdlTypeArray):
        array_ty = field_type.array[0]
        array_len = field_type.array[1]
        inner_layout = _type_layout(array_ty, types)
        return inner_layout[array_len]
    raise ValueError(f"Type {field_type} not implemented yet")


def _field_layout(field: _IdlField, types: _AccountDefsOrTypeDefs) -> Construct:
    """Map IDL spec to `borsh-construct` types.
    Args:
        field: field object from the IDL.
        types: IDL type definitions.
    Raises:
        ValueError: If the user-defined types are not provided.
        ValueError: If the type is not found.
        ValueError: If the type is not implemented yet.
    Returns:
        `Construct` object from `borsh-construct`.
    """  # noqa: DAR402
    field_name = field.name if field.name else ""
    return field_name / _type_layout(field.type, types)
