#!/usr/bin/env python3
import fileinput
import re
import os
import pandas as pd
import solana.rpc.api
import time

RE_SOL_ADDRESS = re.compile(r'[123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz]{32,44}')

OWNER_TO_TYPE = {
    'NativeLoader1111111111111111111111111111111': 'builtin',
    'Stake11111111111111111111111111111111111111': 'stake',
}

def input_y_n(prompt):
    answer = input(prompt + " [y/n] ")
    return answer.strip().lower() == 'y'

def main():
    sol = solana.rpc.api.Client("https://api.mainnet-beta.solana.com")
    accounts = pd.read_csv("accounts.csv")

    known_keys = set(accounts["pubkey"])

    new = {
        'pubkey': [],
        'type': [],
        'description': [],
        'own': [],
    }

    try:
        for line in fileinput.input():
            for match in RE_SOL_ADDRESS.finditer(line):
                sol_address = match.group(0)
                if sol_address in known_keys:
                    continue

                print(sol_address, line.strip())
                sol_acc = sol.get_account_info(sol_address)['result']
                typ = None
                if sol_acc['value'] is not None:
                    time.sleep(0.3)
                    continue
                    print(dict(sol_acc['value'], data="..."))
                    typ = OWNER_TO_TYPE.get(sol_acc['value']['owner'])
                    if sol_acc['value']['executable']:
                        typ = 'program'
                desc = input("description? ").strip()
                if not desc:
                    continue
                if typ is None:
                    typ = input("type? ").strip()
                own = typ not in {"builtin"} and input_y_n("own?")
                new['pubkey'].append(sol_address)
                new['type'].append(typ)
                new['description'].append(desc)
                new['own'].append(own)
    finally:
        new['own'] = pd.Series(new['own'], dtype=bool)
        accounts = pd.concat([accounts, pd.DataFrame(new)])
        os.rename("accounts.csv", ".accounts.csv.bak")
        accounts.to_csv("accounts.csv", index=False)


if __name__ == '__main__':
    main()
