#!/usr/bin/env python3
"""Fetch staking rewards for the given solana account addresses

Usage:
  fetch-staking-rewards.py [options] PUBKEY...

Options:
  -u URL,--rpc=URL          Solana RPC API endpoint [default: https://api.mainnet-beta.solana.com]
  -f FILE, --file=FILE      CSV File to write the reward records to [default: rewards.csv]

"""
from docopt import docopt

from lib import SolRpc, fetch_staking_rewards


def main():
    args = docopt(__doc__ or "")
    rpc = SolRpc(args["--rpc"])

    fetch_staking_rewards(rpc, args['PUBKEY'], args['--file'])


if __name__ == "__main__":
    main()
