#!/usr/bin/env python3
import io
import json
import re
import time

import pandas as pd
import ratelimit
import requests
from tqdm import tqdm

import solana.publickey


def slot_to_epoch(slot):
    return slot // 432000


def first_epoch_slot(epoch):
    return epoch * 432000


class SolRpc:
    def __init__(self, url, calls=3, period=1):
        self.url = url

        rater = ratelimit.limits(calls=calls, period=period)
        self._request_ratelimited = ratelimit.sleep_and_retry(rater(self._request))

    def _request(self, method, *args):
        req = {
            "jsonrpc": "2.0",
            "id": "1",
            "method": method,
            "params": list(args),
        }
        res = requests.post(self.url, json=req).json()
        if "error" in res:
            raise RuntimeError(
                "Solana RPC request failed with response "
                + repr(res)
                + ". Request: "
                + repr(req)
            )
        return res["result"]

    def __call__(self, *args, **kwargs):
        return self._request_ratelimited(*args, **kwargs)

    def rewards(self, pubkeys, epoch=None):
        opts = {"epoch": epoch} if epoch is not None else {}
        return self("getInflationReward", pubkeys, opts)

    def blockTime(self, block):
        return self("getBlockTime", block)

    def epochInfo(self):
        return self("getEpochInfo")

    def epochTime(self, epoch):
        first_block = self('getBlocksWithLimit', first_epoch_slot(epoch), 1)[0]
        return get_block_time(self, first_block)


SCHEMA = {
    "pubkey": str,
    "epoch": int,
    "timestamp": "datetime64[ns, UTC]",
    "effective_slot": 'Int64',
    "amount": int,
    "post_balance": 'Int64',
    "commission": 'Int64',
}

BLOCK_TIME_CACHE = {}


def get_block_time(rpc: SolRpc, slot):
    if slot not in BLOCK_TIME_CACHE:
        BLOCK_TIME_CACHE[slot] = rpc.blockTime(slot)

    return BLOCK_TIME_CACHE[slot]


def fetch_rewards_for_epoch(rpc, fname, rewards: pd.DataFrame, epoch, all_pubkeys):
    known_pks = set(rewards.loc[epoch].index) if epoch in set(rewards['epoch']) else set()
    pubkeys = list(set(all_pubkeys) - known_pks)
    if pubkeys:
        result = rpc.rewards(pubkeys, epoch=epoch)

        new_records = []
        for pk, reward in zip(pubkeys, result):
            new_records.append(
                {
                    "pubkey": pk,
                    "epoch": epoch,
                    "timestamp": pd.to_datetime(get_block_time(rpc, reward["effectiveSlot"]), unit='s', utc=True)
                    if reward
                    else None,
                    "effective_slot": reward["effectiveSlot"] if reward else None,
                    "amount": reward["amount"] if reward else 0,
                    "post_balance": reward["postBalance"] if reward else None,
                    "commission": reward["commission"] if reward else None,
                }
            )
        rewards = pd.concat(
            [rewards, pd.DataFrame(new_records).astype(SCHEMA).set_index(["epoch", "pubkey"], drop=False)] # type: ignore
        )
        rewards.sort_index(inplace=True)
        rewards.to_csv(fname, index=False)

    epoch_time = rewards.loc[epoch].timestamp.min()
    pks_with_timestamp = set(rewards.loc[epoch].timestamp.dropna().index)
    if pd.isna(epoch_time) and all_pubkeys:
        epoch_time = pd.to_datetime(rpc.epochTime(epoch + 1), unit='s', utc=True)
        if epoch_time is None:
            epoch_time = pd.to_datetime(0, unit='s', utc=True)
    if set(all_pubkeys) - pks_with_timestamp:
        records = pd.Series({ (epoch, pk): epoch_time for pk in all_pubkeys })
        rewards = rewards.assign(timestamp=rewards.timestamp.fillna(records))
        rewards.to_csv(fname, index=False)

    return rewards

def load_rewards(filename) -> pd.DataFrame:
    rewards: pd.DataFrame
    try:
        cols = dict(SCHEMA)
        cols.pop('timestamp')
        rewards = pd.read_csv(filename, parse_dates=['timestamp'], dtype=cols).astype(SCHEMA)  # type: ignore
    except FileNotFoundError:
        rewards = pd.read_csv(io.StringIO(""), names=list(SCHEMA), dtype=SCHEMA)  # type: ignore

    rewards.set_index(["epoch", "pubkey"], inplace=True, drop=False)
    rewards.sort_index(inplace=True)

    # populate block time cache
    for _, record in rewards[~rewards["effective_slot"].isna()].iterrows():
        BLOCK_TIME_CACHE[record["effective_slot"]] = record["timestamp"]

    return rewards

def fetch_staking_rewards(rpc, all_pubkeys, filename):
    rewards = load_rewards(filename)

    cur_epoch = rpc.epochInfo()["epoch"]
    new_records = []

    last_checkpoint = time.time()
    for epoch in tqdm(range(cur_epoch), desc="Fetching inflation rewards"):
        pubkeys = [pk for pk in all_pubkeys if (pk, epoch) not in rewards.index]
        if not pubkeys:
            continue

        result = rpc.rewards(pubkeys, epoch=epoch)

        for pk, reward in zip(pubkeys, result):
            new_records.append(
                {
                    "pubkey": pk,
                    "epoch": epoch,
                    "timestamp": pd.to_datetime(get_block_time(rpc, reward["effectiveSlot"]), unit='s', utc=True)
                    if reward
                    else None,
                    "effective_slot": reward["effectiveSlot"] if reward else None,
                    "amount": reward["amount"] if reward else 0,
                    "post_balance": reward["postBalance"] if reward else None,
                    "commission": reward["commission"] if reward else None,
                }
            )

        if time.time() - last_checkpoint > 2 or epoch == cur_epoch - 1:
            last_checkpoint = time.time()
            rewards = pd.concat(
                [rewards, pd.DataFrame(new_records).astype(SCHEMA).set_index(["pubkey", "epoch"], drop=False)]
            )
            rewards.sort_index(inplace=True)
            rewards.to_csv(filename, index=False)
            new_records = []

    rewards.to_csv(filename)
    return rewards


def read_solana_tokens():
    tokenlists = []
    for path in ["solana.tokenlist.archived.json", "solana.tokenlist.json", "solana.tokenlist.custom.json"]:
        with open(path, 'rb') as f:
            tokenlists.append(json.load(f))

    # chainId 101 is solana mainnet-beta
    tokens = {
        token['address']: token
        for tokenlist in tokenlists
        for token in tokenlist['tokens']
        if token['chainId'] == 101
    }

    return tokens


def format_amount(amt, decimals, decimal_sep='.'):
    sign, amt = ('', amt) if amt >= 0 else ('-', -amt)
    one = 10**decimals
    frac = amt % one
    frac_whole = format(int(frac), f'0{decimals}')
    frac_frac = format(frac - int(frac_whole), '.4f')[2:].rstrip('0')
    whole = amt // one
    return f"{sign}{whole}{decimal_sep}{frac_whole}{frac_frac}"



RE_SIMPLE_COMMODITY = re.compile(r"^[A-Za-z]*$")
def amount_to_string(tokens, amt, currency, raw=False):
    if amt is None:
        return ""

    if currency == "EUR" or currency == "USD":
        return f"{amt} {currency}"

    decimal_sep = ',' if raw else '.'

    if currency == "SOL":
        return f"{format_amount(amt, 9, decimal_sep)} SOL"

    if isinstance(currency, solana.publickey.PublicKey):
        currency = str(currency)

    try:
        token = tokens[currency]
    except KeyError:
        raise RuntimeError(
            f"unknown token: {currency}, please add to solana.tokenlist.custom.json"
        )

    symbol = token["symbol"] if not raw else currency
    if not RE_SIMPLE_COMMODITY.match(symbol):
        symbol = f'"{symbol}"'
    return f'{format_amount(amt, token["decimals"], decimal_sep)} {symbol}'
