#!/usr/bin/env python3
"""Contains code for parsing the IDL file."""
from hashlib import sha256
from keyword import kwlist
from dataclasses import dataclass, field, asdict, make_dataclass, fields as dc_fields
from types import MappingProxyType
from typing import List, Union, Optional, Dict, Any, Literal, Tuple, TypedDict, TypeVar, Type, Mapping, cast, Protocol
import re
import typing

from apischema import deserialize, alias
from apischema.metadata import conversion
from borsh_construct import (
    CStruct,
    TupleStruct,
    Vec,
    Enum,
    Bool,
    U8,
    I8,
    U16,
    I16,
    U32,
    I32,
    F32,
    U64,
    I64,
    F64,
    U128,
    I128,
    String,
    Option,
)
import solana.publickey  # noqa: WPS301
from construct import Adapter, Bytes, Construct, Padding, IfThenElse, Switch, Renamed, Container, Sequence

class BorshPubkeyAdapter(Adapter):
    def __init__(self) -> None:
        super().__init__(Bytes(32))  # type: ignore

    def _decode(self, obj: bytes, context, path) -> solana.publickey.PublicKey:
        return solana.publickey.PublicKey(obj)

    def _encode(self, obj: solana.publickey.PublicKey, context, path) -> bytes:
        return bytes(obj)
BorshPubkey = BorshPubkeyAdapter()

class COption(Adapter):
    _discriminator_key = "discriminator"
    _value_key = "value"

    def __init__(self, subcon: Construct) -> None:
        option_struct = CStruct(
            self._discriminator_key / U8,
            self._value_key
            / IfThenElse(
                lambda this: this[self._discriminator_key] == 0,
                Padding(subcon.sizeof()),
                subcon,
            ),
        )
        super().__init__(option_struct)  # type: ignore

    def _decode(self, obj, context, path) -> Any:
        discriminator = obj[self._discriminator_key]
        return None if discriminator == 0 else obj[self._value_key]

    def _encode(self, obj, context, path) -> dict:
        discriminator = 0 if obj is None else 1
        return {self._discriminator_key: discriminator, self._value_key: obj}


T = TypeVar("T")


class _DataclassStruct(Adapter):
    """Converts dataclasses to/from `borsh_construct.CStruct`."""

    def __init__(self, cstruct: CStruct, datacls: Type[T]) -> None:
        """Init.
        Args:
            cstruct: The underlying `CStruct`.
            datacls: The dataclass type.
        """
        super().__init__(cstruct)  # type: ignore
        self.datacls = datacls

    def _decode(self, obj: Container, context, path) -> T:
        kwargs = {}
        for key, value in obj.items():
            if key[0] != "_":
                key_to_use = f"{key}_" if key in kwlist else key
                kwargs[key_to_use] = value
        return self.datacls(**kwargs)  # type: ignore

    def _encode(self, obj: T, context, path) -> Dict[str, Any]:
        if isinstance(obj, dict):
            return obj
        return asdict(obj)


_LiteralStrings = Literal[
    "bool",
    "u8",
    "i8",
    "u16",
    "i16",
    "u32",
    "i32",
    "f32",
    "u64",
    "i64",
    "f64",
    "u128",
    "i128",
    "bytes",
    "string",
    "publicKey",
]
_NonLiteralIdlTypes = Union[
    "_IdlTypeVec",
    "_IdlTypeOption",
    "_IdlTypeCOption",
    "_IdlTypeDefined",
    "_IdlTypeArray",
]
_IdlType = Union[_NonLiteralIdlTypes, _LiteralStrings]


# We have to define these wrappers because snake and upper_camel
# don't have annotations
RE_TO_SNAKE = re.compile(r'(?<!^)(?=[A-Z])')
def underscore(s: str) -> str:
    return RE_TO_SNAKE.sub('_', s).lower()

def camelize(s: str) -> str:
    return ''.join(word.title() for word in s.split('_'))

snake_case_conversion = conversion(underscore, camelize)

@dataclass
class _IdlTypeVec:
    """IDL vector type."""

    vec: _IdlType


@dataclass
class _IdlTypeOption:
    """IDL option type."""

    option: _IdlType


@dataclass
class _IdlTypeCOption:
    """IDL coption type."""

    coption: _IdlType


@dataclass
class _IdlTypeDefined:
    """IDL type that points to a user-defined type."""

    defined: str


@dataclass
class _IdlField:
    """IDL representation of a field.

    Used in instructions and user-defined types.
    """

    name: str = field(metadata=snake_case_conversion)
    type: _IdlType


_IdlSeed = Any


@dataclass
class _IdlPda:
    seeds: List[_IdlSeed]
    program_id: Optional[_IdlSeed] = None


@dataclass
class _IdlAccount:
    """IDL account type."""

    name: str = field(metadata=snake_case_conversion)
    is_mut: bool = field(metadata=alias("isMut"))
    is_signer: bool = field(metadata=alias("isSigner"))
    pda: Optional[_IdlPda] = None


@dataclass
class _IdlAccounts:
    """Nested/recursive version of _IdlAccount."""

    name: str = field(metadata=snake_case_conversion)
    accounts: List["_IdlAccountItem"]


_IdlAccountItem = Union[_IdlAccounts, _IdlAccount]


@dataclass
class _IdlInstruction:
    """IDL representation of a program instruction."""

    name: str = field(metadata=snake_case_conversion)
    accounts: List[_IdlAccountItem]
    args: List[_IdlField]
    returns: Optional[_IdlType] = None


_IdlEnumFieldsNamed = List[_IdlField]
_IdlEnumFieldsTuple = List[_IdlType]
_IdlEnumFields = Union[_IdlEnumFieldsNamed, _IdlEnumFieldsTuple]


@dataclass
class _IdlEnumVariant:
    """IDL representation of a variant of an enum."""

    name: str
    fields: Optional[_IdlEnumFields] = None


_IdlTypeDefStruct = List[_IdlField]


@dataclass
class _IdlTypeDefTyStruct:
    """IDL representation of a struct."""

    fields: _IdlTypeDefStruct
    kind: Literal["struct"] = "struct"


@dataclass
class _IdlTypeDefTyEnum:
    """IDL representation of an enum."""

    variants: List[_IdlEnumVariant]
    kind: Literal["enum"] = "enum"


_IdlTypeDefTy = Union[_IdlTypeDefTyEnum, _IdlTypeDefTyStruct]


@dataclass
class _IdlTypeDef:
    """IDL representation of a user-defined type."""

    name: str
    type: _IdlTypeDefTy


@dataclass
class _IdlAccountDef:
    """IDL representation of an account."""

    name: str
    type: _IdlTypeDefTyStruct


_IdlStateMethod = _IdlInstruction


@dataclass
class _IdlState:
    """IDL representation of a program state method."""

    struct: _IdlTypeDef
    methods: List[_IdlStateMethod]


@dataclass
class _IdlTypeArray:
    """IDL array type."""

    array: Tuple[_IdlType, int]


@dataclass
class _IdlEventField:
    """IDL representation of an event field."""

    name: str
    type: _IdlType
    index: bool


@dataclass
class _IdlConstant:
    """IDL representation of a constant value."""

    name: str
    type: _IdlType
    value: str


@dataclass
class _IdlEvent:
    """IDL representation of an event.

    Composed of a list of event fields.
    """

    name: str
    fields: List[_IdlEventField]


@dataclass
class _IdlErrorCode:
    """IDL error code type."""

    code: int
    name: str
    msg: Optional[str] = None


@dataclass
class _Metadata:
    """IDL metadata field."""

    address: str


@dataclass
class Idl:
    """A parsed IDL object."""

    version: str
    name: str
    instructions: List[_IdlInstruction]
    state: Optional[_IdlState] = None
    accounts: List[_IdlAccountDef] = field(default_factory=list)
    types: List[_IdlTypeDef] = field(default_factory=list)
    events: List[_IdlEvent] = field(default_factory=list)
    errors: List[_IdlErrorCode] = field(default_factory=list)
    constants: List[_IdlConstant] = field(default_factory=list)
    metadata: Optional[_Metadata] = None

    @classmethod
    def from_json(cls, idl: Dict[str, Any]) -> "Idl":
        """Generate a parsed IDL from a JSON dict.

        Args:
            idl: The raw IDL dict.

        Returns:
            The parsed Idl object.
        """
        return deserialize(cls, idl)


def _idl_address(program_id: solana.publickey.PublicKey) -> solana.publickey.PublicKey:
    """Deterministic IDL address as a function of the program id.

    Args:
        program_id: The program ID.

    Returns:
        The public key of the IDL.
    """
    base = solana.publickey.PublicKey.find_program_address([], program_id)[0]
    return solana.publickey.PublicKey.create_with_seed(base, "anchor:idl", program_id)


class IdlProgramAccount(TypedDict):
    """The on-chain account of the IDL."""

    authority: solana.publickey.PublicKey
    data: bytes


IDL_ACCOUNT_LAYOUT = CStruct("authority" / BorshPubkey, "data" / Vec(U8))


def _decode_idl_account(data: bytes) -> IdlProgramAccount:
    """Decode on-chain IDL.

    Args:
        data: binary data from the account that stores the IDL.

    Returns:
        Decoded IDL.
    """
    return IDL_ACCOUNT_LAYOUT.parse(data)


_AccountDefOrTypeDef = Union[_IdlTypeDef, _IdlAccountDef]
_AccountDefsOrTypeDefs = typing.Sequence[_AccountDefOrTypeDef]

FIELD_TYPE_MAP: Mapping[str, Construct] = MappingProxyType(
    {
        "bool": Bool,
        "u8": U8,
        "i8": I8,
        "u16": U16,
        "i16": I16,
        "u32": U32,
        "i32": I32,
        "f32": F32,
        "u64": U64,
        "i64": I64,
        "f64": F64,
        "u128": U128,
        "i128": I128,
        "bytes": Bytes,
        "string": String,
        "publicKey": BorshPubkey,
    },
)


_enums_cache: dict[tuple[str, str], Enum] = {}


def _handle_enum_variants(
    idl_enum: _IdlTypeDefTyEnum,
    types: _AccountDefsOrTypeDefs,
    name: str,
) -> Enum:
    dict_key = (name, str(idl_enum))
    try:
        return _enums_cache[dict_key]
    except KeyError:
        result = _handle_enum_variants_no_cache(idl_enum, types, name)
        _enums_cache[dict_key] = result
        return result


def _handle_enum_variants_no_cache(
    idl_enum: _IdlTypeDefTyEnum,
    types: _AccountDefsOrTypeDefs,
    name: str,
) -> Enum:
    variants = []
    dclasses = {}
    for variant in idl_enum.variants:
        variant_name = variant.name
        if variant.fields is None:
            variants.append(variant_name)
        else:
            variant_fields = variant.fields
            if isinstance(variant_fields[0], _IdlField):
                fields = []
                named_fields = cast(_IdlEnumFieldsNamed, variant_fields)
                for fld in named_fields:
                    fields.append(_field_layout(fld, types))
                cstruct = CStruct(*fields)
                datacls = _idl_enum_fields_named_to_dataclass_type(
                    named_fields,
                    variant_name,
                )
                dclasses[variant_name] = datacls
                renamed = variant_name / cstruct
            else:
                fields = []
                unnamed_fields = cast(_IdlEnumFieldsTuple, variant_fields)
                for type_ in unnamed_fields:
                    fields.append(_type_layout(type_, types))
                tuple_struct = TupleStruct(*fields)
                renamed = variant_name / tuple_struct
            variants.append(renamed)  # type: ignore
    enum_without_types = Enum(*variants, enum_name=name)
    if dclasses:
        for cname in enum_without_types.enum._sumtype_constructor_names:
            try:
                dclass = dclasses[cname]
            except KeyError:
                continue
            dclass_fields = dc_fields(dclass)
            constructr = getattr(enum_without_types.enum, cname)
            for constructor_field in constructr._sumtype_attribs:
                attrib = constructor_field[1]  # type: ignore
                fld_name = constructor_field[0]  # type: ignore
                dclass_field = [f for f in dclass_fields if f.name == fld_name][0]
                attrib.type = dclass_field.type  # type: ignore
    return enum_without_types


def _typedef_layout_without_field_name(
    typedef: _AccountDefOrTypeDef,
    types: _AccountDefsOrTypeDefs,
) -> Construct:
    typedef_type = typedef.type
    name = typedef.name
    if isinstance(typedef_type, _IdlTypeDefTyStruct):
        field_layouts = [_field_layout(field, types) for field in typedef_type.fields]
        cstruct = CStruct(*field_layouts)
        datacls = _idl_typedef_ty_struct_to_dataclass_type(typedef_type, name)
        return _DataclassStruct(cstruct, datacls=datacls)
    elif isinstance(typedef_type, _IdlTypeDefTyEnum):
        return _handle_enum_variants(typedef_type, types, name)
    unknown_type = typedef_type.kind
    raise ValueError(f"Unknown type {unknown_type}")


def _typedef_layout(
    typedef: _AccountDefOrTypeDef,
    types: list[_IdlTypeDef],
    field_name: str,
) -> Construct:
    """Map an IDL typedef to a `Construct` object.
    Args:
        typedef: The IDL typedef object.
        types: IDL type definitions.
        field_name: The name of the field.
    Raises:
        ValueError: If an unknown type is passed.
    Returns:
        `Construct` object from `borsh-construct`.
    """  # noqa: DAR402
    return field_name / _typedef_layout_without_field_name(typedef, types)


def _type_layout(type_: _IdlType, types: _AccountDefsOrTypeDefs) -> Construct:
    if isinstance(type_, str):
        return FIELD_TYPE_MAP[type_]
    field_type = cast(
        _NonLiteralIdlTypes,
        type_,
    )
    if isinstance(field_type, _IdlTypeVec):
        return Vec(_type_layout(field_type.vec, types))
    elif isinstance(field_type, _IdlTypeOption):
        return Option(_type_layout(field_type.option, types))
    elif isinstance(field_type, _IdlTypeCOption):
        return COption(_type_layout(field_type.coption, types))
    elif isinstance(field_type, _IdlTypeDefined):
        defined = field_type.defined
        if not types:
            raise ValueError("User defined types not provided")
        filtered = [t for t in types if t.name == defined]
        if len(filtered) != 1:
            raise ValueError(f"Type not found {defined}")
        return _typedef_layout_without_field_name(filtered[0], types)
    elif isinstance(field_type, _IdlTypeArray):
        array_ty = field_type.array[0]
        array_len = field_type.array[1]
        inner_layout = _type_layout(array_ty, types)
        return inner_layout[array_len]
    raise ValueError(f"Type {field_type} not implemented yet")


def _field_layout(field: _IdlField, types: _AccountDefsOrTypeDefs) -> Construct:
    """Map IDL spec to `borsh-construct` types.
    Args:
        field: field object from the IDL.
        types: IDL type definitions.
    Raises:
        ValueError: If the user-defined types are not provided.
        ValueError: If the type is not found.
        ValueError: If the type is not implemented yet.
    Returns:
        `Construct` object from `borsh-construct`.
    """  # noqa: DAR402
    field_name = field.name if field.name else ""
    return field_name / _type_layout(field.type, types)


def _make_datacls(name: str, fields: list[str]) -> type:
    return make_dataclass(name, fields)


_idl_typedef_ty_struct_to_dataclass_type_cache: dict[tuple[str, str], Type] = {}


def _idl_typedef_ty_struct_to_dataclass_type(
    typedef_type: _IdlTypeDefTyStruct,
    name: str,
) -> Type:
    dict_key = (name, str(typedef_type))
    try:
        return _idl_typedef_ty_struct_to_dataclass_type_cache[dict_key]
    except KeyError:
        result = _idl_typedef_ty_struct_to_dataclass_type_no_cache(typedef_type, name)
        _idl_typedef_ty_struct_to_dataclass_type_cache[dict_key] = result
        return result


def _idl_typedef_ty_struct_to_dataclass_type_no_cache(
    typedef_type: _IdlTypeDefTyStruct,
    name: str,
) -> Type:
    """Generate a dataclass definition from an IDL struct.
    Args:
        typedef_type: The IDL type.
        name: The name of the dataclass.
    Returns:
        Dataclass definition.
    """
    dataclass_fields = []
    for field in typedef_type.fields:
        field_name = field.name
        field_name_to_use = f"{field_name}_" if field_name in kwlist else field_name
        dataclass_fields.append(
            field_name_to_use,
        )
    return _make_datacls(name, dataclass_fields)


_idl_enum_fields_named_to_dataclass_type_cache: dict[tuple[str, str], Type] = {}


def _idl_enum_fields_named_to_dataclass_type(
    fields: _IdlEnumFieldsNamed,
    name: str,
) -> Type:
    dict_key = (name, str(fields))
    try:
        return _idl_enum_fields_named_to_dataclass_type_cache[dict_key]
    except KeyError:
        result = _idl_enum_fields_named_to_dataclass_type_no_cache(fields, name)
        _idl_enum_fields_named_to_dataclass_type_cache[dict_key] = result
        return result


def _idl_enum_fields_named_to_dataclass_type_no_cache(
    fields: _IdlEnumFieldsNamed,
    name: str,
) -> Type:
    """Generate a dataclass definition from IDL named enum fields.
    Args:
        fields: The IDL enum fields.
        name: The name of the dataclass.
    Returns:
        Dataclass type definition.
    """
    dataclass_fields = []
    for field in fields:
        field_name = field.name
        field_name_to_use = f"{field_name}_" if field_name in kwlist else field_name
        dataclass_fields.append(
            field_name_to_use,
        )
    return _make_datacls(name, dataclass_fields)


def _idl_typedef_to_python_type(
    typedef: _AccountDefOrTypeDef,
    types: _AccountDefsOrTypeDefs,
) -> Type:
    """Generate Python type from IDL user-defined type.
    Args:
        typedef: The user-defined type.
        types: IDL type definitions.
    Raises:
        ValueError: If an unknown type is passed.
    Returns:
        The Python type.
    """
    typedef_type = typedef.type
    if isinstance(typedef_type, _IdlTypeDefTyStruct):
        return _idl_typedef_ty_struct_to_dataclass_type(
            typedef_type,
            typedef.name,
        )
    elif isinstance(typedef_type, _IdlTypeDefTyEnum):
        return _handle_enum_variants(typedef_type, types, typedef.name).enum
    unknown_type = typedef_type.kind
    raise ValueError(f"Unknown type {unknown_type}")


def _sighash(ix_name: str) -> bytes:
    """Not technically sighash, since we don't include the arguments.
    (Because Rust doesn't allow function overloading.)
    Args:
        ix_name: The instruction name.
    Returns:
        The sighash bytes.
    """
    formatted_str = f"global:{ix_name}"
    return sha256(formatted_str.encode()).digest()[:8]

class _Sighash(Adapter):
    """Sighash as a Construct Adapter."""

    def __init__(self) -> None:
        """Initialize."""
        super().__init__(Bytes(8))  # type: ignore

    def _encode(self, obj: str, context, path) -> bytes:
        return _sighash(obj)

    def _decode(self, obj: bytes, context, path):
        raise ValueError("Sighash cannot be reversed")


class InstructionCoder(Adapter):
    """Encodes and decodes program instructions."""

    def __init__(self, idl: Idl) -> None:
        """Init.
        Args:
            idl: The parsed IDL object.
        """
        self.ix_layout = _parse_ix_layout(idl)
        sighasher = _Sighash()
        sighash_layouts: Dict[bytes, Construct] = {}
        sighashes: Dict[str, bytes] = {}
        sighash_to_name: Dict[bytes, str] = {}
        for ix in idl.instructions:
            sh = sighasher.build(ix.name)
            sighashes[ix.name] = sh
            sighash_layouts[sh] = self.ix_layout[ix.name]
            sighash_to_name[sh] = ix.name
        self.sighash_layouts = sighash_layouts
        self.sighashes = sighashes
        self.sighash_to_name = sighash_to_name
        subcon = Sequence(
            "sighash" / Bytes(8),
            Switch(lambda this: this.sighash, sighash_layouts),
        )
        super().__init__(subcon)  # type: ignore

    def _decode(self, obj: Tuple[bytes, Any], context, path) -> dict[str, Any]:
        return { 'data': obj[1], 'name': self.sighash_to_name[obj[0]] }


_SA = TypeVar("_SA", bound="_SupportsAdd")


class _SupportsAdd(Protocol):
    """Any type T where +(:T, :T) -> T."""

    def __add__(self: _SA, other: _SA) -> _SA:
        ...


def _parse_ix_layout(idl: Idl) -> Dict[str, Construct]:
    ix_layout: Dict[str, Construct] = {}
    for ix in idl.instructions:
        typedefs = cast(_SupportsAdd, idl.accounts) + cast(_SupportsAdd, idl.types)
        field_layouts = [
            _field_layout(arg, cast(_AccountDefsOrTypeDefs, typedefs))
            for arg in ix.args
        ]
        ix_layout[ix.name] = ix.name / CStruct(*field_layouts)
    return ix_layout
