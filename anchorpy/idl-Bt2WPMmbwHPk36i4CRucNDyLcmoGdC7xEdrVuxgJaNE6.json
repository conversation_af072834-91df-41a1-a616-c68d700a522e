{"version": "0.0.0", "name": "farm", "instructions": [{"name": "createLeveragedFarm", "accounts": [{"name": "global", "isMut": false, "isSigner": false}, {"name": "authority", "isMut": false, "isSigner": true}, {"name": "leveragedFarm", "isMut": true, "isSigner": false}, {"name": "solfarmVaultProgram", "isMut": false, "isSigner": false}, {"name": "serumMarket", "isMut": false, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}], "args": [{"name": "serumMarket", "type": "public<PERSON>ey"}, {"name": "solfarmVaultProgram", "type": "public<PERSON>ey"}, {"name": "solfarmVaultAddress", "type": "public<PERSON>ey"}, {"name": "serumRequestQueue", "type": "public<PERSON>ey"}, {"name": "serumEventQueue", "type": "public<PERSON>ey"}, {"name": "serumMarketBids", "type": "public<PERSON>ey"}, {"name": "serumMarketAsks", "type": "public<PERSON>ey"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "public<PERSON>ey"}, {"name": "serum<PERSON><PERSON><PERSON>aultAccount", "type": "public<PERSON>ey"}, {"name": "serumFeeRecipient", "type": "public<PERSON>ey"}, {"name": "serumDexProgram", "type": "public<PERSON>ey"}, {"name": "raydiumLpMintAddress", "type": "public<PERSON>ey"}, {"name": "raydiumAmmId", "type": "public<PERSON>ey"}, {"name": "raydiumAmmAuthority", "type": "public<PERSON>ey"}, {"name": "raydiumAmmOpenOrders", "type": "public<PERSON>ey"}, {"name": "raydiumAmmQuantitiesOrTargetOrders", "type": "public<PERSON>ey"}, {"name": "raydiumLiquidityProgram", "type": "public<PERSON>ey"}, {"name": "raydiumCoinTokenAccount", "type": "public<PERSON>ey"}, {"name": "raydiumPcTokenAccount", "type": "public<PERSON>ey"}, {"name": "raydiumPoolTempTokenAccount", "type": "public<PERSON>ey"}, {"name": "raydiumPoolWithdrawQueue", "type": "public<PERSON>ey"}, {"name": "lendingMarket", "type": "public<PERSON>ey"}, {"name": "lendingProgram", "type": "public<PERSON>ey"}, {"name": "farm", "type": "u64"}]}, {"name": "initializeLeveragedFarmOne", "accounts": [{"name": "global", "isMut": false, "isSigner": false}, {"name": "authority", "isMut": false, "isSigner": true}, {"name": "leveragedFarm", "isMut": true, "isSigner": false}], "args": [{"name": "baseMint", "type": "public<PERSON>ey"}, {"name": "quoteMint", "type": "public<PERSON>ey"}, {"name": "feeReceiver", "type": "public<PERSON>ey"}, {"name": "lpTokenPriceAccount", "type": "public<PERSON>ey"}, {"name": "coinTokenPriceAccount", "type": "public<PERSON>ey"}, {"name": "pcTokenPriceAccount", "type": "public<PERSON>ey"}, {"name": "coinReserveLiquidityFeeReceiver", "type": "public<PERSON>ey"}, {"name": "pcReserveLiquidityFeeReceiver", "type": "public<PERSON>ey"}, {"name": "lpDecimals", "type": "u8"}, {"name": "baseDecimals", "type": "u8"}, {"name": "quoteDecimals", "type": "u8"}, {"name": "supportsFee", "type": "bool"}]}, {"name": "initializeLeveragedFarmTwo", "accounts": [{"name": "global", "isMut": false, "isSigner": false}, {"name": "authority", "isMut": false, "isSigner": true}, {"name": "leveragedFarm", "isMut": true, "isSigner": false}], "args": [{"name": "baseReserve", "type": "public<PERSON>ey"}, {"name": "quote<PERSON><PERSON><PERSON>", "type": "public<PERSON>ey"}, {"name": "baseTokenAccount", "type": "public<PERSON>ey"}, {"name": "quoteTokenAccount", "type": "public<PERSON>ey"}, {"name": "serumOpenOrdersAccount", "type": "public<PERSON>ey"}, {"name": "buySlip", "type": "u64"}, {"name": "sellSlip", "type": "u64"}]}, {"name": "createUserFarm", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "userFarm", "isMut": true, "isSigner": false}, {"name": "userFarmObligation", "isMut": true, "isSigner": false}, {"name": "lendingMarket", "isMut": true, "isSigner": false}, {"name": "global", "isMut": false, "isSigner": false}, {"name": "leveragedFarm", "isMut": false, "isSigner": false}, {"name": "clock", "isMut": false, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}, {"name": "lendingProgram", "isMut": false, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "obligationVaultAddress", "isMut": true, "isSigner": false}], "args": [{"name": "farmProgramId", "type": "public<PERSON>ey"}]}, {"name": "createUserFarmObligation", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "userFarm", "isMut": true, "isSigner": false}, {"name": "leveragedFarm", "isMut": false, "isSigner": false}, {"name": "userFarmObligation", "isMut": true, "isSigner": false}, {"name": "lendingMarket", "isMut": true, "isSigner": false}, {"name": "obligationVaultAddress", "isMut": true, "isSigner": false}, {"name": "clock", "isMut": false, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}, {"name": "lendingProgram", "isMut": false, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}], "args": []}, {"name": "depositBorrowDual", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "userFarm", "isMut": true, "isSigner": false}, {"name": "leveragedFarm", "isMut": false, "isSigner": false}, {"name": "userFarmObligation", "isMut": true, "isSigner": false}, {"name": "coinSourceTokenAccount", "isMut": true, "isSigner": false}, {"name": "coinDestinationTokenAccount", "isMut": true, "isSigner": false}, {"name": "pcSourceTokenAccount", "isMut": true, "isSigner": false}, {"name": "pcDestinationTokenAccount", "isMut": true, "isSigner": false}, {"name": "coinDepositReserveAccount", "isMut": true, "isSigner": false}, {"name": "pcDepositReserveAccount", "isMut": true, "isSigner": false}, {"name": "coinReserveLiquidityOracle", "isMut": false, "isSigner": false}, {"name": "pcReserveLiquidityOracle", "isMut": false, "isSigner": false}, {"name": "lendingMarketAccount", "isMut": false, "isSigner": false}, {"name": "derivedLendingMarketAuthority", "isMut": false, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "lendingProgram", "isMut": false, "isSigner": false}, {"name": "coinSourceReserveLiquidityTokenAccount", "isMut": true, "isSigner": false}, {"name": "pcSourceReserveLiquidityTokenAccount", "isMut": true, "isSigner": false}, {"name": "coinReserveLiquidityFeeReceiver", "isMut": true, "isSigner": false}, {"name": "pcReserveLiquidityFeeReceiver", "isMut": true, "isSigner": false}, {"name": "borrowAuthorizer", "isMut": false, "isSigner": false}, {"name": "lpPythPriceAccount", "isMut": false, "isSigner": false}, {"name": "vaultAccount", "isMut": true, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}], "args": [{"name": "coinAmount", "type": "u64"}, {"name": "pcAmount", "type": "u64"}, {"name": "coinBorrowAmount", "type": "u64"}, {"name": "pcBorrowAmount", "type": "u64"}, {"name": "obligationIndex", "type": "u8"}]}, {"name": "swapTokensRaydiumStats", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "leveragedFarm", "isMut": true, "isSigner": false}, {"name": "userFarm", "isMut": true, "isSigner": false}, {"name": "userFarmObligation", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "vault<PERSON><PERSON><PERSON>", "isMut": false, "isSigner": false}, {"name": "swapOrLiquidityProgramId", "isMut": false, "isSigner": false}, {"name": "ammId", "isMut": true, "isSigner": false}, {"name": "ammAuthority", "isMut": true, "isSigner": false}, {"name": "ammOpenOrders", "isMut": true, "isSigner": false}, {"name": "ammQuantitiesOrTargetOrders", "isMut": true, "isSigner": false}, {"name": "poolCoinTokenaccount", "isMut": true, "isSigner": false}, {"name": "poolPcTokenaccount", "isMut": true, "isSigner": false}, {"name": "serumProgramId", "isMut": false, "isSigner": false}, {"name": "serumMarket", "isMut": true, "isSigner": false}, {"name": "serumBids", "isMut": true, "isSigner": false}, {"name": "serumAsks", "isMut": true, "isSigner": false}, {"name": "serumEventQueue", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "serum<PERSON><PERSON><PERSON>aultAccount", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "coinWallet", "isMut": true, "isSigner": false}, {"name": "pcW<PERSON>t", "isMut": true, "isSigner": false}], "args": [{"name": "obligationIndex", "type": "u8"}]}, {"name": "swapTokensOrcaStats", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "leveragedFarm", "isMut": true, "isSigner": false}, {"name": "userFarm", "isMut": true, "isSigner": false}, {"name": "userFarmObligation", "isMut": true, "isSigner": false}, {"name": "pcW<PERSON>t", "isMut": true, "isSigner": false}, {"name": "market", "accounts": [{"name": "market", "isMut": true, "isSigner": false}, {"name": "openOrders", "isMut": true, "isSigner": false}, {"name": "requestQueue", "isMut": true, "isSigner": false}, {"name": "eventQueue", "isMut": true, "isSigner": false}, {"name": "bids", "isMut": true, "isSigner": false}, {"name": "asks", "isMut": true, "isSigner": false}, {"name": "order<PERSON>ayer<PERSON>okenAccount", "isMut": true, "isSigner": false}, {"name": "coinVault", "isMut": true, "isSigner": false}, {"name": "p<PERSON><PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "vault<PERSON><PERSON><PERSON>", "isMut": false, "isSigner": false}, {"name": "coinWallet", "isMut": true, "isSigner": false}]}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}, {"name": "dexProgram", "isMut": false, "isSigner": false}, {"name": "vault<PERSON><PERSON><PERSON>", "isMut": false, "isSigner": false}], "args": [{"name": "obligationIndex", "type": "u8"}]}, {"name": "addLiquidityStats", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "userFarm", "isMut": true, "isSigner": false}, {"name": "leveragedFarm", "isMut": true, "isSigner": false}, {"name": "liquidityProgramId", "isMut": false, "isSigner": false}, {"name": "ammId", "isMut": true, "isSigner": false}, {"name": "ammAuthority", "isMut": true, "isSigner": false}, {"name": "ammOpenOrders", "isMut": true, "isSigner": false}, {"name": "ammQuantitiesOrTargetOrders", "isMut": true, "isSigner": false}, {"name": "lpMintAddress", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Account", "isMut": true, "isSigner": false}, {"name": "poolPcTokenAccount", "isMut": true, "isSigner": false}, {"name": "serumMarket", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "levFarmCoinTokenAccount", "isMut": true, "isSigner": false}, {"name": "levFarmPcTokenAccount", "isMut": true, "isSigner": false}, {"name": "userLpTokenAccount", "isMut": true, "isSigner": false}, {"name": "pyth<PERSON>riceA<PERSON>unt", "isMut": false, "isSigner": false}, {"name": "lendingMarketAccount", "isMut": false, "isSigner": false}, {"name": "userFarmObligation", "isMut": true, "isSigner": false}, {"name": "derivedLendingMarketAuthority", "isMut": false, "isSigner": false}, {"name": "lendingProgram", "isMut": false, "isSigner": false}, {"name": "clock", "isMut": false, "isSigner": false}, {"name": "dexProgram", "isMut": false, "isSigner": false}], "args": [{"name": "obligationIndex", "type": "u8"}]}, {"name": "orcaAddLiquidityQueue", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "userFarm", "isMut": true, "isSigner": false}, {"name": "leveragedFarm", "isMut": true, "isSigner": false}, {"name": "vaultAccount", "isMut": true, "isSigner": false}, {"name": "vaultUserAccount", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}, {"name": "vaultPda", "isMut": true, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}, {"name": "levFarmCoinTokenAccount", "isMut": true, "isSigner": false}, {"name": "levFarmPcTokenAccount", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Account", "isMut": true, "isSigner": false}, {"name": "poolPcTokenAccount", "isMut": true, "isSigner": false}, {"name": "liquidityProgramId", "isMut": false, "isSigner": false}, {"name": "ammId", "isMut": true, "isSigner": false}, {"name": "ammAuthority", "isMut": true, "isSigner": false}, {"name": "vaultDepositQueue", "isMut": true, "isSigner": false}, {"name": "lpMintAddress", "isMut": true, "isSigner": false}, {"name": "lendingMarketAccount", "isMut": true, "isSigner": false}, {"name": "userFarmObligation", "isMut": true, "isSigner": false}, {"name": "derivedLendingMarketAuthority", "isMut": true, "isSigner": false}, {"name": "lendingProgram", "isMut": false, "isSigner": false}, {"name": "dexProgram", "isMut": false, "isSigner": false}, {"name": "solfarmVaultProgram", "isMut": false, "isSigner": false}, {"name": "obligationVaultAddress", "isMut": true, "isSigner": false}], "args": [{"name": "args", "type": {"defined": "OrcaAddLiqQueueArgs"}}]}, {"name": "depositOrcaVaultDd", "accounts": [{"name": "authority", "isMut": true, "isSigner": true}, {"name": "vaultAccount", "isMut": true, "isSigner": false}, {"name": "vaultUserAccount", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}, {"name": "vaultPda", "isMut": true, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}, {"name": "userFarmTokenAccount", "isMut": true, "isSigner": false}, {"name": "userFarmDdTokenAccount", "isMut": true, "isSigner": false}, {"name": "userRewardDdTokenAccount", "isMut": true, "isSigner": false}, {"name": "globalBaseDdTokenVault", "isMut": true, "isSigner": false}, {"name": "farmDdTokenMint", "isMut": true, "isSigner": false}, {"name": "globalFarmDd", "isMut": true, "isSigner": false}, {"name": "userFarmDd", "isMut": true, "isSigner": false}, {"name": "globalRewardDdTokenVault", "isMut": true, "isSigner": false}, {"name": "convertAuthorityDd", "isMut": false, "isSigner": false}, {"name": "aquaFarmProgram", "isMut": false, "isSigner": false}, {"name": "solfarmVaultProgram", "isMut": false, "isSigner": false}, {"name": "obligationVaultAddress", "isMut": true, "isSigner": false}, {"name": "leveragedFarm", "isMut": false, "isSigner": false}, {"name": "leveragedUserFarm", "isMut": true, "isSigner": false}], "args": [{"name": "depositArgs", "type": {"defined": "DepositOrcaVaultArgs"}}, {"name": "obligationIndex", "type": "u8"}]}, {"name": "depositOrcaVaultWithoutShares", "accounts": [{"name": "authority", "isMut": true, "isSigner": true}, {"name": "vaultAccount", "isMut": true, "isSigner": false}, {"name": "vaultUserAccount", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}, {"name": "vaultPda", "isMut": true, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}, {"name": "userFarmOwner", "isMut": true, "isSigner": false}, {"name": "userTransferAuthority", "isMut": false, "isSigner": false}, {"name": "userBaseTokenAccount", "isMut": true, "isSigner": false}, {"name": "userFarmTokenAccount", "isMut": true, "isSigner": false}, {"name": "userRewardTokenAccount", "isMut": true, "isSigner": false}, {"name": "globalBaseTokenVault", "isMut": true, "isSigner": false}, {"name": "farmTokenMint", "isMut": true, "isSigner": false}, {"name": "globalFarm", "isMut": true, "isSigner": false}, {"name": "orcaUserFarm", "isMut": true, "isSigner": false}, {"name": "globalRewardTokenVault", "isMut": true, "isSigner": false}, {"name": "convertAuthority", "isMut": false, "isSigner": false}, {"name": "aquaFarmProgram", "isMut": false, "isSigner": false}, {"name": "fundingTokenAccount", "isMut": true, "isSigner": false}, {"name": "leveragedFarm", "isMut": false, "isSigner": false}, {"name": "leveragedUserFarm", "isMut": true, "isSigner": false}, {"name": "clock", "isMut": false, "isSigner": false}, {"name": "solfarmVaultProgram", "isMut": false, "isSigner": false}, {"name": "obligationVaultAddress", "isMut": true, "isSigner": false}], "args": [{"name": "depositArgs", "type": {"defined": "DepositOrcaVaultArgs"}}, {"name": "obligationIndex", "type": "u8"}]}, {"name": "depositOrcaVault", "accounts": [{"name": "authority", "isMut": true, "isSigner": true}, {"name": "vaultAccount", "isMut": true, "isSigner": false}, {"name": "vaultUserAccount", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}, {"name": "vaultPda", "isMut": true, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}, {"name": "userFarmOwner", "isMut": true, "isSigner": false}, {"name": "userTransferAuthority", "isMut": false, "isSigner": false}, {"name": "userBaseTokenAccount", "isMut": true, "isSigner": false}, {"name": "userFarmTokenAccount", "isMut": true, "isSigner": false}, {"name": "userRewardTokenAccount", "isMut": true, "isSigner": false}, {"name": "globalBaseTokenVault", "isMut": true, "isSigner": false}, {"name": "farmTokenMint", "isMut": true, "isSigner": false}, {"name": "globalFarm", "isMut": true, "isSigner": false}, {"name": "orcaUserFarm", "isMut": true, "isSigner": false}, {"name": "globalRewardTokenVault", "isMut": true, "isSigner": false}, {"name": "convertAuthority", "isMut": false, "isSigner": false}, {"name": "aquaFarmProgram", "isMut": false, "isSigner": false}, {"name": "fundingTokenAccount", "isMut": true, "isSigner": false}, {"name": "leveragedFarm", "isMut": false, "isSigner": false}, {"name": "leveragedUserFarm", "isMut": true, "isSigner": false}, {"name": "clock", "isMut": false, "isSigner": false}, {"name": "solfarmVaultProgram", "isMut": false, "isSigner": false}, {"name": "obligationVaultAddress", "isMut": true, "isSigner": false}], "args": [{"name": "depositArgs", "type": {"defined": "DepositOrcaVaultArgs"}}, {"name": "obligationIndex", "type": "u8"}]}, {"name": "withdrawOrcaVaultDdClose", "accounts": [{"name": "authority", "isMut": true, "isSigner": true}, {"name": "vaultAccount", "isMut": true, "isSigner": false}, {"name": "vaultUserAccount", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}, {"name": "vaultPda", "isMut": true, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}, {"name": "userFarmTokenAccount", "isMut": true, "isSigner": false}, {"name": "userFarmDdTokenAccount", "isMut": true, "isSigner": false}, {"name": "userRewardDdTokenAccount", "isMut": true, "isSigner": false}, {"name": "globalBaseDdTokenVault", "isMut": true, "isSigner": false}, {"name": "farmDdTokenMint", "isMut": true, "isSigner": false}, {"name": "globalFarmDd", "isMut": true, "isSigner": false}, {"name": "userFarmDd", "isMut": true, "isSigner": false}, {"name": "globalRewardDdTokenVault", "isMut": true, "isSigner": false}, {"name": "convertAuthorityDd", "isMut": false, "isSigner": false}, {"name": "aquaFarmProgram", "isMut": false, "isSigner": false}, {"name": "leveragedUserFarm", "isMut": true, "isSigner": false}, {"name": "leveragedFarm", "isMut": true, "isSigner": false}, {"name": "solfarmVaultProgram", "isMut": false, "isSigner": false}, {"name": "obligationVaultAddress", "isMut": true, "isSigner": false}], "args": [{"name": "obligationIndex", "type": "u8"}, {"name": "withdrawPercent", "type": "u8"}, {"name": "closeMethod", "type": "u8"}]}, {"name": "closePositionInfoAccount", "accounts": [{"name": "authority", "isMut": true, "isSigner": true}, {"name": "positionInfoAccount", "isMut": true, "isSigner": false}], "args": []}, {"name": "withdrawOrcaVault", "accounts": [{"name": "authority", "isMut": true, "isSigner": true}, {"name": "vaultAccount", "isMut": true, "isSigner": false}, {"name": "vaultUserAccount", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}, {"name": "vaultPda", "isMut": true, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}, {"name": "userFarmOwner", "isMut": true, "isSigner": false}, {"name": "userTransferAuthority", "isMut": false, "isSigner": false}, {"name": "userBaseTokenAccount", "isMut": true, "isSigner": false}, {"name": "userFarmTokenAccount", "isMut": true, "isSigner": false}, {"name": "userRewardTokenAccount", "isMut": true, "isSigner": false}, {"name": "globalBaseTokenVault", "isMut": true, "isSigner": false}, {"name": "farmTokenMint", "isMut": true, "isSigner": false}, {"name": "globalFarm", "isMut": true, "isSigner": false}, {"name": "orcaUserFarm", "isMut": true, "isSigner": false}, {"name": "globalRewardTokenVault", "isMut": true, "isSigner": false}, {"name": "convertAuthority", "isMut": false, "isSigner": false}, {"name": "aquaFarmProgram", "isMut": false, "isSigner": false}, {"name": "receivingTokenAccount", "isMut": true, "isSigner": false}, {"name": "clock", "isMut": false, "isSigner": false}, {"name": "leveragedUserFarm", "isMut": true, "isSigner": false}, {"name": "leveragedFarm", "isMut": true, "isSigner": false}, {"name": "solfarmVaultProgram", "isMut": false, "isSigner": false}, {"name": "obligationVaultAddress", "isMut": true, "isSigner": false}], "args": [{"name": "obligationIndex", "type": "u8"}, {"name": "withdrawPercent", "type": "u8"}, {"name": "closeMethod", "type": "u8"}]}, {"name": "withdrawOrcaVaultClose", "accounts": [{"name": "authority", "isMut": true, "isSigner": true}, {"name": "vaultAccount", "isMut": true, "isSigner": false}, {"name": "vaultUserAccount", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}, {"name": "vaultPda", "isMut": true, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}, {"name": "userFarmOwner", "isMut": true, "isSigner": false}, {"name": "userTransferAuthority", "isMut": false, "isSigner": false}, {"name": "userBaseTokenAccount", "isMut": true, "isSigner": false}, {"name": "userFarmTokenAccount", "isMut": true, "isSigner": false}, {"name": "userRewardTokenAccount", "isMut": true, "isSigner": false}, {"name": "globalBaseTokenVault", "isMut": true, "isSigner": false}, {"name": "farmTokenMint", "isMut": true, "isSigner": false}, {"name": "globalFarm", "isMut": true, "isSigner": false}, {"name": "orcaUserFarm", "isMut": true, "isSigner": false}, {"name": "globalRewardTokenVault", "isMut": true, "isSigner": false}, {"name": "convertAuthority", "isMut": false, "isSigner": false}, {"name": "aquaFarmProgram", "isMut": false, "isSigner": false}, {"name": "receivingTokenAccount", "isMut": true, "isSigner": false}, {"name": "clock", "isMut": false, "isSigner": false}, {"name": "leveragedUserFarm", "isMut": true, "isSigner": false}, {"name": "leveragedFarm", "isMut": true, "isSigner": false}, {"name": "solfarmVaultProgram", "isMut": false, "isSigner": false}, {"name": "obligationVaultAddress", "isMut": true, "isSigner": false}], "args": [{"name": "obligationIndex", "type": "u8"}, {"name": "withdrawPercent", "type": "u8"}, {"name": "closeMethod", "type": "u8"}]}, {"name": "withdrawOrcaVaultWithoutShares", "accounts": [{"name": "authority", "isMut": true, "isSigner": true}, {"name": "vaultAccount", "isMut": true, "isSigner": false}, {"name": "vaultUserAccount", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}, {"name": "vaultPda", "isMut": true, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}, {"name": "userFarmOwner", "isMut": true, "isSigner": false}, {"name": "userTransferAuthority", "isMut": false, "isSigner": false}, {"name": "userBaseTokenAccount", "isMut": true, "isSigner": false}, {"name": "userFarmTokenAccount", "isMut": true, "isSigner": false}, {"name": "userRewardTokenAccount", "isMut": true, "isSigner": false}, {"name": "globalBaseTokenVault", "isMut": true, "isSigner": false}, {"name": "farmTokenMint", "isMut": true, "isSigner": false}, {"name": "globalFarm", "isMut": true, "isSigner": false}, {"name": "orcaUserFarm", "isMut": true, "isSigner": false}, {"name": "globalRewardTokenVault", "isMut": true, "isSigner": false}, {"name": "convertAuthority", "isMut": false, "isSigner": false}, {"name": "aquaFarmProgram", "isMut": false, "isSigner": false}, {"name": "receivingTokenAccount", "isMut": true, "isSigner": false}, {"name": "clock", "isMut": false, "isSigner": false}, {"name": "leveragedUserFarm", "isMut": true, "isSigner": false}, {"name": "leveragedFarm", "isMut": true, "isSigner": false}, {"name": "solfarmVaultProgram", "isMut": false, "isSigner": false}, {"name": "obligationVaultAddress", "isMut": true, "isSigner": false}], "args": [{"name": "obligationIndex", "type": "u8"}]}, {"name": "depositVault", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "userFarm", "isMut": true, "isSigner": false}, {"name": "obligationVaultAddress", "isMut": true, "isSigner": false}, {"name": "leveragedFarm", "isMut": true, "isSigner": false}, {"name": "vaultProgram", "isMut": false, "isSigner": false}, {"name": "authorityTokenAccount", "isMut": true, "isSigner": false}, {"name": "vaultPdaAccount", "isMut": true, "isSigner": false}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "lpTokenAccount", "isMut": true, "isSigner": false}, {"name": "userBalanceAccount", "isMut": true, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}, {"name": "stakeProgramId", "isMut": false, "isSigner": false}, {"name": "poolId", "isMut": true, "isSigner": false}, {"name": "poolAuthority", "isMut": true, "isSigner": false}, {"name": "userInfoAccount", "isMut": true, "isSigner": false}, {"name": "poolLpTokenAccount", "isMut": true, "isSigner": false}, {"name": "userRewardATokenAccount", "isMut": true, "isSigner": false}, {"name": "poolRewardATokenAccount", "isMut": true, "isSigner": false}, {"name": "userRewardBTokenAccount", "isMut": true, "isSigner": false}, {"name": "poolRewardBTokenAccount", "isMut": true, "isSigner": false}, {"name": "clock", "isMut": false, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}, {"name": "tokenProgramId", "isMut": false, "isSigner": false}, {"name": "userBalanceMetadata", "isMut": true, "isSigner": false}], "args": [{"name": "depositArgs", "type": {"defined": "DepositFarmArgs"}}, {"name": "obligationIndex", "type": "u8"}]}, {"name": "withdrawRaydiumVaultClose", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "userFarm", "isMut": true, "isSigner": false}, {"name": "obligationVaultAddress", "isMut": true, "isSigner": false}, {"name": "leveragedFarm", "isMut": false, "isSigner": false}, {"name": "authorityTokenAccount", "isMut": true, "isSigner": false}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultProgram", "isMut": false, "isSigner": false}, {"name": "userBalanceAccount", "isMut": true, "isSigner": false}, {"name": "userInfoAccount", "isMut": true, "isSigner": false}, {"name": "userLpTokenAccount", "isMut": true, "isSigner": false}, {"name": "userRewardATokenAccount", "isMut": true, "isSigner": false}, {"name": "poolRewardATokenAccount", "isMut": true, "isSigner": false}, {"name": "userRewardBTokenAccount", "isMut": true, "isSigner": false}, {"name": "poolRewardBTokenAccount", "isMut": true, "isSigner": false}, {"name": "tokenProgramId", "isMut": false, "isSigner": false}, {"name": "clock", "isMut": false, "isSigner": false}, {"name": "vaultPdaAccount", "isMut": true, "isSigner": false}, {"name": "poolLpTokenAccount", "isMut": true, "isSigner": false}, {"name": "poolAuthority", "isMut": true, "isSigner": false}, {"name": "poolId", "isMut": true, "isSigner": false}, {"name": "stakeProgramId", "isMut": false, "isSigner": false}, {"name": "userBalanceMeta", "isMut": true, "isSigner": false}], "args": [{"name": "<PERSON><PERSON><PERSON>s", "type": {"defined": "WithdrawFarmArgs"}}, {"name": "obligationIndex", "type": "u8"}, {"name": "withdrawPercent", "type": "u8"}, {"name": "closeMethod", "type": "u8"}]}, {"name": "removeLiquidityNew", "accounts": [{"name": "removeLiq", "accounts": [{"name": "userFarm", "isMut": true, "isSigner": false}, {"name": "obligationVaultAddress", "isMut": true, "isSigner": false}, {"name": "leveragedFarm", "isMut": false, "isSigner": false}, {"name": "liquidityProgramId", "isMut": false, "isSigner": false}, {"name": "ammId", "isMut": true, "isSigner": false}, {"name": "ammAuthority", "isMut": true, "isSigner": false}, {"name": "ammOpenOrders", "isMut": true, "isSigner": false}, {"name": "ammQuantitiesOrTargetOrders", "isMut": true, "isSigner": false}, {"name": "lpMintAddress", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Account", "isMut": true, "isSigner": false}, {"name": "poolPcTokenAccount", "isMut": true, "isSigner": false}, {"name": "poolWithdrawQueue", "isMut": true, "isSigner": false}, {"name": "poolTempLpTokenAccount", "isMut": true, "isSigner": false}, {"name": "serumProgramId", "isMut": false, "isSigner": false}, {"name": "serumMarket", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "serum<PERSON><PERSON><PERSON>aultAccount", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "levFarmCoinTokenAccount", "isMut": true, "isSigner": false}, {"name": "levFarmPcTokenAccount", "isMut": true, "isSigner": false}, {"name": "userLpTokenAccount", "isMut": true, "isSigner": false}, {"name": "clock", "isMut": false, "isSigner": false}]}], "args": [{"name": "obligationIndex", "type": "u8"}, {"name": "obligationVaultNonce", "type": "u8"}]}, {"name": "swapTokensSerum", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "leveragedFarm", "isMut": true, "isSigner": false}, {"name": "userFarm", "isMut": true, "isSigner": false}, {"name": "userFarmObligation", "isMut": true, "isSigner": false}, {"name": "pcW<PERSON>t", "isMut": true, "isSigner": false}, {"name": "market", "accounts": [{"name": "market", "isMut": true, "isSigner": false}, {"name": "openOrders", "isMut": true, "isSigner": false}, {"name": "requestQueue", "isMut": true, "isSigner": false}, {"name": "eventQueue", "isMut": true, "isSigner": false}, {"name": "bids", "isMut": true, "isSigner": false}, {"name": "asks", "isMut": true, "isSigner": false}, {"name": "order<PERSON>ayer<PERSON>okenAccount", "isMut": true, "isSigner": false}, {"name": "coinVault", "isMut": true, "isSigner": false}, {"name": "p<PERSON><PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "vault<PERSON><PERSON><PERSON>", "isMut": false, "isSigner": false}, {"name": "coinWallet", "isMut": true, "isSigner": false}]}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}, {"name": "dexProgram", "isMut": false, "isSigner": false}, {"name": "vault<PERSON><PERSON><PERSON>", "isMut": false, "isSigner": false}], "args": [{"name": "reserves", "type": {"vec": "public<PERSON>ey"}}, {"name": "obligationIndex", "type": "u8"}]}, {"name": "swapTokensToRepayOrca", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "leveragedFarm", "isMut": true, "isSigner": false}, {"name": "userFarm", "isMut": true, "isSigner": false}, {"name": "userFarmObligation", "isMut": true, "isSigner": false}, {"name": "pcW<PERSON>t", "isMut": true, "isSigner": false}, {"name": "market", "accounts": [{"name": "market", "isMut": true, "isSigner": false}, {"name": "openOrders", "isMut": true, "isSigner": false}, {"name": "requestQueue", "isMut": true, "isSigner": false}, {"name": "eventQueue", "isMut": true, "isSigner": false}, {"name": "bids", "isMut": true, "isSigner": false}, {"name": "asks", "isMut": true, "isSigner": false}, {"name": "order<PERSON>ayer<PERSON>okenAccount", "isMut": true, "isSigner": false}, {"name": "coinVault", "isMut": true, "isSigner": false}, {"name": "p<PERSON><PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "vault<PERSON><PERSON><PERSON>", "isMut": false, "isSigner": false}, {"name": "coinWallet", "isMut": true, "isSigner": false}]}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}, {"name": "dexProgram", "isMut": false, "isSigner": false}, {"name": "vault<PERSON><PERSON><PERSON>", "isMut": false, "isSigner": false}], "args": [{"name": "reserves", "type": {"vec": "public<PERSON>ey"}}, {"name": "obligationIndex", "type": "u8"}, {"name": "minCoinSwap", "type": "u64"}, {"name": "minPcSwap", "type": "u64"}]}, {"name": "swapToRepayOrca", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "leveragedFarm", "isMut": true, "isSigner": false}, {"name": "userFarm", "isMut": true, "isSigner": false}, {"name": "userFarmObligation", "isMut": true, "isSigner": false}, {"name": "pcW<PERSON>t", "isMut": true, "isSigner": false}, {"name": "market", "accounts": [{"name": "market", "isMut": true, "isSigner": false}, {"name": "openOrders", "isMut": true, "isSigner": false}, {"name": "requestQueue", "isMut": true, "isSigner": false}, {"name": "eventQueue", "isMut": true, "isSigner": false}, {"name": "bids", "isMut": true, "isSigner": false}, {"name": "asks", "isMut": true, "isSigner": false}, {"name": "order<PERSON>ayer<PERSON>okenAccount", "isMut": true, "isSigner": false}, {"name": "coinVault", "isMut": true, "isSigner": false}, {"name": "p<PERSON><PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "vault<PERSON><PERSON><PERSON>", "isMut": false, "isSigner": false}, {"name": "coinWallet", "isMut": true, "isSigner": false}]}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}, {"name": "dexProgram", "isMut": false, "isSigner": false}, {"name": "vault<PERSON><PERSON><PERSON>", "isMut": false, "isSigner": false}], "args": [{"name": "reserves", "type": {"vec": "public<PERSON>ey"}}, {"name": "obligationIndex", "type": "u8"}]}, {"name": "repayObligationLiquidity", "accounts": [{"name": "authority", "isMut": true, "isSigner": true}, {"name": "userFarm", "isMut": true, "isSigner": false}, {"name": "userFarmObligation", "isMut": true, "isSigner": false}, {"name": "leveragedFarm", "isMut": false, "isSigner": false}, {"name": "coinSourceTokenAccount", "isMut": true, "isSigner": false}, {"name": "coinDestinationTokenAccount", "isMut": true, "isSigner": false}, {"name": "pcSourceTokenAccount", "isMut": true, "isSigner": false}, {"name": "pcDestinationTokenAccount", "isMut": true, "isSigner": false}, {"name": "coinReserveAccount", "isMut": true, "isSigner": false}, {"name": "pc<PERSON><PERSON><PERSON><PERSON><PERSON>unt", "isMut": true, "isSigner": false}, {"name": "lendingMarketAccount", "isMut": true, "isSigner": false}, {"name": "derivedLendingMarketAuthority", "isMut": true, "isSigner": false}, {"name": "clock", "isMut": false, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "lendingProgram", "isMut": false, "isSigner": false}, {"name": "lpPythPriceAccount", "isMut": false, "isSigner": false}, {"name": "coinPriceAccount", "isMut": false, "isSigner": false}, {"name": "pc<PERSON>riceAccount", "isMut": false, "isSigner": false}, {"name": "vaultAccount", "isMut": true, "isSigner": false}, {"name": "userCoinTokenAccount", "isMut": true, "isSigner": false}, {"name": "userPcTokenAccount", "isMut": true, "isSigner": false}, {"name": "positionInfoAccount", "isMut": true, "isSigner": false}], "args": [{"name": "reserves", "type": {"vec": "public<PERSON>ey"}}, {"name": "obligationIndex", "type": "u8"}]}, {"name": "repayObligationLiquidityExternal", "accounts": [{"name": "authority", "isMut": true, "isSigner": true}, {"name": "userFarm", "isMut": true, "isSigner": false}, {"name": "userFarmObligation", "isMut": true, "isSigner": false}, {"name": "leveragedFarm", "isMut": false, "isSigner": false}, {"name": "coinSourceTokenAccount", "isMut": true, "isSigner": false}, {"name": "coinDestinationTokenAccount", "isMut": true, "isSigner": false}, {"name": "pcSourceTokenAccount", "isMut": true, "isSigner": false}, {"name": "pcDestinationTokenAccount", "isMut": true, "isSigner": false}, {"name": "coinReserveAccount", "isMut": true, "isSigner": false}, {"name": "pc<PERSON><PERSON><PERSON><PERSON><PERSON>unt", "isMut": true, "isSigner": false}, {"name": "lendingMarketAccount", "isMut": true, "isSigner": false}, {"name": "derivedLendingMarketAuthority", "isMut": true, "isSigner": false}, {"name": "clock", "isMut": false, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "lendingProgram", "isMut": false, "isSigner": false}, {"name": "lpPythPriceAccount", "isMut": false, "isSigner": false}, {"name": "coinPriceAccount", "isMut": false, "isSigner": false}, {"name": "pc<PERSON>riceAccount", "isMut": false, "isSigner": false}, {"name": "vaultAccount", "isMut": true, "isSigner": false}, {"name": "userCoinTokenAccount", "isMut": true, "isSigner": false}, {"name": "userPcTokenAccount", "isMut": true, "isSigner": false}, {"name": "positionInfoAccount", "isMut": true, "isSigner": false}], "args": [{"name": "reserves", "type": {"vec": "public<PERSON>ey"}}, {"name": "obligationIndex", "type": "u8"}]}, {"name": "harvestTulips", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "userFarm", "isMut": true, "isSigner": false}, {"name": "obligationVaultAddress", "isMut": true, "isSigner": false}, {"name": "leveragedFarm", "isMut": false, "isSigner": false}, {"name": "vaultProgram", "isMut": false, "isSigner": false}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultPdaAccount", "isMut": true, "isSigner": false}, {"name": "userInfoAccount", "isMut": true, "isSigner": false}, {"name": "userBalanceAccount", "isMut": true, "isSigner": false}, {"name": "userTulipRewardMetadata", "isMut": true, "isSigner": false}, {"name": "userTulipTokenAccount", "isMut": true, "isSigner": false}, {"name": "vaultTulipTokenAccount", "isMut": true, "isSigner": false}, {"name": "authorityTulipTokenAccount", "isMut": true, "isSigner": false}, {"name": "tokenProgramId", "isMut": false, "isSigner": false}, {"name": "clock", "isMut": false, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}], "args": [{"name": "harvestArgs", "type": {"defined": "HarvestFarmTulipsArgs"}}, {"name": "obligationIndex", "type": "u8"}]}, {"name": "startUserObligationLiquidation", "accounts": [{"name": "global", "isMut": false, "isSigner": false}, {"name": "authority", "isMut": false, "isSigner": true}, {"name": "userFarm", "isMut": true, "isSigner": false}, {"name": "userObligation", "isMut": true, "isSigner": false}, {"name": "userObligationLiquidation", "isMut": true, "isSigner": false}, {"name": "tempLiquidationLpTokenAccount", "isMut": true, "isSigner": false}, {"name": "tempLiquidationBaseTokenAccount", "isMut": true, "isSigner": false}, {"name": "tempLiquidationQuoteTokenAccount", "isMut": true, "isSigner": false}, {"name": "levfarmBaseTokenAccount", "isMut": true, "isSigner": false}, {"name": "levfarmQuoteTokenAccount", "isMut": true, "isSigner": false}, {"name": "lendingProgram", "isMut": false, "isSigner": false}, {"name": "leveragedFarm", "isMut": false, "isSigner": false}, {"name": "clock", "isMut": false, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}, {"name": "pyth<PERSON>riceA<PERSON>unt", "isMut": false, "isSigner": false}, {"name": "coinPriceAccount", "isMut": false, "isSigner": false}, {"name": "pc<PERSON>riceAccount", "isMut": false, "isSigner": false}, {"name": "vaultAccount", "isMut": true, "isSigner": false}, {"name": "lendingMarketAccount", "isMut": false, "isSigner": false}, {"name": "derivedLendingMarketAuthority", "isMut": false, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}], "args": [{"name": "reserves", "type": {"vec": "public<PERSON>ey"}}, {"name": "obligationIndex", "type": "u8"}, {"name": "temporaryAccountNonce", "type": "u8"}]}, {"name": "startUserObligationLiquidationNew", "accounts": [{"name": "global", "isMut": false, "isSigner": false}, {"name": "authority", "isMut": false, "isSigner": true}, {"name": "userFarm", "isMut": true, "isSigner": false}, {"name": "userObligation", "isMut": true, "isSigner": false}, {"name": "userObligationLiquidation", "isMut": true, "isSigner": false}, {"name": "tempLiquidationLpTokenAccount", "isMut": true, "isSigner": false}, {"name": "tempLiquidationBaseTokenAccount", "isMut": true, "isSigner": false}, {"name": "tempLiquidationQuoteTokenAccount", "isMut": true, "isSigner": false}, {"name": "levfarmBaseTokenAccount", "isMut": true, "isSigner": false}, {"name": "levfarmQuoteTokenAccount", "isMut": true, "isSigner": false}, {"name": "lendingProgram", "isMut": false, "isSigner": false}, {"name": "leveragedFarm", "isMut": false, "isSigner": false}, {"name": "clock", "isMut": false, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}, {"name": "pyth<PERSON>riceA<PERSON>unt", "isMut": false, "isSigner": false}, {"name": "coinPriceAccount", "isMut": false, "isSigner": false}, {"name": "pc<PERSON>riceAccount", "isMut": false, "isSigner": false}, {"name": "vaultAccount", "isMut": true, "isSigner": false}, {"name": "lendingMarketAccount", "isMut": false, "isSigner": false}, {"name": "derivedLendingMarketAuthority", "isMut": false, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "baseMint", "isMut": false, "isSigner": false}, {"name": "quoteMint", "isMut": false, "isSigner": false}, {"name": "lpMint", "isMut": false, "isSigner": false}], "args": [{"name": "reserves", "type": {"vec": "public<PERSON>ey"}}, {"name": "obligationIndex", "type": "u8"}, {"name": "temporaryAccountNonce", "type": "u8"}, {"name": "baseTokenAccountNonce", "type": "u8"}, {"name": "quoteTokenAccountNonce", "type": "u8"}, {"name": "lpTokenAccountNonce", "type": "u8"}]}, {"name": "startSsjUserLiquidationTest", "accounts": [{"name": "global", "isMut": false, "isSigner": false}, {"name": "authority", "isMut": false, "isSigner": true}, {"name": "userFarm", "isMut": true, "isSigner": false}, {"name": "userObligation", "isMut": true, "isSigner": false}, {"name": "userObligationLiquidation", "isMut": true, "isSigner": false}, {"name": "tempLiquidationLpTokenAccount", "isMut": true, "isSigner": false}, {"name": "tempLiquidationBaseTokenAccount", "isMut": true, "isSigner": false}, {"name": "tempLiquidationQuoteTokenAccount", "isMut": true, "isSigner": false}, {"name": "levfarmBaseTokenAccount", "isMut": true, "isSigner": false}, {"name": "levfarmQuoteTokenAccount", "isMut": true, "isSigner": false}, {"name": "lendingProgram", "isMut": false, "isSigner": false}, {"name": "leveragedFarm", "isMut": false, "isSigner": false}, {"name": "clock", "isMut": false, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}, {"name": "pyth<PERSON>riceA<PERSON>unt", "isMut": false, "isSigner": false}, {"name": "coinPriceAccount", "isMut": false, "isSigner": false}, {"name": "pc<PERSON>riceAccount", "isMut": false, "isSigner": false}, {"name": "vaultAccount", "isMut": true, "isSigner": false}, {"name": "lendingMarketAccount", "isMut": false, "isSigner": false}, {"name": "derivedLendingMarketAuthority", "isMut": false, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}], "args": [{"name": "args", "type": {"defined": "Liq<PERSON><PERSON><PERSON>"}}]}, {"name": "pullLpForLiquidationSplTokenSwap", "accounts": [{"name": "userObligationLiquidation", "isMut": false, "isSigner": false}, {"name": "withdrawFarm", "accounts": [{"name": "authority", "isMut": true, "isSigner": true}, {"name": "vaultAccount", "isMut": true, "isSigner": false}, {"name": "vaultUserAccount", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}, {"name": "vaultPda", "isMut": true, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}, {"name": "userFarmOwner", "isMut": true, "isSigner": false}, {"name": "userTransferAuthority", "isMut": false, "isSigner": false}, {"name": "userBaseTokenAccount", "isMut": true, "isSigner": false}, {"name": "userFarmTokenAccount", "isMut": true, "isSigner": false}, {"name": "userRewardTokenAccount", "isMut": true, "isSigner": false}, {"name": "globalBaseTokenVault", "isMut": true, "isSigner": false}, {"name": "farmTokenMint", "isMut": true, "isSigner": false}, {"name": "globalFarm", "isMut": true, "isSigner": false}, {"name": "orcaUserFarm", "isMut": true, "isSigner": false}, {"name": "globalRewardTokenVault", "isMut": true, "isSigner": false}, {"name": "convertAuthority", "isMut": false, "isSigner": false}, {"name": "aquaFarmProgram", "isMut": false, "isSigner": false}, {"name": "receivingTokenAccount", "isMut": true, "isSigner": false}, {"name": "clock", "isMut": false, "isSigner": false}, {"name": "leveragedUserFarm", "isMut": true, "isSigner": false}, {"name": "leveragedFarm", "isMut": true, "isSigner": false}, {"name": "solfarmVaultProgram", "isMut": false, "isSigner": false}, {"name": "obligationVaultAddress", "isMut": true, "isSigner": false}]}], "args": [{"name": "obligationIndex", "type": "u8"}]}, {"name": "pullLpForLiquidationDoubleDip", "accounts": [{"name": "userObligationLiquidation", "isMut": false, "isSigner": false}, {"name": "withdrawFarm", "accounts": [{"name": "authority", "isMut": true, "isSigner": true}, {"name": "vaultAccount", "isMut": true, "isSigner": false}, {"name": "vaultUserAccount", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}, {"name": "vaultPda", "isMut": true, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}, {"name": "userFarmTokenAccount", "isMut": true, "isSigner": false}, {"name": "userFarmDdTokenAccount", "isMut": true, "isSigner": false}, {"name": "userRewardDdTokenAccount", "isMut": true, "isSigner": false}, {"name": "globalBaseDdTokenVault", "isMut": true, "isSigner": false}, {"name": "farmDdTokenMint", "isMut": true, "isSigner": false}, {"name": "globalFarmDd", "isMut": true, "isSigner": false}, {"name": "userFarmDd", "isMut": true, "isSigner": false}, {"name": "globalRewardDdTokenVault", "isMut": true, "isSigner": false}, {"name": "convertAuthorityDd", "isMut": false, "isSigner": false}, {"name": "aquaFarmProgram", "isMut": false, "isSigner": false}, {"name": "leveragedUserFarm", "isMut": true, "isSigner": false}, {"name": "leveragedFarm", "isMut": true, "isSigner": false}, {"name": "solfarmVaultProgram", "isMut": false, "isSigner": false}, {"name": "obligationVaultAddress", "isMut": true, "isSigner": false}]}], "args": [{"name": "obligationIndex", "type": "u8"}]}, {"name": "pullLpForLiquidationOrcaWithoutShares", "accounts": [{"name": "userObligationLiquidation", "isMut": false, "isSigner": false}, {"name": "withdrawFarm", "accounts": [{"name": "authority", "isMut": true, "isSigner": true}, {"name": "vaultAccount", "isMut": true, "isSigner": false}, {"name": "vaultUserAccount", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}, {"name": "vaultPda", "isMut": true, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}, {"name": "userFarmOwner", "isMut": true, "isSigner": false}, {"name": "userTransferAuthority", "isMut": false, "isSigner": false}, {"name": "userBaseTokenAccount", "isMut": true, "isSigner": false}, {"name": "userFarmTokenAccount", "isMut": true, "isSigner": false}, {"name": "userRewardTokenAccount", "isMut": true, "isSigner": false}, {"name": "globalBaseTokenVault", "isMut": true, "isSigner": false}, {"name": "farmTokenMint", "isMut": true, "isSigner": false}, {"name": "globalFarm", "isMut": true, "isSigner": false}, {"name": "orcaUserFarm", "isMut": true, "isSigner": false}, {"name": "globalRewardTokenVault", "isMut": true, "isSigner": false}, {"name": "convertAuthority", "isMut": false, "isSigner": false}, {"name": "aquaFarmProgram", "isMut": false, "isSigner": false}, {"name": "receivingTokenAccount", "isMut": true, "isSigner": false}, {"name": "clock", "isMut": false, "isSigner": false}, {"name": "leveragedUserFarm", "isMut": true, "isSigner": false}, {"name": "leveragedFarm", "isMut": true, "isSigner": false}, {"name": "solfarmVaultProgram", "isMut": false, "isSigner": false}, {"name": "obligationVaultAddress", "isMut": true, "isSigner": false}]}], "args": [{"name": "obligationIndex", "type": "u8"}]}, {"name": "pullLpForLiquidation", "accounts": [{"name": "global", "isMut": false, "isSigner": false}, {"name": "userObligationLiquidation", "isMut": false, "isSigner": false}, {"name": "withdrawFarm", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "userFarm", "isMut": true, "isSigner": false}, {"name": "obligationVaultAddress", "isMut": true, "isSigner": false}, {"name": "leveragedFarm", "isMut": false, "isSigner": false}, {"name": "authorityTokenAccount", "isMut": true, "isSigner": false}, {"name": "vault", "isMut": true, "isSigner": false}, {"name": "vaultProgram", "isMut": false, "isSigner": false}, {"name": "userBalanceAccount", "isMut": true, "isSigner": false}, {"name": "userInfoAccount", "isMut": true, "isSigner": false}, {"name": "userLpTokenAccount", "isMut": true, "isSigner": false}, {"name": "userRewardATokenAccount", "isMut": true, "isSigner": false}, {"name": "poolRewardATokenAccount", "isMut": true, "isSigner": false}, {"name": "userRewardBTokenAccount", "isMut": true, "isSigner": false}, {"name": "poolRewardBTokenAccount", "isMut": true, "isSigner": false}, {"name": "tokenProgramId", "isMut": false, "isSigner": false}, {"name": "clock", "isMut": false, "isSigner": false}, {"name": "vaultPdaAccount", "isMut": true, "isSigner": false}, {"name": "poolLpTokenAccount", "isMut": true, "isSigner": false}, {"name": "poolAuthority", "isMut": true, "isSigner": false}, {"name": "poolId", "isMut": true, "isSigner": false}, {"name": "stakeProgramId", "isMut": false, "isSigner": false}, {"name": "userBalanceMeta", "isMut": true, "isSigner": false}]}], "args": [{"name": "metaNonce", "type": "u8"}, {"name": "nonce", "type": "u8"}, {"name": "obligationIndex", "type": "u8"}]}, {"name": "removeLiquidityForLiquidation", "accounts": [{"name": "global", "isMut": false, "isSigner": false}, {"name": "userObligationLiquidation", "isMut": false, "isSigner": false}, {"name": "removeLiquidity", "accounts": [{"name": "removeLiq", "accounts": [{"name": "userFarm", "isMut": true, "isSigner": false}, {"name": "obligationVaultAddress", "isMut": true, "isSigner": false}, {"name": "leveragedFarm", "isMut": false, "isSigner": false}, {"name": "liquidityProgramId", "isMut": false, "isSigner": false}, {"name": "ammId", "isMut": true, "isSigner": false}, {"name": "ammAuthority", "isMut": true, "isSigner": false}, {"name": "ammOpenOrders", "isMut": true, "isSigner": false}, {"name": "ammQuantitiesOrTargetOrders", "isMut": true, "isSigner": false}, {"name": "lpMintAddress", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Account", "isMut": true, "isSigner": false}, {"name": "poolPcTokenAccount", "isMut": true, "isSigner": false}, {"name": "poolWithdrawQueue", "isMut": true, "isSigner": false}, {"name": "poolTempLpTokenAccount", "isMut": true, "isSigner": false}, {"name": "serumProgramId", "isMut": false, "isSigner": false}, {"name": "serumMarket", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "serum<PERSON><PERSON><PERSON>aultAccount", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "levFarmCoinTokenAccount", "isMut": true, "isSigner": false}, {"name": "levFarmPcTokenAccount", "isMut": true, "isSigner": false}, {"name": "userLpTokenAccount", "isMut": true, "isSigner": false}, {"name": "clock", "isMut": false, "isSigner": false}]}]}], "args": [{"name": "obligationIndex", "type": "u8"}]}, {"name": "removeLiquidityForLiquidationImproved", "accounts": [{"name": "global", "isMut": false, "isSigner": false}, {"name": "userObligationLiquidation", "isMut": false, "isSigner": false}, {"name": "removeLiquidity", "accounts": [{"name": "userFarm", "isMut": true, "isSigner": false}, {"name": "obligationVaultAddress", "isMut": true, "isSigner": false}, {"name": "leveragedFarm", "isMut": false, "isSigner": false}, {"name": "liquidityProgramId", "isMut": false, "isSigner": false}, {"name": "ammId", "isMut": true, "isSigner": false}, {"name": "ammAuthority", "isMut": true, "isSigner": false}, {"name": "ammOpenOrders", "isMut": true, "isSigner": false}, {"name": "ammQuantitiesOrTargetOrders", "isMut": true, "isSigner": false}, {"name": "lpMintAddress", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Account", "isMut": true, "isSigner": false}, {"name": "poolPcTokenAccount", "isMut": true, "isSigner": false}, {"name": "poolWithdrawQueue", "isMut": true, "isSigner": false}, {"name": "poolTempLpTokenAccount", "isMut": true, "isSigner": false}, {"name": "serumProgramId", "isMut": false, "isSigner": false}, {"name": "serumMarket", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "serum<PERSON><PERSON><PERSON>aultAccount", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "levFarmCoinTokenAccount", "isMut": true, "isSigner": false}, {"name": "levFarmPcTokenAccount", "isMut": true, "isSigner": false}, {"name": "userLpTokenAccount", "isMut": true, "isSigner": false}, {"name": "clock", "isMut": false, "isSigner": false}]}, {"name": "obligationLpTokenAccount", "isMut": true, "isSigner": false}], "args": [{"name": "obligationIndex", "type": "u8"}]}, {"name": "rayLiquidationSwap", "accounts": [{"name": "raySwap", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "leveragedFarm", "isMut": true, "isSigner": false}, {"name": "userFarm", "isMut": true, "isSigner": false}, {"name": "userFarmObligation", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "vault<PERSON><PERSON><PERSON>", "isMut": false, "isSigner": false}, {"name": "swapOrLiquidityProgramId", "isMut": false, "isSigner": false}, {"name": "ammId", "isMut": true, "isSigner": false}, {"name": "ammAuthority", "isMut": true, "isSigner": false}, {"name": "ammOpenOrders", "isMut": true, "isSigner": false}, {"name": "ammQuantitiesOrTargetOrders", "isMut": true, "isSigner": false}, {"name": "poolCoinTokenaccount", "isMut": true, "isSigner": false}, {"name": "poolPcTokenaccount", "isMut": true, "isSigner": false}, {"name": "serumProgramId", "isMut": false, "isSigner": false}, {"name": "serumMarket", "isMut": true, "isSigner": false}, {"name": "serumBids", "isMut": true, "isSigner": false}, {"name": "serumAsks", "isMut": true, "isSigner": false}, {"name": "serumEventQueue", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "serum<PERSON><PERSON><PERSON>aultAccount", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "coinWallet", "isMut": true, "isSigner": false}, {"name": "pcW<PERSON>t", "isMut": true, "isSigner": false}]}], "args": [{"name": "obligationIndex", "type": "u8"}]}, {"name": "splLiquidationSwapExperimental", "accounts": [{"name": "serumSwap", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "leveragedFarm", "isMut": true, "isSigner": false}, {"name": "userFarm", "isMut": true, "isSigner": false}, {"name": "userFarmObligation", "isMut": true, "isSigner": false}, {"name": "pcW<PERSON>t", "isMut": true, "isSigner": false}, {"name": "market", "accounts": [{"name": "market", "isMut": true, "isSigner": false}, {"name": "openOrders", "isMut": true, "isSigner": false}, {"name": "requestQueue", "isMut": true, "isSigner": false}, {"name": "eventQueue", "isMut": true, "isSigner": false}, {"name": "bids", "isMut": true, "isSigner": false}, {"name": "asks", "isMut": true, "isSigner": false}, {"name": "order<PERSON>ayer<PERSON>okenAccount", "isMut": true, "isSigner": false}, {"name": "coinVault", "isMut": true, "isSigner": false}, {"name": "p<PERSON><PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "vault<PERSON><PERSON><PERSON>", "isMut": false, "isSigner": false}, {"name": "coinWallet", "isMut": true, "isSigner": false}]}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}, {"name": "dexProgram", "isMut": false, "isSigner": false}, {"name": "vault<PERSON><PERSON><PERSON>", "isMut": false, "isSigner": false}]}], "args": [{"name": "obligationIndex", "type": "u8"}, {"name": "reserves", "type": {"vec": "public<PERSON>ey"}}]}, {"name": "setTemporaryLiquidationAccount", "accounts": [{"name": "global", "isMut": false, "isSigner": false}, {"name": "authority", "isMut": false, "isSigner": true}, {"name": "userObligationLiquidation", "isMut": true, "isSigner": false}, {"name": "destinationLpTokenAccount", "isMut": true, "isSigner": false}, {"name": "sourceLpTokenAccount", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "userFarmManager", "isMut": true, "isSigner": false}, {"name": "userFarmOwner", "isMut": true, "isSigner": false}], "args": [{"name": "lpTokenAccount", "type": "public<PERSON>ey"}, {"name": "index", "type": "u8"}, {"name": "farm", "type": {"defined": "Farms"}}]}, {"name": "repayLiquidationDebt", "accounts": [{"name": "global", "isMut": false, "isSigner": false}, {"name": "userObligationLiquidation", "isMut": true, "isSigner": false}, {"name": "obligationLiquidity", "accounts": [{"name": "authority", "isMut": true, "isSigner": true}, {"name": "userFarm", "isMut": true, "isSigner": false}, {"name": "userFarmObligation", "isMut": true, "isSigner": false}, {"name": "leveragedFarm", "isMut": false, "isSigner": false}, {"name": "coinSourceTokenAccount", "isMut": true, "isSigner": false}, {"name": "coinDestinationTokenAccount", "isMut": true, "isSigner": false}, {"name": "pcSourceTokenAccount", "isMut": true, "isSigner": false}, {"name": "pcDestinationTokenAccount", "isMut": true, "isSigner": false}, {"name": "coinReserveAccount", "isMut": true, "isSigner": false}, {"name": "pc<PERSON><PERSON><PERSON><PERSON><PERSON>unt", "isMut": true, "isSigner": false}, {"name": "lendingMarketAccount", "isMut": true, "isSigner": false}, {"name": "derivedLendingMarketAuthority", "isMut": true, "isSigner": false}, {"name": "clock", "isMut": false, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "lendingProgram", "isMut": false, "isSigner": false}, {"name": "lpPythPriceAccount", "isMut": false, "isSigner": false}, {"name": "coinPriceAccount", "isMut": false, "isSigner": false}, {"name": "pc<PERSON>riceAccount", "isMut": false, "isSigner": false}, {"name": "vaultAccount", "isMut": true, "isSigner": false}, {"name": "userCoinTokenAccount", "isMut": true, "isSigner": false}, {"name": "userPcTokenAccount", "isMut": true, "isSigner": false}, {"name": "positionInfoAccount", "isMut": true, "isSigner": false}]}], "args": [{"name": "reserves", "type": {"vec": "public<PERSON>ey"}}, {"name": "obligationIndex", "type": "u8"}]}, {"name": "endObligationLiquidation", "accounts": [{"name": "global", "isMut": false, "isSigner": false}, {"name": "userObligationLiquidation", "isMut": true, "isSigner": false}, {"name": "authority", "isMut": true, "isSigner": true}, {"name": "userFarm", "isMut": true, "isSigner": false}, {"name": "leveragedFarm", "isMut": false, "isSigner": false}, {"name": "lpTokenAccount", "isMut": true, "isSigner": false}, {"name": "baseTokenAccount", "isMut": true, "isSigner": false}, {"name": "quoteTokenAccount", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "liquidatorBaseAccount", "isMut": true, "isSigner": false}, {"name": "liquidatorQuoteAccount", "isMut": true, "isSigner": false}, {"name": "liquidatorLpAccount", "isMut": true, "isSigner": false}], "args": [{"name": "obligationIndex", "type": "u8"}]}, {"name": "endObligationLiquidationModified", "accounts": [{"name": "global", "isMut": false, "isSigner": false}, {"name": "userObligationLiquidation", "isMut": true, "isSigner": false}, {"name": "authority", "isMut": true, "isSigner": true}, {"name": "userFarm", "isMut": true, "isSigner": false}, {"name": "leveragedFarm", "isMut": false, "isSigner": false}, {"name": "lpTokenAccount", "isMut": true, "isSigner": false}, {"name": "baseTokenAccount", "isMut": true, "isSigner": false}, {"name": "quoteTokenAccount", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "liquidatorBaseAccount", "isMut": true, "isSigner": false}, {"name": "liquidatorQuoteAccount", "isMut": true, "isSigner": false}, {"name": "liquidatorLpAccount", "isMut": true, "isSigner": false}], "args": [{"name": "obligationIndex", "type": "u8"}]}, {"name": "topUpPositionStats", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "userFarm", "isMut": true, "isSigner": false}, {"name": "leveragedFarm", "isMut": false, "isSigner": false}, {"name": "userFarmObligation", "isMut": true, "isSigner": false}, {"name": "coinSourceTokenAccount", "isMut": true, "isSigner": false}, {"name": "coinDestinationTokenAccount", "isMut": true, "isSigner": false}, {"name": "pcSourceTokenAccount", "isMut": true, "isSigner": false}, {"name": "pcDestinationTokenAccount", "isMut": true, "isSigner": false}, {"name": "coinDepositReserveAccount", "isMut": true, "isSigner": false}, {"name": "pcDepositReserveAccount", "isMut": true, "isSigner": false}, {"name": "coinReserveLiquidityOracle", "isMut": false, "isSigner": false}, {"name": "pcReserveLiquidityOracle", "isMut": false, "isSigner": false}, {"name": "lendingMarketAccount", "isMut": false, "isSigner": false}, {"name": "derivedLendingMarketAuthority", "isMut": false, "isSigner": false}, {"name": "clock", "isMut": false, "isSigner": false}, {"name": "lendingProgram", "isMut": false, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}], "args": [{"name": "coinAmount", "type": "u64"}, {"name": "pcAmount", "type": "u64"}, {"name": "obligationIndex", "type": "u8"}]}, {"name": "topUpPositionBorrowStats", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "userFarm", "isMut": true, "isSigner": false}, {"name": "leveragedFarm", "isMut": false, "isSigner": false}, {"name": "userFarmObligation", "isMut": true, "isSigner": false}, {"name": "coinSourceTokenAccount", "isMut": true, "isSigner": false}, {"name": "coinDestinationTokenAccount", "isMut": true, "isSigner": false}, {"name": "pcSourceTokenAccount", "isMut": true, "isSigner": false}, {"name": "pcDestinationTokenAccount", "isMut": true, "isSigner": false}, {"name": "coinDepositReserveAccount", "isMut": true, "isSigner": false}, {"name": "pcDepositReserveAccount", "isMut": true, "isSigner": false}, {"name": "coinReserveLiquidityOracle", "isMut": false, "isSigner": false}, {"name": "pcReserveLiquidityOracle", "isMut": false, "isSigner": false}, {"name": "lendingMarketAccount", "isMut": false, "isSigner": false}, {"name": "derivedLendingMarketAuthority", "isMut": false, "isSigner": false}, {"name": "lendingProgram", "isMut": false, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "borrowAuthorizer", "isMut": false, "isSigner": false}, {"name": "lpPythPriceAccount", "isMut": false, "isSigner": false}, {"name": "vaultAccount", "isMut": true, "isSigner": false}, {"name": "sourceReserveLiquidityTokenAccount", "isMut": true, "isSigner": false}, {"name": "reserveLiquidityFeeReceiver", "isMut": true, "isSigner": false}], "args": [{"name": "reserves", "type": {"vec": "public<PERSON>ey"}}, {"name": "coinAmount", "type": "u64"}, {"name": "pcAmount", "type": "u64"}, {"name": "borrowAmount", "type": "u64"}, {"name": "obligationIndex", "type": "u8"}, {"name": "borrowToken", "type": "u8"}]}, {"name": "topUpPositionDualBorrowStats", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "userFarm", "isMut": true, "isSigner": false}, {"name": "leveragedFarm", "isMut": false, "isSigner": false}, {"name": "userFarmObligation", "isMut": true, "isSigner": false}, {"name": "coinSourceTokenAccount", "isMut": true, "isSigner": false}, {"name": "coinDestinationTokenAccount", "isMut": true, "isSigner": false}, {"name": "pcSourceTokenAccount", "isMut": true, "isSigner": false}, {"name": "pcDestinationTokenAccount", "isMut": true, "isSigner": false}, {"name": "coinDepositReserveAccount", "isMut": true, "isSigner": false}, {"name": "pcDepositReserveAccount", "isMut": true, "isSigner": false}, {"name": "coinReserveLiquidityOracle", "isMut": false, "isSigner": false}, {"name": "pcReserveLiquidityOracle", "isMut": false, "isSigner": false}, {"name": "lendingMarketAccount", "isMut": false, "isSigner": false}, {"name": "derivedLendingMarketAuthority", "isMut": false, "isSigner": false}, {"name": "lendingProgram", "isMut": false, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "borrowAuthorizer", "isMut": false, "isSigner": false}, {"name": "lpPythPriceAccount", "isMut": false, "isSigner": false}, {"name": "vaultAccount", "isMut": true, "isSigner": false}, {"name": "coinSourceReserveLiquidityTokenAccount", "isMut": true, "isSigner": false}, {"name": "pcSourceReserveLiquidityTokenAccount", "isMut": true, "isSigner": false}, {"name": "coinReserveLiquidityFeeReceiver", "isMut": true, "isSigner": false}, {"name": "pcReserveLiquidityFeeReceiver", "isMut": true, "isSigner": false}], "args": [{"name": "coinAmount", "type": "u64"}, {"name": "pcAmount", "type": "u64"}, {"name": "coinBorrowAmount", "type": "u64"}, {"name": "pcBorrowAmount", "type": "u64"}, {"name": "obligationIndex", "type": "u8"}, {"name": "order", "type": "u8"}]}, {"name": "forcePositionStateReset", "accounts": [{"name": "userFarmAuthority", "isMut": true, "isSigner": false}, {"name": "authority", "isMut": false, "isSigner": true}, {"name": "global", "isMut": false, "isSigner": false}, {"name": "userFarm", "isMut": true, "isSigner": false}, {"name": "leveragedFarm", "isMut": false, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}], "args": [{"name": "farm", "type": "u64"}, {"name": "newState", "type": "u64"}, {"name": "obligationIndex", "type": "u8"}]}, {"name": "forceSyncPositionTokenAmounts", "accounts": [{"name": "userFarmAuthority", "isMut": true, "isSigner": false}, {"name": "authority", "isMut": false, "isSigner": true}, {"name": "global", "isMut": false, "isSigner": false}, {"name": "userFarm", "isMut": true, "isSigner": false}, {"name": "leveragedFarm", "isMut": false, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "tempLiquidationBaseTokenAccount", "isMut": true, "isSigner": false}, {"name": "tempLiquidationQuoteTokenAccount", "isMut": true, "isSigner": false}], "args": [{"name": "farm", "type": "u64"}, {"name": "obligationIndex", "type": "u8"}]}, {"name": "moveTokensFromLevFarm", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "global", "isMut": false, "isSigner": false}, {"name": "leveragedFarm", "isMut": false, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "levFarmTokenAccount", "isMut": true, "isSigner": false}, {"name": "destinationTokenAccount", "isMut": true, "isSigner": false}], "args": [{"name": "amount", "type": "u64"}, {"name": "obligationIndex", "type": "u8"}]}, {"name": "updateLeveragedFarmAccount", "accounts": [{"name": "global", "isMut": false, "isSigner": false}, {"name": "authority", "isMut": false, "isSigner": true}, {"name": "leveragedFarm", "isMut": true, "isSigner": false}], "args": [{"name": "fieldName", "type": "string"}, {"name": "fieldValue", "type": "string"}, {"name": "raydiumUpdate", "type": {"defined": "RaydiumLevFarmUpdate"}}, {"name": "acceptDanger", "type": "bool"}]}, {"name": "emergencyUserFarmLpTokenTransfer", "accounts": [{"name": "global", "isMut": false, "isSigner": false}, {"name": "authority", "isMut": false, "isSigner": true}, {"name": "leveragedFarm", "isMut": true, "isSigner": false}, {"name": "userObligationLiquidation", "isMut": false, "isSigner": false}, {"name": "userFarm", "isMut": false, "isSigner": false}, {"name": "destinationTokenAccount", "isMut": true, "isSigner": false}, {"name": "sourceTokenAccount", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "obligationVaultAddress", "isMut": true, "isSigner": false}, {"name": "serumMarket", "isMut": false, "isSigner": false}, {"name": "solfarmVaultProgram", "isMut": false, "isSigner": false}], "args": []}, {"name": "emergencyLevFarmLpTokenTransfer", "accounts": [{"name": "global", "isMut": false, "isSigner": false}, {"name": "authority", "isMut": false, "isSigner": true}, {"name": "leveragedFarm", "isMut": true, "isSigner": false}, {"name": "userObligationLiquidation", "isMut": false, "isSigner": false}, {"name": "userFarm", "isMut": false, "isSigner": false}, {"name": "destinationTokenAccount", "isMut": true, "isSigner": false}, {"name": "sourceTokenAccount", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "obligationVaultAddress", "isMut": true, "isSigner": false}, {"name": "serumMarket", "isMut": false, "isSigner": false}, {"name": "solfarmVaultProgram", "isMut": false, "isSigner": false}], "args": []}, {"name": "swapTokensToRepayRaydiumNew", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "leveragedFarm", "isMut": true, "isSigner": false}, {"name": "userFarm", "isMut": true, "isSigner": false}, {"name": "userFarmObligation", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "vault<PERSON><PERSON><PERSON>", "isMut": false, "isSigner": false}, {"name": "swapOrLiquidityProgramId", "isMut": false, "isSigner": false}, {"name": "ammId", "isMut": true, "isSigner": false}, {"name": "ammAuthority", "isMut": true, "isSigner": false}, {"name": "ammOpenOrders", "isMut": true, "isSigner": false}, {"name": "ammQuantitiesOrTargetOrders", "isMut": true, "isSigner": false}, {"name": "poolCoinTokenaccount", "isMut": true, "isSigner": false}, {"name": "poolPcTokenaccount", "isMut": true, "isSigner": false}, {"name": "serumProgramId", "isMut": false, "isSigner": false}, {"name": "serumMarket", "isMut": true, "isSigner": false}, {"name": "serumBids", "isMut": true, "isSigner": false}, {"name": "serumAsks", "isMut": true, "isSigner": false}, {"name": "serumEventQueue", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "serum<PERSON><PERSON><PERSON>aultAccount", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "coinWallet", "isMut": true, "isSigner": false}, {"name": "pcW<PERSON>t", "isMut": true, "isSigner": false}], "args": [{"name": "obligationIndex", "type": "u8"}, {"name": "minCoinSwap", "type": "u64"}, {"name": "minPcSwap", "type": "u64"}]}, {"name": "swapTokensToRepayRaydium", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "leveragedFarm", "isMut": true, "isSigner": false}, {"name": "userFarm", "isMut": true, "isSigner": false}, {"name": "userFarmObligation", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "vault<PERSON><PERSON><PERSON>", "isMut": false, "isSigner": false}, {"name": "swapOrLiquidityProgramId", "isMut": false, "isSigner": false}, {"name": "ammId", "isMut": true, "isSigner": false}, {"name": "ammAuthority", "isMut": true, "isSigner": false}, {"name": "ammOpenOrders", "isMut": true, "isSigner": false}, {"name": "ammQuantitiesOrTargetOrders", "isMut": true, "isSigner": false}, {"name": "poolCoinTokenaccount", "isMut": true, "isSigner": false}, {"name": "poolPcTokenaccount", "isMut": true, "isSigner": false}, {"name": "serumProgramId", "isMut": false, "isSigner": false}, {"name": "serumMarket", "isMut": true, "isSigner": false}, {"name": "serumBids", "isMut": true, "isSigner": false}, {"name": "serumAsks", "isMut": true, "isSigner": false}, {"name": "serumEventQueue", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "serum<PERSON><PERSON><PERSON>aultAccount", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "coinWallet", "isMut": true, "isSigner": false}, {"name": "pcW<PERSON>t", "isMut": true, "isSigner": false}], "args": [{"name": "obligationIndex", "type": "u8"}, {"name": "minCoinSwap", "type": "u64"}, {"name": "minPcSwap", "type": "u64"}]}, {"name": "swapToRepayRaydium", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "leveragedFarm", "isMut": true, "isSigner": false}, {"name": "userFarm", "isMut": true, "isSigner": false}, {"name": "userFarmObligation", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "vault<PERSON><PERSON><PERSON>", "isMut": false, "isSigner": false}, {"name": "swapOrLiquidityProgramId", "isMut": false, "isSigner": false}, {"name": "ammId", "isMut": true, "isSigner": false}, {"name": "ammAuthority", "isMut": true, "isSigner": false}, {"name": "ammOpenOrders", "isMut": true, "isSigner": false}, {"name": "ammQuantitiesOrTargetOrders", "isMut": true, "isSigner": false}, {"name": "poolCoinTokenaccount", "isMut": true, "isSigner": false}, {"name": "poolPcTokenaccount", "isMut": true, "isSigner": false}, {"name": "serumProgramId", "isMut": false, "isSigner": false}, {"name": "serumMarket", "isMut": true, "isSigner": false}, {"name": "serumBids", "isMut": true, "isSigner": false}, {"name": "serumAsks", "isMut": true, "isSigner": false}, {"name": "serumEventQueue", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "serum<PERSON><PERSON><PERSON>aultAccount", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isMut": true, "isSigner": false}, {"name": "coinWallet", "isMut": true, "isSigner": false}, {"name": "pcW<PERSON>t", "isMut": true, "isSigner": false}], "args": [{"name": "obligationIndex", "type": "u8"}]}, {"name": "directDebtRepayment", "accounts": [{"name": "authority", "isMut": true, "isSigner": true}, {"name": "userFarm", "isMut": true, "isSigner": false}, {"name": "userFarmObligation", "isMut": true, "isSigner": false}, {"name": "leveragedFarm", "isMut": false, "isSigner": false}, {"name": "coinSourceTokenAccount", "isMut": true, "isSigner": false}, {"name": "coinDestinationTokenAccount", "isMut": true, "isSigner": false}, {"name": "pcSourceTokenAccount", "isMut": true, "isSigner": false}, {"name": "pcDestinationTokenAccount", "isMut": true, "isSigner": false}, {"name": "coinReserveAccount", "isMut": true, "isSigner": false}, {"name": "pc<PERSON><PERSON><PERSON><PERSON><PERSON>unt", "isMut": true, "isSigner": false}, {"name": "lendingMarketAccount", "isMut": true, "isSigner": false}, {"name": "derivedLendingMarketAuthority", "isMut": true, "isSigner": false}, {"name": "clock", "isMut": false, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "lendingProgram", "isMut": false, "isSigner": false}, {"name": "lpPythPriceAccount", "isMut": false, "isSigner": false}, {"name": "coinPriceAccount", "isMut": false, "isSigner": false}, {"name": "pc<PERSON>riceAccount", "isMut": false, "isSigner": false}, {"name": "vaultAccount", "isMut": true, "isSigner": false}, {"name": "positionInfoAccount", "isMut": true, "isSigner": false}], "args": [{"name": "reserves", "type": {"vec": "public<PERSON>ey"}}, {"name": "obligationIndex", "type": "u8"}, {"name": "coinRepayAmount", "type": "u64"}, {"name": "pcRepayAmount", "type": "u64"}]}], "state": {"struct": {"name": "Global", "type": {"kind": "struct", "fields": [{"name": "authority", "type": "public<PERSON>ey"}, {"name": "whitelisted", "type": {"array": ["public<PERSON>ey", 5]}}]}}, "methods": [{"name": "new", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}], "args": []}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}], "args": [{"name": "toAdd", "type": "public<PERSON>ey"}, {"name": "index", "type": "u8"}]}, {"name": "setAuthority", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}], "args": [{"name": "auth", "type": "public<PERSON>ey"}]}]}, "accounts": [{"name": "LeveragedFarm", "type": {"kind": "struct", "fields": [{"name": "global", "type": "public<PERSON>ey"}, {"name": "solfarmVaultProgram", "type": "public<PERSON>ey"}, {"name": "solfarmVaultAddress", "type": "public<PERSON>ey"}, {"name": "serumMarket", "type": "public<PERSON>ey"}, {"name": "serumRequestQueue", "type": "public<PERSON>ey"}, {"name": "serumEventQueue", "type": "public<PERSON>ey"}, {"name": "serumMarketBids", "type": "public<PERSON>ey"}, {"name": "serumMarketAsks", "type": "public<PERSON>ey"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "public<PERSON>ey"}, {"name": "serum<PERSON><PERSON><PERSON>aultAccount", "type": "public<PERSON>ey"}, {"name": "serumFeeRecipient", "type": "public<PERSON>ey"}, {"name": "serumDexProgram", "type": "public<PERSON>ey"}, {"name": "serumBaseReferralAccount", "type": "public<PERSON>ey"}, {"name": "serumQuoteReferralAccount", "type": "public<PERSON>ey"}, {"name": "serumOpenOrdersAccount", "type": "public<PERSON>ey"}, {"name": "raydiumLpMintAddress", "type": "public<PERSON>ey"}, {"name": "raydiumAmmId", "type": "public<PERSON>ey"}, {"name": "raydiumAmmAuthority", "type": "public<PERSON>ey"}, {"name": "raydiumAmmOpenOrders", "type": "public<PERSON>ey"}, {"name": "raydiumAmmQuantitiesOrTargetOrders", "type": "public<PERSON>ey"}, {"name": "raydiumLiquidityProgram", "type": "public<PERSON>ey"}, {"name": "ray<PERSON><PERSON>oin<PERSON><PERSON>unt", "type": "public<PERSON>ey"}, {"name": "raydiumPcAccount", "type": "public<PERSON>ey"}, {"name": "raydiumPoolTempTokenAccount", "type": "public<PERSON>ey"}, {"name": "raydiumPoolWithdrawQueue", "type": "public<PERSON>ey"}, {"name": "lendingMarket", "type": "public<PERSON>ey"}, {"name": "lendingProgram", "type": "public<PERSON>ey"}, {"name": "baseMint", "type": "public<PERSON>ey"}, {"name": "quoteMint", "type": "public<PERSON>ey"}, {"name": "baseReserve", "type": "public<PERSON>ey"}, {"name": "quote<PERSON><PERSON><PERSON>", "type": "public<PERSON>ey"}, {"name": "baseTokenAccount", "type": "public<PERSON>ey"}, {"name": "quoteTokenAccount", "type": "public<PERSON>ey"}, {"name": "lpDecimals", "type": "u8"}, {"name": "baseDecimals", "type": "u8"}, {"name": "quoteDecimals", "type": "u8"}, {"name": "farmType", "type": {"defined": "Farms"}}, {"name": "initialized", "type": "bool"}, {"name": "supportsFee", "type": "bool"}, {"name": "feeReceiver", "type": "public<PERSON>ey"}, {"name": "lpTokenPriceAccount", "type": "public<PERSON>ey"}, {"name": "coinPriceAccount", "type": "public<PERSON>ey"}, {"name": "pc<PERSON>riceAccount", "type": "public<PERSON>ey"}, {"name": "coinReserveLiquidityFeeReceiver", "type": "public<PERSON>ey"}, {"name": "pcReserveLiquidityFeeReceiver", "type": "public<PERSON>ey"}, {"name": "borrowAuthorizer", "type": "public<PERSON>ey"}, {"name": "borrowAuthorizerNonce", "type": "u8"}, {"name": "nonce", "type": "u8"}, {"name": "buySlip", "type": "u64"}, {"name": "sellSlip", "type": "u64"}, {"name": "buffer", "type": {"array": ["u8", 304]}}]}}, {"name": "UserFarm", "type": {"kind": "struct", "fields": [{"name": "authority", "type": "public<PERSON>ey"}, {"name": "leveragedFarm", "type": "public<PERSON>ey"}, {"name": "userFarmNumber", "type": "u8"}, {"name": "numberOfObligations", "type": "u8"}, {"name": "numberOfUserFarms", "type": "u8"}, {"name": "nonce", "type": "u8"}, {"name": "obligations", "type": {"array": [{"defined": "Obligation"}, 3]}}]}}, {"name": "ObligationLiquidationAccount", "type": {"kind": "struct", "fields": [{"name": "userFarm", "type": "public<PERSON>ey"}, {"name": "obligation", "type": "public<PERSON>ey"}, {"name": "obligationIndex", "type": "u8"}, {"name": "lpTokenAccount", "type": "public<PERSON>ey"}, {"name": "baseTokenAccount", "type": "public<PERSON>ey"}, {"name": "quoteTokenAccount", "type": "public<PERSON>ey"}, {"name": "coinReturn", "type": "u64"}, {"name": "pcReturn", "type": "u64"}, {"name": "<PERSON><PERSON><PERSON>", "type": {"defined": "Farms"}}, {"name": "leveragedFarmAccount", "type": "public<PERSON>ey"}, {"name": "doubleDipLpTokenAccount", "type": "public<PERSON>ey"}, {"name": "partialLiq", "type": "bool"}, {"name": "buffer", "type": {"array": ["u8", 94]}}]}}, {"name": "PositionInfo", "type": {"kind": "struct", "fields": [{"name": "nonce", "type": "u8"}, {"name": "coinDeposit", "type": "u64"}, {"name": "pcDeposit", "type": "u64"}, {"name": "withdraw<PERSON>oin", "type": "u64"}, {"name": "withdrawPc", "type": "u64"}, {"name": "openTime", "type": "u32"}, {"name": "useless<PERSON>ield", "type": "u16"}, {"name": "repayCoinPercent", "type": "u16"}, {"name": "repayPcPercent", "type": "u16"}, {"name": "bufferCoin", "type": "u64"}, {"name": "bufferPc", "type": "u64"}, {"name": "depositLp", "type": "u64"}, {"name": "withdrawLp", "type": "u64"}, {"name": "openCoinCost", "type": "u64"}, {"name": "openPcCost", "type": "u64"}, {"name": "withdrawCoinCost", "type": "u64"}, {"name": "withdrawPcCost", "type": "u64"}, {"name": "coinSwap", "type": "i64"}, {"name": "pcSwap", "type": "i64"}, {"name": "coinDepositLp", "type": "u64"}, {"name": "pcDepositLp", "type": "u64"}, {"name": "coinWithdrawLp", "type": "u64"}, {"name": "pcWithdrawLp", "type": "u64"}, {"name": "settleMethod", "type": "u8"}, {"name": "withdrawPercent", "type": "u32"}, {"name": "oldAccount", "type": "bool"}, {"name": "closeMethod", "type": "u8"}, {"name": "buffer", "type": {"array": ["u8", 130]}}]}}], "types": [{"name": "Farms", "type": {"kind": "enum", "variants": [{"name": "RayUsdcVault"}, {"name": "RaySol<PERSON>ault"}, {"name": "RayUsdtVault"}, {"name": "RaySrmVault"}, {"name": "MerUsdcVault"}, {"name": "MediaUsdcVault"}, {"name": "CopeUsdcVault"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "StepUsdcVault"}, {"name": "RopeUsdcVault"}, {"name": "AlephUsdcVault"}, {"name": "TulipUsdcVault"}, {"name": "SnyUsdcVault"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "SlrsUsdcVault"}, {"name": "SamoR<PERSON><PERSON>ault"}, {"name": "LikeUsdcVault"}, {"name": "OrcaUsdcVault"}, {"name": "OrcaSolVault"}, {"name": "AtlasUsdcVault"}, {"name": "AtlasRayVault"}, {"name": "PolisUsdcVault"}, {"name": "PolisRayVault"}, {"name": "EthUsdcOrcaVault"}, {"name": "SolUsdcOrcaVault"}, {"name": "SolUsdtOrcaVault"}, {"name": "EthSolOrcaVault"}, {"name": "AtlasUsdcOrcaVault"}, {"name": "PolisUsdcOrcaVault"}, {"name": "whEthUsdcOrcaVault"}, {"name": "whEthSolOrcaVault"}, {"name": "mSolUsdcRayVault"}, {"name": "mSolUsdtRayVault"}, {"name": "EthMSolRayVault"}, {"name": "BtcMSolRayVault"}, {"name": "mSolRayRayVault"}, {"name": "SamoRayRayVault"}, {"name": "SamoUsdcOrcaVault"}, {"name": "SrmUsdcRayVault"}, {"name": "whEthUsdcRayVault"}, {"name": "whEthSolRayVault"}, {"name": "weSushiUsdcRayVault"}, {"name": "weUniUsdcRayVault"}, {"name": "StarsUsdcRayVault"}, {"name": "weDydxUsdcRayVault"}, {"name": "GeneUsdcRayVault"}, {"name": "GeneRayRay<PERSON>ault"}, {"name": "DflUsdcRayVault"}, {"name": "CaveUsdcRayVault"}, {"name": "wbWBNBUsdcRayVault"}, {"name": "SolUsdcRayVault"}, {"name": "SolUsdtRayVault"}, {"name": "RealUsdcRayVault"}, {"name": "PrsmUsdcRayVault"}, {"name": "MbsUsdcRayVault"}, {"name": "ShdwUsdcOrcaVault"}, {"name": "ShdwSolOrcaVault"}, {"name": "BasisUsdcOrcVault"}, {"name": "stSolUsdcOrcaVault"}, {"name": "stSolUsdcRayVault"}, {"name": "stSolUsdtRayVault"}, {"name": "BtcstSolRayVault"}, {"name": "EthstSolRayVault"}, {"name": "stSolwUstOrcaVault"}, {"name": "GstUsdcOrcaVault"}, {"name": "ZbcUsdcRayVault"}, {"name": "wAlephUsdcRayVault"}, {"name": "SlclUsdcRayVault"}, {"name": "PrismUsdcRayVault"}, {"name": "SlcUsdcRayVault"}, {"name": "HbbUsdhRayVault"}, {"name": "UsdhUsdcRayVault"}, {"name": "sRlySolOrcaVault"}, {"name": "HawkUsdcRayVault"}, {"name": "GmtUsdcOrcaVault"}, {"name": "RaywhEthRayVault"}, {"name": "Unknown"}]}}, {"name": "Position", "type": {"kind": "enum", "variants": [{"name": "Opening"}, {"name": "Swapped"}, {"name": "AddedLiquidity"}, {"name": "Opened"}, {"name": "Withdrawing"}, {"name": "RemovedLiquidity"}, {"name": "SwappedForRepaying"}, {"name": "Repaid"}, {"name": "Withdrawn"}, {"name": "Closing"}, {"name": "ClosingAndExiting"}, {"name": "Closed"}, {"name": "ExitingAndLiquidated"}, {"name": "Liquidated"}, {"name": "TopUp"}, {"name": "Borrowed"}, {"name": "TopUpSwapped"}, {"name": "TopUpAddedLiquidity"}, {"name": "DepositedOrcaAquaFarm"}, {"name": "WithdrawnOrcaDoubleDip"}, {"name": "SwappedForLiquidation"}, {"name": "RepaidForLiquidation"}, {"name": "DDPulledForLiquidation"}, {"name": "LPPulledForLiquidation"}, {"name": "RemovedLiquidityForLiquidation"}]}}, {"name": "Obligation", "type": {"kind": "struct", "fields": [{"name": "obligationAccount", "type": "public<PERSON>ey"}, {"name": "coinAmount", "type": "u64"}, {"name": "pcAmount", "type": "u64"}, {"name": "depositedLpTokens", "type": "u64"}, {"name": "positionState", "type": {"defined": "Position"}}]}}, {"name": "BorrowedAsset", "type": {"kind": "struct", "fields": [{"name": "reserve", "type": "public<PERSON>ey"}, {"name": "amount", "type": "u64"}]}}, {"name": "DepositedAsset", "type": {"kind": "struct", "fields": [{"name": "reserve", "type": "public<PERSON>ey"}, {"name": "amount", "type": "u64"}, {"name": "returnAmount", "type": "u64"}, {"name": "penaltyAmount", "type": "u64"}]}}, {"name": "AddLiquidityArgs", "type": {"kind": "struct", "fields": [{"name": "coinTakepnl", "type": "u64"}, {"name": "pcTakepnl", "type": "u64"}]}}, {"name": "Side", "type": {"kind": "enum", "variants": [{"name": "Bid"}, {"name": "Ask"}]}}, {"name": "DepositFarmArgs", "type": {"kind": "struct", "fields": [{"name": "nonce", "type": "u8"}, {"name": "metaNonce", "type": "u8"}]}}, {"name": "WithdrawFarmArgs", "type": {"kind": "struct", "fields": [{"name": "metaNonce", "type": "u8"}, {"name": "nonce", "type": "u8"}]}}, {"name": "HarvestFarmTulipsArgs", "type": {"kind": "struct", "fields": [{"name": "nonce", "type": "u8"}, {"name": "metaNonce", "type": "u8"}, {"name": "rewardNonce", "type": "u8"}]}}, {"name": "RaydiumLevFarmUpdate", "type": {"kind": "struct", "fields": [{"name": "raydiumLpMintAddress", "type": "public<PERSON>ey"}, {"name": "raydiumAmmId", "type": "public<PERSON>ey"}, {"name": "raydiumAmmAuthority", "type": "public<PERSON>ey"}, {"name": "raydiumAmmOpenOrders", "type": "public<PERSON>ey"}, {"name": "raydiumAmmQuantitiesOrTargetOrders", "type": "public<PERSON>ey"}, {"name": "raydiumLiquidityProgram", "type": "public<PERSON>ey"}, {"name": "ray<PERSON><PERSON>oin<PERSON><PERSON>unt", "type": "public<PERSON>ey"}, {"name": "raydiumPcAccount", "type": "public<PERSON>ey"}, {"name": "raydiumPoolTempTokenAccount", "type": "public<PERSON>ey"}, {"name": "raydiumPoolWithdrawQueue", "type": "public<PERSON>ey"}]}}, {"name": "DepositOrcaVaultArgs", "type": {"kind": "struct", "fields": [{"name": "accountNonce", "type": "u8"}]}}, {"name": "OrcaAddLiqQueueArgs", "type": {"kind": "struct", "fields": [{"name": "accountNonce", "type": "u8"}, {"name": "obligationIndex", "type": "u8"}]}}, {"name": "Liq<PERSON><PERSON><PERSON>", "type": {"kind": "struct", "fields": [{"name": "reserves", "type": {"vec": "public<PERSON>ey"}}, {"name": "obligationIndex", "type": "u8"}, {"name": "temporaryAccountNonce", "type": "u8"}]}}], "errors": [{"code": 100, "name": "Unauthorized", "msg": "unauthorized access"}, {"code": 101, "name": "TooManyReserves", "msg": "market has too many reserves"}, {"code": 102, "name": "InvalidReserveCount", "msg": "given reserves does not match remaining accounts length"}, {"code": 103, "name": "NoReserveSpaceLeft", "msg": "obligation has no reserve space available for action"}, {"code": 104, "name": "Unknown<PERSON><PERSON><PERSON>", "msg": "unknown reserve given"}, {"code": 105, "name": "InvalidReferralAccount", "msg": "invalid referral account given"}, {"code": 106, "name": "InvalidFarmProgram", "msg": "invalid farm program given"}, {"code": 107, "name": "InvalidDestinationCollateralTokenAccount", "msg": "invalid destination collateral token account given"}, {"code": 108, "name": "InvalidSourceCollateralTokenAccount", "msg": "invalid source collateral token account given"}, {"code": 109, "name": "InvalidLendingMarket", "msg": "invalid lending market given"}, {"code": 110, "name": "InvalidLendingProgram", "msg": "invalid lending program given"}, {"code": 111, "name": "InvalidUserFarmManager", "msg": "user farm manager is invalid"}, {"code": 112, "name": "InvalidLeveragedFarm", "msg": "invalid leveraged farm given"}, {"code": 113, "name": "InvalidObligationAccount", "msg": "invalid obligation account given"}, {"code": 114, "name": "InvalidSourceLiquidityTokenAccount", "msg": "invalid source liquidity token account given"}, {"code": 115, "name": "InvalidPCWallet", "msg": "invalid pc wallet given"}, {"code": 116, "name": "InvalidOrder<PERSON>ayer", "msg": "invalid order payer account given"}, {"code": 117, "name": "InvalidCoinWallet", "msg": "invalid coin wallet given"}, {"code": 118, "name": "InvalidSerumOpenOrders", "msg": "invalid serum open orders account given"}, {"code": 119, "name": "InvalidPCVault", "msg": "invalid serum pc vault given"}, {"code": 120, "name": "InvalidCoin<PERSON>ault", "msg": "invalid serum coin vault given"}, {"code": 121, "name": "InvalidAsks", "msg": "invalid serum asks given"}, {"code": 122, "name": "InvalidBids", "msg": "invalid serum bids given"}, {"code": 123, "name": "InvalidMarket", "msg": "invalid serum market given"}, {"code": 124, "name": "InvalidRequestQueue", "msg": "invalid serum request queue given"}, {"code": 125, "name": "InvalidEventQueue", "msg": "invalid serum event queue given"}, {"code": 126, "name": "InvalidOpenOrders", "msg": "invalid serum open orders account"}, {"code": 127, "name": "InvalidAmmId", "msg": "invalid raydium amm id"}, {"code": 128, "name": "InvalidAmmAuthority", "msg": "invalid raydium amm authority"}, {"code": 129, "name": "InvalidAmmOpenOrders", "msg": "invalid raydium amm open orders"}, {"code": 130, "name": "InvalidAmmQuantitiesOrTargetOrders", "msg": "invalid raydium amm quantities or target ordres"}, {"code": 131, "name": "InvalidLpMint", "msg": "invalid lp token mint"}, {"code": 132, "name": "InvalidFarmTokenAccount", "msg": "invalid farm coin or farm pc token account"}, {"code": 133, "name": "InvalidLpToken", "msg": "invalid lp token account given"}, {"code": 134, "name": "InvalidLiquidityProgram", "msg": "invalid liquidity program given"}, {"code": 135, "name": "InvalidPoolTokenAccount", "msg": "invalid pool coin or pc token account"}, {"code": 136, "name": "InvalidPoolWithdrawQueue", "msg": "invalid pool withdraw queue given"}, {"code": 137, "name": "InvalidSerumProgram", "msg": "invalid serum dex program given"}, {"code": 138, "name": "InvalidUserVaultAccount", "msg": "invalid solfarm vault user balance or metadata account"}, {"code": 139, "name": "AlreadyInitialized", "msg": "leveraged farm is already initialized"}, {"code": 140, "name": "Uninitialized", "msg": "leveraged farm is uninitialized"}, {"code": 141, "name": "InvalidBaseMint", "msg": "invalid base token mint"}, {"code": 142, "name": "InvalidQuoteMint", "msg": "invalid quote mint"}, {"code": 143, "name": "InvalidTokenOwner", "msg": "invalid token owner"}, {"code": 144, "name": "InvalidReferral", "msg": "referral account is none or incorrect account"}, {"code": 145, "name": "TooManyObligations", "msg": "too many obligations already created, please use a different account"}, {"code": 146, "name": "InvalidObligationIndex", "msg": "obligation index is invalid"}, {"code": 147, "name": "ObligationIsClosing", "msg": "obligation account is closing, liquidation in process"}, {"code": 148, "name": "ObligationIsOpen", "msg": "obligation must be closing to be liquidated"}, {"code": 149, "name": "InvalidTokenAccount", "msg": "invalid token account given"}, {"code": 150, "name": "ObligationIsLiquidated", "msg": "the obligation account has been liquidated please close"}, {"code": 151, "name": "ObligationIsNotLiquidated", "msg": "obligation account must be liquidated to proceed"}, {"code": 152, "name": "UnhealthyObligation", "msg": "obligation is unhealthy"}, {"code": 153, "name": "ObligationIsExiting", "msg": "obligation is already exiting"}, {"code": 154, "name": "InvalidPythPriceAccount", "msg": "invalid pyth price account given"}, {"code": 155, "name": "InvalidAccountOwner", "msg": "Input account owner is not the program address"}, {"code": 156, "name": "InvalidAccountInput", "msg": "Invalid account input"}, {"code": 157, "name": "ReserveStale", "msg": "Reserve state needs to be refreshed"}, {"code": 158, "name": "HealthyAccount", "msg": "Trying to start liquidation on healthy account"}, {"code": 159, "name": "InsufficientTokenAmount", "msg": "zero base and quote tokens in obligation"}, {"code": 160, "name": "DepositTooLowOrTooHigh", "msg": "deposit is below the minimum required amount or above the limit"}, {"code": 161, "name": "NotEnoughCollateral", "msg": "not enough collateral to borrow this amount"}, {"code": 162, "name": "InvalidOracleConfig", "msg": "Input oracle config is invalid"}, {"code": 163, "name": "InvalidPositionState", "msg": "Invalid position state"}, {"code": 164, "name": "InvalidFeeReceiver", "msg": "Invalid fee receier"}, {"code": 165, "name": "Invalid<PERSON>aultAccount", "msg": "Invalid vault account"}, {"code": 166, "name": "InvalidProdBool", "msg": "Flip prod bool"}, {"code": 167, "name": "HighSpread", "msg": "High Spread"}, {"code": 168, "name": "HighSlippage", "msg": "High Slippage"}, {"code": 169, "name": "InvalidWithdrawAmount", "msg": "Invalid <PERSON>draw Amount"}, {"code": 170, "name": "InvalidBorrowToken", "msg": "Invalid <PERSON> Token"}]}