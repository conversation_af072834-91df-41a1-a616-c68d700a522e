#!/usr/bin/env python
import websocket
import json
import threading
import time
from modules.config import *
import modules.config
import logging
import sys



def send_request(ws):

    print(f"pump_address: {sys.argv[1]}")
    request = {
        "jsonrpc": "2.0",
        "id": "1",
        "method": "blockSubscribe",
        "params": [
            {
            "mentionsAccountOrProgram": sys.argv[1],
            },
            {
            "commitment": "confirmed",
            "encoding": "jsonParsed",
            "showRewards": True,
            "transactionDetails": "full",
            "maxSupportedTransactionVersion": 0
            }
        ]
        }
    ws.send(json.dumps(request))

# Function to send a ping to the WebSocket server
def start_ping(ws):
    def ping():
        while True:
            if ws.sock and ws.sock.connected:
                ws.send('{"jsonrpc": "2.0", "method": "ping"}')
                print('Ping sent')
            time.sleep(30)  # Ping every 30 seconds
    threading.Thread(target=ping).start()

# Define WebSocket event handlers
def on_open(ws):
    print('WebSocket is open')
    send_request(ws)  # Send a request once the WebSocket is open
    start_ping(ws)    # Start sending pings

def on_message(ws, message):

    print(json.loads(message)["params"]["result"]["value"]["block"]["transactions"][0]["transaction"]["signatures"][0])
                  

def on_error(ws, error):
    print('WebSocket error:', error)

def on_close(ws, close_status_code, close_msg):
    print('WebSocket is closed')

# Create a WebSocket connection


def ws_run():
  
    

    ws_url="wss://go.getblock.io/9c188ee75a47487ca8f5508c18ae6cdd"
    ws = websocket.WebSocketApp(ws_url,
                                on_open=on_open,
                                on_message=on_message,
                                on_error=on_error,
                                on_close=on_close)
    ws.run_forever()

ws_run()




