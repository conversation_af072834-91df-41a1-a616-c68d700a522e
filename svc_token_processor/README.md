# Token processor

Logic

**INPUT**: *token address*

1. Get all transaction from token address every 100 tranactons. going backwards from position to start
    1. In parallel another process checkingt every second only new tranaction  (old)<--|--> (new)
    2. process transactions store to separate table     token_tx  - tx info , calculate price
        3. extract new addresses store toseparate table token_addresses - account info ( balance ) owning this tokens 

2. Run process to get balances for account.( token amount and sol amountl, age of account ) 
    live calculate pnl, tx amount , 

2. Draw tranaction data on chart


use event driven 
