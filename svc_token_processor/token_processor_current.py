#!/usr/bin/env python3

from datetime import datetime, timedelta
from datetime import time
import sys
import time,os
import modules.transfer as transfer
import modules.db as db
from mysql.connector import pooling
import modules.utils as utils
from modules.config import *
import modules.token as token
import redis
from modules.token import get_token_supply
from modules.config import token_supply
from modules.config import pump_address
from modules.config import token_address
from modules.config import redis_connection
from modules.redismq import get_redis_latest_sig
import threading
from modules.token_updates import ws_run
import modules.config


log=utils.get_logger()


MYSQL_SERVER=os.getenv('MYSQL_SERVER')
MYSQL_USER=os.getenv('MYSQL_USER')
MYSQL_PASSWORD=os.getenv('MYSQL_PASSWORD')
MYSQL_DB=os.getenv('MYSQL_DB')
MYSQL_POOL=os.getenv('MYSQL_POOL')
TOKEN_SLEEP=os.getenv('TOKEN_SLEEP')

TOKEN_ADDRESS=os.getenv('TOKEN_ADDRESS')
TOKEN_BOOTSTRAP=bool(os.getenv('TOKEN_BOOTSTRAP'))

modules.config.redis_connection = redis.Redis(host='**************', port=6379, db=0,password="pump2pump")

import argparse
parser = argparse.ArgumentParser(description='Fetch token price')
parser.add_argument('--token', type=str, help='Token address')
parser.add_argument('--mint', type=str, help='Token address')
parser.add_argument('--drop', type=bool, help='Drop token data')
args = parser.parse_args()

if __name__ == "__main__":

     # my account

# must pickup tx from kafka 


    # log.info("Creating mysql pool")
    # pool = pooling.MySQLConnectionPool(
    #     pool_name=MYSQL_POOL, 
    #     user=MYSQL_USER,
    #     password=MYSQL_PASSWORD,
    #     host=MYSQL_SERVER,
    #     db=MYSQL_DB)

    if  TOKEN_BOOTSTRAP == True:
        #db.delete_all_token_data(pool,token_address)
        pass

    TOKEN_ADDRESS="5qHRHMmy9uBvcmcG2KFVB34hXuwkLYNxFRunJmdypump"
    TOKEN_ADDRESS="3JAiPhE1PKAc5z9WBpYqT8vxbKeLt2sVAYuCtZ8Dpump"
  


    TOKEN_ADDRESS=args.token
    MINT_ADDRESS=args.mint
    # tsupply=get_token_supply(TOKEN_ADDRESS)
    # token_supply[TOKEN_ADDRESS]=tsupply
    
    # print(f"Token supply: {token_supply[TOKEN_ADDRESS]}") 

    #if TOKEN_BOOTSTRAP == True:
    if args.drop == True:
        modules.config.redis_connection.delete(f'trades:{MINT_ADDRESS}')

    #print(get_redis_latest_sig(redis_connection,TOKEN_ADDRESS,"lpush"))
    
    modules.config.pump_address=MINT_ADDRESS
    modules.config.token_address=TOKEN_ADDRESS
    ws_thread = threading.Thread(target=ws_run)
    ws_thread.start()

    #while True:
    #while True:
    #    #try:
    #   token.fetch_all_transactions_to_sig(redis_connection,TOKEN_ADDRESS,"rpush")
    #    time.sleep(1)
