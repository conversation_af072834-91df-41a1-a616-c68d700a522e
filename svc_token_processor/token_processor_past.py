#!/usr/bin/env python3

from datetime import datetime, timedelta
from datetime import time
import sys
import time,os
import modules.transfer as transfer
import modules.db as db
from mysql.connector import pooling
import modules.utils as utils
from modules.config import *
import modules.token as token
import redis
from modules.token import get_token_supply
from modules.config import token_supply
from modules.redismq import get_redis_latest_sig
import modules.token_past as token_past
import modules.config


log=utils.get_logger()


MYSQL_SERVER=os.getenv('MYSQL_SERVER')
MYSQL_USER=os.getenv('MYSQL_USER')
MYSQL_PASSWORD=os.getenv('MYSQL_PASSWORD')
MYSQL_DB=os.getenv('MYSQL_DB')
MYSQL_POOL=os.getenv('MYSQL_POOL')
TOKEN_SLEEP=os.getenv('TOKEN_SLEEP')

MYSQL_SERVER="pumpfun.mooo.com"
MYSQL_PORT=3306
MYSQL_USER="pump2"
MYSQL_PASSWORD="pump2"
MYSQL_DB="pump"
MYSQL_POOL="mypool"
MYSQL_POOL_SIZE="10"


pool = pooling.MySQLConnectionPool(
    pool_name=MYSQL_POOL, 
    user=MYSQL_USER,
    password=MYSQL_PASSWORD,
    host=MYSQL_SERVER,
    db=MYSQL_DB)

modules.config.pool=pool


TOKEN_ADDRESS=os.getenv('TOKEN_ADDRESS')
TOKEN_BOOTSTRAP=bool(os.getenv('TOKEN_BOOTSTRAP'))

#redis_connection = redis.Redis(host='**************', port=6379, db=0,password="pump2pump")

modules.config.redis_connection = redis.Redis(host='**************', port=6379, db=0,password="pump2pump")


import argparse
parser = argparse.ArgumentParser(description='Fetch token price')
parser.add_argument('--token', type=str, help='Token address')
parser.add_argument('--mint', type=str, help='MINT address')
parser.add_argument('--drop', type=bool, help='Drop token data')
args = parser.parse_args()

if __name__ == "__main__":

     # my account

# must pickup tx from kafka 


    # log.info("Creating mysql pool")
    # pool = pooling.MySQLConnectionPool(
    #     pool_name=MYSQL_POOL, 
    #     user=MYSQL_USER,
    #     password=MYSQL_PASSWORD,
    #     host=MYSQL_SERVER,
    #     db=MYSQL_DB)

    if  TOKEN_BOOTSTRAP == True:
        #db.delete_all_token_data(pool,token_address)
        pass

   
    TOKEN_ADDRESS=args.token
    MINT_ADDRESS=args.mint

    modules.config.pump_address=MINT_ADDRESS
    modules.config.token_address=TOKEN_ADDRESS

    #token_supply[TOKEN_ADDRESS]=get_token_supply(TOKEN_ADDRESS)
    #print(f"Token supply: {token_supply[TOKEN_ADDRESS]}") 

    if args.drop == True:
        modules.config.redis_connection.delete(f'trades:{MINT_ADDRESS}')

    #need mysql pool
    token_past.batchTransactions(MINT_ADDRESS,TOKEN_ADDRESS)

    sys.exit()
    #while True:
    if True:
        #try:
        token.fetch_all_transactions_to_sig(redis_connection,TOKEN_ADDRESS,"lpush")

        #except Exception as e:
        #    log.error("Error processing token")
        #    log.error(e)
