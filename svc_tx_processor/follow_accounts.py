#!/usr/bin/env python3
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor
from mysql.connector import pooling
from mysql.connector import Error
from modules.transfer import fetch_last_account
import time
from modules.config import *

# Database credentials
MYSQL_SERVER = "kroocoin.xyz"
MYSQL_PORT = 3306
MYSQL_USER = "pump2"
MYSQL_PASSWORD = "pump2"
MYSQL_DB = "pump"
MYSQL_POOL_NAME = "mypool"
MYSQL_POOL_SIZE = 10

# Set up the MySQL connection pool
pool = pooling.MySQLConnectionPool(
    pool_name=MYSQL_POOL_NAME,
    pool_size=MYSQL_POOL_SIZE,
    pool_reset_session=True,
    host=MYSQL_SERVER,
    port=MYSQL_PORT,
    user=MYSQL_USER,
    password=MYSQL_PASSWORD,
    database=MYSQL_DB
)

def get_follow_zero_accounts(pool):
    """Fetch accounts with zero follow status from the database."""
    try:
        connection = pool.get_connection()
        cursor = connection.cursor()
        query = "SELECT master,address FROM accounts WHERE balance = 0 and enabled = 4;"
        cursor.execute(query)
        results = cursor.fetchall()
        return results
    except Error as e:
        print("Error fetching data from MySQL:", e)
        return []
    finally:
        if connection:
            connection.close()

def process_account(pool, mint, address):
    """Process each account by fetching the last account info."""
    try:
        print(f"Processing: {address}")
        fetch_last_account(pool, mint, address)
    except Exception as e:
        print(f"Error processing {address}: {e}")

if __name__ == "__main__":
    # Fetch accounts to process
    while True:
        time.sleep(0.5) # Sleep for 100 seconds
        accounts = get_follow_zero_accounts(pool)

        if not accounts:
            print("No accounts to process.")
        else:
            # Use ThreadPoolExecutor to process accounts in parallel
            max_threads = 10
            with ThreadPoolExecutor(max_threads) as executor:
                futures = [executor.submit(process_account, pool, mint, address) for mint, address in accounts]

                # Wait for all threads to complete
                for future in futures:
                    try:
                        future.result()
                    except Exception as e:
                        print("Error in thread execution:", e)

            print("Processing completed.")
