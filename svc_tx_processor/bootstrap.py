#!/usr/bin/env python3

from datetime import datetime, timedelta
from datetime import time
import sys
import time,os
import modules.transfer as transfer
import modules.db as db
from mysql.connector import pooling
import modules.utils as utils
from modules.config import *
from neo4j import GraphDatabase
from modules.neo import Neo4jClient
import modules.neo as neo
import dotenv

dotenv.load_dotenv()

log=utils.get_logger("bootstrap","bootstrap.log")

# api_key = [
#         "69463031-b3c1-455a-addd-0a8d100dca90",
#         "c29c0ee4-6308-4789-a694-e2dfa40ad7c7",
#         "814e2855-a7f8-4d84-b0bc-82cedf5694e3",
#         "e10ad31d-205d-4bd2-ad35-e261cd901f38",
#         "6d8bfbc2-44f3-4c30-b31e-c1322741dce8",
#         "4a53c62c-2be7-4030-8e51-7f4867bf16c4",
#         "f5678270-3a1a-4868-ba6a-1d70a0891966",
#         "8c5ca67a-9aa2-4986-905f-2a95b1927b34",
#         "12728bae-550f-4f59-a42c-94b89d9e86b8",
#         "0c496773-5e98-428a-90da-4b24095db327"]

neo4j_server = os.getenv('NEO4J_SERVER')
neo4j_user = os.getenv('NEO4J_USER')
neo4j_password = os.getenv('NEO4J_PASSWORD')


MYSQL_SERVER=os.getenv('MYSQL_SERVER')
MYSQL_USER=os.getenv('MYSQL_USER')
MYSQL_PASSWORD=os.getenv('MYSQL_PASSWORD')
MYSQL_DB=os.getenv('MYSQL_DB')
MYSQL_POOL=os.getenv('MYSQL_POOL')
TX_SLEEP=os.getenv('TX_SLEEP')
TX_SLEEP=0
#BOOTSTRAP_ADDRESS=os.getenv('BOOTSTRAP_ADDRESS')

BOOTSTRAP=bool(os.getenv('BOOTSTRAP'))

MIN_BALANCE_CHANGE=os.getenv('MIN_BALANCE_CHANGE')

BOOTSTRAP_ADDRESS=master_address
MAX_HOPS=max_hops


## TODO : aDD MAIN ACCOUNT STATUS SOMEWHERE #####

if __name__ == "__main__":

     # my account
    log.info("Creating mysql pool")
    pool = pooling.MySQLConnectionPool(
        pool_name=MYSQL_POOL, 
        user=MYSQL_USER,
        password=MYSQL_PASSWORD,
        host=MYSQL_SERVER,
        db=MYSQL_DB)
    log.info("Creating Neo4J connection")
    neo_client = Neo4jClient(neo4j_server, neo4j_user,neo4j_password)
    BOOTSTRAP=True
    if  BOOTSTRAP == True:
        db.delete_all_tables(pool)
        #load all accounts, last sig, last balance
        account_address=BOOTSTRAP_ADDRESS
        log.info("Drop all nodes")
        neo_client.print_nodes(neo_client)
        neo_client.drop_all()
        log.info("Adding master node to neo")
        address1 = neo_client.add_unique_node("Address", {"name": account_address, "balance": 0, "total_tx": 20, "role": "user", "group": "B", "age": 30, "type": "wallet"})

        log.info("Load existing accounts and tokens to memory")
        accounts,tokens=db.load_account_address_list_from_db(pool)


        log.info("load transactions for account")
        
        txs,last_sig=transfer.fetch_all_transactions_to_sig(account_address,"4VCRqKfu5qmcTrCRq2SJELDUFk2kuXAXPouXDJR6drmzxup8xJE5h3XkWvbYF1W3A9LTqN3Ff3HyHnbUmdoTcxBm")
        if len(txs)>0:
            log.info(f" --- process transactions: {account_address} {last_sig}")
            transfer.process_transactions(pool,neo_client,txs,tokens,account_address,accounts,last_sig)
    
# end bootstrap 


    while True:
            log.info("Load account,tokens")
            accounts,tokens=db.load_account_address_list_from_db(pool)
            # get all accounts 'update' to start acan theirs transactions 
            _,_,last_sigs=db.get_all_accounts(pool,"update")
            

            for sig_address in last_sigs:
                log.info(f"prosess transactins for address: {sig_address['address']}")
                addr=sig_address["address"]
                if addr != master_address:
                    distance=neo_client.count_hops(master_address,sig_address['address'])
                else:
                    distance=0
                log.info(f"distance: {distance}" )

                if distance == None:
                    distance=db.check_manual_disatance(pool,addr)

                if distance == None or distance > max_hops:
                    #db.retire_account(pool,addr)
                    continue
                      
                log.info(f"distance: {distance}" )
                if distance != None and distance<=max_hops:
                    txs,last_sig=transfer.fetch_all_transactions_to_sig(sig_address["address"],sig_address["last_sig"])

                    if len(txs)>0:
                        log.info(f"--- process transactions: {addr} {last_sig}")
                        transfer.process_transactions(pool,neo_client,txs,tokens,addr,accounts,last_sig)
                        
                    else:
                        db.update_account_last_sig(pool,str(sig_address["address"]),sig_address["last_sig"])
                log.info("Transaction finished. waiting for next ")        
            time.sleep(int(TX_SLEEP))
                