#!/usr/bin/env python3
import argparse
import requests
import json
import random
import time
from solders.pubkey import Pubkey
from solana.rpc.async_api import AsyncClient
import mysql.connector
from mysql.connector import pooling
from mysql.connector import Error

# Database credentials
MYSQL_SERVER = "kroocoin.xyz"
MYSQL_PORT = 3306
MYSQL_USER = "pump2"
MYSQL_PASSWORD = "pump2"
MYSQL_DB = "pump"
MYSQL_POOL_NAME = "mypool"
MYSQL_POOL_SIZE = 10

# Maximum number of signatures to fetch
max_sig = 30_000_000

# Special account addresses
RAYDIUM = "5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1"

def get_bonding_curve_address(mint_address: str):
    """
    Derive the bonding curve address based on the mint address.
    """
    mint_pubkey = Pubkey.from_string(mint_address)
    program_pubkey = Pubkey.from_string("6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P")

    # Example seed derivation logic (protocol-specific)
    seeds = [b"bonding-curve", bytes(mint_pubkey)]
    pda, _ = Pubkey.find_program_address(seeds, program_pubkey)

    return str(pda)

def fetch_all_transactions_to_sig(address, sig=None):
    """
    Fetch all transactions related to the given address from the Helius API.
    """
    last_signature = None
    transactions = []
    api_key = "cf3aa81f-7796-401b-a170-5567272f5f65"
    while True:
        url = f"https://api.helius.xyz/v0/addresses/{address}/transactions?api-key={api_key}"

        if last_signature:
            url_with_signature = f"{url}&before={last_signature}"
        else:
            url_with_signature = url

        response = requests.get(url_with_signature)

        if response.status_code != 200:
            print("Error code:", response.status_code)
            time.sleep(3)
            continue

        data = response.json()
        # Uncomment the next line for debugging purposes
        # print(json.dumps(data, indent=4))
        if "error" in data:
            if "exceeded limit for api" in data:
                print(data)
                time.sleep(3)
                continue
            else:
                if len(transactions) > 0:
                    return transactions
        for row in data:
            transactions.append(row)

        if len(data) > 0:
            last_signature = data[-1]["signature"]
        elif len(transactions) > max_sig:
            return transactions
        else:
            return transactions

def extract_transaction_data(transaction, bonding_curve):
    """
    Extract relevant data from a transaction.
    """
    extracted_data = []

    # Extract data from tokenTransfers
    for token_transfer in transaction.get("tokenTransfers", []):
        from_user = token_transfer.get("fromUserAccount")
        to_user = token_transfer.get("toUserAccount")
        token_amount = token_transfer.get("tokenAmount")
        mint = token_transfer.get("mint")

        # Determine if the operation is a buy or sell based on accountData
        trade_type = None
        sol_amount = None
        trader = ""
        for account in transaction.get("accountData", []):
            if account.get("account") == from_user and from_user not in [bonding_curve, RAYDIUM]:
                if account.get("nativeBalanceChange", 0) > 0:
                    trade_type = "sell"
                    trader = from_user
                    sol_amount = account.get("nativeBalanceChange")
                else:
                    trader = from_user
                    trade_type = "buy"
                    sol_amount = account.get("nativeBalanceChange")
            if account.get("account") == from_user and from_user in [bonding_curve, RAYDIUM]:
                if account.get("nativeBalanceChange", 0) > 0:
                    trade_type = "buy"
                    trader = to_user
                    sol_amount = account.get("nativeBalanceChange")
                else:
                    trader = to_user
                    trade_type = "sell"
                    sol_amount = account.get("nativeBalanceChange")

        extracted_data.append({
            "mint": mint,
            "trader": trader,
            "from": from_user,
            "to": to_user,
            "token_amount": token_amount,
            "signer": transaction.get("feePayer"),
            "block_time": transaction.get("timestamp"),
            "trade_type": trade_type,
            "sol_amount": sol_amount,
            "slot": transaction.get("slot"),
            "signature": transaction.get("signature")
        })

    return extracted_data

def insert_transactions_into_db(connection, transaction_entries):
    """
    Insert multiple transaction entries into the MySQL database.
    """
    try:
        cursor = connection.cursor()


        insert_accounts ="""
            INSERT INTO accounts (master, address, balance, status, scan,scanned, distance,enabled)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            ON DUPLICATE KEY UPDATE 
                balance = VALUES(balance)
        """

        data_tuples0 = []
        for entry in transaction_entries:
            data_tuple = (
                entry.get("mint"),
                entry.get("trader"),
                *********,
                'new',
                1,
                0,
                0,
                4

            )
            data_tuples0.append(data_tuple)

        insert_query = """
            INSERT INTO token_tx (mint, account, sol_amount, token_amount, trade, price, market_cup, block_time, block_id, signature)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            ON DUPLICATE KEY UPDATE 
                sol_amount = VALUES(sol_amount),
                token_amount = VALUES(token_amount),
                trade = VALUES(trade),
                price = VALUES(price),
                market_cup = VALUES(market_cup),
                block_time = VALUES(block_time),
                block_id = VALUES(block_id)
        """

        data_tuples = []
        for entry in transaction_entries:
            data_tuple = (
                entry.get("mint"),
                entry.get("trader"),
                entry.get("sol_amount"),
                entry.get("token_amount"),
                entry.get("trade_type"),
                None,  # price is not provided; set to NULL
                0,     # market_cup set to default value 0
                entry.get("block_time"),
                entry.get("slot"),
                entry.get("signature")
            )
            data_tuples.append(data_tuple)



        if data_tuples:
            cursor.executemany(insert_query, data_tuples)
        if data_tuples0:
            cursor.executemany(insert_accounts, data_tuples0)
            connection.commit()
            print(f"Inserted {len(data_tuples)} transactions into database.")
    except Error as e:
        print(f"Error inserting into database: {e}")
    finally:
        if cursor:
            cursor.close()

def main():
    """
    Main execution function.
    """
    # Parse command-line arguments
    parser = argparse.ArgumentParser(description="Fetch and store Solana token transactions.")
    parser.add_argument(
        "--mint",
        type=str,
        required=True,
        help="The mint address to fetch transactions for."
    )
    args = parser.parse_args()

    mint = args.mint
    bonding_curve = get_bonding_curve_address(mint)

    try:
        # Initialize MySQL connection pool
        db_pool = pooling.MySQLConnectionPool(
            pool_name=MYSQL_POOL_NAME,
            pool_size=MYSQL_POOL_SIZE,
            pool_reset_session=True,
            host=MYSQL_SERVER,
            port=MYSQL_PORT,
            user=MYSQL_USER,
            password=MYSQL_PASSWORD,
            database=MYSQL_DB
        )
        print("MySQL connection pool created successfully.")

        # Get a single connection from the pool
        connection = db_pool.get_connection()
        if connection.is_connected():
            print("Connected to MySQL database.")

        # Fetch transactions related to the mint address
        transactions = fetch_all_transactions_to_sig(mint)
        all_extracted_entries = []

        for transaction in transactions:
            extracted_entries = extract_transaction_data(transaction, bonding_curve)
            for entry in extracted_entries:
                # Removed print statement for transaction data
                all_extracted_entries.append(entry)

        # Insert all transactions into the database
        insert_transactions_into_db(connection, all_extracted_entries)

    except Error as e:
        print(f"MySQL Error: {e}")
    finally:
        if 'connection' in locals() and connection.is_connected():
            connection.close()
            print("MySQL connection closed.")

if __name__ == "__main__":
    main()
