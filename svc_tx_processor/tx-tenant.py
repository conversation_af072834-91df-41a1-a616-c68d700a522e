#!/usr/bin/env python3

from datetime import datetime, timedelta
import sys
import time
import os
import dotenv
from concurrent.futures import ThreadPoolExecutor, as_completed

import modules.transfer as transfer
import modules.db as db
import modules.utils as utils
import modules.neo as neo
from modules.config import *
from mysql.connector import pooling
from neo4j import GraphDatabase
from modules.neo import Neo4jClient
import modules.config 

dotenv.load_dotenv()

log = utils.get_logger("bootstrap", "bootstrap.log")

neo4j_server = os.getenv('NEO4J_SERVER')
neo4j_user = os.getenv('NEO4J_USER')
neo4j_password = os.getenv('NEO4J_PASSWORD')

MYSQL_SERVER = os.getenv('MYSQL_SERVER')
MYSQL_USER = os.getenv('MYSQL_USER')
MYSQL_PASSWORD = os.getenv('MYSQL_PASSWORD')
MYSQL_DB = os.getenv('MYSQL_DB')
MYSQL_POOL = os.getenv('MYSQL_POOL')
TX_SLEEP = os.getenv('TX_SLEEP')
TX_SLEEP=1
# Convert to bool if your environment variable is stored as a string
BOOTSTRAP = bool(os.getenv('BOOTSTRAP'))

MIN_BALANCE_CHANGE = os.getenv('MIN_BALANCE_CHANGE')

# master_address and max_hops presumably come from modules.config or environment
master_address="E197uwUXRjLMQBEZ4PYazDq5rGXwRB6xuTJ7f56UaLDC"
modules.config.master=master_address

BOOTSTRAP_ADDRESS = master_address
MASTERS=[master_address]

MAX_HOPS = max_hops
MAX_CONCURRENT_THREADS = 20

def process_address(sig_address, pool, neo_client, tokens, accounts):
    """
    Process the transactions for a single address in a thread-safe manner.
    Each thread obtains its own MySQL connection from the pool.
    """
    addr = sig_address["address"]
    log.info(f"Processing transactions for address: {addr}")

    # Obtain a new DB connection from the pool for each thread
    #cnx = pool.get_connection()
    try:
        # Use a fresh Neo4j session for each thread if needed
        distance = 0
        if addr != master_address:
            distance = neo_client.count_hops(master_address, addr)
            log.info(f"distance from master: {distance}")
        else:
            distance = 0

        if distance is None:
            distance = db.check_manual_disatance(pool, addr)

        # If distance is None or too large, skip
        if distance is None or distance > max_hops:
            # db.retire_account(cnx, addr)  # Uncomment if you want to retire the account
            log.info(f"Skipping {addr}, distance is None or too large.")
            return

        log.info(f"distance final: {distance}")
        # If within allowed distance, fetch transactions
        if distance <= max_hops:
            txs, last_sig = transfer.fetch_all_transactions_to_sig(
                sig_address["address"], sig_address["last_sig"]
            )

            if len(txs) > 0:
                log.info(f"--- processing {len(txs)} transactions: {addr} last_sig={last_sig}")
                transfer.process_transactions(pool, neo_client, txs, tokens, addr, accounts, last_sig)
            else:
                db.update_account_last_sig(pool, str(sig_address["address"]), sig_address["last_sig"])
            
        log.info(f"Finished processing address: {addr}")

    except Exception as e:
        log.error(f"Error in process_address for {addr}: {e}", exc_info=True)


if __name__ == "__main__":

    log.info("Creating MySQL pool...")
    pool = pooling.MySQLConnectionPool(
        pool_name=MYSQL_POOL,
        user=MYSQL_USER,
        password=MYSQL_PASSWORD,
        host=MYSQL_SERVER,
        db=MYSQL_DB,
        pool_size=20,
    )

    log.info("Creating Neo4J connection...")
    neo_client = Neo4jClient(neo4j_server, neo4j_user, neo4j_password)

    # You mentioned setting BOOTSTRAP=False here
    BOOTSTRAP=False
    if  BOOTSTRAP == True:
        db.delete_all_tables(pool)

        account_address=BOOTSTRAP_ADDRESS
        log.info("Drop all nodes")
        neo_client.print_nodes(neo_client)
        neo_client.drop_all()
        log.info("Adding master node to neo")
        address1 = neo_client.add_unique_node("Address", {"name": account_address, "balance": 0, "total_tx": 20, "role": "user", "group": "B", "age": 30, "type": "wallet"})

        log.info("Load existing accounts and tokens to memory")
        accounts,tokens=db.load_account_address_list_from_db(pool)


        log.info("load transactions for account")
        
        txs,last_sig=transfer.fetch_all_transactions_to_sig(account_address,"2ieJZNE7TBZdpmiK6m6ReC5svrxuXJmFfEYbKCxAd6TKPMQgE7cMesFHq6V7s7xeJhFpvc8sKEYLZR14n2xKa651")
        if len(txs)>0:
            log.info(f" --- process transactions: {account_address} {last_sig}")
            transfer.process_transactions(pool,neo_client,txs,tokens,account_address,accounts,last_sig)

    while True:
        log.info("Load account, tokens from DB")
        accounts, tokens = db.load_account_address_list_from_db(pool)

        # get all accounts 'update' to start scanning their transactions
        _, _, last_sigs = db.get_all_accounts(pool, "update")

        # We will process these addresses in parallel:
        # Up to 10 threads at a time
        with ThreadPoolExecutor(max_workers=MAX_CONCURRENT_THREADS) as executor:
            futures = []
            for sig_address in last_sigs:
                # Submit each address for concurrent processing
                futures.append(
                    executor.submit(process_address, sig_address, pool, neo_client, tokens, accounts)
                )

            # If you want to wait for all to complete and possibly handle results/exceptions:
            for future in as_completed(futures):
                try:
                    future.result()  # re-raises exception if any occurred in the thread
                except Exception as e:
                    log.error(f"Thread execution error: {e}", exc_info=True)

        # Sleep before the next cycle
        time.sleep(int(TX_SLEEP))
