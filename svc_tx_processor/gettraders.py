#!/usr/bin/env python3
import requests
import mysql.connector
import argparse

# =================================
# Adjustable variables
# =================================
LIMIT = 100  # Number of trades to fetch per API call

# Time window to filter trades (e.g., first 60 seconds of trading)
TIME_WINDOW_SECONDS = 60

# MySQL credentials
DB_HOST = "kroocoin.xyz"
DB_USER = "pump2"
DB_PASSWORD = "pump2"
DB_NAME = "pump"
TABLE_NAME = "accounts"

def fetch_all_trades(base_api_url):
    """
    Since the API's first entry is the *latest* trade, we must fetch
    *all* pages to ensure we get the earliest trade. Then we can
    figure out which trades occur within the first X seconds of the dataset.
    
    Args:
        base_api_url (str): The base API URL constructed with the token address.
    
    Returns:
        list of dict: All trades returned by the API, with fields such as:
                      'user', 'is_buy', 'sol_amount', 'timestamp', etc.
    """
    all_trades = []
    offset = 0

    while True:
        url = f"{base_api_url}?limit={LIMIT}&offset={offset}&minimumSize=0"
        response = requests.get(url, headers={"accept": "*/*"})
        
        if response.status_code != 200:
            print("Error fetching data from API:", response.status_code)
            break

        trades = response.json()
        if not trades:
            # No more trades returned
            break
        
        all_trades.extend(trades)
        offset += LIMIT

    return all_trades

def fetch_db_addresses():
    """
    Fetch addresses from the MySQL database's 'accounts' table.
    """
    try:
        connection = mysql.connector.connect(
            host=DB_HOST,
            user=DB_USER,
            password=DB_PASSWORD,
            database=DB_NAME
        )
        cursor = connection.cursor()
        cursor.execute(f"SELECT address FROM {TABLE_NAME}")
        result = cursor.fetchall()
        return [row[0] for row in result]
    except mysql.connector.Error as err:
        print("Error:", err)
        return []
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()

def main():
    # Set up argument parsing for token address
    parser = argparse.ArgumentParser(description="Process trades for a given token address.")
    parser.add_argument("token_address", type=str, help="The token address to fetch trades for.")
    args = parser.parse_args()

    # Construct the API URL using the token address
    base_api_url = f"https://frontend-api.pump.fun/trades/all/{args.token_address}"

    # Fetch all trades for the given token
    api_trades = fetch_all_trades(base_api_url)
    total_trades = len(api_trades)

    if not api_trades:
        print("No trades returned from the API. Exiting.")
        return

    # Fetch database addresses
    db_addresses = fetch_db_addresses()

    # Determine the earliest timestamp among all trades
    earliest_timestamp = min(t["timestamp"] for t in api_trades if "timestamp" in t)
    cutoff_timestamp = earliest_timestamp + TIME_WINDOW_SECONDS

    # Filter trades to only those in [earliest_timestamp, cutoff_timestamp]
    filtered_trades = [
        trade for trade in api_trades
        if earliest_timestamp <= trade.get("timestamp", 0) <= cutoff_timestamp
    ]

    # Identify missing addresses (those not in the DB) among filtered trades
    db_set = set(db_addresses)
    missing_addresses = {
        t["user"] for t in filtered_trades
        if t.get("user") and t["user"] not in db_set
    }

    # Sum up buy/sell totals for those missing addresses
    address_sums = {}
    for trade in filtered_trades:
        user = trade.get("user")
        if user in missing_addresses:
            lamports = trade.get("sol_amount", 0)
            is_buy = trade.get("is_buy", False)

            if user not in address_sums:
                address_sums[user] = {"buy_sum": 0, "sell_sum": 0}

            if is_buy:
                address_sums[user]["buy_sum"] += lamports
            else:
                address_sums[user]["sell_sum"] += lamports

    # Print cumulative totals (convert lamports to SOL)
    total_buy_lamports = sum(sums["buy_sum"] for sums in address_sums.values())
    total_sell_lamports = sum(sums["sell_sum"] for sums in address_sums.values())
    total_buy_sol = total_buy_lamports / 1e9
    total_sell_sol = total_sell_lamports / 1e9

    print("\n=== Cumulative Totals for All Missing Addresses ===")
    print(f"  Buy sum (SOL):  {total_buy_sol:.2f}")
    print(f"  Sell sum (SOL): {total_sell_sol:.2f}")

if __name__ == "__main__":
    main()
