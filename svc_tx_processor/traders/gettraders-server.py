#!/usr/bin/env python3
from flask import Flask, request, render_template, jsonify
import requests
import mysql.connector
import time

app = Flask(__name__)

# =================================
# Adjustable variables
# =================================
BASE_API_URL = "https://frontend-api-v3.pump.fun/trades/all/"
LIMIT = 100  # Number of trades to fetch per API call
DEFAULT_TIME_WINDOW_SECONDS = 60  # Default time window in seconds

# MySQL credentials
DB_HOST = "kroocoin.xyz"
DB_USER = "pump2"
DB_PASSWORD = "pump2"
DB_NAME = "pump"
TABLE_NAME = "accounts"

def calculate_holders(filtered_trades, uniq_traders):
    """
    Calculate the number of users who are still holding tokens by comparing their cumulative buys and sells.
    Only include addresses in the uniq_traders set.
    """
    # Dictionary to store cumulative buy and sell amounts for each user
    user_holdings = {}

    for trade in filtered_trades:
        user = trade.get("user")
        if user not in uniq_traders:
            continue  # Skip users not in the uniq_traders set

        token_amount = trade.get("token_amount", 0)
        is_buy = trade.get("is_buy", False)

        if user not in user_holdings:
            user_holdings[user] = {"buy_sum": 0, "sell_sum": 0}

        if is_buy:
            user_holdings[user]["buy_sum"] += token_amount
        else:
            user_holdings[user]["sell_sum"] += token_amount

    # Calculate the number of users still holding tokens
    still_holding_count = 0
    for user, amounts in user_holdings.items():
        if amounts["buy_sum"] > amounts["sell_sum"]:
            still_holding_count += 1

    return still_holding_count

def fetch_all_trades(token_address):
    """
    Fetch all trades for the given token address.
    """
    all_trades = []
    offset = 0

    while True:
        url = f"{BASE_API_URL}{token_address}?limit={LIMIT}&offset={offset}&minimumSize=0"
        response = requests.get(url, headers={"accept": "*/*"})

        if response.status_code != 200:
            print("Error fetching data from API:", response.status_code)
            break

        trades = response.json()
        if not trades:
            # No more trades returned
            break

        all_trades.extend(trades)
        offset += LIMIT

    return all_trades


def fetch_db_addresses():
    """
    Fetch addresses from the MySQL database's 'accounts' table.
    """
    connection = None
    try:
        connection = mysql.connector.connect(
            host=DB_HOST,
            user=DB_USER,
            password=DB_PASSWORD,
            database=DB_NAME
        )
        cursor = connection.cursor()
        cursor.execute(f"SELECT master,address FROM {TABLE_NAME} where scan = 1")
        result = cursor.fetchall()
        #return [row[0] for row in result]
        return [[row[0],row[1]] for row in result]
    except mysql.connector.Error as err:
        print("Error:", err)
        return []
    finally:
        if connection and connection.is_connected():
            cursor.close()
            connection.close()


@app.route("/analyzer", methods=["GET"])
def index():
    # Check query parameters
    token_address = request.args.get("token_address")
    requested_format = request.args.get("format", "").lower()  # e.g. 'json'
    
    # Use the default time window unless a valid integer is provided
    time_window_seconds = DEFAULT_TIME_WINDOW_SECONDS
    time_window_query = request.args.get("time_window")
    if time_window_query:
        try:
            time_window_seconds = int(time_window_query)
        except ValueError:
            pass  # Keep the default if invalid input

    # If the user has not provided a token address, just render the page with default info
    if not token_address:
        if requested_format == "json":
            # Return a JSON error if they requested JSON specifically but didn't provide a token
            return jsonify({"error": "No token_address provided."}), 400
        return render_template("index.html", result=None, last_token="", last_time=time_window_seconds)

    # Fetch all trades for the given token address
    trades = fetch_all_trades(token_address)
    if not trades:
        # If no trades are found, handle JSON or HTML output
        if requested_format == "json":
            return jsonify({"error": "No trades found for this token address."}), 404
        return render_template(
            "index.html",
            error="No trades found for this token address.",
            last_token=token_address,
            last_time=time_window_seconds
        )

    # Fetch addresses from the DB
    db_addresses = fetch_db_addresses()
    #[[row[0],row[1]] for row in result]
    db_addr=[row[1] for row in db_addresses]
   
    db_set = set(db_addr)
    #list of unique masters


    # Find the earliest timestamp in the fetched trades
    earliest_timestamp = min(t["timestamp"] for t in trades if "timestamp" in t)
    cutoff_timestamp = earliest_timestamp + time_window_seconds

    # Calculate seconds from the first request
    current_timestamp = int(time.time())
    seconds_from_first_request = current_timestamp - earliest_timestamp

    # Filter trades to only those within the time window
    filtered_trades = [
        t for t in trades
        if earliest_timestamp <= t["timestamp"] <= cutoff_timestamp
    ]

    # Identify addresses in the filtered trades that are NOT in the DB
    missing_addresses = {
        t["user"] for t in filtered_trades
        if t.get("user") and t["user"] not in db_set
    }

    # Identify addresses in the filtered trades that ARE in the DB
    matched_addresses = {
        t["user"] for t in filtered_trades
        if t.get("user") and t["user"] in db_set
    }
    matched_addresses_count = len(matched_addresses)

    masters=[]
    for row in db_addresses:
        if row[1] in matched_addresses:
            masters.append(row[0])
    db_masters_set = set(masters)


    # Prepare data structures for calculations on missing addresses
    address_sums = {}
    address_data = []
    uniq_traders = set()
    
    total_buys = 0
    total_sells = 0
    for trade in filtered_trades:
        user = trade.get("user")
        if user in missing_addresses:
            lamports = trade.get("sol_amount", 0)
            is_buy = trade.get("is_buy", False)
            timestamp = trade.get("timestamp", 0)

            if user not in address_sums:
                address_sums[user] = {"buy_sum": 0, "sell_sum": 0}

            if is_buy:
                total_buys += 1
                address_sums[user]["buy_sum"] += lamports
            else:
                total_sells += 1
                address_sums[user]["sell_sum"] += lamports

            # Store per-trade detail if needed in HTML
            uniq_traders.add(user)
            address_data.append({
                "address": user,
                "type": "Buy" if is_buy else "Sell",
                "amount": lamports / 1e9,  # Convert to SOL
                "timestamp": timestamp
            })

    # Calculate cumulative totals for missing addresses
    total_buy_lamports = sum(sums["buy_sum"] for sums in address_sums.values())
    #count buyers
    

    total_sell_lamports = sum(sums["sell_sum"] for sums in address_sums.values())
    #count sellers
    
    #togal traders
    total_traders = total_buys + total_sells

    total_buy_sol = total_buy_lamports / 1e9
    total_sell_sol = total_sell_lamports / 1e9
    difference = total_buy_sol - total_sell_sol

    # Prepare result data (use rounding for readability)
    result_data = {
        "token_address": token_address,
        "time_window": time_window_seconds,
        "cumulative_buy": round(total_buy_sol, 2),
        "cumulative_sell": round(total_sell_sol, 2),
        "difference": round(difference, 2),
        "matched_addresses_count": matched_addresses_count,  # new field
        "address_data": address_data,
        "master_count": len(db_masters_set),
        "total_traders": total_traders,
        "total_buys": total_buys,
        "total_sells": total_sells,
        "uniq_traders": len(uniq_traders),
        "holding_count": calculate_holders(filtered_trades,uniq_traders),
        "seconds_from_first_request": seconds_from_first_request,  # new field
        "earliest_timestamp": earliest_timestamp  # new field
    }
    print(result_data)
    # If JSON is requested, return four fields: buy, sell, difference, matched_addresses_count
    if requested_format == "json":
        return jsonify({
            "cumulative_buy": result_data["cumulative_buy"],
            "cumulative_sell": result_data["cumulative_sell"],
            "difference": result_data["difference"],
            "matched_addresses_count": matched_addresses_count,
            "master_count": len(db_masters_set),
            "total_traders": total_traders,
            "total_buys": total_buys,
            "total_sells": total_sells,
            "uniq_traders": len(uniq_traders),
            "holding_count": calculate_holders(filtered_trades,uniq_traders),
            "seconds_from_first_request": seconds_from_first_request,  # existing field
            "earliest_timestamp": earliest_timestamp  # new field
        })

    # Otherwise, render the HTML template
    return render_template(
        "index.html",
        result=result_data,
        last_token=token_address,
        last_time=time_window_seconds
    )


if __name__ == '__main__':
    app.run(port=1212, host="0.0.0.0", debug=True)

