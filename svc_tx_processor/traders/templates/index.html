<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Trade Analyzer (Dark Mode)</title>

  <!-- Bootstrap CSS -->
  <link 
    rel="stylesheet" 
    href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css"
  />

  <!-- Font Awesome for Icons (Optional) -->
  <link 
    rel="stylesheet" 
    href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
  />

  <script 
    src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"
  ></script>

  <style>
    /* Global page background & text */
    html, body {
      margin: 0;
      padding: 0;
      background-color: #121212; /* Darker background */
      color: #E0E0E0; /* Slightly muted white for text */
      font-family: 'Roboto', sans-serif;
    }

    /* Top navbar */
    .topbar {
      background-color: #1E1E2E;
      padding: 1rem 2rem;
      box-shadow: 0 1px 4px rgba(0,0,0,0.5);
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .topbar h4 {
      margin: 0;
      font-weight: 600;
      color: #FFFFFF;
    }
    .breadcrumbs {
      color: #888;
      font-size: 0.95rem;
    }

    /* Container for spacing */
    .container {
      max-width: 1200px;
      margin: 20px auto;
      padding: 24px;
    }

    /* Panel/Card styling */
    .panel {
      background-color: #1E1E2E; /* Match dark panels */
      border-radius: 10px;
      padding: 24px;
      margin-bottom: 24px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.5); /* Subtle shadow */
    }

    /* Headings */
    h1, h2, h3, h4, h5 {
      margin: 0;
      font-weight: 600;
      color: #FFFFFF;
    }
    h1 {
      margin-bottom: 16px;
    }

    /* Cards row */
    .cards-row {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      margin-bottom: 1.5rem;
    }
    .stat-card {
      flex: 1;
      min-width: 220px;
      background-color: #2C2C3C;
      padding: 1rem;
      border-radius: 8px;
      box-shadow: 0 1px 4px rgba(0,0,0,0.3);
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .stat-card .card-info h5 {
      margin: 0;
      font-weight: 600;
      font-size: 1.2rem;
      color: #FFFFFF;
    }
    .stat-card .card-info p {
      margin: 0;
      color: #888;
      font-size: 0.9rem;
    }
    .stat-card .card-icon {
      font-size: 2rem;
      opacity: 0.7;
    }
    .red{
        color: red;
    }
    .green{
        color: green;
    }
    /* Table container for horizontal overflow */
    .table-container {
      margin-top: 16px;
      overflow-x: auto; /* Scroll for wide tables */
    }

    /* Table styling */
    table.table {
      width: 100%;
      min-width: 600px ; /* Minimum table width */
      color: #E0E0E0;    /* Table text color */
      background-color: #2C2C3C !important; /* Grayish background */
      border-radius: 8px;
      overflow: hidden;
    }

    /* Table headers */
    thead {
      background-color: #3C3C4C; 
    }
    thead th {
      padding: 12px 16px;
      text-align: left;
      font-weight: 600;
      white-space: nowrap;
      border-bottom: 2px solid #444 !important;
      background-color: #38324c !important;
      color:#e1e1e1 !important;
    }
    /* Table rows */
    tbody td {
      padding: 12px 16px;
      white-space: nowrap; /* Avoid wrapping */
      background-color: #3A3A4A !important;
      color:#dcdcdc !important;
    }

    /* Row striping */
    tbody tr:nth-child(even) {
      background-color: #3A3A4A !important; /* Subtle zebra effect */
    }
    table.missing tr:nth-child(even) {
      background-color: #3A3A4A !important; /* Subtle zebra effect */
    }

    /* Hover effect */
   /* tbody tr:hover {
      background-color: #33334B; 
    } 
    *

    /* Form inputs */
    .form-control,
    .btn {
      background-color: #2C2C3C;
      color: #FFFFFF;
      border: 1px solid #444;
    }

    .form-label {
      color: #FFFFFF;
    }

    /* Button hover */
    .btn:hover {
      background-color: #444455;
      color: #FFFFFF;
    }

    /* Alert styling */
    .alert-danger {
      background-color: #660000;
      color: #FFFFFF;
      border-color: #AA0000;
    }

    /* Gauges: a bit of margin between progress bars */
    .gauge-bar {
      margin-bottom: 16px;
    }

    /* Responsive tweaks */
    @media(max-width: 768px){
      .cards-row {
        flex-direction: column;
      }
    }
    a {
      color: #FFFFFF;
      decoration: none;
    }
    .highlight {
      background-color: #308348 !important; /* Highlight color */
    }
  </style>
</head>
<body>

  <!-- Top bar -->
  <div class="topbar">
    <h4>Trade Analyzer <small class="text-muted">Dashboard</small></h4>
    <div class="breadcrumbs">
      Home &raquo; Trade Analyzer
    </div>
  </div>

  <div class="container">
    <!-- Cards row -->
    <div class="cards-row">
      <!-- Example Stat Cards (You can dynamically generate these based on your data) -->
      <div class="stat-card">
        <div class="card-info">
          <h5>{{ result.cumulative_buy }}({{ result.total_buys }})</h5>
          <p>Total Buy (SOL)</p>
        </div>
        <div class="card-icon text-success">
          <i class="fas fa-arrow-up"></i>
        </div>
      </div>
      <div class="stat-card">
        <div class="card-info">
          <h5>{{ result.cumulative_sell }}({{ result.total_sells }})</h5>
          <p>Total Sell (SOL)</p>
        </div>
        <div class="card-icon text-danger">
          <i class="fas fa-arrow-down"></i>
        </div>
      </div>
      <div class="stat-card">
        <div class="card-info">
          <h5>{{ result.difference }}({{ result.holding_count   }})</h5>
          <p>Difference (Buy - Sell)</p>
        </div>
        <div class="card-icon text-warning">
          <i class="fas fa-balance-scale"></i>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="card-info">
          <h5 ><span class="red">{{ result.matched_addresses_count }}({{ result.master_count }})</span><span class="green">  {{result.uniq_traders}}</span></h5>
          <p>Bundle/Trader Addresses</p>
        </div>
        <div class="card-icon text-primary">
          <i class="fas fa-users"></i>
        </div>
      </div>

      
    </div>

    <!-- Wrap form in a panel -->
    <div class="panel">
      <form method="GET" class="mb-4">
        <div class="mb-3">
          <label for="token_address" class="form-label">Token Address</label>
          <input 
            type="text" 
            class="form-control" 
            id="token_address" 
            name="token_address"
            value="{{ last_token if last_token else '' }}" 
            required
          />
        </div>
        <div class="mb-3">
          <label for="time_window" class="form-label">Time Window (Seconds)</label>
          <input 
            type="number" 
            class="form-control" 
            id="time_window" 
            name="time_window" 
            value="{{ last_time if last_time else 60 }}" 
            min="1"
          />
        </div>
        <button type="submit" class="btn btn-primary">Analyze</button>
      </form>
    </div>

    {% if result %}

      <!-- Gauges panel -->
     

       <!--
      <div class="panel">
        <h3>Transactions Over Time</h3>
        <canvas id="salesChart" width="400" height="200"></canvas>
      </div>

      {% if result.matched_addresses %}
      <div class="panel">
        <h3>Bundle Addresses (Matched)</h3>
        <div class="table-container">
          <table class="table tablesorter">
            <thead>
              <tr>
                <th>Address</th>
             
              </tr>
            </thead>
            <tbody>
            {% for address in result.matched_addresses %}
              <tr>
                <td>{{ address }}</td>
         
              </tr>
            {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
      {% endif %}
    -->

      <!-- Missing Addresses and Transactions -->
      <div class="panel">
        <h3>Missing Addresses and Transactions</h3>
        <div class="table-container">
          <table class="table tablesorter missing">
            <thead>
              <tr>
                <th>Address</th>
                <th>Type</th>
                <th>Amount (SOL)</th>
                <th>Timestamp</th>
                <th>Seconds from First Transaction</th> <!-- updated column header -->
              </tr>
            </thead>
            <tbody>
              {% for entry in result.address_data %}
              <tr>
                <td><a href="https://solscan.io/account/{{ entry.address }}#defiactivities">{{ entry.address }}</a></td>
                <td>{{ entry.type }}</td>
                <td>{{ entry.amount }}</td>
                <td>{{ entry.timestamp }}</td>
                <td>{{ entry.timestamp - result.earliest_timestamp }}</td> <!-- updated column data -->
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>

    {% elif error %}
      <div class="panel">
        <div class="alert alert-danger">{{ error }}</div>
      </div>
    {% endif %}
  </div>

  <!-- Bootstrap JS (Bundle) -->


  <!-- Luxon library for date management -->
  <script src="https://cdn.jsdelivr.net/npm/luxon@3.3.0/build/global/luxon.min.js"></script>

  <!-- Chart.js Luxon adapter -->
  <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-luxon@1.4.0"></script>

  <!-- jQuery / Tablesorter JS (if needed at the end) -->
  <script 
    src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.4/jquery.min.js"
  ></script>
  <script 
    src="https://cdnjs.cloudflare.com/ajax/libs/jquery.tablesorter/2.31.3/js/jquery.tablesorter.min.js"
  ></script>

  <script>
    $(document).ready(function() {
      $("table").tablesorter({
        sortList: [[3, 1]] // Sort by the 4th column (timestamp) in ascending order
      });

      // Add hover effect to highlight rows with the same address
      $("table tbody tr").hover(
        function() {
          var address = $(this).find("td:first a").text();
          $("table tbody tr").each(function() {
            if ($(this).find("td:first a").text() === address) {
              console.log("add  highlight");
              $(this).addClass("highlight");
            }
          });
        },
        function() {
          var address = $(this).find("td:first a").text();
          $("table tbody tr").each(function() {
            if ($(this).find("td:first a").text() === address) {
              console.log("remove  highlight");
              $(this).removeClass("highlight");
            }
          });
        }
      );

      // Add click effect to highlight rows with the same address
      $("table tbody tr td:first-child").click(function(event) {
        
        if (event.target.tagName !== 'A') { // Ensure the link is not clicked
          var address = $(this).find("a").text();
          $("table tbody tr").each(function() {
            if ($(this).find("td:first a").text() === address) {
              console.log("toggle highlight");
              $(this).toggleClass("highlight");
            }
          });
        }
      });
    });

    // Build a line chart from amount vs timestamp using address_data
    document.addEventListener('DOMContentLoaded', function () {
      // Convert your address_data into { x: Date, y: number } points
      // Chart.js can handle dates if your timestamp is in seconds (epoch). We multiply by 1000 to get milliseconds.
      const dataPoints = [
        {% for entry in result.address_data %}
          {
            x: new Date({{ entry.timestamp }} * 1000),
            y: {{ entry.amount }}
          }{% if not loop.last %},{% endif %}
        {% endfor %}
      ];

      // Optional: Sort data points by timestamp
      dataPoints.sort((a, b) => a.x - b.x);

      const ctx = document.getElementById('salesChart').getContext('2d');
      const salesChart = new Chart(ctx, {
        type: 'line',
        data: {
          datasets: [{
            label: 'SOL Over Time',
            data: dataPoints,
            borderColor: 'rgba(75, 192, 192, 1)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            fill: false,       // If you want area shading, set this to true
            tension: 0.1,      // Smoothness of the line
            pointRadius: 3,    // Size of the data points
            pointBackgroundColor: 'rgba(75, 192, 192, 1)'
          }]
        },
        options: {
          responsive: true,
          plugins: {
            legend: { 
              display: true 
            },
            tooltip: {
              enabled: true,
              callbacks: {
                label: function(context) {
                  // Format the date using Luxon
                  const date = luxon.DateTime.fromJSDate(context.parsed.x).toFormat('MMM dd, HH:mm');
                  return `SOL: ${context.parsed.y} on ${date}`;
                }
              }
            }
          },
          scales: {
            x: {
              type: 'time',        // Time axis
              time: {
                unit: 'hour',      // Adjust to 'day', 'minute', 'second', etc. as needed
                displayFormats: {
                  hour: 'MMM d, HH:mm' // Example: 'Sep 4, 14:30'
                }
              },
              ticks: {
                color: '#E0E0E0'
              },
              grid: {
                color: '#444'
              }
            },
            y: {
              beginAtZero: true,
              ticks: {
                color: '#E0E0E0'
              },
              grid: {
                color: '#444'
              }
            }
          }
        }
      });
    });
  </script>
</body>
</html>
