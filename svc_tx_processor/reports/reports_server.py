from flask import Flask, jsonify,render_template
import pymysql
from datetime import datetime

# Flask app
app = Flask(__name__)

# Database configuration
db_config = {
    'user': 'pump2',
    'password': 'pump2',
    'host': 'localhost',
    'db': 'pump'
}

def to_timestamp(last_update):
    if isinstance(last_update, datetime):
        return int(last_update.timestamp())  # Convert to Unix timestamp (seconds since epoch)
    else:
        return None  # Handle invalid data

def calculate_time_difference(last_update):
    if isinstance(last_update, datetime):
        # Convert to a Unix timestamp
        last_update_timestamp = int(last_update.timestamp())
        # Get the current timestamp
        current_timestamp = int(datetime.now().timestamp())
        # Subtract to find the difference in seconds
        time_difference = current_timestamp - last_update_timestamp
        #minuts
        return time_difference // 60
    else:
        return "Invalid date"


def get_db_connection():
    return pymysql.connect(
        user=db_config['user'],
        password=db_config['password'],
        host=db_config['host'],
        database=db_config['db'],
        cursorclass=pymysql.cursors.DictCursor
    )

def format_age(minutes):
    """Convert minutes into a human-readable format."""
    days = minutes // (24 * 60)
    hours = (minutes % (24 * 60)) // 60
    mins = minutes % 60
    
    readable_age = []
    if days > 0:
        readable_age.append(f"{days} day{'s' if days > 1 else ''}")
    if hours > 0:
        readable_age.append(f"{hours} hour{'s' if hours > 1 else ''}")
    if mins > 0 or (days == 0 and hours == 0):  # Always show minutes if no days/hours
        readable_age.append(f"{mins} minute{'s' if mins > 1 else ''}")
    
    return ", ".join(readable_age)

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/balances/<int:balance_id>')
def balances(balance_id):
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            query = """
                SELECT address, master, balance,distance, last_balance_update FROM accounts WHERE balance = %s
            """
            cursor.execute(query, (balance_id,))
            master_data = cursor.fetchall()
            balances = []
            if master_data:
                for data in master_data:
                    balances.append(data)
                    
        print(balances)
        # Pass the balances to the template
        return render_template('balances.html', balances = [(data['address'], data['master'], data['balance'],data['distance'],format_age(calculate_time_difference(data['last_balance_update']))     ) for data in master_data]
)
    finally:
        connection.close()


    

@app.route('/api/master')
def get_master_data():
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            query = """
                SELECT 
                    a.address, 
                    ab.balance, 
                    a.master,
                    TIMESTAMPDIFF(MINUTE, ab.ts, NOW()) AS update_age_minutes
                FROM 
                    accounts a
                JOIN 
                    account_balance ab 
                ON 
                    a.address = ab.address
                JOIN 
                    (SELECT address, MAX(ts) AS latest_ts 
                    FROM account_balance 
                    GROUP BY address) latest 
                ON 
                    ab.address = latest.address AND ab.ts = latest.latest_ts
                WHERE 
                    a.role = 'master'
                ORDER BY 
                    ab.ts DESC;
                
            """
            cursor.execute(query)
            master_data = cursor.fetchall()
            new_master_data = []
            if master_data:
                for data in master_data:
                    data['update_age_readable'] = format_age(data['update_age_minutes'])
                    new_master_data.append(data)
                    

        return jsonify(new_master_data)
    finally:
        connection.close()

@app.route('/api/distance/<int:distance>/<string:address>')
def get_distance0_data(distance,address):
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            query = """
                SELECT 
                    a.address, 
                    ab.balance, 
                    a.master,
                    TIMESTAMPDIFF(MINUTE, ab.ts, NOW()) AS update_age_minutes
                FROM 
                    accounts a
                JOIN 
                    account_balance ab 
                ON 
                    a.address = ab.address
                JOIN 
                    (SELECT address, MAX(ts) AS latest_ts 
                    FROM account_balance 
                    GROUP BY address) latest 
                ON 
                    ab.address = latest.address AND ab.ts = latest.latest_ts
                WHERE 
                    a.distance = %s
                    AND a.master = %s
                     AND a.last_balance_update > NOW() - INTERVAL 60 MINUTE
                ORDER BY 
                    ab.ts DESC;
                
            """
            cursor.execute(query, (distance,address,))
            master_data = cursor.fetchall()
            new_master_data = []
            if master_data:
                for data in master_data:
                    data['update_age_readable'] = format_age(data['update_age_minutes'])
                    new_master_data.append(data)
                    

        return jsonify(new_master_data)
    finally:
        connection.close()

@app.route('/api/distance1')
def get_distance1_data():
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            query = """
                SELECT 
                    a.address, 
                    ab.balance, 
                    a.master,
                    TIMESTAMPDIFF(MINUTE, ab.ts, NOW()) AS update_age_minutes
                FROM 
                    accounts a
                JOIN 
                    account_balance ab 
                ON 
                    a.address = ab.address
                JOIN 
                    (SELECT address, MAX(ts) AS latest_ts 
                    FROM account_balance 
                    GROUP BY address) latest 
                ON 
                    ab.address = latest.address AND ab.ts = latest.latest_ts
                WHERE 
                    a.distance = 1
                    AND a.last_balance_update > NOW() - INTERVAL 60 MINUTE
                ORDER BY 
                    ab.ts DESC
                limit 10;
                
            """
            cursor.execute(query)
            master_data = cursor.fetchall()
            new_master_data = []
            if master_data:
                for data in master_data:
                    data['update_age_readable'] = format_age(data['update_age_minutes'])
                    new_master_data.append(data)
                    

        return jsonify(new_master_data)
    finally:
        connection.close()
@app.route('/api/balance/<string:address>')
def get_balance_data(address):
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            query = """
                SELECT 
                    a.balance, 
                    a.master,
                    COUNT(*) AS count ,
                    FROM_UNIXTIME(MAX(UNIX_TIMESTAMP(a.last_balance_update))) AS avg_timestamp
                FROM 
                    accounts a 
                WHERE 
                    a.scan = 1 
and balance > 0
and a.balance/1e9 > 1
and a.master = %s
                    AND a.last_balance_update > NOW() - INTERVAL 60 MINUTE -- Filter records younger than 5 minutes
                GROUP BY 
                    a.balance, a.master
                ORDER BY 
                    avg_timestamp desc;

            """
            cursor.execute(query, (address,))
            distance_data = cursor.fetchall()

        # Prepare data for chart or table
        formatted_data = {"balances": [],"masters":[], "counts": [], "avg_timestamps": []}
        now = datetime.now()
        for row in distance_data:
            formatted_data["balances"].append(row["balance"])
            formatted_data["masters"].append(row["master"])
            formatted_data["counts"].append(row["count"])
            formatted_data["avg_timestamps"].append(format_age(  int(( int(now.timestamp())-to_timestamp(row["avg_timestamp"])     ) / 60)   ) )

        return jsonify(formatted_data)
    finally:
        connection.close()

@app.route('/api/chart/<string:address>')
def get_chart_data(address):
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            query = """
                SELECT 
                    *  from account_balance where address = %s AND `ts` >= NOW() - INTERVAL 24*7 HOUR order by ts asc;

            """
            cursor.execute(query, (address,))
            distance_data = cursor.fetchall()

        # Prepare data for chart or table
        formatted_data = {"labels": [],"data":[]}
        now = datetime.now()
        for row in distance_data:
            formatted_data["labels"].append(row["ts"])
            formatted_data["data"].append(row["balance"])

        return jsonify(formatted_data)
    finally:
        connection.close()


@app.route('/api/distance')
def get_distance_data():
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            query = """
                SELECT 
                    a.distance, 
                    a.master,
                    COUNT(*) AS count 
                FROM 
                    accounts a 
                WHERE 
                    a.scan = 1 
                    AND a.last_balance_update > NOW() - INTERVAL 600 MINUTE -- Filter records younger than 5 minutes
                GROUP BY 
                    a.distance, a.master
                ORDER BY 
                    a.distance ASC;

            """
            cursor.execute(query)
            distance_data = cursor.fetchall()

        # Prepare data for chart or table
        formatted_data = {"distances": [],"masters":[], "counts": []}
        for row in distance_data:
            formatted_data["distances"].append(row["distance"])
            formatted_data["masters"].append(row["master"])
            formatted_data["counts"].append(row["count"])

        return jsonify(formatted_data)
    finally:
        connection.close()

if __name__ == '__main__':
    app.run(debug=True, host="0.0.0.0", port=5151)
