
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reports</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #1e1e2f;
            color: #ffffff;
        }

        h1, h2 {
            text-align: center;
            margin-top: 20px;
        }

        h1 {
            font-size: 2.5em;
        }

        h2 {
            font-size: 1.8em;
        }

        .table-container {
            margin: 20px auto;
            max-width: 90%;
            background-color: #28293e;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        table {
            border-collapse: collapse;
            width: 100%;
        }

        table, th, td {
            border: 1px solid #44475a;
        }

        th, td {
            padding: 12px;
            text-align: left;
        }

        th {
            background-color: #3e405b;
            color: #ffffff;
            font-weight: bold;
        }

        tr:nth-child(even) {
            background-color: #2d2f44;
        }

        tr:hover {
            background-color: #3e405b;
        }

        td {
            color: #dcdcdc;
        }

        .modal {
            display: none;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 80%;
            height: 80%;
            max-width: 1200px;
            max-height: 700px;
            background-color: #28293e;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            z-index: 1000;
        }

        .modal-header {
            background-color: #3e405b;
            color: #ffffff;
            padding: 10px;
            border-top-left-radius: 10px;
            border-top-right-radius: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-body {
            padding: 20px;
            height: calc(100% - 60px);
        }

        .modal-close {
            background: none;
            border: none;
            color: #ffffff;
            font-size: 20px;
            cursor: pointer;
        }

        .overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 999;
        }

        canvas {
            width: 100%;
            height: 100%;
        }
        a {
    color: #c5cbd2;
    text-decoration: none;
}
    </style>
</head>
<body>
    <div class="container">
        <h1>Accounts by Balance</h1>

        <div class="table-container">
            <h2>Balance Changes by Distance (1 Hour Interval)</h2>
            <div id="balance-report" class="loading">
                {% if balances %}
                    <table>
                        <thead>
                            <tr>
                                <th>Address</th>
                                <th>Master</th>
                                <th>Balance</th>
                                <th>Distance</th>
                                <th>Age</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for address, master, balance, distance, update_age_readable in balances %}
                            <tr>
                                <td><a href="https://solscan.io/account/{{ address }}?flow=out&amount=1&amount=&token_address=So11111111111111111111111111111111111111111#transfers">{{ address }}</a></td>
                                <td>{{ master[:6] }}</td>
                                <td class="balance" data-address="{{ address }}">{{ balance }}</td>
                                <td>{{ distance }}</td>
                                <td>{{ update_age_readable }}</td>
                            </tr>
                        {% endfor %}
                        </tbody>
                    </table>
                {% else %}
                    <p>No data available for this balance.</p>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="overlay" id="overlay" onclick="closeModal()"></div>
    <div class="modal" id="modal">
        <div class="modal-header">
            <span>Chart Data</span>
            <button class="modal-close" onclick="closeModal()">&times;</button>
        </div>
        <div class="modal-body">
            <canvas id="chart"></canvas>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        let chart;

        async function fetchAndRenderChart(address) {
            const apiUrl = `http://kroocoin.xyz:5151/api/chart/${address}`;
            const response = await fetch(apiUrl);
            const { data, labels } = await response.json();

            const formattedLabels = labels.map((label, index) => {
                const date = new Date(label);
                const hours = date.getHours().toString().padStart(2, "0");
                const minutes = date.getMinutes().toString().padStart(2, "0");
                const day = date.getDate().toString().padStart(2, "0");
                const month = date.toLocaleString("default", { month: "short" });

                // Add date label between hours
                return index % 12 === 0 ? `${day}-${month} ${hours}:${minutes}` : `${hours}:${minutes}`;
            });

            const formattedData = data.map(value => (value / 1e9).toFixed(2));

            if (chart) chart.destroy();
            const ctx = document.getElementById("chart").getContext("2d");
            chart = new Chart(ctx, {
                type: "line",
                data: {
                    labels: formattedLabels,
                    datasets: [{
                        label: `Balance (in SOL)`,
                        data: formattedData,
                        fill: true,
                        backgroundColor: "rgba(75, 192, 192, 0.2)",
                        borderColor: "rgba(75, 192, 192, 1)",
                        borderWidth: 2,
                    }],
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            labels: { color: "#ffffff" },
                        },
                    },
                    scales: {
                        x: {
                            ticks: {
                                color: "#ffffff",
                                padding: 0, // Remove padding
                                autoSkip: true,
                                maxTicksLimit: 10,
                                callback: function (value, index, ticks) {
                                    return formattedLabels[index];
                                }
                            },
                        },
                        y: {
                            ticks: {
                                color: "#ffffff",
                                padding: 0, // Remove padding
                                callback: function (value) {
                                    return `${value} SOL`;
                                }
                            },
                        }
                    }
                },
            });
        }

        function showModal(address) {
            document.getElementById("modal").style.display = "block";
            document.getElementById("overlay").style.display = "block";
            fetchAndRenderChart(address);
        }

        function closeModal() {
            document.getElementById("modal").style.display = "none";
            document.getElementById("overlay").style.display = "none";
        }

        document.querySelectorAll(".balance").forEach(balanceCell => {
            balanceCell.addEventListener("click", () => {
                const address = balanceCell.getAttribute("data-address");
                showModal(address);
            });
        });
    </script>
</body>
</html>
