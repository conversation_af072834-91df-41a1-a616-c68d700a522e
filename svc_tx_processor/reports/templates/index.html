<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Reports</title>
  <style>
    /* General Styles */
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 0;
      background-color: #1e1e2f;
      color: #ffffff;
    }
    h1, h2 {
      text-align: center;
      margin-top: 20px;
    }
    h1 {
      font-size: 2.2em;
      margin-bottom: 10px;
    }
    a {
      color: #c5cbd2;
      text-decoration: none;
    }
    h2 {
      font-size: 1.4em;
      margin: 10px 0;
    }
    .loading {
      text-align: center;
      color: #888;
      font-size: 1.1em;
    }

    /* Container / Row Styles */
    .container {
  width: 95%;   /* or 100%, whichever you prefer */
  margin: 0 auto;  /* keeps it centered if you like */
  padding: 0 20px; /* optional padding */
}
    .row {
      display: flex;
      gap: 20px;
      margin-bottom: 30px;
      justify-content: space-between;
      flex-wrap: wrap; /* so they can wrap on smaller screens */
    }
    .column {
      flex: 1;
      min-width: 250px; /* prevent too narrow columns on small screens */
      display: flex;
      flex-direction: column;
      gap: 20px;
    }

    /* Table Container */
    .table-container {
      background-color: #28293e;
      padding: 15px;
      border-radius: 10px;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }
    table {
      border-collapse: collapse;
      width: 100%;
    }
    table, th, td {
      border: 1px solid #44475a;
    }
    th, td {
      padding: 12px;
      text-align: left;
    }
    th {
      background-color: #3e405b;
      color: #ffffff;
      font-weight: bold;
    }
    tr:nth-child(even) {
      background-color: #2d2f44;
    }
    tr:hover {
      background-color: #3e405b;
    }
    td {
      color: #dcdcdc;
    }

    /* Optional row coloring by short address */
    .master-E197uw { background-color: #6e624a !important; }
    .master-GUqaKL { background-color: #52737e !important; }
    
    .master-4URFn1 { background-color: #51776a !important; }
    .master-6D9Ayf { background-color: #6f5f52 !important; }

    /* Copy Address Link */
    .copy-address {
      cursor: pointer;
      color: #007bff;
      text-decoration: underline;
    }
    .copy-address:hover {
      color: #0056b3;
    }

    /* Toggle Container */
    #toggle-container {
      text-align: center;
      margin: 20px 0;
    }
    #toggle-container label {
      margin: 0 15px;
      cursor: pointer;
    }

    /* Modal + Overlay */
    .modal {
      display: none;
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 80%;
      height: 80%;
      max-width: 1200px;
      max-height: 700px;
      background-color: #28293e;
      border-radius: 10px;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
      z-index: 1000;
    }
    .modal-header {
      background-color: #3e405b;
      color: #ffffff;
      padding: 10px;
      border-top-left-radius: 10px;
      border-top-right-radius: 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .modal-body {
      padding: 20px;
      height: calc(100% - 60px);
    }
    .modal-close {
      background: none;
      border: none;
      color: #ffffff;
      font-size: 20px;
      cursor: pointer;
    }
    .overlay {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      z-index: 999;
    }

    canvas {
      width: 100%;
      height: 100%;
    }
  </style>
</head>
<body>
  <div class="container">
    <!-- Master Table (always visible) -->
    <h1>Reports</h1>
    <div class="table-container">
      <h2>Master Latest Balance Updates</h2>
      <div id="master-report" class="loading">Loading...</div>
    </div>

    <!-- Toggle Checkboxes -->
    <div id="toggle-container">
      <label>
        <input type="checkbox" id="toggle-E197uw" checked />
        Show E197uw
      </label>
      <label>
        <input type="checkbox" id="toggle-GUqaKL" checked />
        Show GUqaKL
      </label>
      <label>
        <input type="checkbox" id="toggle-4URFn1" checked />
        Show 4URFn1
      </label>
      <label>
        <input type="checkbox" id="toggle-6D9Ayf" checked />
        Show 6D9Ayf
      </label>
    </div>

    <!-- DISTANCE=0 ROW (4 columns) -->
    <h1>Accounts by Balance (distance=0)</h1>
    <div class="row">
      <div id="column-E197uw-dist0" class="column">
        <div class="table-container">
          <h2>Dist 0 (E197uw)</h2>
          <div id="distance-report-0-E197uw" class="loading">Loading...</div>
        </div>
      </div>
      <div id="column-GUqaKL-dist0" class="column">
        <div class="table-container">
          <h2>Dist 0 (GUqaKL)</h2>
          <div id="distance-report-0-GUqaKL" class="loading">Loading...</div>
        </div>
      </div>
      <div id="column-4URFn1-dist0" class="column">
        <div class="table-container">
          <h2>Dist 0 (4URFn1)</h2>
          <div id="distance-report-0-4URFn1" class="loading">Loading...</div>
        </div>
      </div>
      <div id="column-6D9Ayf-dist0" class="column">
        <div class="table-container">
          <h2>Dist 0 (6D9Ayf)</h2>
          <div id="distance-report-0-6D9Ayf" class="loading">Loading...</div>
        </div>
      </div>
    </div>

    <!-- DISTANCE=1 ROW (4 columns) -->
    <h1>Accounts by Balance (distance=1)</h1>
    <div class="row">
      <div id="column-E197uw-dist1" class="column">
        <div class="table-container">
          <h2>Dist 1 (E197uw)</h2>
          <div id="distance-report-1-E197uw" class="loading">Loading...</div>
        </div>
      </div>
      <div id="column-GUqaKL-dist1" class="column">
        <div class="table-container">
          <h2>Dist 1 (GUqaKL)</h2>
          <div id="distance-report-1-GUqaKL" class="loading">Loading...</div>
        </div>
      </div>
      <div id="column-4URFn1-dist1" class="column">
        <div class="table-container">
          <h2>Dist 1 (4URFn1)</h2>
          <div id="distance-report-1-4URFn1" class="loading">Loading...</div>
        </div>
      </div>
      <div id="column-6D9Ayf-dist1" class="column">
        <div class="table-container">
          <h2>Dist 1 (6D9Ayf)</h2>
          <div id="distance-report-1-6D9Ayf" class="loading">Loading...</div>
        </div>
      </div>
    </div>

    <!-- BALANCES ROW (4 columns) -->
    <h1>Order by Balance Over 1 SOL (1 hour interval)</h1>
    <div class="row">
      <div id="column-E197uw-bal" class="column">
        <div class="table-container">
          <h2>Balances (E197uw)</h2>
          <div id="balance-report-E197uw" class="loading">Loading...</div>
        </div>
      </div>
      <div id="column-GUqaKL-bal" class="column">
        <div class="table-container">
          <h2>Balances (GUqaKL)</h2>
          <div id="balance-report-GUqaKL" class="loading">Loading...</div>
        </div>
      </div>
      <div id="column-4URFn1-bal" class="column">
        <div class="table-container">
          <h2>Balances (4URFn1)</h2>
          <div id="balance-report-4URFn1" class="loading">Loading...</div>
        </div>
      </div>
      <div id="column-6D9Ayf-bal" class="column">
        <div class="table-container">
          <h2>Balances (6D9Ayf)</h2>
          <div id="balance-report-6D9Ayf" class="loading">Loading...</div>
        </div>
      </div>
    </div>
  </div>

  <!-- Modal + Overlay -->
  <div class="overlay" id="overlay" onclick="closeModal()"></div>
  <div class="modal" id="modal">
    <div class="modal-header">
      <span>Chart Data</span>
      <button class="modal-close" onclick="closeModal()">&times;</button>
    </div>
    <div class="modal-body">
      <canvas id="chart"></canvas>
    </div>
  </div>

  <!-- Scripts -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script>
    // --------------------------------------------------------------------
    // COPY TO CLIPBOARD
    // --------------------------------------------------------------------
    function copyToClipboard(text) {
      navigator.clipboard.writeText(text)
        .then(() => {
          alert(`Address copied to clipboard: ${text}`);
        })
        .catch(err => {
          console.error('Failed to copy: ', err);
        });
    }

    // --------------------------------------------------------------------
    // MODAL
    // --------------------------------------------------------------------
    function showModal(address) {
      document.getElementById("modal").style.display = "block";
      document.getElementById("overlay").style.display = "block";
      fetchAndRenderChart(address);
    }

    function closeModal() {
      document.getElementById("modal").style.display = "none";
      document.getElementById("overlay").style.display = "none";
    }

    // --------------------------------------------------------------------
    // MASTER TABLE
    // --------------------------------------------------------------------
    async function fetchMasterData() {
      try {
        const response = await fetch('/api/master');
        const masterData = await response.json();

        const masterDiv = document.getElementById('master-report');
        if (masterData.length > 0) {
          let tableHtml = `
            <table>
              <thead>
                <tr>
                  <th>Address</th>
                  <th>Balance (billions)</th>
                  <th>Update Age</th>
                </tr>
              </thead>
              <tbody>
          `;
          masterData.forEach((item) => {
            const shortAddress = item.address.slice(0, 6);
            const formattedBalance = (item.balance / 1e9).toFixed(2);
            tableHtml += `
              <tr class="${shortAddress}">
                <td>
                  <a href="https://solscan.io/account/${item.address}" target="_blank">
                    ${shortAddress}
                  </a>
                </td>
                <td class="balance" data-address="${item.address}">${formattedBalance}</td>
                <td>${item.update_age_readable}</td>
              </tr>
            `;
          });
          tableHtml += '</tbody></table>';
          masterDiv.innerHTML = tableHtml;
        } else {
          masterDiv.innerHTML = '<p class="loading">No master data available.</p>';
        }
      } catch (err) {
        console.error('Error fetching Master Data:', err);
      }
    }

    // --------------------------------------------------------------------
    // DISTANCE
    // --------------------------------------------------------------------
    async function fetchDistanceData(distance, masterFull) {
      try {
        const response = await fetch('/api/distance/' + distance + '/' + masterFull);
        const data = await response.json();
        const shortMaster = masterFull.slice(0, 6);
        const targetDivId = `distance-report-${distance}-${shortMaster}`;
        const targetDiv = document.getElementById(targetDivId);
        if (!targetDiv) return; // If hidden or doesn't exist

        if (data.length > 0) {
          let tableHtml = `
            <table>
              <thead>
                <tr>
                  <th>Address</th>
                  <th>Balance (billions)</th>
                  <th>Master</th>
                  <th>Update Age</th>
                </tr>
              </thead>
              <tbody>
          `;
          data.forEach(item => {
            const shortAddr = item.address.slice(0, 6);
            const shortMst = item.master.slice(0, 6);
            const formattedBalance = (item.balance / 1e9).toFixed(2);
            tableHtml += `
              <tr class="master-${shortMst}">
                <td>
                  <a href="https://solscan.io/account/${item.address}" target="_blank">
                    ${shortAddr}
                  </a>
                </td>
                <td class="balance" data-address="${item.address}">${formattedBalance}</td>
                <td>${shortMst}</td>
                <td>${item.update_age_readable}</td>
              </tr>
            `;
          });
          tableHtml += '</tbody></table>';
          targetDiv.innerHTML = tableHtml;
        } else {
          targetDiv.innerHTML = `<p class="loading">No data available (distance=${distance}).</p>`;
        }
      } catch (err) {
        console.error('Error fetching distance data:', err);
      }
    }

    // --------------------------------------------------------------------
    // BALANCE
    // --------------------------------------------------------------------
    async function fetchBalanceData(masterFull) {
      try {
        const response = await fetch('/api/balance/' + masterFull);
        const balData = await response.json();
        const shortMaster = masterFull.slice(0, 6);
        const targetDiv = document.getElementById('balance-report-' + shortMaster);
        if (!targetDiv) return;

        if (balData.balances && balData.balances.length > 0) {
          let tableHtml = `
            <table>
              <thead>
                <tr>
                  <th>Balance</th>
                  <th>Master</th>
                  <th>Count</th>
                  <th>Update Age</th>
                </tr>
              </thead>
              <tbody>
          `;
          for (let i = 0; i < balData.balances.length; i++) {
            const lamports = balData.balances[i];
            const solBalance = (lamports / 1e9).toFixed(2);
            const shortM = balData.masters[i].slice(0, 6);

            tableHtml += `
              <tr class="master-${shortM}">
                <td><a href="/balances/${lamports}">${solBalance}</a></td>
                <td>${shortM}</td>
                <td>${balData.counts[i]}</td>
                <td>${balData.avg_timestamps[i]}</td>
              </tr>
            `;
          }
          tableHtml += '</tbody></table>';
          targetDiv.innerHTML = tableHtml;
        } else {
          targetDiv.innerHTML = '<p class="loading">No data available (balance over 1 SOL).</p>';
        }
      } catch (err) {
        console.error('Error fetching balance data:', err);
      }
    }

    // --------------------------------------------------------------------
    // CHART
    // --------------------------------------------------------------------
    let chart;
    async function fetchAndRenderChart(address) {
      const apiUrl = `http://kroocoin.xyz:5151/api/chart/${address}`;
      try {
        const response = await fetch(apiUrl);
        const { data, labels } = await response.json();

        // Format labels
        const formattedLabels = labels.map((label, index) => {
          const date = new Date(label);
          const hours = date.getHours().toString().padStart(2, "0");
          const minutes = date.getMinutes().toString().padStart(2, "0");
          const day = date.getDate().toString().padStart(2, "0");
          const month = date.toLocaleString("default", { month: "short" });
          // Show day-month occasionally
          return index % 12 === 0
            ? `${day}-${month} ${hours}:${minutes}`
            : `${hours}:${minutes}`;
        });

        // Convert lamports to SOL
        const formattedData = data.map(value => (value / 1e9).toFixed(2));

        // Destroy old chart if it exists
        if (chart) chart.destroy();

        const ctx = document.getElementById("chart").getContext("2d");
        chart = new Chart(ctx, {
          type: "line",
          data: {
            labels: formattedLabels,
            datasets: [
              {
                label: "Balance (SOL)",
                data: formattedData,
                fill: true,
                backgroundColor: "rgba(75, 192, 192, 0.2)",
                borderColor: "rgba(75, 192, 192, 1)",
                borderWidth: 2,
              },
            ],
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                labels: { color: "#ffffff" },
              },
            },
            scales: {
              x: {
                ticks: {
                  color: "#ffffff",
                  autoSkip: true,
                  maxTicksLimit: 10,
                },
              },
              y: {
                ticks: {
                  color: "#ffffff",
                  callback: function (value) {
                    return `${value} SOL`;
                  },
                },
              },
            },
          },
        });
      } catch (err) {
        console.error("Error fetching chart data:", err);
      }
    }

    // --------------------------------------------------------------------
    // OPEN CHART ON CLICK
    // --------------------------------------------------------------------
    document.addEventListener("click", function (event) {
      if (event.target.classList.contains("balance")) {
        const address = event.target.getAttribute("data-address");
        showModal(address);
      }
    });

    // --------------------------------------------------------------------
    // TOGGLE / INTERVALS
    // --------------------------------------------------------------------
    // Keep track of intervals for each master short name
    let intervalsMap = {
      E197uw: [],
      GUqaKL: [],
      "4URFn1": [],
      "6D9Ayf": []
    };

    // Full addresses for each short name
    const MASTER_ADDR = {
      E197uw: "E197uwUXRjLMQBEZ4PYazDq5rGXwRB6xuTJ7f56UaLDC",
      GUqaKL: "GUqaKLm1JpA3adxn6kYUDeBbMne3Mz5VxVP6QNKH2UNo",
      "4URFn1": "4URFn1JswoVb7rZyh42dmoHRyxXnnaNetxUit5XmpCEs",
      "6D9Ayf": "6D9AyfijfWYe3UM1YPj83RuReU7eenRJJ5EU4QNiMmea"
    };

    function startMasterIntervals(shortMaster) {
      const fullAddr = MASTER_ADDR[shortMaster];
      if (!fullAddr) return;

      // Fetch immediately
      fetchDistanceData(0, fullAddr);
      fetchDistanceData(1, fullAddr);
      fetchBalanceData(fullAddr);

      // Create intervals for each dataset
      const arr = [];
      arr.push(setInterval(() => fetchDistanceData(0, fullAddr), 2000));
      arr.push(setInterval(() => fetchDistanceData(1, fullAddr), 2000));
      arr.push(setInterval(() => fetchBalanceData(fullAddr), 2000));

      intervalsMap[shortMaster] = arr;
    }

    function stopMasterIntervals(shortMaster) {
      if (!intervalsMap[shortMaster]) return;
      intervalsMap[shortMaster].forEach(id => clearInterval(id));
      intervalsMap[shortMaster] = [];
    }

    // Show/Hide columns for a master shortName
    function toggleMaster(shortMaster, show) {
      // Dist 0
      document.getElementById(`column-${shortMaster}-dist0`).style.display = show ? "flex" : "none";
      // Dist 1
      document.getElementById(`column-${shortMaster}-dist1`).style.display = show ? "flex" : "none";
      // Balances
      document.getElementById(`column-${shortMaster}-bal`).style.display  = show ? "flex" : "none";

      if (show) {
        startMasterIntervals(shortMaster);
      } else {
        stopMasterIntervals(shortMaster);
      }
    }

    // --------------------------------------------------------------------
    // ON PAGE LOAD
    // --------------------------------------------------------------------
    document.addEventListener("DOMContentLoaded", () => {
      // 1. Master table always updates
      fetchMasterData();
      setInterval(fetchMasterData, 1000);

      // 2. Start intervals for each master by default
      startMasterIntervals("E197uw");
      startMasterIntervals("GUqaKL");
      startMasterIntervals("4URFn1");
      startMasterIntervals("6D9Ayf");

      // 3. Hook up toggles
      document.getElementById("toggle-E197uw").addEventListener("change", e => {
        toggleMaster("E197uw", e.target.checked);
      });
      document.getElementById("toggle-GUqaKL").addEventListener("change", e => {
        toggleMaster("GUqaKL", e.target.checked);
      });
      document.getElementById("toggle-4URFn1").addEventListener("change", e => {
        toggleMaster("4URFn1", e.target.checked);
      });
      document.getElementById("toggle-6D9Ayf").addEventListener("change", e => {
        toggleMaster("6D9Ayf", e.target.checked);
      });
    });
  </script>
</body>
</html>
