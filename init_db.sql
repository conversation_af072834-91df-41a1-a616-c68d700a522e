-- Adminer 4.8.1 MySQL 8.0.40-0ubuntu0.24.04.1 dump

SET NAMES utf8;
SET time_zone = '+00:00';
SET foreign_key_checks = 0;

SET NAMES utf8mb4;

DROP TABLE IF EXISTS `account_balance`;
CREATE TABLE `account_balance` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `address` varchar(255) DEFAULT NULL,
  `balance` bigint unsigned DEFAULT NULL,
  `ts` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


DROP TABLE IF EXISTS `accounts`;
CREATE TABLE `accounts` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `master` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '',
  `address` varchar(255) DEFAULT NULL,
  `balance` bigint unsigned DEFAULT NULL,
  `balance_ui` float unsigned DEFAULT NULL,
  `account_type` varchar(255) DEFAULT NULL,
  `owner` varchar(255) DEFAULT NULL,
  `disovery_account_ts` timestamp NULL DEFAULT NULL,
  `first_tx_ts` timestamp NULL DEFAULT NULL,
  `last_tx_ts` timestamp NULL DEFAULT NULL,
  `last_sig` varchar(255) DEFAULT NULL,
  `status` varchar(50) DEFAULT NULL,
  `role` varchar(100) DEFAULT NULL,
  `scan` decimal(10,0) NOT NULL DEFAULT '1',
  `scanned` decimal(10,0) DEFAULT NULL,
  `mint_path` decimal(10,0) DEFAULT NULL,
  `name` varchar(100) DEFAULT NULL,
  `distance` smallint DEFAULT NULL,
  `notify` smallint DEFAULT '0',
  `trader` smallint DEFAULT NULL,
  `scan_prior` smallint DEFAULT '100',
  `enabled` smallint DEFAULT NULL,
  `description` varchar(200) DEFAULT NULL,
  `url` varchar(200) DEFAULT NULL,
  `helius_api` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `id` (`id`),
  UNIQUE KEY `address` (`address`),
  KEY `address_index` (`address`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


DROP TABLE IF EXISTS `instagram`;
CREATE TABLE `instagram` (
  `id` int NOT NULL AUTO_INCREMENT,
  `code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `username` varchar(100) NOT NULL,
  `full_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `profile_pic_url` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `caption` text NOT NULL,
  `taken_at` datetime NOT NULL,
  `image_urls` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `video_urls` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


DROP TABLE IF EXISTS `minting`;
CREATE TABLE `minting` (
  `id` int NOT NULL AUTO_INCREMENT,
  `mintPubKey` varchar(100) NOT NULL,
  `mintPrivKey` varchar(100) NOT NULL,
  `tradePubKey` varchar(100) NOT NULL,
  `tradePrivKey` varchar(100) NOT NULL,
  `status` char(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `updated` timestamp NULL DEFAULT NULL,
  `insol` float DEFAULT NULL,
  `outsol` float DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


DROP TABLE IF EXISTS `pushover`;
CREATE TABLE `pushover` (
  `id` int NOT NULL AUTO_INCREMENT,
  `address` varchar(100) NOT NULL,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `rules` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `enabled` int NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


DROP TABLE IF EXISTS `token_discoveries`;
CREATE TABLE `token_discoveries` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `mint` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `owner` varchar(100) DEFAULT NULL,
  `symbol` varchar(255) DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `url` varchar(255) DEFAULT NULL,
  `status` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT 'new',
  `score` int DEFAULT NULL,
  `risks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `freeze_auth` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `mint_auth` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `mutable` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `first_sig` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `slot` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `enabled` int DEFAULT '1',
  `update_ts` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `discovered_ts` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `id` (`id`),
  UNIQUE KEY `address` (`mint`),
  KEY `mint` (`mint`),
  KEY `owner` (`owner`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


DROP TABLE IF EXISTS `token_discoveries2`;
CREATE TABLE `token_discoveries2` (
  `id` int NOT NULL AUTO_INCREMENT,
  `mint` varchar(200) NOT NULL,
  `symbol` varchar(200) NOT NULL,
  `description` text NOT NULL,
  `name` varchar(200) NOT NULL,
  `image_uri` varchar(200) NOT NULL,
  `twitter` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `telegram` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `creator` varchar(200) NOT NULL,
  `created_timestamp` bigint NOT NULL,
  `website` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `image_md5` varchar(200) NOT NULL,
  `image_local_uri` varchar(200) NOT NULL,
  `bonding_curve` varchar(200) NOT NULL,
  `associated_bonding_curve` varchar(200) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


DROP TABLE IF EXISTS `token_swaps`;
CREATE TABLE `token_swaps` (
  `id` int NOT NULL AUTO_INCREMENT,
  `mint` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `account` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `account_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `token_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `sol_amount` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `token_amount` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `trade` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `price` decimal(10,10) NOT NULL,
  `pnl_from_average` decimal(10,10) NOT NULL,
  `pnl_from_avg` int NOT NULL,
  `pnl_from_avg2` float NOT NULL,
  `market_cup` int NOT NULL DEFAULT '0',
  `program` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `block_time` int NOT NULL,
  `block_timestamp` timestamp NOT NULL,
  `block_id` int NOT NULL,
  `signature` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `token_data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `token_url` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `token_description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `token_created` timestamp NOT NULL,
  `token_timestamp` int NOT NULL,
  `token_symbol` varchar(100) NOT NULL,
  `twitter` varchar(200) NOT NULL,
  `website` varchar(200) NOT NULL,
  `telegram` varchar(200) NOT NULL,
  `mcap` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `pnl_array` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  PRIMARY KEY (`id`),
  KEY `mint` (`mint`),
  KEY `account` (`account`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


DROP TABLE IF EXISTS `tokens`;
CREATE TABLE `tokens` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `address` varchar(255) DEFAULT NULL,
  `balance` decimal(30,20) DEFAULT NULL,
  `symbol` varchar(255) DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `url` varchar(255) DEFAULT NULL,
  `discovered_ts` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `id` (`id`),
  UNIQUE KEY `address` (`address`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


DROP TABLE IF EXISTS `trans`;
CREATE TABLE `trans` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `tx_type` varchar(255) DEFAULT NULL,
  `source` varchar(255) DEFAULT NULL,
  `destination` varchar(255) DEFAULT NULL,
  `amount` varchar(100) DEFAULT NULL,
  `currency_name` varchar(255) DEFAULT NULL,
  `master` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `mint_address` varchar(255) DEFAULT NULL,
  `mint_name` varchar(255) DEFAULT NULL,
  `sig` varchar(255) DEFAULT NULL,
  `slot` decimal(10,0) DEFAULT NULL,
  `transaction_date` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `id` (`id`),
  KEY `tx_type` (`tx_type`),
  KEY `source` (`source`),
  KEY `destination` (`destination`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


DROP TABLE IF EXISTS `transactions`;
CREATE TABLE `transactions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `mint` varchar(255) DEFAULT NULL,
  `tx_type` varchar(255) DEFAULT NULL,
  `from_address` varchar(255) DEFAULT NULL,
  `to_address` varchar(255) DEFAULT NULL,
  `amount` decimal(30,20) DEFAULT NULL,
  `block` int NOT NULL,
  `block_time` int NOT NULL,
  `signature` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


DROP TABLE IF EXISTS `tweets`;
CREATE TABLE `tweets` (
  `id` int NOT NULL AUTO_INCREMENT,
  `authorName` varchar(100) NOT NULL,
  `authorHandle` varchar(222) NOT NULL,
  `tweetLink` varchar(222) NOT NULL,
  `userText` text NOT NULL,
  `targetUserText` text NOT NULL,
  `actionType` varchar(100) NOT NULL,
  `targetUser` varchar(100) NOT NULL,
  `imageLinks` text NOT NULL,
  `timestamp` bigint NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `tweetLink` (`tweetLink`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


DROP TABLE IF EXISTS `urls`;
CREATE TABLE `urls` (
  `id` int NOT NULL AUTO_INCREMENT,
  `long_url` text NOT NULL,
  `short_url` varchar(10) NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `short_url` (`short_url`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


-- 2025-01-15 13:54:17


CREATE USER IF NOT EXISTS 'pump'@'%' IDENTIFIED BY 'pump';
GRANT ALL PRIVILEGES ON *.* TO 'pump'@'%' WITH GRANT OPTION;
FLUSH PRIVILEGES;

CREATE USER 'pump2'@'%' IDENTIFIED WITH mysql_native_password BY 'pump2';
GRANT ALL PRIVILEGES ON *.* TO 'pump'@'%' WITH GRANT OPTION;
FLUSH PRIVILEGES;
