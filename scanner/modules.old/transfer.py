import requests,random,json,time,re
import pandas as pd
import modules.db as db 
import modules.utils as utils
log=utils.get_logger("transfer")

api_key = [
        "69463031-b3c1-455a-addd-0a8d100dca90",
        "c29c0ee4-6308-4789-a694-e2dfa40ad7c7",
        "814e2855-a7f8-4d84-b0bc-82cedf5694e3",
        "e10ad31d-205d-4bd2-ad35-e261cd901f38",
        "6d8bfbc2-44f3-4c30-b31e-c1322741dce8",
        "4a53c62c-2be7-4030-8e51-7f4867bf16c4",
        "f5678270-3a1a-4868-ba6a-1d70a0891966",
        "8c5ca67a-9aa2-4986-905f-2a95b1927b34",
        "12728bae-550f-4f59-a42c-94b89d9e86b8",
        "0c496773-5e98-428a-90da-4b24095db327"]
#omniatechio
omni_url="https://endpoints.omniatech.io/v1/sol/mainnet/"
omni_keys =[
        "b6efe195db51499ca3719ad3eb838aa6",
        "438ffec158c5401e9b00d2de309a86a5",
        "4fd8f6aa33f443ebb14c52e25551890a"
        ]

exchange = pd.read_csv("exchange.csv")

def add_key_value(d, key, value):
    row={'key':key,'value':value}
    d.append(row)
    return d

# Function to find a key by its value
def find_key_by_value(d, value):
    for row in d:
        if row['value'] == value:
            return row['key']
    return None
    
def fetch_1transactions(address="4NVoofLVJqExqFCLGEaw2hfNT7pDRd1Rzbas1XR8f2YY"):
    randNum = random.randint(0, len(api_key) - 1)
    url = f"https://api.helius.xyz/v0/addresses/{address}/transactions?api-key={api_key[randNum]}"
    response = requests.get(url)
    data = response.json()
    return  data   

def fetch_jupiter_price(token_name= "SOL"):
    url = "https://price.jup.ag/v4/price"
    params = {"ids": token_name}
    price = 0

    response = requests.get(url, params=params)

    if response.status_code == 200:
        data = response.json()
        try:
            price = data["data"][token_name]["price"]
            
        except:
            #print(f"{token_name} Failed to fetch price:", data)
            pass
            
    else:
        #print(f"{token_name} Failed to fetch price:", response.status_code)
        pass
    
    return price

def get_multiple_accounts2(accounts):
    try:
        print(len(accounts))
        randNum = random.randint(0, len(api_key) - 1)
        url = "https://endpoints.omniatech.io/v1/sol/mainnet/b6efe195db51499ca3719ad3eb838aa6"
        #url = f"https://mainnet.helius-rpc.com/?api-key={api_key[randNum]}"
        headers = {'Content-Type': 'application/json'}
        data = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "getMultipleAccounts",
            "params": [accounts]
            
        }
        response = requests.post(url, headers=headers, json=data)
        print(response.json())
        sys.exit()
        balances=[]
        data=response.json()['result']['value']
        for row in data:
            if row == None:
                balances.append(0)
            else:
                balances.append(row['lamports'])
        return balances
    except Exception as e:
        print(e)
        return 0
    
def get_multiple_accounts(accounts):
    try:
        print(len(accounts))
        randNum = random.randint(0, len(api_key) - 1)
        url = f"https://mainnet.helius-rpc.com/?api-key={api_key[randNum]}"
        headers = {'Content-Type': 'application/json'}
        data = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "getMultipleAccounts",
            "params": [accounts]
            
        }
        response = requests.post(url, headers=headers, json=data)
    
        balances=[]
        data=response.json()['result']['value']
        for row in data:
            if row == None:
                balances.append(0)
            else:
                balances.append(row['lamports'])
        return balances
    except Exception as e:
        print(e)
        return 0
    
def getBalance(address):
    try:
        randNum = random.randint(0, len(api_key) - 1)
        url = f"https://mainnet.helius-rpc.com/?api-key={api_key[randNum]}"
        headers = {'Content-Type': 'application/json'}
        data = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "getBalance",
            "params": [
                address ]

            
        }
        response = requests.post(url, headers=headers, json=data)
        
        return response.json()['result']['value'] / 10e8
    
    except:
        print(response.json())
        return 0
    
def get_token_info(asset_id):

    randNum = random.randint(0, len(api_key) - 1)
    url = f"https://mainnet.helius-rpc.com/?api-key={api_key[randNum]}"
    headers = {"Content-Type": "application/json"}
    payload = {
        "jsonrpc": "2.0",
        "id": "M4tCEjM9B7k8kNMVBtAfktmRJShMjVrdPWZDZboXfQG",
        "method": "getAsset",
        "params": {
            "id": asset_id
        }
    }


    response = requests.post(url, headers=headers, data=json.dumps(payload))
    response_data = response.json()


    if "result" in response_data and "content" in response_data['result'] and "metadata" in response_data['result']['content']:

        return response_data['result']['content']['metadata']
    else:
        raise ValueError("Token info not found in the response")

def fetch_all_transactions_to_sig(address="J4JHxjX58SNH9AMS2RWz9WKnX8cwe66GyC17FdoTzx2b",sig=None):
    max_sig=98
    last_signature = None 
    transactions = []
 
    while True:
        randNum = random.randint(0, len(api_key) - 1)
        url = f"https://api.helius.xyz/v0/addresses/{address}/transactions?api-key={api_key[randNum]}"

        if last_signature:
            url_with_signature = f"{url}&before={last_signature}"
        else:
            url_with_signature = url
        response = requests.get(url_with_signature)


        if response.status_code != 200:
            print("Error code：", response.status_code)
            time.sleep(1)
            continue

      
        data = response.json()


        if "error" in data:
            if "exceeded limit for api" in data:
                print(data)
                time.sleep(3)
                continue   
            else:
                if len(transactions) > 0:
                    return  transactions,transactions[0]["signature"] 
             
        for row in data:
            if row['signature']==sig:
                if len(transactions) > 0:
                    return transactions,transactions[0]['signature']
                else:
                    return transactions,sig
            else:
                transactions.append(row)
                

        
        
        if data and len(data) > 0:
            last_signature = data[-1]["signature"]

          
        elif len(transactions)>max_sig:
            return transactions,transactions[0]['signature']
          
        else:
            print("End of check")
            print(f"Transaction count:{len(transactions)}")
            break
        
        if len(transactions)>max_sig:
            return transactions,transactions[0]['signature']
        print(f"Processed {len(transactions)}", end='\r')
    if len(transactions)> 0:       
        return  transactions,transactions[0]['signature']  
    else:
        return []



def fetch_all_transactions(address="4NVoofLVJqExqFCLGEaw2hfNT7pDRd1Rzbas1XR8f2YY"):
    max_tx=100
    last_signature = None 
    transactions = []
 
    while True:
        randNum = random.randint(0, len(api_key) - 1)
        url = f"https://api.helius.xyz/v0/addresses/{address}/transactions?api-key={api_key[randNum]}"

        if last_signature:
            url_with_signature = f"{url}&before={last_signature}"
        else:
            url_with_signature = url
        response = requests.get(url_with_signature)


        if response.status_code != 200:
            print("Error code：", response.status_code)
            time.sleep(1)
            continue

      
        data = response.json()
        
        if "error" in data:
            if "exceeded limit for api" in data:
                print(data)
                time.sleep(3)
                continue   
            else:
                return  transactions  
        
        transactions = transactions + data
        
        if data and len(data) > 0:
            print("Transaction count：", len(transactions))
            last_signature = data[-1]["signature"]
            print(f"transaction count:{len(transactions)}")
          
        elif len(transactions)>max_tx:
            break
          
        else:
            print("End of check")
            print(f"transaction count:{len(transactions)}")
            break
        
        if len(transactions)>max_tx:
          break
          
    return  transactions  

def parse_transactions(transactions, address, threshold, jup_check=True):
    df = pd.DataFrame(transactions)
    df.to_json('data1.json', orient='records')
    import sys
    sys.exit()
    df["UTC"] = pd.to_datetime(df["timestamp"], unit="s", utc=True)
    df = df.query("type=='TRANSFER'& not description.str.contains('multiple') & not description.str.contains('0 SOL')")

    def parse_description(description):
        matches = re.findall(r'(\w+) transferred ([\d.]+) (\w+) to (\w+)\.', description)
        if matches:
            sender_address, amount, token_name, receiver_address = matches[0]
            return sender_address, amount, token_name, receiver_address
        else:
            return None, None, None, None
        
    df["sender"], df["amount"], df["token_name"], df["receiver"] = zip(*df["description"].apply(parse_description))
    df = df[["sender","amount","token_name","receiver","UTC","signature"]]
    


    if jup_check == True:
        df["amount"] = df["amount"].astype(float).round(2)
        df = df.query("token_name.str.len() < 10") # 排除 token_name 是地址的

        # 取得 token price by Jupiter Api
        token_list = df["token_name"].unique()
        token_price = {}
        for token in token_list:
            price = fetch_jupiter_price(token)
            token_price[token] = price
            
        df["USD"] = df.apply(lambda row: row["amount"] * token_price.get(row["token_name"], 0), axis=1)
        df["USD"] = df["USD"].astype(float).round(0)
        df = df.query(f"USD > {threshold}")

        sendTX = df.query(f"sender=='{address}'")
        receiveTX = df.query(f"receiver=='{address}'")

        sendTX_group = sendTX.groupby(["receiver", "token_name"]).agg(total_amount=("amount", "sum"), usd=("USD", "sum"), tx_count=("amount", "count")).sort_values("usd",ascending=False)
        receiveTX_group = receiveTX.groupby(["sender", "token_name"]).agg(total_amount=("amount", "sum"), usd=("USD", "sum"), tx_count=("amount", "count")).sort_values("usd",ascending=False)
    
    elif jup_check == False:
        sendTX = df.query(f"sender=='{address}'")
        receiveTX = df.query(f"receiver=='{address}'")

        sendTX_group = sendTX.groupby(["receiver"]).count()
        receiveTX_group = receiveTX.groupby(["sender"]).count()
    
    return sendTX_group, receiveTX_group

def recent_tx_count(address="AYhux5gJzCoeoc1PoJ1VxwPDe22RwcvpHviLDD1oCGvW"): 
    try:
        data = fetch_1transactions(address)
        current_timestamp = time.time()
        one_hour_ago_timestamp = current_timestamp - 7200 # 最近2hr 
        recent_timestamps = [transaction['timestamp'] for transaction in data if transaction['timestamp'] >= one_hour_ago_timestamp]
        return data, len(recent_timestamps)
    except:
        print("error recent_tx_count: "+address)
        return data, 0
    

def exchange_deposit_address_check(ddd, address):
    sss, rrr = parse_transactions(ddd, address, 10, jup_check=False)
    idx = sss.index.get_level_values("receiver").isin(exchange["address"])
    contains_exchange = sss.index.get_level_values("receiver")[idx]
    if contains_exchange.any():
        exchange_name = exchange.loc[exchange['address'] == contains_exchange[0], 'exchange'].values[0]
    else:
        exchange_name = False

    return exchange_name


def find_associated_wallet(sendTX_group, receiveTX_group):
    send_usd = sendTX_group.groupby("receiver").agg(send=("usd", "sum"), sendTX=("tx_count", "sum")).sort_values(["send"],ascending=False)
    receive_usd = receiveTX_group.groupby("sender").agg(receive=("usd", "sum"), receiveTX=("tx_count", "sum")).sort_values(["receive"],ascending=False)
    total_interact = pd.concat([send_usd, receive_usd], axis=1)
    total_interact.columns = ["sendUSD", "sendTX","receiveUSD","receiveTX"]
    total_interact = total_interact.fillna(0)
    total_interact.insert(0, "totalUSD", total_interact["sendUSD"] + total_interact["receiveUSD"])
    total_interact.insert(1, "totalTX", total_interact["sendTX"] + total_interact["receiveTX"])
    total_interact = total_interact.sort_values("totalUSD", ascending=False)


    # 判斷是否為 cex 發錢，標記為 cex
    address_list = total_interact.index
    total_interact.insert(0, "mark", "")
    total_interact["lastTx"] = ""

    for i in range(len(exchange)):
        try:
            eidx = address_list.get_loc(exchange["address"].loc[i])
            total_interact["mark"].iloc[eidx] = exchange["exchange"].loc[i]
        except:
            pass

    # 查每個地址的 tx, sol balance
    total_interact["SOL bal."] = 0
    count = 0
    for aaa in total_interact.index:
        if  total_interact["mark"].loc[aaa] == "":  # 尚未標記成 cex 則繼續
            print(aaa)
            total_interact.at[aaa, "SOL bal."] = getBalance(aaa)
            ddd, txs = recent_tx_count(aaa)

            # 2hr Txs > 50, 可能是某合約地址/cex/bot
            if txs > 50: 
                total_interact["mark"].loc[aaa] = "🤖"

            # 判斷是否為打錢去 cex
            if total_interact["receiveTX"].loc[aaa] == 0 and total_interact["sendTX"].loc[aaa] > 1:
                exchangeTF = exchange_deposit_address_check(ddd, aaa)
                print(exchangeTF)
                if exchangeTF != False:
                    total_interact["mark"].loc[aaa] = "its "+exchangeTF 

            # 計算最後交易日
            try:
                lastTx = pd.to_datetime(ddd[0]["timestamp"], unit='s').strftime('%Y-%m-%d')        
                total_interact["lastTx"].loc[aaa] = lastTx
            except:
                total_interact["lastTx"].loc[aaa] = ""
              
            count += 1
            if count > 7:
                pass
                #break

    total_interact["SOL bal."] = total_interact["SOL bal."].round(4)
    
    # 高機率是小號
    total_interact.loc[(total_interact['sendTX'] >= 3) & (total_interact['receiveTX'] >= 3), 'mark'] = "🔗"

    return total_interact



def process_transactions(pool,transactions,tokens):
    df = pd.DataFrame(transactions)
    #df = pd.read_json('data.json', orient='records' )
    df["UTC"] = pd.to_datetime(df["timestamp"], unit="s", utc=True)
    mints = []
    transfers_data = []
   
    for index, row in df.iterrows():
        log.info(f"Process tx: {row['signature']}")
        if row['nativeTransfers']:
            if row['type'] == "UNKNOWN":
                row_type="TRANSFER"
            else:
                row_type=row['type']
            for tx in row['nativeTransfers']:
                native_transfer_record = {
                    'type': row_type,
                    'coin': 'sol',
                    'slot': row['slot'],
                    'timestamp': row['timestamp'],
                    'source': tx['fromUserAccount'],
                    'destination': tx['toUserAccount'],
                    'amount': tx['amount'],
                    'mint': '',
                    'mint_name': '',
                    'UTC': row['UTC'],
                    'signature': row['signature']
                }
                transfers_data.append(native_transfer_record)
        if row['tokenTransfers']:
            if row['type'] == "UNKNOWN":
                row_type="TRANSFER"
            else:
                row_type=row['type']
            for tx in row['tokenTransfers']:
                # process tokens 
                tokens,mint_name=db.add_token_to_tokens_list(pool,tokens,tx['mint'])    

                if tx['mint'] == "So11111111111111111111111111111111111111112":
                    amount= float(tx['tokenAmount'])***********
                    mint_name="WSOL"
                else:
                    amount=tx['tokenAmount']
                
                token_transfer_record = {
                    'type': row_type,
                    'coin': 'token',
                    'slot': row['slot'],
                    'timestamp': row['timestamp'],
                    'source': tx['fromUserAccount'],
                    'destination': tx['toUserAccount'],
                    'amount': amount,
                    'mint': tx['mint'],
                    'mint_name': mint_name,
                    'UTC': row['UTC'],
                    'signature': row['signature']
                }
                print(token_transfer_record)
                transfers_data.append(token_transfer_record)
    

    df1 = pd.DataFrame(transfers_data)
    df1['amount']=df1['amount'].astype(float)
    return df1,tokens

def get_account_balances(accounts,current_balances):
    balances=get_multiple_accounts(accounts)

    data=zip(accounts,balances)
    if len(accounts) != len(balances) and len(balances) != len(current_balances):
        raise ValueError("The length of accounts and balances lists must be the same.")
    data = {
        'account': accounts,
        'current_balances': current_balances,
        'balance': balances
    }
    df = pd.DataFrame(data)
    df['current_balances']=df['current_balances']
    df['balance']=df['balance'].apply(lambda x: x)
    df=df.query("current_balances != balance")
    # add balance to balance table 
    #update accounts set to update 
    return df

