import time
from datetime import datetime
import mysql.connector
from mysql.connector import errorcode
from mysql.connector import pooling
import pandas as pd
import modules.transfer as transfer
import modules.utils as utils
dblog=utils.get_logger("db")


def load_account_address_list_from_db(pool):
    conn = pool.get_connection()
    try:
        with conn.cursor() as cursor:
            # Check if the account exists
            query = """
            SELECT address,balance,last_sig FROM accounts 
            """
            cursor.execute(query)
            result = cursor.fetchall()    
            conn.commit()
            accounts=[]
            for row in result:
               addr={'address':row[0],'balance':row[1],'last_sig':row[2]}
               accounts.append(addr)
            #get tokens
            query = """
            SELECT address,symbol,name FROM tokens 
            """
            cursor.execute(query)
            result = cursor.fetchall()    
            conn.commit()
            tokens=[]
            for row in result:
               token={'address':row[0],'symbol':row[1],'name':row[2]}
               tokens.append(token)

            return accounts,tokens

    finally:
        conn.close()

def get_account_address_list():
    return "[ list from memory ]"

def add_token_to_tokens_list(pool,tokens,address):
    # address, balance,lastsig
    def _add_to_tokens(tokens,address):
        token_info=transfer.get_token_info(address)
        if not token_info:
            return False
        addr={'address':address,'symbol':token_info['symbol'],'name':token_info['name'],'url':''}
        tokens.append(addr) 
        #add to db
        add_token(pool, address,token_info['symbol'],token_info['name'],"")
        return tokens,token_info['symbol']
    if tokens == None:
        tokens=[]
        return _add_to_tokens(tokens,address)
    for t in tokens:
        if t['address'] == address:
            return tokens,t['symbol']
  
    return _add_to_tokens(tokens,address)

def add_address_to_account_list(accounts,address,balance=None,last_sig=None):
    # address, balance,lastsig
    def _add_to_accounts(accounts,address,balance=None,last_sig=None):
        addr={'address':address,'balance':balance,'last_sig':last_sig}
        accounts.append(addr) 
        return accounts,"new"
    if accounts == None:
        accounts=[]
        return _add_to_accounts(accounts,address,balance,last_sig)
    for acc in accounts:
        if acc['address'] == address:
            if balance == None and last_sig == None:
                print(f"Already exist: {acc['address']}")
                return accounts,"exist"
            else:
                accounts[acc]['balance']=balance
                accounts[acc]['last_sig']=last_sig
                return accounts
  
    return _add_to_accounts(accounts,address,balance,last_sig)

def add_token(pool, address,symbol,name,url):

    conn = pool.get_connection()
    try:
        with conn.cursor() as cursor:
            # Check if the account exists
            query = """
            SELECT address FROM tokens WHERE address = %s
            """
            cursor.execute(query, (address,))
            result = cursor.fetchone()  # Fetch the first matching record
            
            if result:
                return "Token already exists."
            
            # Insert the account if it does not exist
            insert_query = """
            INSERT INTO tokens (address, symbol,name,url)
            VALUES (%s, %s, %s, %s)
            """
            cursor.execute(insert_query, (address, symbol,name,url))
            conn.commit()
            return True

    finally:
        conn.close()


def get_unique_address_pairs(pool):

    conn = pool.get_connection()
    try:
        with conn.cursor() as cursor:
            # Check if the account exists
            query = """
            SELECT  source,  destination, SUM(amount) AS amount FROM trans GROUP BY source, destination;
            """
            cursor.execute(query)
            result = cursor.fetchall()
            return result

    finally:
        conn.close()
    
def add_account(pool, address, balance, account_type, owner, discovery_account_ts, first_tx_ts, last_tx_ts, last_sig,status,role="regular"):

    conn = pool.get_connection()
    try:
        with conn.cursor() as cursor:
            # Check if the account exists
            query = """
            SELECT address FROM accounts WHERE address = %s
            """
            cursor.execute(query, (address,))
            result = cursor.fetchone()  # Fetch the first matching record
            
            if result:
                return "Account already exists."
            
            # Insert the account if it does not exist
            insert_query = """
            INSERT INTO accounts (address, balance, account_type, owner, disovery_account_ts, first_tx_ts, last_tx_ts, last_sig,status,role)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s,%s,%s)
            """
            cursor.execute(insert_query, (address, balance, account_type, owner, discovery_account_ts, first_tx_ts, last_tx_ts, last_sig,status,role))
            conn.commit()
            return "Account added successfully."

    finally:
        conn.close()

def extract_address_from_tx(pool,accounts,df,last_sig,address):
    '''
    Extract addresses from transactions
    '''
    for _, row in df.iterrows():
        if row['source'] != address:
            accounts,status=add_address_to_account_list(accounts,row['source'],balance=None,last_sig=None)
            if status == "new":
                print(f"Add source to db: {row['source']}")
                add_account(pool, row['source'], ***************, "sol", "system", datetime.now(), datetime.now(), datetime.now(), "",status)
        if row['destination'] != address:
            accounts,status=add_address_to_account_list(accounts,row['destination'],balance=None,last_sig=None)
            if status == "new":
                print(f"Add dest to db: {row['destination']}")
                add_account(pool, row['destination'], ***************, "sol", "system", datetime.now(), datetime.now(), datetime.now(), "",status)
            
    return accounts
     
        #process transaction to normal view
        #iterate transaction andd new accounts
        #last: update account wiwthnew balance and sig 

                #    'type': row_type,
                #     'coin': 'token',
                #     'slot': row['slot'],
                #     'timestamp': row['timestamp'],
                #     'source': tx['fromUserAccount'],
                #     'destination': tx['toUserAccount'],
                #     'amount': tx['tokenAmount'],
                #     'mint': tx['mint'],
                #     'mint_name': mint_name,
                #     'UTC': row['UTC'],
                #     'signature': row['signature']
                   

def get_account(address):
    #get account data 
    pass
    return "all info"

def get_all_accounts(pool,status="%"):
    conn = pool.get_connection()
    try:
        with conn.cursor() as cursor:
            # Check if the account exists
            query = """
            SELECT address,balance,last_sig FROM accounts WHERE scan = 1 and address not like '' and status like %s
            """
            cursor.execute(query, (status,))
            result = cursor.fetchall()  # Fetch the first matching record
            conn.commit()
            accounts=[]
            current_balances=[]
            addr_sigs=[]
            for row in result:
                addr_sig={"address":row[0],"last_sig":row[2]}
                accounts.append(row[0])
                current_balances.append(row[1])
                addr_sigs.append(addr_sig)
            return accounts,current_balances,addr_sigs
            
    finally:
        conn.close()


def get_account_last_sig(account):
    #retrieve account last signature 
    pass

def get_account_balance(address,start=None,end=None):
    #get balance in sol 
    pass

def get_account_turnaround(address,start=None,end=None):
    # count in+out amounts to get turnaround for specific period default 24h
    pass


def get_account_tx_count(address,start=None,end=None):
    #retrieve amount of transactions 
    pass

def get_account_token_balance(address,token,start=None,end=None):
    #return specific token balance for account
    pass

def get_account_all_token_balance(address):
    #retur list of alltokens acocunt has and its balance
    pass

def get_account_age(address):
    #substracte now - oldest signatrure date 
    pass

def get_account_last_sig(address):
    #return sig,time
    pass

def check_all_account_balances():
    #get all account balances int oarray 
    #get all new balances and compare
    # update account balance if difernece
    pass
def update_account_last_sig(pool,account,last_sig):
    conn = pool.get_connection()
    try:
        with conn.cursor() as c:
            # Check if the account exists
            print(f"acc:{account} sig:{last_sig}")
            query = """
            update accounts set last_sig = %s,status='exist'  where address = %s 
            """
            c.execute(query,(last_sig,account))
            conn.commit()
            return True
    finally:
        dblog.info(f"update: account: {account}  last_sig: {last_sig}")
        conn.close()

def update_account_balance(pool,account,balance):
    conn = pool.get_connection()
    try:
        with conn.cursor() as cursor:
            # Check if the account exists
            query = """
            update accounts set balance = %s,status="update"   where address = %s 
            """
            cursor.execute(query,(balance,account))
            conn.commit()

            insert_query = """
            INSERT INTO account_balance (address, balance,ts)
            VALUES (%s, %s, %s)
            """
            cursor.execute(insert_query, (account, balance,datetime.now() ))
            conn.commit()

    finally:
        conn.close()

def update_account_balances(pool,balances):
    conn = pool.get_connection()
    try:
        with conn.cursor() as cursor:
            ts=datetime.now() 
            for index, row in balances.iterrows():
                print(f"update account: {row['account']}  new balance: {row['balance']}")

                query = """
                update accounts set balance = %s,status="update"   where address = %s 
                """
                cursor.execute(query,(row['balance'],row['account']))


                insert_query = """
                INSERT INTO account_balance (address, balance,ts)
                VALUES (%s, %s, %s)
                """
                cursor.execute(insert_query, (row['account'], row['balance'],ts ))
            print("committing")
            conn.commit()

    finally:
        conn.close()       

def add_tx_to_db(pool,df,minsol):
    print("procesing txs")
    conn = pool.get_connection()
    try:
        with conn.cursor() as cursor:

            for _, tx in df.iterrows():
                if tx["coin"] == "sol" and float(tx["amount"]) < minsol:
                    continue
                elif tx["type"] == "BURN":
                    continue
                else:
                    ts=datetime.fromtimestamp(tx["timestamp"])   
                    q=f"INSERT INTO trans (tx_type, currency_name, slot, source, destination, amount, mint_address,mint_name, sig,transaction_date) VALUES ('{tx["type"]}', '{tx["coin"]}', {tx["slot"]}, '{tx["source"]}', '{tx["destination"]}', '{tx["amount"]}','{tx["mint"]}','{tx["mint_name"]}','{tx["signature"]}','{ts}');"
                    cursor.execute(q)
            conn.commit()
            
            return "txs added successfully."

    finally:
        conn.close()

def update_account_balances_many(pool, balances):
    conn = pool.get_connection()
    try:
        with conn.cursor() as cursor:
            ts = datetime.now()
            update_data = []
            insert_data = []

            for index, row in balances.iterrows():
                print(f"update account: {row['account']}  new balance: {row['balance']}")
                update_data.append((row['balance'], row['account']))
                insert_data.append((row['account'], row['balance'], ts))

            # Batch update queries
            update_query = """
            UPDATE accounts SET balance = %s, status="update" WHERE address = %s
            """
            cursor.executemany(update_query, update_data)

            insert_query = """
            INSERT INTO account_balance (address, balance, ts)
            VALUES (%s, %s, %s)
            """
            cursor.executemany(insert_query, insert_data)

            print("Committing")
            conn.commit()

    except mysql.connector.Error as err:
        print(f"Error: {err}")
        conn.rollback()
    finally:
        conn.close()