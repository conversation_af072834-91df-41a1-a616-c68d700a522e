from neo4j import GraphDatabase


class Neo4jClient:

    def __init__(self, uri, user, password):
        self.driver = GraphDatabase.driver(uri, auth=(user, password))

    def close(self):
        self.driver.close()

    def add_unique_node(self, label, properties):
        with self.driver.session() as session:
            result = session.write_transaction(self._create_unique_node, label, properties)
            return result

    @staticmethod
    def _create_unique_node(tx, label, properties):
        # Check if the node exists
        match_query = f"MATCH (n:{label} {{name: $name}}) RETURN n"
        result = tx.run(match_query, name=properties['name']).single()
        
        if result:
            return result[0]
        
        # If the node does not exist, create it
        create_query = f"CREATE (n:{label} {{"
        create_query += ", ".join([f"{key}: ${key}" for key in properties.keys()])
        create_query += "}) RETURN n"
        result = tx.run(create_query, **properties)
        return result.single()[0]

    def update_node(self, label, name, properties):
        with self.driver.session() as session:
            result = session.write_transaction(self._update_node, label, name, properties)
            return result

    @staticmethod
    def _update_node(tx, label, name, properties):
        set_clause = ", ".join([f"n.{key} = ${key}" for key in properties.keys()])
        query = f"MATCH (n:{label} {{name: $name}}) SET {set_clause} RETURN n"
        params = {"name": name}
        params.update(properties)
        result = tx.run(query, **params)
        return result.single()[0]

    def add_unique_relationship(self, label1, properties1, label2, properties2, rel_type, rel_properties):
        with self.driver.session() as session:
            result = session.write_transaction(self._create_unique_relationship, label1, properties1, label2, properties2, rel_type, rel_properties)
            return result

    @staticmethod
    def _create_unique_relationship(tx, label1, properties1, label2, properties2, rel_type, rel_properties):
        query = (
            f"MATCH (a:{label1} {{name: $a_name}}), (b:{label2} {{name: $b_name}}) "
            f"MERGE (a)-[r:{rel_type}]->(b) "
            f"ON CREATE SET "
            + ", ".join([f"r.{key} = ${'r_' + key}" for key in rel_properties.keys()]) +
            f" ON MATCH SET "
            + ", ".join([f"r.{key} = COALESCE(r.{key}, ${'r_' + key})" for key in rel_properties.keys()]) +
            " RETURN r"
        )
        params = {'a_name': properties1['name'], 'b_name': properties2['name']}
        params.update({f'r_{key}': value for key, value in rel_properties.items()})
        result = tx.run(query, **params)

        #return result.single()[0]

    def read_data(self, query):
        with self.driver.session() as session:
            result = session.read_transaction(self._execute_read_query, query)
            return result

    @staticmethod
    def _execute_read_query(tx, query):
        result = tx.run(query)
        return [record.data() for record in result]

    def drop_all(self):
        with self.driver.session() as session:
            session.write_transaction(self._drop_all)

    @staticmethod
    def _drop_all(tx):
        tx.run("MATCH (n) DETACH DELETE n")