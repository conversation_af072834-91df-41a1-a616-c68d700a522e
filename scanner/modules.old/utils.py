#!/usr/bin/env python3

import logging

def get_logger(logname="default"):
    logger = logging.getLogger(logname)
    logger.setLevel(logging.INFO)

    # Create console handler and set level to info
    ch = logging.StreamHandler()
    ch.setLevel(logging.INFO)

    # Create formatter and add it to the handler
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    ch.setFormatter(formatter)

    # Add the handler to the logger
    logger.addHandler(ch)

    # Suppress logging from other modules
    logging.getLogger('asyncio').setLevel(logging.WARNING)
    logging.getLogger('pika').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)
    logging.getLogger('solana').setLevel(logging.WARNING)
    logging.getLogger('solders').setLevel(logging.WARNING)

    return logger

