#!/usr/bin/env python3
from neo4j import GraphDatabase
from modules.neo import Neo4jClient

import pandas as pd
from tabulate import tabulate

from datetime import datetime, timedelta,time
import logging
import sys
import modules.transfer as transfer
import modules.db as db
from mysql.connector import pooling
import modules.utils as utils
log=utils.get_logger("graph")

def print_nodes():
    query = """
    MATCH (a)-[r]->(b)
    RETURN a.name AS From, type(r) AS Relationship, b.name AS To, r.amount_send AS Amount_Sent, r.amount_received AS Amount_Received, r.amount AS amount
    """
    data = client.read_data(query)
    df = pd.DataFrame(data)
    print(tabulate(df, headers='keys', tablefmt='psql'))

if __name__ == "__main__":
    uri = "bolt://***************:7687"
    user = "neo4j"
    password = "pump2pump"

    client = Neo4jClient(uri, user, password)

    pool = pooling.MySQLConnectionPool(
        pool_name="mypool", 
        pool_size=10,user='pump2',
        password='pump2',
        host='***************',
        db='pump')

    print_nodes()
    #sys.exit()
    client.drop_all()
    sys.exit()
    
    accounts,current_balances,_=db.get_all_accounts(pool,"%")
    df0 = pd.DataFrame({'account': accounts, 'balance': current_balances})


    def _get_balance(address):
        global df0
      
        for _,row in df0.iterrows():
            if row["account"][0:5] == address:
                print("got it ")
                if row['balance'] == ***************:
                    return 0
                else:
                    return float(row['balance'])
        return 0

    address_list=db.get_unique_address_pairs(pool)
    df=pd.DataFrame(address_list)
    for _, addr in df.iterrows():

        print(f"1:{addr[0]}    2:{addr[1]} balance:{_get_balance(addr[0])} ,balance2:{_get_balance(addr[1])} ")
        address1 = client.add_unique_node("Address", {"name": str(addr[0]), "balance": _get_balance(addr[0]), "total_tx": 20, "role": "user", "group": "B", "age": 30, "type": "wallet"})
        address2 = client.add_unique_node("Address", {"name": str(addr[1]), "balance": _get_balance(addr[1]), "total_tx": 20, "role": "user", "group": "B", "age": 30, "type": "wallet"})
        print(f"edgelabel: {addr[2]}")
        relationship = client.add_unique_relationship("Address", {"name": str(addr[0])}, "Address", {"name": str(addr[1])}, "TRANSFERRED", {"amount": float(addr[2]),"amount_send": 50, "amount_received": 50}
    )
    client.close()
    sys.exit()


    #address2 = client.add_unique_node("Address", {"name": "address9", "balance": 200, "total_tx": 20, "role": "user", "group": "B", "age": 30, "type": "wallet"})

    #relationship = client.add_unique_relationship(
    #    "Address", {"name": "address8"}, "Address", {"name": "address§"}, "TRANSFERRED", {"amount_send": 50, "amount_received": 50}
    #)





  



    # Update node
    #updated_address1 = client.update_node("Address", "address1", {"balance": 150, "total_tx": 15, "role": "admin"})




    client.close()