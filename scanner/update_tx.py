#!/usr/bin/env python3

import pandas as pd
from datetime import datetime, timedelta
import logging
import sys
import modules.transfer as transfer
import modules.db as db
import modules.neo as neo
from mysql.connector import pooling
import modules.utils as utils
log=utils.get_logger("tx")
from modules.config import *
import redis as redis
from neo4j import GraphDatabase
from modules.neo import Neo4jClient
import time

if __name__ == "__main__":

     # my account
    db_pool = pooling.MySQLConnectionPool(
        pool_name=mysql_pool_name, 
        pool_size=mysql_pool_size,
        user=mysql_user,
        password=mysql_password,
        host=mysql_host,
        db=mysql_db )


    neo_client = Neo4jClient(neo_uri, neo_user, neo_password)

    # pool = redis.ConnectionPool(host='pump', port=6379, db=0)
    # client = redis.Redis(connection_pool=pool)
    # client.set('key', 'value')
    # print(client.get('key'))  # Output will be b'value'

    while True:
        log.info("Load account,tokens")
        accounts,tokens=db.load_account_address_list_from_db(db_pool)
        _,_,last_sigs=db.get_all_accounts(db_pool,"update")
        

        for address in last_sigs:
            log.info(f"prosess transactins for address: {address["address"]}")
            addr=address["address"]
            if addr != master_address:
                distance=neo_client.count_hops(master_address,address["address"])
                print(f"distance: {distance}" )
                if distance != None and distance<=max_hops:
                    txs,last_sig=transfer.fetch_all_transactions_to_sig(address["address"],address["last_sig"])

                    if len(txs)>0:
                        log.info("process transactions")
                        log.info("Extracting data from transaction log")
                        df,tokens=transfer.process_transactions(db_pool,txs,tokens,addr)
                        log.info("add tx to db")
                        db.add_tx_to_db(db_pool,df,1)
                        log.info("add to graph")
                        log.info("update address last_sig and satus")
                        db.update_account_last_sig(db_pool,addr,last_sig)
                        log.info("Process transactoins 2")
                        accounts=db.extract_address_from_tx(neo_client,db_pool,accounts,df,last_sig,address)
                        print(f"Adding to graph")
                         
                    else:
                        db.update_account_last_sig(db_pool,address["address"],address["last_sig"])
        log.info("Transaction finished. waiting for next ")        
        time.sleep(5)
        


   