#!/usr/bin/env python3


from datetime import datetime, timedelta
import modules.transfer as transfer
import modules.db as db
from mysql.connector import pooling

import modules.db as db
import time
import pandas as pd

import modules.utils as utils
log=utils.get_logger()

if __name__ == "__main__":
    log.info("start balace updates")
     # my account
    pool = pooling.MySQLConnectionPool(
        pool_name="mypool", 
        pool_size=10,user='pump2',
        password='pump2',
        host='***************',
        db='pump')


    while True:

        accounts, current_balances, _ = db.get_all_accounts(pool, "%")

        batch_size = 100
        balances_df = pd.DataFrame()  # Initialize an empty DataFrame to store balances

        # Process accounts in batches of 100
        for start in range(0, len(accounts), batch_size):
            end = start + batch_size
            account_batch = accounts[start:end]
            current_balance_batch = current_balances[start:end]
            
            # Get account balances for the current batch
            batch_balances_df = transfer.get_account_balances(account_batch, current_balance_batch)
            
            # Append the batch balances to the main DataFrame
            balances_df = pd.concat([balances_df, batch_balances_df], ignore_index=True)

        # Process the DataFrame in batches of 100 for updating

        db.update_account_balances_many(pool, balances_df)

        print("All accounts updated. Waiting for the next cycle.")
        time.sleep(5)
            
