#!/usr/bin/env python
from flask import Flask, request, jsonify
import json

app = Flask(__name__)

# Webhook endpoint
@app.route('/webhook', methods=['POST'])
def helius_webhook():
    try:
        # Get JSON data from the incoming request
        data = request.json

        #print(json.dumps(data))
        # Process the received data
        # "description": "B73DWVJRGXcHN6xmmiRGRnNTvjnbWbRvV1Br2BzoTYjn minted 1 tokens.",
        # "events": {
        #     "setAuthority": [
        #         {
        #             "account": "DGgi94QL7WVbgbeWyWY6zPmuu2dmNMzkLyP9SRC7pump",
        #             "from": "TSLvdd1pWpHVjahSpsvCXUbgwsL3JAcvokwaKt1eokM",
        #             "innerInstructionIndex": 13,
        #             "instructionIndex": 3,
        #             "to": ""
        #         } 
        #     ]



        
        if "setAuthority" in data[0]["events"]:
            print(len(data),data[0]["description"],data[0]["events"]["setAuthority"][0]["account"])  
        else:   
            #print(len(data),data[0]["description"],json.dumps(data[0]["tokenTransfers"]))

            for token in data[0]["tokenTransfers"]:
                if token["fromTokenAccount"] != "" and token["mint"] != "So11111111111111111111111111111111111111112":
                    print(json.dumps(token))

            

        # Respond to Helius to acknowledge the receipt of the webhook
        return jsonify({"status": "success"}), 200
    except Exception as e:
        print("Error processing webhook:", e)
        return jsonify({"status": "error", "message": str(e)}), 500

if __name__ == '__main__':
    # Start the Flask web server
    app.run(host='0.0.0.0', port=6000)