import os
import requests
from flask import Flask, request, jsonify, Response, render_template, redirect
from dotenv import load_dotenv
from PIL import Image
from functools import wraps
from base64 import b64decode
import tempfile
import mysql.connector
from datetime import datetime
import time
import json 
import bleach
from bs4 import BeautifulSoup
import re
from flask import send_from_directory
import uuid
from flask_cors import CORS
import cv2
import shortuuid
from moviepy.editor import VideoFileClip

# Load environment variables
#load_dotenv()

# Initialize Flask app
app = Flask(__name__)
CORS(app)

API_URL = "https://openrouter.ai/api/v1/chat/completions"
API_KEY = "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"
MODEL = "meta-llama/llama-3.1-405b-instruct:free"

TWEETS_URL = "http://pumpfun.mooo.com:7777/get-tweets"
processed_reviews = []

# API URL
CREATE_TOKEN_URL = 'https://api.solanaapis.com/pumpfun/create/token'

# Basic Auth Credentials
USERNAME = 'admin'  
PASSWORD = 'pumpfun'

UPLOAD_FOLDER = 'videos'
IMAGES_FOLDER = 'static/images'

os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(IMAGES_FOLDER, exist_ok=True)


DB_CONFIG = {
    'user': 'pump2',
    'password': 'pump2',
    'host': '**************',
    'database': 'pump'
}

@app.route('/shorten', methods=['POST'])
def shorten_url():
    data = request.json
    long_url = data.get('long_url')

    if not long_url:
        return jsonify({'error': 'Missing long_url'}), 400

    short_url = shortuuid.ShortUUID().random(length=6)


    conn = mysql.connector.connect(**DB_CONFIG)
    cursor = conn.cursor()

    try:
        cursor.execute("INSERT INTO urls (long_url, short_url) VALUES (%s, %s)", (long_url, short_url))
        conn.commit()
    except mysql.connector.Error as err:
        conn.rollback()
        return jsonify({'error': str(err)}), 500
    finally:
        cursor.close()
        conn.close()

    return jsonify({'short_url': f"https://kroocoin.xyz/shortened/{short_url}"}), 201


@app.route('/shortened/<short_url>', methods=['GET'])
def redirect_url(short_url):
    conn = mysql.connector.connect(**DB_CONFIG)
    cursor = conn.cursor(dictionary=True)

    try:
        cursor.execute("SELECT long_url FROM urls WHERE short_url = %s", (short_url,))
        result = cursor.fetchone()
        if not result:
            return jsonify({'error': 'URL not found'}), 404
    finally:
        cursor.close()
        conn.close()

    return redirect(result['long_url'])


@app.route('/process_image_url', methods=['POST'])
def process_image_url():
    """
    Downloads the provided image_url and saves it to the static/images folder for preview.
    """
    data = request.json
    image_url = data.get('image_url')

    if not image_url:
        return jsonify({'error': 'Image URL is required'}), 400

    try:
        # Generate a unique filename for the image
        image_id = str(uuid.uuid4())
        image_path = os.path.join(UPLOAD_FOLDER, f"{image_id}.jpg")

        # Download the image
        response = requests.get(image_url, stream=True)
        response.raise_for_status()

        # Save the image locally in static/images
        with open(image_path, 'wb') as image_file:
            for chunk in response.iter_content(chunk_size=8192):
                image_file.write(chunk)

        # Return the relative path to the image for use in the frontend
        return jsonify({'image_path': f"/videos/{image_id}.jpg"})
    except requests.exceptions.RequestException as e:
        return jsonify({'error': f"Error downloading image: {str(e)}"}), 500


    
@app.route('/download', methods=['POST'])
def download_video():
    data = request.json
    video_url = data.get('url')
    video_id = str(uuid.uuid4())
    video_path = os.path.join(UPLOAD_FOLDER, f"{video_id}.mp4")
    
    # Download the video file
    try:
        response = requests.get(video_url, stream=True)
        response.raise_for_status()  # Check if the download was successful
        with open(video_path, 'wb') as video_file:
            for chunk in response.iter_content(chunk_size=8192):
                video_file.write(chunk)
    except requests.exceptions.RequestException as e:
        return jsonify({'error': f"Failed to download video: {str(e)}"}), 400

    # Extract images
    try:
        clip = VideoFileClip(video_path)
        duration = clip.duration
        timestamps = [int(duration * (i / 10)) for i in range(1, 11)]
        image_paths = []

        for idx, timestamp in enumerate(timestamps):
            frame_path = os.path.join(IMAGES_FOLDER, f"{video_id}_{idx}.jpg")
            frame = clip.get_frame(timestamp)
            cv2.imwrite(frame_path, cv2.cvtColor(frame, cv2.COLOR_RGB2BGR))
            image_paths.append(f"/static/images/{video_id}_{idx}.jpg")
        
        return jsonify({'images': image_paths, 'video_id': video_id})
    except Exception as e:
        return jsonify({'error': f"Failed to process video: {str(e)}"}), 500

@app.route('/static/images/<filename>')
def get_image(filename):
    return send_from_directory(IMAGES_FOLDER, filename)

@app.route('/videos/<filename>')
def get_videos(filename):
    return send_from_directory(UPLOAD_FOLDER, filename)

def clean_html(text):
    soup = BeautifulSoup(text, "html.parser")
    return soup.get_text()

def rank_tweets_for_hype(tweets):
    """
    Sends the tweets to the GPT model and asks it to evaluate which ones have the most hype potential.
    Returns the top 5 tweets ranked by interest.
    """
    combined_tweets = "\n\n".join([f"Tweet by {tweet['author']}: {tweet['tweet']}" for tweet in tweets])
    prompt = (
        f"Here are some tweets. Rank them by their potential to generate hype and excitement in the community. "
        f"Pick the top 5 most interesting tweets:\n\n{combined_tweets}\n\n"
        "Return the ranked tweets as a JSON array with the format: "
        '[{"tweet": "original tweet text", "author": "author handle"}].'
    )

    payload = {
        "model": MODEL,
        "messages": [{"role": "user", "content": prompt}],
        "temperature": 0.7,
        "max_tokens": 1000,
    }
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json",
    }

    try:
        response = requests.post(API_URL, headers=headers, json=payload)
        if response.status_code == 200:
            data = response.json()
            print(data)
            ranked_tweets = json.loads(data['choices'][0]['message']['content'])
            return ranked_tweets
        else:
            print("GPT API Error:", response.status_code, response.text)
            return []
    except Exception as e:
        print("Error ranking tweets:", e)
        return []

# Function to find top 5 bullish tweets
def get_top_5_bullish_tweets(tweets):
    """
    Sends the tweets to GPT and retrieves the top 5 most bullish tweets.
    """
    combined_tweets = "\n\n".join([f"Tweet by {tweet['author']}: {tweet['tweet']}" for tweet in tweets])
    prompt = (
        f"Here are some tweets. Identify the top 5 most bullish and hype-generating tweets. "
        f"Focus on tweets that could excite the community about investing or buying:\n\n{combined_tweets}\n\n"
        "Return the ranked tweets as a JSON array with the format: "
        '[{"tweet": "original tweet text", "author": "author handle"}].'
    )

    payload = {
        "model": MODEL,
        "messages": [{"role": "user", "content": prompt}],
        "temperature": 0.7,
        "max_tokens": 1000,
    }
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json",
    }

    try:
        response = requests.post(API_URL, headers=headers, json=payload)
        if response.status_code == 200:
            data = response.json()
            ranked_tweets = json.loads(data['choices'][0]['message']['content'])
            return ranked_tweets
        else:
            print("GPT API Error:", response.status_code, response.text)
            return []
    except Exception as e:
        print("Error getting top 5 bullish tweets:", e)
        return []

# Route to get the top 5 bullish tweets
@app.route('/top-tweets', methods=['GET'])
def top_tweets():
    """
    Fetch tweets, process them, and return the top 5 most bullish ones.
    """
    try:
        # Fetch and clean the latest tweets
        tweets = fetch_and_clean_tweets()

        # Rank tweets for bullish sentiment
        top_bullish_tweets = get_top_5_bullish_tweets(tweets)

        return jsonify({
            "status": "success",
            "top_bullish_tweets": top_bullish_tweets
        }), 200

    except Exception as e:
        return jsonify({
            "status": "error",
            "message": f"An error occurred: {str(e)}"
        }), 500


# Function to fetch and clean tweets
def fetch_and_clean_tweets():
    try:
        response = requests.get(TWEETS_URL)
        if response.status_code == 200:
            data = response.json()
            if data["status"] == "success":
                cleaned_tweets = [
                    {"tweet": clean_html(tweet["userText"]), "author": tweet["authorHandle"]}
                    for tweet in data["tweets"]
                ]
                return cleaned_tweets
            else:
                print("Error: API response indicates failure.")
                return []
        else:
            print("Error fetching tweets:", response.status_code)
            return []
    except Exception as e:
        print("Error during fetching tweets:", e)
        return []

# Function to process tweets with GPT model
def process_tweets_with_gpt(tweets):
    reviews = []
    for tweet_data in tweets:
        tweet = tweet_data["tweet"]
        author = tweet_data["author"]
        if tweet.strip():  # Skip empty tweets
            payload = {
                "model": MODEL,
                "messages": [{"role": "user", "content": tweet.strip()}],
                "temperature": 0.7,
                "max_tokens": 8000
            }
            headers = {
                "Authorization": f"Bearer {API_KEY}",
                "Content-Type": "application/json"
            }
            try:
                response = requests.post(API_URL, headers=headers, json=payload)
                if response.status_code == 200:
                    data = response.json()
                    review = data['choices'][0]['message']['content']
                    reviews.append({"tweet": tweet, "author": author, "review": review})
                else:
                    print("GPT API Error:", response.status_code, response.text)
            except Exception as e:
                print("Error processing tweet:", e)
    return reviews

# Route to serve processed reviews
@app.route('/get-reviews', methods=['GET'])
def get_reviews():
    tweets = fetch_and_clean_tweets()
    processed_reviews = process_tweets_with_gpt(tweets)
    return jsonify(processed_reviews)

# Update `/reviews` route to include top 5 tweets
@app.route('/reviews')
def reviews():
    """
    Renders the reviews page, including the top 5 ranked tweets.
    """
    try:
        # Fetch and clean tweets
        tweets = fetch_and_clean_tweets()

        # Rank tweets for hype
        top_tweets = rank_tweets_for_hype(tweets)

        # Render the page with the ranked tweets
        return render_template('reviews.html', tweets=tweets, top_tweets=top_tweets)
    except Exception as e:
        return render_template('error.html', message=f"An error occurred: {str(e)}"), 500


@app.route('/get-tweets', methods=['GET'])
def get_tweets():
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        cursor = connection.cursor(dictionary=True)

        # Fetch the latest 100 tweets
        query = "SELECT * FROM tweets ORDER BY timestamp DESC LIMIT 100"
        cursor.execute(query)
        tweets = cursor.fetchall()

        # Process tweets
        for tweet in tweets:
            # Convert timestamp from Unix to ISO format
            tweet['timestamp'] = datetime.fromtimestamp(tweet['timestamp']).isoformat()

            # Convert imageLinks from JSON string back to list
            if tweet['imageLinks']:
                tweet['imageLinks'] = json.loads(tweet['imageLinks'])
            else:
                tweet['imageLinks'] = []

        return jsonify({
            "status": "success",
            "tweets": tweets
        }), 200

    except Exception as e:
        return jsonify({
            "status": "error",
            "message": f"An error occurred: {str(e)}"
        }), 500

    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()

def convert_to_unix_timestamp(time_string):
    """
    Converts a given time string into a Unix timestamp (seconds since epoch).

    Args:
        time_string (str): A time string to convert.

    Returns:
        int: A Unix timestamp as an integer.
    """
    try:
        # Try parsing ISO 8601 format
        dt = datetime.fromisoformat(time_string)
    except ValueError:
        # Handle other common formats (e.g., 'Nov 21 2024 12:34 PM')
        try:
            dt = datetime.strptime(time_string, "%b %d %Y %I:%M %p")
        except ValueError:
            # Handle MySQL-compatible format (YYYY-MM-DD HH:MM:SS)
            try:
                dt = datetime.strptime(time_string, "%Y-%m-%d %H:%M:%S")
            except Exception as e:
                raise ValueError(f"Unsupported time format: {time_string}. Error: {str(e)}")
    
    # Convert to Unix timestamp
    return int(time.mktime(dt.timetuple()))


def add_token_to_database(tweet_data):
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        cursor = connection.cursor()

        # Insert or update query
        query = """
            INSERT INTO tweets (
                authorName, authorHandle, tweetLink, userText, targetUserText,
                actionType, targetUser, imageLinks, timestamp
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            ON DUPLICATE KEY UPDATE
                authorName = VALUES(authorName),
                authorHandle = VALUES(authorHandle),
                userText = VALUES(userText),
                targetUserText = VALUES(targetUserText),
                actionType = VALUES(actionType),
                targetUser = VALUES(targetUser),
                imageLinks = VALUES(imageLinks),
                timestamp = VALUES(timestamp)
        """
        values = (
            tweet_data['authorName'],
            tweet_data['authorHandle'],
            tweet_data['tweetLink'],
            tweet_data['userText'],
            tweet_data['targetUserText'],
            tweet_data['actionType'],
            tweet_data['targetUser'],
            json.dumps(tweet_data['imageLinks']),
            convert_to_unix_timestamp(tweet_data['timestamp'])
        )
       
        # Execute the query
        cursor.execute(query, values)
        connection.commit()

        #print(f"Tweet successfully inserted/updated in the database: {tweet_data['tweetLink']}")
    except mysql.connector.Error as err:
        print(f"Error inserting/updating tweet into the database: {err}")
        raise
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()


# Function to check authentication
def check_auth(username, password):
    """Check if a username/password combination is valid."""
    return username == USERNAME and password == PASSWORD

# Function to prompt for authentication
def authenticate():
    """Send a 401 response to prompt for basic authentication."""
    return Response(
        'Could not verify your access level for that URL.\n'
        'You have to login with proper credentials.', 401,
        {'WWW-Authenticate': 'Basic realm="Login Required"'})

# Decorator to require authentication on routes
def requires_auth(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        auth = request.authorization
        if not auth or not check_auth(auth.username, auth.password):
            return authenticate()
        return f(*args, **kwargs)
    return decorated

# Convert image to PNG
def convert_to_png(image_path):
    try:
        with Image.open(image_path) as img:
            png_path = f"{os.path.splitext(image_path)[0]}.png"
            img.save(png_path, format="PNG")
            return png_path
    except Exception as e:
        raise ValueError(f"Error converting image to PNG: {str(e)}")




@app.route('/image/<filename>')
def serve_image(filename):
    return send_from_directory('static/images', filename)

@app.route('/', methods=['GET'])
@requires_auth
def home():
    return render_template('home.html')

@app.route('/upload', methods=['POST'])
def upload_data():
    try:
        # Get the JSON data from the request
        json_data = request.get_json()

        # If no data is received, return an error
        if not json_data:
            return jsonify({"error": "No JSON data provided"}), 400

        # Save the data to a file
        with open("upload.json", "w", encoding="utf-8") as file:
            file.write(request.data.decode("utf-8"))  # Save the raw JSON string

        return jsonify({"message": "Data uploaded successfully"}), 200
    except Exception as e:
        # Handle any errors that occur
        return jsonify({"error": str(e)}), 500
    

@app.route('/instagram', methods=['GET'])
@requires_auth
def instagram():
    code = request.args.get("code")
    inst_url=f"https://www.instagram.com/p/{code}"
    image_url = request.args.get("image_url")
    video_url = request.args.get("video_url")
    local_image_paths = []


    response = requests.get(image_url)
    filename = f"image_{uuid.uuid4().hex}.jpg"
    filepath = f"static/images/{filename}"
    with open(filepath, 'wb') as f:
        f.write(response.content)
    local_image_paths.append(f"/image/{filename}")


    return render_template('instagram.html', image_urls=local_image_paths,inst_url=inst_url,video_url=video_url)

@app.route('/test', methods=['GET'])
@requires_auth
def test():
    return render_template('test.html')

@app.route('/proxy')
def proxy_video():
    # Get the video URL from the query parameter
    video_url = request.args.get('url')
    if not video_url:
        return "No video URL provided!", 400

    try:
        # Fetch the video content from the external source
        resp = requests.get(video_url, stream=True)
        # Return the response with the correct headers
        headers = {
            'Content-Type': resp.headers.get('Content-Type', 'video/mp4'),
            'Access-Control-Allow-Origin': '*',  # Allow cross-origin access
        }
        return Response(resp.iter_content(chunk_size=8192), headers=headers, status=resp.status_code)
    except Exception as e:
        return f"Failed to fetch video: {str(e)}", 500
    

@app.route('/tweets')
@requires_auth
def tweets():
    return render_template('tweets.html')


# Route to handle form submission with password protection
@app.route('/create-token', methods=['POST'])
@requires_auth
def create_token():
    try:
        # Parse form data
        token_name = request.form['token_name']
        token_symbol = request.form['token_symbol']
        description = request.form.get('token_description', '')
        website = request.form.get('website', '')
        twitter = request.form.get('twitter', '')
        telegram = request.form.get('telegram', '')
        sol_amount = float(request.form.get('sol_amount', 1.4))
        pasted_image_data = request.form.get('pasted_image_data', '')

        # Load private key from environment variable
        private_key = os.getenv("PRIVATE_KEY")
        if not private_key:
            return render_template('result.html', title="Error", header_icon="🚫", header_message="Error Creating Token", message="Wallet private key not found in environment variables.", links=None), 500

        # Ensure an image has been pasted
        if not pasted_image_data:
            return render_template('result.html', title="Error", header_icon="🚫", header_message="Error Creating Token", message="No image was pasted. Please paste an image to proceed.", links=None), 400

        # Handle pasted image data
        try:
            header, encoded = pasted_image_data.split(",", 1)
            file_ext = header.split("/")[1].split(";")[0]  # Extract the image file extension
            image_data = b64decode(encoded)
        except Exception as e:
            return render_template('result.html', title="Error", header_icon="🚫", header_message="Error Processing Image", message=f"Invalid image data. {str(e)}", links=None), 400

        # Save the image to a temporary file
        temp_image_file = tempfile.NamedTemporaryFile(delete=False, suffix=f".{file_ext}")
        temp_image_file.write(image_data)
        temp_image_file.close()
        temp_image_path = temp_image_file.name
        print(f"Image saved to: {temp_image_path}")
        # Convert to PNG if necessary
        if not temp_image_path.endswith('.png'):
            temp_image_path = convert_to_png(temp_image_path)

        # Prepare data for API request
        files = {
            "private_key": (None, private_key),
            "amount": (None, str(sol_amount)),
            "name": (None, token_name),
            "symbol": (None, token_symbol),
            "description": (None, description),
            "website": (None, website),
            "twitter": (None, twitter),
            "telegram": (None, telegram),
            "image": open(temp_image_path, "rb"),
        }

        # Send request to the API
        response = requests.post(CREATE_TOKEN_URL, files=files)

        os.remove(temp_image_path)  # Clean up the temporary file

        # Return the API response
        if response.ok:
            data = response.json()
            mint_address = data.get('mint')
            transaction_id = data.get('txid')
            if mint_address and transaction_id:
                # Construct URLs
                solscan_url = f"https://solscan.io/tx/{transaction_id}"
                bullx_url = f"https://neo.bullx.io/terminal?chainId=1399811149&address={mint_address}"
                pumpfun_url = f"https://pump.fun/coin/{mint_address}"
    

                # Links to display
                links = [
                    {"url": solscan_url, "icon": "🔗", "text": "View Transaction on Solscan"},
                    {"url": bullx_url, "icon": "🔗", "text": "View Token on Bullx Terminal"},
                    {"url": pumpfun_url, "icon": "🔗", "text": "View Token on Pump.fun"},
    
                ]

                # Render success page
                return render_template(
                    'result.html',
                    title="Token Created",
                    header_icon="🎉",
                    header_message="Token Successfully Created!",
                    message="Your token has been created. Here are some useful links:",
                    mint=mint_address,
                    links=links
                )
            else:
                return render_template(
                    'result.html',
                    title="Error Creating Token",
                    header_icon="🚫",
                    header_message="Error Creating Token",
                    message="Successful response but missing 'mint' or 'transaction' in response data.",
                    links=None
                ), 500
        else:
            return render_template(
                'result.html',
                title="Error Creating Token",
                header_icon="🚫",
                header_message="Error Creating Token",
                message=f"There was an error creating your token.<br><strong>Error Details:</strong> {response.text}",
                links=None
            ), response.status_code

    except Exception as e:
        return render_template(
            'result.html',
            title="Server Error",
            header_icon="⚠️",
            header_message="Server Error",
            message=f"An unexpected error occurred.<br><strong>Error Details:</strong> {str(e)}",
            links=None
        ), 500

@app.route('/add-tweet', methods=['POST'])
@requires_auth
def add_tweet():
    try:
        # Parse JSON data from the request body
        tweet_data = request.get_json()
      

        # Validate required fields
        required_fields = [
            "authorName", "authorHandle", "tweetLink", "userText",
            "targetUserText", "actionType", "targetUser", "imageLinks", "timestamp"
        ]
        missing_fields = [field for field in required_fields if field not in tweet_data["tweets"][0]]
        if missing_fields:
            return jsonify({
                "status": "error",
                "message": f"Missing required fields: {', '.join(missing_fields)}"
            }), 400

        # Insert the tweet into the database
 
        add_token_to_database(tweet_data["tweets"][0])

        return jsonify({
            "status": "success",
            "message": "Tweet added to the database successfully!"
        }), 200

    except Exception as e:
        return jsonify({
            "status": "error",
            "message": f"An error occurred: {str(e)}"
        }), 500

@app.route('/show-tweets')
@requires_auth
def tweets_render():
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        cursor = connection.cursor(dictionary=True)

        # Fetch the latest 100 tweets
        query = "SELECT * FROM tweets ORDER BY timestamp DESC LIMIT 100"
        cursor.execute(query)
        tweets = cursor.fetchall()

        # Process tweets
        for tweet in tweets:
            # Convert timestamp to formatted string
            tweet['timestamp'] = datetime.fromtimestamp(tweet['timestamp']).strftime('%Y-%m-%d %H:%M:%S')

            # Convert imageLinks back to list
            if tweet['imageLinks']:
                tweet['imageLinks'] = json.loads(tweet['imageLinks'])
            else:
                tweet['imageLinks'] = []

            # Sanitize userText and targetUserText
            allowed_tags = [
                'a', 'span', 'strong', 'em', 'div', 'img', 'p', 'blockquote', 'br', 'ul', 'li', 'ol', 'b', 'i'
            ]
            allowed_attributes = {
                'a': ['href', 'title', 'target', 'rel', 'class'],
                'img': ['src', 'alt', 'class', 'data-*'],
                '*': ['class', 'style', 'role', 'tabindex', 'aria-expanded', 'data-*', 'title']
            }

            # Use get() method with default empty string
            tweet['userText'] = bleach.clean(tweet.get('userText', ''), tags=allowed_tags, attributes=allowed_attributes, strip=True)
            tweet['targetUserText'] = bleach.clean(tweet.get('targetUserText', ''), tags=allowed_tags, attributes=allowed_attributes, strip=True)

        return render_template('tweets_render.html', tweets=tweets)

    except Exception as e:
        # Render the error.html template with the error message
        return render_template('error.html', message=f"An error occurred: {str(e)}"), 500

    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()



if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=7777)
