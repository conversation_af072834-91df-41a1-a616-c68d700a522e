function updateImagePreview(base64String) {
    const imagePreview = document.getElementById('image_preview');
    imagePreview.src = base64String;
    imagePreview.style.display = 'block';

    const pastedImageData = document.getElementById('pasted_image_data');
    pastedImageData.value = base64String;
}

function handlePaste(e) {
    const clipboardData = e.clipboardData || window.clipboardData;
    const items = clipboardData.items;

    // Prevent default paste behavior
    e.preventDefault();

    let processed = false;

    for (let index in items) {
        const item = items[index];
        if (item.kind === 'file') {
            const blob = item.getAsFile();
            const reader = new FileReader();
            reader.onload = function(event) {
                updateImagePreview(event.target.result);
            };
            reader.readAsDataURL(blob);
            processed = true;
            break;
        } else if (item.kind === 'string') {
            item.getAsString(function(str) {
                str = str.trim();
                if (str.startsWith('data:image/')) {
                    updateImagePreview(str);
                } else if (isValidBase64(str)) {
                    const base64Image = 'data:image/png;base64,' + str;
                    updateImagePreview(base64Image);
                } else {
                    alert('Please paste a valid image or base64 image string.');
                }
            });
            processed = true;
            break;
        }
    }

    if (!processed) {
        alert('Please paste a valid image or base64 image string.');
    }

    // Keep the paste area editable
    resetPasteArea();
}

function isValidBase64(str) {
    // Remove whitespace and line breaks
    str = str.replace(/\s/g, '');
    // Simple regex to check for base64 validity
    const base64Pattern = /^[A-Za-z0-9+/=]+={0,2}$/;
    return base64Pattern.test(str);
}

function resetPasteArea() {
    const imagePasteArea = document.getElementById('image_paste_area');
    imagePasteArea.innerHTML = '<p>Click here and paste your image or base64 data (Ctrl+V or Right-Click & Paste)</p>';
    imagePasteArea.setAttribute('contenteditable', 'true');
    imagePasteArea.focus();
}

window.addEventListener('DOMContentLoaded', () => {
    const imagePasteArea = document.getElementById('image_paste_area');
    if (imagePasteArea) {
        imagePasteArea.addEventListener('paste', handlePaste);

        imagePasteArea.addEventListener('click', function() {
            // Ensure the paste area is focusable
            imagePasteArea.focus();
        });

        // Re-apply contenteditable attribute when the focus is lost
        imagePasteArea.addEventListener('blur', function() {
            imagePasteArea.setAttribute('contenteditable', 'true');
        });
    }
});
