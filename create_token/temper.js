// ==UserScript==
// @name         CopyTweets
// @namespace    http://tampermonkey.net/
// @version      2.1.10
// @description  Captures data from Discord, captures all images with data-role="img" by prioritizing data-safe-src, allows copying images to clipboard by clicking on them, and automatically adds new tweets to a buffer. On the pumpfun.mooo.com:7777/tweets page, it displays all buffered tweets in the #content div, updating automatically with the latest tweet on top and a maximum width of 600px per tweet. Applies different background colors based on tweet actions (Tweeted, Retweeted, Replied to, Quoted, Followed).
// <AUTHOR> @match        https://discord.com/*
// @match        http://pumpfun.mooo.com:7777/tweets*
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_xmlhttpRequest
// @grant        GM_setClipboard
// @connect      *
// ==/UserScript==

(function() {
    'use strict';

    // === Utility Functions ===
    function replaceEmojiImages(html) {
        // Create a temporary DOM element to parse the HTML string
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = html;

        // Select all <img> elements with data-type="emoji"
        const emojiImages = tempDiv.querySelectorAll('img[data-type="emoji"]');

        emojiImages.forEach(img => {
            // Get the alt attribute value
            const altText = img.getAttribute('alt') || '';

            // Create a text node with the alt text
            const textNode = document.createTextNode(altText);

            // Replace the <img> tag with the text node
            img.parentNode.replaceChild(textNode, img);
        });

        // Return the modified HTML as a string
        return tempDiv.innerHTML;
    }

    /**
     * Replaces all instances of '/assets/' with 'https://discord.com/assets/' in a given HTML string.
     * @param {string} html - The HTML string to process.
     * @returns {string} - The updated HTML string with absolute asset URLs.
     */
    function replaceAssetPaths(html) {
        return html.replace(/\/assets\/.*.svg/g, '#');
    }


    /**
     * Initializes the tweet buffer if it doesn't exist.
     */
    function initializeBuffer() {
        if (!GM_getValue('tweetBuffer')) {
            const initialBuffer = [];
            GM_setValue('tweetBuffer', JSON.stringify(initialBuffer));
        }
    }

    /**
     * Adds tweet data to the buffer if it's not identical to the last added tweet.
     * @param {Object} tweetData - The tweet data object to add.
     */
/**
 * Adds tweet data to the buffer. If a tweet with the same tweetLink already exists,
 * it replaces the existing tweet data with the new tweetData. Otherwise, it adds the new tweetData to the buffer.
 * @param {Object} tweetData - The tweet data object to add or update.
 */
    function addToBuffer(tweetData) {
        try {
            // Retrieve the current buffer from storage
            let buffer = JSON.parse(GM_getValue("tweetBuffer", "[]"));

            // Find the index of the tweet with the same tweetLink, if it exists
            const existingTweetIndex = buffer.findIndex(tweet => tweet.tweetLink === tweetData.tweetLink);

            if (existingTweetIndex !== -1) {
                // If the tweet exists, replace the existing tweet data with the new tweetData
                buffer[existingTweetIndex] = tweetData;
                console.log('Existing tweet found. Updated tweet in buffer:', tweetData.tweetLink);
            } else {
                // If the tweet does not exist, add the new tweetData to the buffer
                buffer.push(tweetData);
                console.log('New tweet added to buffer:', tweetData.tweetLink);
            }

            // Update the buffer in storage with the modified buffer
            GM_setValue('tweetBuffer', JSON.stringify(buffer));
        } catch (error) {
            console.error('Error in addToBuffer:', error);
        }
    }


    // Initialize buffer on script load
    initializeBuffer();

    /**
     * Retrieves the current path of the URL.
     * @returns {string} The pathname of the current URL.
     */
    function getPath() {
        return window.location.pathname;
    }

    // === Discord Integration ===

    if (window.location.hostname === 'discord.com') {

        /**
         * Extracts detailed tweet data from a Discord message and adds it to the buffer.
         * @param {HTMLElement} message - The Discord message element.
         */
        function extractAndBufferTweet(message) {
            // Find the tweet link




            const tweetLinkElement = message.querySelector('a[href*="x.com"][href*="/status/"]');

            if (!tweetLinkElement) return; // No tweet link found

            const tweetLink = tweetLinkElement.href;

            // Find all image elements with data-role="img" within the message

            const imageAnchorElements = message.querySelectorAll('a[data-role="img"]');
            const imageLinks = [];
            imageAnchorElements.forEach(img => {
                const safeSrc = img.getAttribute('data-safe-src');
                const imgSrc = safeSrc ? safeSrc : img.href;
                imageLinks.push(imgSrc);
            });

            // Extract author information
            const authorElement = message.querySelector('.embedAuthorNameLink_b0068a'); // Adjust selector as needed
            let authorName = 'Unknown';
            let authorHandle = '';
            if (authorElement) {
                const fullName = authorElement.textContent.trim(); // e.g., "DogeDesigner (@cb_doge)"
                const nameMatch = fullName.match(/^(.*?)\s*\((@[\w\d_]+)\)$/);
                if (nameMatch) {
                    authorName = nameMatch[1];
                    authorHandle = nameMatch[2];
                } else {
                    authorName = fullName;
                }
            }

            // Extract action type and target user
            const embedTitleElement = message.querySelector('.embedTitle_b0068a .embedTitleLink_b0068a');
            let actionType = 'Tweeted'; // Default action
            let targetUser = ''; // For retweets, replies, quotes, and follows

            if (embedTitleElement) {
                const spanElements = embedTitleElement.querySelectorAll('span');
                if (spanElements.length >= 1) {
                    const actionText = spanElements[0].textContent.trim(); // e.g., "@VitalikButerin Retweeted ", "@phantom Replied to ", "@DonaldJTrumpJr Quoted @Surabees:", "@idrawline Followed @kotaroonsol:"
                    //const actionText = spanElements[1]?.textContent.trim() || ''; // Correct: "_eth Replied to"

                    // Determine action type
                    if (actionText.includes('Retweeted')) {
                        actionType = 'Retweeted';
                        if (spanElements.length >= 2) {
                            targetUser = spanElements[1].textContent.trim(); // e.g., "@AyaMiyagotchi"
                        }
                    } else if (actionText.includes('Replied to')) {
                        actionType = 'Replied to';
                        if (spanElements.length >= 2) {
                            targetUser = spanElements[1].textContent.trim(); // e.g., "@JorgeVegaM87"
                        }
                    } else if (actionText.includes('Quoted')) {
                        actionType = 'Quoted';
                        if (spanElements.length >= 2) {
                            targetUser = spanElements[1].textContent.trim(); // e.g., "@Surabees"
                        }
                    } else if (actionText.includes('Followed')) {
                        actionType = 'Followed';
                        if (spanElements.length >= 2) {
                            targetUser = spanElements[1].textContent.trim(); // e.g., "@kotaroonsol"
                        }
                    } else if (actionText.includes('Tweeted')) {
                        actionType = 'Tweeted';
                        // No target user for direct tweets
                    }
                }
            }

            // Extract userText and targetUserText
            const descriptionElement = message.querySelector('.embedDescription_b0068a');
            let userText = '';
            let targetUserText = '';

            if (descriptionElement) {
                // Clone the descriptionElement to avoid modifying the actual DOM
                const clonedDescription = descriptionElement.cloneNode(true);
                const blockquoteElement = clonedDescription.querySelector('blockquote');

                if (blockquoteElement) {
                    targetUserText = blockquoteElement.innerHTML.trim();
                    targetUserText = replaceAssetPaths(targetUserText);
                    targetUserText = replaceEmojiImages(targetUserText);

                    blockquoteElement.remove(); // Remove blockquote to isolate userText
                }

                userText = clonedDescription.innerHTML.trim();
                userText = replaceAssetPaths(userText);
                userText = replaceEmojiImages(userText);
            }

            // **Enhanced Timestamp Extraction**
            // Select all time elements within the message
            const timeElements = message.querySelectorAll('time[datetime]');
            let timestamp = new Date().toISOString(); // Default timestamp

            // Iterate through time elements to find the main timestamp
            for (let timeEl of timeElements) {
                const ariaLabel = timeEl.getAttribute('aria-label') || '';
                if (!ariaLabel.toLowerCase().includes('edited')) {
                    const datetime = timeEl.getAttribute('datetime');
                    if (datetime) {
                        timestamp = datetime;
                        break; // Found the main timestamp
                    }
                }
            }

            const tweetData = {
                authorName: authorName,
                authorHandle: authorHandle,
                tweetLink: tweetLink,
                userText: userText,
                targetUserText: targetUserText,
                actionType: actionType, // 'Tweeted', 'Retweeted', 'Replied to', 'Quoted', 'Followed'
                targetUser: targetUser, // e.g., '@AyaMiyagotchi', '@JorgeVegaM87', '@Surabees', '@kotaroonsol'
                imageLinks: imageLinks, // Array of image URLs
                timestamp: timestamp
            };

            addToBuffer(tweetData);
        }

        /**
         * Observes Discord's DOM for new messages and processes them.
         */
        const messageObserver = new MutationObserver((mutations) => {
            mutations.forEach(mutation => {
                mutation.addedNodes.forEach(node => {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        const message = node.querySelector('.messageListItem_d5deea') || node;
                        if (message && !message.dataset.buffered) {
                            // Mark as Processed
                            message.dataset.buffered = 'true';

                            // Extract and Buffer Tweet Data
                            extractAndBufferTweet(message);
                        }
                    }
                });
            });
        });

        // Start Observing Discord DOM
        messageObserver.observe(document.body, { childList: true, subtree: true });

    }

    // === Pump.fun /tweets Page Integration ===

    else if (window.location.hostname === 'pumpfun.mooo.com' && getPath().startsWith('/tweets')) {

        /**
         * Determines the background color based on the action type.
         * @param {string} actionType - The type of action ('Tweeted', 'Retweeted', 'Replied to', 'Quoted', 'Followed').
         * @returns {string} The corresponding background color in HEX.
         */
        function getBackgroundColor(actionType) {
            switch(actionType) {
                case 'Tweeted':
                    return '#DFFFD6'; // Soft Green
                case 'Retweeted':
                    return '#D6E8FF'; // Soft Blue
                case 'Replied to':
                    return '#FFD6D6'; // Soft Red
                case 'Quoted':
                    return '#FFFACD'; // Soft Yellow
                case 'Followed':
                    return '#E0E0E0'; // Soft Gray
                default:
                    return '#F9F9F9'; // Default Light Gray
            }
        }

        /**
         * Renders all tweets from the buffer into the #content div.
         */
        function renderTweets() {
            const contentDiv = document.getElementById('content');
            if (!contentDiv) {
                console.error('Content div with id "content" not found.');
                return;
            }

            // Retrieve the buffer
            const buffer = JSON.parse(GM_getValue('tweetBuffer', '[]'));

            // Clear existing content
            contentDiv.innerHTML = '';

            if (buffer.length === 0) {
                contentDiv.innerHTML = '<p>No tweets in the buffer.</p>';
                return;
            }

            // Create container for tweets
            const tweetsContainer = document.createElement('div');
            tweetsContainer.style.display = 'flex';
            tweetsContainer.style.flexDirection = 'column';
            tweetsContainer.style.gap = '10px';
            tweetsContainer.style.alignItems = 'center'; // Center the tweets horizontally

            // Iterate over the buffer in reverse order to show latest tweet on top
            buffer.slice().reverse().forEach((tweet, index) => {
                const tweetDiv = document.createElement('div');
                tweetDiv.style.border = '1px solid #CCC';
                tweetDiv.style.borderRadius = '4px';
                tweetDiv.style.padding = '10px';
                tweetDiv.style.display = 'flex';
                tweetDiv.style.flexDirection = 'column';
                tweetDiv.style.backgroundColor = getBackgroundColor(tweet.actionType);
                tweetDiv.style.maxWidth = '600px'; // Adjusted to 600px for better layout
                tweetDiv.style.width = '100%'; // Ensure it doesn't exceed container's width

                // Author Information with Tweet Link applied to title
                const authorInfo = document.createElement('div');
                authorInfo.style.marginBottom = '5px';
                authorInfo.style.display = 'flex';
                authorInfo.style.flexDirection = 'column';

                // Author Name and Handle wrapped in Tweet Link
                const authorLink = document.createElement('a');
                authorLink.href = tweet.tweetLink;
                authorLink.target = '_blank';
                authorLink.style.textDecoration = 'none'; // Remove underline
                authorLink.style.color = '#1DA1F2'; // Twitter blue
                authorLink.style.fontWeight = 'bold';
                authorLink.style.fontSize = '1.1em';
                authorLink.innerHTML = `${tweet.authorName} ${tweet.authorHandle}`; // Use innerHTML to preserve emojis and line breaks
                authorInfo.appendChild(authorLink);

                tweetDiv.appendChild(authorInfo);

                // Timestamp
                const timestamp = document.createElement('span');
                const date = new Date(tweet.timestamp);
                timestamp.textContent = `Captured at: ${date.toLocaleString()}`;
                timestamp.style.fontSize = '0.9em';
                timestamp.style.color = '#555';
                tweetDiv.appendChild(timestamp);

                // User Text
                if (tweet.userText) {
                    const userText = document.createElement('p');
                    userText.innerHTML = tweet.userText; // Use innerHTML to render emojis/images and line breaks
                    userText.style.marginTop = '10px';
                    userText.style.color = '#333';
                    // Add CSS to preserve line breaks and wrapping
                    userText.style.whiteSpace = 'pre-wrap';
                    tweetDiv.appendChild(userText);
                }

                // Action Information (Retweet, Reply, Quoted, Followed)
                if (['Retweeted', 'Replied to', 'Quoted', 'Followed'].includes(tweet.actionType)) {
                    const actionInfo = document.createElement('div');
                    actionInfo.style.marginTop = '10px';
                    actionInfo.style.padding = '10px';
                    actionInfo.style.borderLeft = '4px solid #1DA1F2';
                    actionInfo.style.backgroundColor = '#EFEFEF';
                    actionInfo.style.borderRadius = '4px';

                    const actionLabel = document.createElement('span');
                    actionLabel.textContent = `${tweet.actionType} ${tweet.targetUser}`;
                    actionLabel.style.fontWeight = 'bold';
                    actionLabel.style.color = '#333';
                    actionInfo.appendChild(actionLabel);

                    // Target User Text
                    if (tweet.targetUserText) {
                        const targetUserText = document.createElement('p');
                        targetUserText.innerHTML = tweet.targetUserText; // Use innerHTML to render emojis/images and line breaks
                        targetUserText.style.marginTop = '5px';
                        targetUserText.style.color = '#555';
                        // Add CSS to preserve line breaks and wrapping
                        targetUserText.style.whiteSpace = 'pre-wrap';
                        actionInfo.appendChild(targetUserText);
                    }

                    tweetDiv.appendChild(actionInfo);
                }

                // Image(s) (if available)
                console.log(tweet.imageLinks);
                if (tweet.imageLinks && tweet.imageLinks.length > 0) {
                    const imagesContainer = document.createElement('div');
                    imagesContainer.style.display = 'flex';
                    imagesContainer.style.flexDirection = 'column';
                    imagesContainer.style.gap = '10px';
                    imagesContainer.style.marginTop = '10px';

                    tweet.imageLinks.forEach((imgSrc, imgIndex) => {
                        const image = document.createElement('img');
                        image.src = imgSrc;
                        image.alt = 'Tweet Image';
                        image.style.maxWidth = '100%';
                        image.style.borderRadius = '4px';
                        image.style.cursor = 'pointer'; // Make image clickable if desired

                        // Add click event to view image in a new tab
                        image.addEventListener('click', () => {
                            window.open(imgSrc, '_blank');
                        });

                        imagesContainer.appendChild(image);
                    });

                    tweetDiv.appendChild(imagesContainer);
                }

                tweetsContainer.appendChild(tweetDiv);
            });

            contentDiv.appendChild(tweetsContainer);
        }

        /**
         * Periodically checks for buffer updates and re-renders tweets if there are new entries.
         */
        function startAutoUpdate() {
            let previousBufferLength = 0;

            // Initial render
            renderTweets();
            previousBufferLength = JSON.parse(GM_getValue('tweetBuffer', '[]')).length;

            // Check every 3 seconds for updates
            setInterval(() => {
                const currentBuffer = JSON.parse(GM_getValue('tweetBuffer', '[]'));
                if (currentBuffer.length !== previousBufferLength) {
                    renderTweets();
                    previousBufferLength = currentBuffer.length;
                    console.log('Tweets buffer updated. Re-rendering tweets.');
                }
            }, 3000); // 3 seconds interval
        }

        /**
         * Creates and appends a "Clear Buffer" button to the #content div.
         */
        function appendClearBufferButton(contentDiv) {
            const clearButton = document.createElement('button');
            clearButton.textContent = 'Clear Buffer';
            clearButton.style.marginBottom = '20px';
            clearButton.style.padding = '10px 20px';
            clearButton.style.backgroundColor = '#e74c3c'; // Red color
            clearButton.style.color = '#FFF';
            clearButton.style.border = 'none';
            clearButton.style.borderRadius = '4px';
            clearButton.style.cursor = 'pointer';
            clearButton.style.alignSelf = 'flex-start';

            clearButton.addEventListener('click', () => {
                if (confirm('Are you sure you want to clear the tweet buffer?')) {
                    GM_setValue('tweetBuffer', JSON.stringify([]));
                    renderTweets();
                    console.log('Tweet buffer cleared.');
                }
            });

            contentDiv.prepend(clearButton);
        }

        /**
         * Ensures that the #content div exists on the page. Creates it if it doesn't.
         * @returns {HTMLElement} The #content div element.
         */
        function ensureContentDiv() {
            let contentDiv = document.getElementById('content');
            if (!contentDiv) {
                contentDiv = document.createElement('div');
                contentDiv.id = 'content';
                contentDiv.style.padding = '20px';
                contentDiv.style.backgroundColor = '#FFF';
                contentDiv.style.minHeight = '100vh';
                document.body.appendChild(contentDiv);
            }
            return contentDiv;
        }

        /**
         * Initializes the tweets page by ensuring the content div exists, appending the clear buffer button, and starting the auto-update.
         */
        function initializePage() {
            const contentDiv = ensureContentDiv();
            appendClearBufferButton(contentDiv);
            startAutoUpdate();
        }

        // Initialize after the DOM is fully loaded
        window.addEventListener('load', initializePage);
    }

})();
