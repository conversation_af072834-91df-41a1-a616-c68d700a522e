<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Create Token</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <script src="{{ url_for('static', filename='js/script.js') }}" defer></script>
</head>
<body>
    <h1>Create a New Token</h1>
    <div class="container">
        <form action="/create-token1" method="post">
            <button type="submit">Create Token</button>

            <label for="image_paste_area">Image Paste/Upload Area:</label>
            <div id="image_paste_area" contenteditable="true" class="image-paste-area" onclick="triggerFileInput()">
                <p>Click here to upload or paste your image</p>
            </div>

            <div class="image-preview">
                <img id="image_preview" src="" alt="Image Preview" style="display: none;">
            </div>

            <!-- File input for image upload -->
            <input type="file" id="file_input" style="display: none;" accept="image/*" onchange="handleFileInput(event)">

            <label for="token_symbol">Token Symbol:</label>
            <input type="text" id="token_symbol" name="token_symbol" required>

            <label for="token_name">Token Name:</label>
            <input type="text" id="token_name" name="token_name" required>

           


            <label for="website">Website:</label>
            <input type="url" id="website" name="website">

            <label for="twitter">Twitter:</label>
            <input type="url" id="twitter" name="twitter">

            <label for="telegram">Telegram:</label>
            <input type="url" id="telegram" name="telegram">

            <label for="sol_amount">Amount (SOL):</label>
            <input type="number" id="sol_amount" name="sol_amount" step="0.01" value="1.2">

       
            <!-- Hidden input to store the pasted or uploaded image data -->
            <input type="hidden" id="pasted_image_data" name="pasted_image_data" required>

           
        </form>
    </div>
</body>
</html>
