<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Create Token</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <script src="{{ url_for('static', filename='js/script.js') }}" defer></script>
    <style>
        .image-list {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .image-item {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .image-item img {
            max-width: 300px;
            max-height: 200px;
            object-fit: cover;
            border: 1px solid #ccc;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>Create a New Token</h1>
    <div class="container">
        <form action="/create-token" method="post">
            <h2>Select an Image</h2>
            <div class="image-list">
                {% for image_url in image_urls %}
                <div class="image-item">
                    <input type="radio" id="image_{{ loop.index }}" name="selected_image" value="{{ image_url }}" required>
                    <label for="image_{{ loop.index }}">
                        <img src="{{ image_url }}" alt="Image {{ loop.index }}">
                    </label>
                </div>
                {% endfor %}
            </div>
            {%  if video_url %} 
            <video id="video" width="640" height="360" controls>
                <source src="{{ video_url }}" type="video/mp4">
                Your browser does not support the video tag.
              </video>
              <br>
              <button id="screenshot-button">Take Screenshot</button>
              <h2>Captured Screenshot</h2>
              <img id="captured-image" width="640" height="360" alt="Screenshot will appear here">
              <script>
                const video = document.getElementById('video');
                video.crossOrigin = 'anonymous';
                
                    const button = document.getElementById('screenshot-button');
                    const capturedImage = document.getElementById('captured-image');
                
                    button.addEventListener('click', () => {
                      // Create a canvas to draw the video frame
                      const canvas = document.createElement('canvas');
                      canvas.width = video.videoWidth;
                      canvas.height = video.videoHeight;
                      const context = canvas.getContext('2d');
                      
                
                      // Draw the current video frame on the canvas
                      context.drawImage(video, 0, 0, canvas.width, canvas.height);
                
                      // Get the data URL of the screenshot
                      const dataURL = canvas.toDataURL('image/png');
                
                      // Set the data URL as the source of the img element
                      capturedImage.src = dataURL;
                
                      // Optionally pause the video
                      video.pause();
                    });
                  </script>

            {% endif %}
            <label for="token_symbol">Token Symbol:</label>
            <input type="text" id="token_symbol" name="token_symbol" required>

            <label for="token_name">Token Name:</label>
            <input type="text" id="token_name" name="token_name" required>

            <label for="website">Website:</label>
            <input type="url" id="website" name="website" value="{{inst_url}}">

            <label for="twitter">Twitter:</label>
            <input type="url" id="twitter" name="twitter" value="{{inst_url}}">

            <label for="telegram">Telegram:</label>
            <input type="url" id="telegram" name="telegram" value="{{inst_url}}">

            <label for="sol_amount">Amount (SOL):</label>
            <input type="number" id="sol_amount" name="sol_amount" step="0.01" value="1.2">

            <button type="submit">Create Token</button>
        </form>
    </div>
</body>
</html>
