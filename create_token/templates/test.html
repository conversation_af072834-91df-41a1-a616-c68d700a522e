<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Video and Image Cropper</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.12/cropper.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.12/cropper.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f8f9fa;
        }

        h1 {
            text-align: center;
            margin-top: 20px;
            color: #333;
        }

        .container {
            width: 90%;
            margin: 0 auto;
            max-width: 600px;
            text-align: center;
        }

        #image-container img {
            max-width: 150px;
            margin: 5px;
            cursor: pointer;
            border: 2px solid #ddd;
            border-radius: 5px;
            transition: transform 0.2s;
        }

        #image-container img:hover {
            transform: scale(1.1);
        }

        #crop-container {
            margin-top: 20px;
            padding: 10px;
            background-color: #fff;
            border: 1px solid #ccc;
            border-radius: 10px;
        }

        #crop-container img {
            max-width: 100%;
            display: block;
            margin: 0 auto;
        }

        .crop-actions {
            margin-top: 20px;
            display: flex;
            justify-content: space-around;
        }

        .crop-actions button {
            flex: 1;
            margin: 0 5px;
            padding: 10px;
            font-size: 16px;
            background-color: #28a745;
            color: white;
            border: none;
            border-radius: 5px;
        }

        .crop-actions button:hover {
            background-color: #218838;
        }
    </style>
</head>
<body>

    <h1>Create a New Token</h1>
    <div class="container">
        <form action="/create-token" method="post">
            <div id="image-container" class="container"></div>

            <div id="crop-container" class="container" style="display: none;">
                <div style="padding: 0px;">
                    <img id="crop-image" src="" alt="Image to crop">
                </div>
                <div class="crop-actions">
                    <button id="apply-crop" type="button" onclick="applyCrop()">Apply Crop</button>
                </div>
            </div>

            <label for="token_symbol">Token Symbol:</label>
            <input type="text" id="token_symbol" name="token_symbol" required maxlength="10">

            <label for="token_name">Token Name:</label>
            <input type="text" id="token_name" name="token_name" required maxlength="32">

            <label for="token_description">Token Description:</label>
            <textarea id="token_description" name="token_description" required maxlength="2000" rows="10"></textarea>

            <label for="website">Website:</label>
            <input type="url" id="website" name="website">

            <label for="twitter">Twitter:</label>
            <input type="url" id="twitter" name="twitter">

            <label for="telegram">Telegram:</label>
            <input type="url" id="telegram" name="telegram">

            <label for="sol_amount">Amount (SOL):</label>
            <input type="number" id="sol_amount" name="sol_amount" step="0.1" value="1.5">

            <!-- Hidden input to store the cropped image data -->
            <input type="hidden" id="pasted_image_data" name="pasted_image_data" required>

            <button type="submit" id="submit-button">Create Token</button>
            <br><br>
        </form>
    </div>
    <script>
        let cropper;
        let appliedCrop = false;
        const submitButton = document.getElementById('submit-button');
        submitButton.hidden = true;

        // Function to get URL parameters
        function getQueryParam(param) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(param);
        }

        // Automatically fetch video and process images on page load
        document.addEventListener("DOMContentLoaded", () => {
            const videoUrl = getQueryParam('video_url');
            const imageUrl = getQueryParam('image_url');
            const galleryEnc = getQueryParam('gallery');
            const gallery = galleryEnc ? decodeURIComponent(galleryEnc).split(",") : [];

            if (!imageUrl) {
                alert('Image URL must be provided.');
                return;
            }

            // Process the main image
            processSingleImage(imageUrl);

            // Process gallery images if provided
            if (gallery && gallery.length > 0 && gallery[0] !== "") {
                gallery.forEach(galleryImageUrl => {
                    processSingleImage(galleryImageUrl);
                });
            }

            // If video is provided, process video frames
            if (videoUrl) {
                processVideo(videoUrl);
            }

            // Populate fields if link_url provided
            const linkUrl = getQueryParam('link_url');
            const desciptionData = getQueryParam('description');
            const animalName = getQueryParam('name');
            const animalBreed = getQueryParam('breed');
            const eventType = getQueryParam('type');


            if (linkUrl) {
                if (eventType === 'newborn') {
                    document.getElementById('token_name').value = 'Baby ' + animalBreed;
                    document.getElementById('token_symbol').value = animalName;
                } else if (eventType === 'died') {
                    document.getElementById('token_name').value = 'RIP ' + animalName;
                    document.getElementById('token_symbol').value = animalName;
                }else {
                    document.getElementById('token_name').value = "";
                    document.getElementById('token_symbol').value = "";
                }

                //document.getElementById('twitter').value = linkUrl;
                //document.getElementById('website').value = linkUrl;
                //document.getElementById('telegram').value = linkUrl;
                //document.getElementById('token_description').value = desciptionData;
            }

            // Add confirmation dialog on form submission
            const form = document.querySelector("form");
            form.addEventListener("submit", (event) => {
                event.preventDefault(); // Prevent form submission
                const tokenSymbol = document.getElementById("token_symbol").value;
                const tokenName = document.getElementById("token_name").value;
                const solAmount = document.getElementById("sol_amount").value;
                const twitterUrl = document.getElementById("twitter").value;
                const tokenDescription = document.getElementById("token_description").value;

                // Confirmation message
                const confirmationMessage = `
                    Double check?
                    - Token Symbol: ${tokenSymbol}
                    - Token Name: ${tokenName}
                    - Token Description: ${tokenDescription}
                    - Twitter URL: ${twitterUrl}
                    - Amount (SOL): ${solAmount}
                `;

                // Show confirmation dialog
                if (confirm(confirmationMessage)) {
                    form.submit(); // Submit the form if confirmed
                }
            });
        });

        function processSingleImage(imageUrl) {
            fetch('/process_image_url', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ image_url: imageUrl })
            })
            .then(response => {
                if (!response.ok) {
                    return response.json().then(err => { throw new Error(err.error); });
                }
                return response.json();
            })
            .then(data => {
                if (data.image_path) {
                    const imageContainer = document.getElementById('image-container');
                    // Add the downloaded image as a preview
                    const img = document.createElement('img');
                    img.src = data.image_path; // The static local image path returned by the server
                    img.onclick = () => loadImageForCrop(data.image_path);
                    imageContainer.appendChild(img);
                }
            })
            .catch(error => {
                console.error('Error processing image:', error.message);
                alert(`Failed to process image URL: ${error.message}`);
            });
        }

        function processVideo(videoUrl) {
            const imageContainer = document.getElementById('image-container');
//////

                fetch('/download', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ url: videoUrl })
                })
                .then(response => response.json())
                .then(data => {
                    data.images.forEach(image => {
                        const img = document.createElement('img');
                        img.src = image;
                        img.onclick = () => loadImageForCrop(image);
                        imageContainer.appendChild(img);
                    });
                })
                .catch(error => {
                    console.error('Error processing video:', error);
                    //alert('Failed to process video.');
                });
            




        


        }

        function loadImageForCrop(imageUrl) {
            submitButton.hidden = true;
            const cropImage = document.getElementById('crop-image');
            const cropContainer = document.getElementById('crop-container');
            const applyCropButton = document.getElementById('apply-crop');
            appliedCrop = false;

            // Set the image source and enable cross-origin requests
            cropImage.src = imageUrl;
            cropImage.setAttribute('crossorigin', 'anonymous');
            cropContainer.style.display = 'block';
            applyCropButton.style.display = 'block'; // Show the Apply Crop button

            // Scroll to the crop container
            cropContainer.scrollIntoView({ behavior: 'smooth' });

            // Initialize or replace cropper with 1:1 aspect ratio
            if (cropper) {
                cropper.destroy();
            }
            cropper = new Cropper(cropImage, {
                aspectRatio: 1, // 1:1 aspect ratio for square crop
                viewMode: 1,
            });
        }

        function applyCrop() {
            if (!cropper) return;

            const croppedCanvas = cropper.getCroppedCanvas();
            const croppedImage = croppedCanvas.toDataURL();

            // Show the cropped image as the final image
            const cropImage = document.getElementById('crop-image');
            const applyCropButton = document.getElementById('apply-crop');
            cropImage.src = croppedImage;

            const pastedImageData = document.getElementById('pasted_image_data');
            pastedImageData.value = croppedImage;

            console.log("Cropped image data:", pastedImageData.value);

            applyCropButton.style.display = 'none';

            // Destroy the cropper to finalize the cropping
            cropper.destroy();
            appliedCrop = true;
            submitButton.hidden = false;
        }
    </script>
</body>
</html>
