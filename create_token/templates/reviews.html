<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Top Reviews</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            padding: 0;
        }
        h1 {
            text-align: center;
        }
        .review-container {
            margin-top: 20px;
        }
        .tweet {
            border: 1px solid #ccc;
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        .top-tweet {
            background-color: #f0f8ff;
            border-color: #007bff;
        }
        .author {
            color: #555;
            font-style: italic;
        }
    </style>
</head>
<body>
    <h1>Top 5 Tweets</h1>
    <div id="top-tweets" class="review-container">
        {% if top_tweets %}
            {% for tweet in top_tweets %}
                <div class="tweet top-tweet">
                    <p class="author">Author: {{ tweet.author }}</p>
                    <p>Tweet: {{ tweet.tweet }}</p>
                </div>
            {% endfor %}
        {% else %}
            <p>No top tweets available at the moment.</p>
        {% endif %}
    </div>

    <h1>All Tweets</h1>
    <div id="all-tweets" class="review-container">
        {% for tweet in tweets %}
            <div class="tweet">
                <p class="author">Author: {{ tweet.author }}</p>
                <p>Tweet: {{ tweet.tweet }}</p>
            </div>
        {% endfor %}
    </div>
</body>
</html>
