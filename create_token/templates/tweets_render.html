<!DOCTYPE html>
<html>
<head>
    <title>Tweets Render</title>
    <style>
        /* Your existing CSS styles */
        body {
            background-color: #FFF;
            font-family: Arial, sans-serif;
            padding: 20px;
        }
        .tweet {
            border: 1px solid #CCC;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 10px;
            max-width: 600px;
            background-color: #F9F9F9;
        }
        .tweet a {
            text-decoration: none;
            color: #1DA1F2;
        }
        .tweet .author {
            font-weight: bold;
            font-size: 1.1em;
        }
        .tweet .timestamp {
            font-size: 0.9em;
            color: #555;
        }
        .tweet .user-text, .tweet .target-user-text {
            margin-top: 10px;
            color: #333;
            white-space: pre-wrap;
        }
        .tweet .action-info {
            margin-top: 10px;
            padding: 10px;
            border-left: 4px solid #1DA1F2;
            background-color: #EFEFEF;
            border-radius: 4px;
        }
        .tweet .action-info span {
            font-weight: bold;
            color: #333;
        }
        .tweet img {
            max-width: 100%;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
        }
        /* Background colors based on action type */
        .Tweeted { background-color: #DFFFD6; }
        .Retweeted { background-color: #D6E8FF; }
        .Replied_to { background-color: #FFD6D6; }
        .Quoted { background-color: #FFFACD; }
        .Followed { background-color: #E0E0E0; }
    </style>
    <!-- Include DOMPurify from a CDN for sanitizing HTML -->
    <script src="/static/js/purify.js"  ></script>
</head>
<body>
    <div id="tweets-container"></div>

    <script>
        // Basic authentication credentials
        const USERNAME = 'admin';
        const PASSWORD = 'pumpfun';

        function getBackgroundColor(actionType) {
            switch(actionType) {
                case 'Tweeted':
                    return '#DFFFD6'; // Soft Green
                case 'Retweeted':
                    return '#D6E8FF'; // Soft Blue
                case 'Replied to':
                    return '#FFD6D6'; // Soft Red
                case 'Quoted':
                    return '#FFFACD'; // Soft Yellow
                case 'Followed':
                    return '#E0E0E0'; // Soft Gray
                default:
                    return '#F9F9F9'; // Default Light Gray
            }
        }

        function fetchTweets() {
            fetch('/get-tweets', {
                method: 'GET',
                headers: {
                    'Authorization': 'Basic ' + btoa(USERNAME + ':' + PASSWORD)
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    renderTweets(data.tweets);
                } else {
                    console.error('Failed to fetch tweets:', data.message);
                }
            })
            .catch(error => {
                console.error('Error fetching tweets:', error);
            });
        }

        function renderTweets(tweets) {
            const container = document.getElementById('tweets-container');
            container.innerHTML = ''; // Clear existing tweets

            tweets.forEach(tweet => {
                const tweetDiv = document.createElement('div');
                tweetDiv.classList.add('tweet');

                // Set background color based on action type
                tweetDiv.style.backgroundColor = getBackgroundColor(tweet.actionType);

                // Author Info
                const authorDiv = document.createElement('div');
                authorDiv.classList.add('author');
                authorDiv.innerHTML = `<a href="${tweet.tweetLink}" target="_blank">${DOMPurify.sanitize(tweet.authorName)} ${DOMPurify.sanitize(tweet.authorHandle)}</a>`;
                tweetDiv.appendChild(authorDiv);

                // Timestamp
                const timestampDiv = document.createElement('div');
                timestampDiv.classList.add('timestamp');
                timestampDiv.textContent = `Captured at: ${tweet.timestamp}`;
                tweetDiv.appendChild(timestampDiv);

                // User Text
                if (tweet.userText) {
                    const userTextDiv = document.createElement('div');
                    userTextDiv.classList.add('user-text');
                    userTextDiv.innerHTML = DOMPurify.sanitize(tweet.userText);
                    tweetDiv.appendChild(userTextDiv);
                }

                // Action Info
                if (['Retweeted', 'Replied to', 'Quoted', 'Followed'].includes(tweet.actionType)) {
                    const actionInfoDiv = document.createElement('div');
                    actionInfoDiv.classList.add('action-info');
                    actionInfoDiv.innerHTML = `<span>${DOMPurify.sanitize(tweet.actionType)} ${DOMPurify.sanitize(tweet.targetUser)}</span>`;
                    if (tweet.targetUserText) {
                        const targetUserTextDiv = document.createElement('div');
                        targetUserTextDiv.classList.add('target-user-text');
                        targetUserTextDiv.innerHTML = DOMPurify.sanitize(tweet.targetUserText);
                        actionInfoDiv.appendChild(targetUserTextDiv);
                    }
                    tweetDiv.appendChild(actionInfoDiv);
                }

                // Images
                if (tweet.imageLinks && tweet.imageLinks.length > 0) {
                    tweet.imageLinks.forEach(imgSrc => {
                        const img = document.createElement('img');
                        img.src = imgSrc;
                        img.alt = 'Tweet Image';
                        img.onclick = () => {
                            window.open(imgSrc, '_blank');
                        };
                        tweetDiv.appendChild(img);
                    });
                }

                container.appendChild(tweetDiv);
            });
        }

        // Fetch tweets every second
        setInterval(fetchTweets, 1000);

        // Initial fetch
        fetchTweets();
    </script>
</body>
</html>
