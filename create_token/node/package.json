{"name": "pump-fun-token-launcher", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@types/express": "^5.0.0", "@types/fluent-ffmpeg": "^2.1.27", "@types/node": "^22.10.2", "nodemon": "^3.1.9", "ts-node": "^10.9.2", "typescript": "^5.7.2"}, "dependencies": {"@openbook-dex/openbook": "^0.0.9", "@raydium-io/raydium-sdk": "^1.3.1-beta.52", "@solana/spl-token": "^0.4.6", "@solana/spl-token-swap": "^0.4.4", "@types/amqplib": "^0.10.6", "@types/uuid": "^10.0.0", "@web-std/file": "^3.0.3", "amqplib": "^0.10.5", "axios": "^1.6.8", "bs58": "^5.0.0", "buffer-layout": "^1.2.2", "dotenv": "^16.4.7", "ejs": "^3.1.10", "express": "^4.21.2", "ffmpeg": "^0.0.4", "fluent-ffmpeg": "^2.1.3", "ipfs-http-client": "^60.0.1", "js-kubo-rpc-client": "^1.0.0", "kubo-rpc-client": "^5.0.2", "mysql2": "^3.11.5", "mysql2-promise": "^0.1.4", "pinata-web3": "^0.5.3", "short-uuid": "^5.2.0", "solana-pump-fun": "github:etumulkans/solana-pump-fun", "web3": "^4.16.0"}}