import express, { Request, Response } from "express";
import { getKeyPairFromPrivate<PERSON>ey, createTransaction, sendAndConfirmTransactionWrapper, bufferFromUInt64, bufferFromString } from './src/utils';
import web3, { Connection, Keypair, PublicKey, clusterApiUrl, TransactionInstruction,LAMPORTS_PER_SOL,Transaction,SystemProgram,sendAndConfirmTransaction} from '@solana/web3.js';
import { getAssociatedTokenAddress, createAssociatedTokenAccountInstruction } from '@solana/spl-token';
import { TOKEN_PROGRAM_ID, ASSOCIATED_TOKEN_PROGRAM_ID, } from '@solana/spl-token';
import { COMPUTE_BUDGET_PROGRAM_ID, GLOBAL, MINT_AUTHORITY, MPL_TOKEN_METADATA, PUMP_FUN_ACCOUNT, PUMP_FUN_PROGRAM, RENT, SYSTEM_PROGRAM,SYSTEM_PROGRAM_ID} from './src/constants';
import path from "path";
import { createConnection } from 'mysql2/promise';
import shortUUID from 'short-uuid';
import { v4 as uuidv4 } from 'uuid';
import axios from 'axios';
import * as fs from 'fs';
import * as fsa from 'fs/promises';
import amqp from 'amqplib';

import ffmpeg from 'fluent-ffmpeg';
import FormData = require('form-data');
require('dotenv').config();
import { PinataSDK } from "pinata-web3";
import {File} from '@web-std/file';
import bs58 from 'bs58';
const https = require('https');


//TODO: wallet management from sql 
//TODO: autosell from gui 
//TODO: (optional) chart in gui and price fetching

const isSimulated = false;
const tradeDiedWalletPubKey="KJbW9sgdSKR8Q9C1UNZaqhir2hU1nYaHhQ8ppoyruv3";
const tradeOtherWalletPubKey="8cELLU91DQh5gAFBZooFPVaXAggXLX3AxAVwRUhNTZuE";
const tradeNewbornWalletPubKey="CBiJZd2vJfBGHPrp5VFV7cBdoAG5xdN2o4u7EwfeMGnJ";
const TOKEN_METADATA_PROGRAM_ID = new PublicKey("metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s");

const SYSPROGRAM= new PublicKey("********************************");
const TRANSFER_FEE=5000;
const UPLOAD_FOLDER = path.join(__dirname, 'static/videos');
const IMAGES_FOLDER = path.join(__dirname, 'static/images');

const pinataApiKey = "eae622f81b2b9d595268";
const pinataSecretApiKey = "****************************************************************";
const gateway = "https://ipfs.io/ipfs/";


const wallets={ 
    "newborn":tradeNewbornWalletPubKey, 
    "died":tradeDiedWalletPubKey,
    "other":tradeOtherWalletPubKey
}
//let wallets = {};





const DB_CONFIG = {
    user: 'pump2',
    password: 'pump2',
    host: '**************',
    database: 'pump'
};

// Initialize Express application
const app = express(); // No explicit Application type needed
const port = 7777;
app.set("view engine", "ejs");
app.set("views", path.join(__dirname, "views"));
app.use(express.static('public'));
app.use(express.json({limit: '50mb'}));
app.use(express.urlencoded({limit: '50mb',extended: true}));

const PRIVATE_KEY = process.env.PRIVATE_KEY ? process.env.PRIVATE_KEY : "";
const PRIVATE_KEY_DIED = process.env.PRIVATE_KEY_DIED ? process.env.PRIVATE_KEY_DIED : "";
console.log("key:",PRIVATE_KEY);
console.log("key(died):",PRIVATE_KEY_DIED);

const pinataGW="https://gateway.pinata.cloud/ipfs";
//const RPC="https://api.mainnet-beta.solana.com";
//clusterApiUrl("devnet")
//const RPC="https://mainnet.helius-rpc.com/?api-key=cf3aa81f-7796-401b-a170-5567272f5f65";
const RPC="https://solana-mainnet.g.alchemy.com/v2/********************************";

const jsonFilePath = path.join(__dirname, '.wallets.json');
let walletsJson: { type: string; pubkey: string; privatekey: string }[] = [];
// Read and parse the JSON file
fs.readFile(jsonFilePath, 'utf8', (err, data) => {
    if (err) {
        console.error('Error reading the JSON file:', err);
        return;
    }
    try {
        walletsJson = JSON.parse(data);
        console.log('JSON Data:', walletsJson);
    } catch (parseErr) {
        console.error('Error parsing JSON:', parseErr);
    }
});




function decodeString(buffer: Buffer, offset: number, length: number): string {
    return buffer.slice(offset, offset + length).toString('utf8').replace(/\0/g, '');
}

export async function getTokenMetadata(mintAddress: string) {
    const mintPubkey = new PublicKey(mintAddress);

    const metadata_seeds = [
        Buffer.from('metadata'),
        TOKEN_METADATA_PROGRAM_ID.toBuffer(),
        mintPubkey.toBuffer(),
    ];
    const [metadata_pda] = PublicKey.findProgramAddressSync(metadata_seeds, TOKEN_METADATA_PROGRAM_ID);

    
    console.log(metadata_pda.toBase58());
    const connection = new Connection(
        RPC,
        'confirmed'
    );
    const accountInfo = await connection.getAccountInfo(metadata_pda);
    if (!accountInfo || !accountInfo.data) {
        throw new Error("No metadata account found for this mint.");
    }
    const buf = Buffer.from(accountInfo.data);

    // Offsets are per https://github.com/metaplex-foundation/mpl-token-metadata/blob/master/programs/token-metadata/program/src/state/metadata.rs
    let offset = 1; // key
    offset += 32; // update_authority
    offset += 32; // mint
    offset += 4;  // name string length prefix (always 4, name is fixed 32 bytes)
    const name = decodeString(buf, offset, 32);
    offset += 32;
    offset += 4; // symbol length prefix (always 4, symbol is fixed 10 bytes)
    const symbol = decodeString(buf, offset, 10);
    offset += 10;
    offset += 4; // uri length prefix (always 4, uri is fixed 200 bytes)
    const uri = decodeString(buf, offset, 200);

    return { name, symbol, uri };
}



async function loadWallets() {
    try {
        const data = await fsa.readFile(jsonFilePath, 'utf8'); // Read the file asynchronously
        walletsJson = JSON.parse(data); // Parse JSON data
        console.log('Wallets loaded:', walletsJson);
        
        // Example: Create wallets object dynamically
        const wallets = walletsJson.reduce((acc: { [key: string]: string }, wallet) => {
            acc[wallet.type] = wallet.pubkey; // Now TypeScript knows acc is a string dictionary
            return acc;
        }, {});
        console.log(wallets)
        return wallets;
        console.log('Dynamic wallets object:', wallets);

    } catch (err) {
        console.error('Error loading wallets:', err);
    }
}


// loadWallets().then((s) => {
//     wallets = s; // Wallets is now the resolved data
//     console.log('Wallets:', wallets); // Wallets is now the resolved data
// }).catch((err) => {
//     console.error('Error loading wallets:', err);
// });


// constructk wallets from json use type and pubkey
// let wallets: { [key: string]: string } = {};
// walletsJson.forEach((wallet) => {
//     wallets[wallet.type] = wallet.pubkey;
// });


// function to Get private key from public key
function getPrivateKey(pubKey: string) {
    const wallet = walletsJson.find((w) => w.pubkey === pubKey);
    return wallet ? wallet.privatekey : '';
}

async function sendMessageToQueue(message: any) {
    const username = 'pump2';
    const password = 'pump2';
    const host = 'kroocoin.xyz';
    const port = 5672; // Default RabbitMQ port
    const queue = 'autosell';
    

    // Connection URL
    const connectionUrl = `amqp://${username}:${password}@${host}:${port}`;

    try {
        // Connect to RabbitMQ
        const connection = await amqp.connect(connectionUrl);
        console.log('Connected to RabbitMQ');

        // Create a channel
        const channel = await connection.createChannel();

        // Ensure the queue exists
        await channel.assertQueue(queue, {
            durable: true, // Messages persist even if RabbitMQ restarts
        });

        // Send the message to the queue
        const messageBuffer = Buffer.from(JSON.stringify(message));
        channel.sendToQueue(queue, messageBuffer);
        console.log(`Message sent to queue "${queue}":`, message);

        // Close the connection
        await channel.close();
        await connection.close();
        console.log('Connection closed');
    } catch (error) {
        console.error('Error sending message to queue:', error);
    }
}


async function getAccountBalance(publicKeyString: string): Promise<number> {
    const connection = new Connection(RPC, 'processed');
    const publicKey = new PublicKey(publicKeyString);
  
    const accountInfo = await connection.getAccountInfo(publicKey);
  
    if (accountInfo === null) {
      return 0; // Account not found
    }
  
    return accountInfo.lamports;
  }

export async function transferSol(fromPrivKey:string,toPubKey:string,amount:number,drain=false) {
    const privateKey = new Uint8Array(bs58.decode(fromPrivKey));
    const fromKey = Keypair.fromSecretKey(privateKey);
    const txBuilder = new Transaction();
    let final_amount=0;
    if (drain) {
        const fromPubKey=fromKey.publicKey.toBase58();
        const balance = await getAccountBalance(fromPubKey);
         final_amount=(balance-TRANSFER_FEE);
    }else{
         final_amount=LAMPORTS_PER_SOL * amount;
    }
    
    const connection = new Connection(
        RPC,
         'confirmed'
     );

     const transaction = new Transaction().add(
        SystemProgram.transfer({
            fromPubkey: fromKey.publicKey,
            toPubkey: new PublicKey(toPubKey),
            lamports: final_amount
        }),
       );
       console.log('TRANSACTION', transaction);
       const signature = await sendAndConfirmTransaction(
        connection,
        transaction,
        [fromKey],
        {
            skipPreflight: true,
            preflightCommitment: 'processed'
        }
       );
       console.log('SIGNATURE', signature);


}

export async function getPumpWallet(status='fresh') {

    if ( status == "generated"){
        const mint = Keypair.generate();
        const trade = Keypair.generate();
        const mintPubKey = mint.publicKey.toBase58();
        const mintPrivKey = bs58.encode(mint.secretKey);
        const tradePubKey = trade.publicKey.toBase58();
        const tradePrivKey = bs58.encode(trade.secretKey);
        return { mintPubKey, mintPrivKey, tradePubKey, tradePrivKey };

    }else{

    const conn = await createConnection(DB_CONFIG);
        let retObj = {};
        const query = 'select mintPubKey,mintPrivKey,tradePubKey,tradePrivKey from minting where status = ? order by id asc limit 1';
        const [rows, fields] = await conn.execute(query,[status]);
        conn.end();
        if (Array.isArray(rows) && rows.length > 0) {
            //console.log(rows[0]);
            return rows[0] || null;
          } else {
            // No rows found (or an OkPacket for some reason), return null
            return null;
          }
        }
}

export async function getWalletByKey(pubKey:string) {
    const conn = await createConnection(DB_CONFIG);
        let retObj = {};
        const query = 'select mintPubKey,mintPrivKey,tradePubKey,tradePrivKey from minting where mintPubKey = ? or mintPrivKey = ? or tradePubKey = ? or tradePrivKey = ? order by id asc limit 1';
        const [rows, fields] = await conn.execute(query,[pubKey,pubKey,pubKey,pubKey]);
        conn.end();
        if (Array.isArray(rows) && rows.length > 0) {
            //console.log(rows[0]);
            return rows[0];
          } else {
            // No rows found (or an OkPacket for some reason), return null
            return null;
          }

}
export async function updateWalletStatus(pubKey:string,status:string) {

        try {
            const conn = await createConnection(DB_CONFIG);
            const query = 'update minting set status = ? , updated = NOW() where mintPubKey = ?';
            await conn.execute(query, [status, pubKey]);
            conn.end();
        } catch (error) {
            console.error('Error updating wallet status:', error);
        }
       

}

async function uploadFileToIPFS(filePath: string) {
    const url = 'http://localhost:5001/api/v0/add';
    const formData = new FormData();
    formData.append('file', fs.createReadStream(filePath));

    try {
        const response = await axios.post(url, formData, {
            headers: {
                ...formData.getHeaders(),
            },
        });
        console.log('File uploaded to IPFS:', response.data);
        return response.data;
    } catch (error) {
        console.error('Error uploading file to IPFS:', error);
    }
}

const pinata = new PinataSDK({
    pinataJwt: process.env.JWT!,
    pinataGateway: "jade-personal-gecko-402.mypinata.cloud",
  });

//   async function addFileWithOptions(filePath: String) {
//    // const ipfs = create({ url: 'http://localhost:5001' });
//     const fs = require('fs');
//     const fileContent = fs.readFileSync(filePath);
  
//     const { cid } = await ipfs.add(fileContent, {
//       pin: true, // Ensure the file is pinned
//       wrapWithDirectory: false, // Don't wrap in a directory
//     });
  
//     console.log(`File added with CID: ${cid}`);
//     return cid;
//   }
  
export async function launchToken(deployerPrivatekey: string, name: string, symbol: string, uri: string, sol_amount: number = 1.4, simulation: boolean = false,sol_amount_autosell: number = 0): Promise<string> {
    const connection = new Connection(
       RPC,
        'confirmed'
    );

    const payer = await getKeyPairFromPrivateKey(deployerPrivatekey);
    const owner = payer.publicKey;

    //Create new wallet to be used as mint
    
    //const pairFromDB=await getPumpWallet('active');
    const pairFromDB=await getPumpWallet('generated');
    

    if (!pairFromDB) {
        throw new Error("No fresh wallet found in the database");
    }
    const privKey = (pairFromDB as any)['mintPrivKey'];
    const pubKey = (pairFromDB as any)['mintPubKey'];
    
    //const secretKeyBytes = bs58.decode("22YGR93sqrANTd5PgUx7JisrbHrCpvfc7xrKV8nYUDksrEP7Z2yTewPBMngeheAGXEQKjb9ZKhMgGXi4d6E6Gz1c"); //D8N3YSEF79QaBGjM4tpzsJDWjM8PTsk8VZpQXPbBpump
    //const mint = Keypair.fromSecretKey(secretKeyBytes);

    const secretKeyBytes = bs58.decode(privKey); 
    const mint = Keypair.fromSecretKey(secretKeyBytes);
    
    //const mint = Keypair.generate();

    //const mintPrivateKeyBase58 = "2D1xTbxqGLxoeL8tXHUJC5huJiUxG27bDCsNqgzTZE8gSxctmRfXwu1uZvdLRd3ong1t5NoNMfb49TgwuoqEomZG";
    //const mintPrivateKey = bs58.decode(mintPrivateKeyBase58);
    //const mint = Keypair.fromSecretKey(mintPrivateKey);

    const [bondingCurve, bondingCurveBump] = PublicKey.findProgramAddressSync(
        [Buffer.from("bonding-curve"), mint.publicKey.toBuffer()],
        PUMP_FUN_PROGRAM
    );
    

    const [associatedBondingCurve, associatedBondingCurveBump] = PublicKey.findProgramAddressSync(
        [
            bondingCurve.toBuffer(),
            new PublicKey("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA").toBuffer(),
            mint.publicKey.toBuffer()
        ],
        new PublicKey("ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL")
    );

    const [metadata, metadataBump] = await PublicKey.findProgramAddressSync(
        [Buffer.from("metadata"), MPL_TOKEN_METADATA.toBuffer(), mint.publicKey.toBuffer()],
        MPL_TOKEN_METADATA
    );

    const txBuilder = new web3.Transaction();
    const txBuilder2 = new web3.Transaction();
    // Adding the Compute Budget instruction
    const computeBudgetInstruction = new web3.TransactionInstruction({
        keys: [],
        programId: COMPUTE_BUDGET_PROGRAM_ID,
        data: Buffer.concat([
            Buffer.from(Uint8Array.of(3)), // discriminator for SetComputeUnitPrice
            bufferFromUInt64(1000000) // microLamports
        ])
    });

    txBuilder.add(computeBudgetInstruction);
    txBuilder2.add(computeBudgetInstruction);
    const keys = [
        { pubkey: mint.publicKey, isSigner: true, isWritable: true }, // Mint account
        { pubkey: MINT_AUTHORITY, isSigner: false, isWritable: false }, // Mint authority
        { pubkey: bondingCurve, isSigner: false, isWritable: true }, // Bonding curve PDA
        { pubkey: associatedBondingCurve, isSigner: false, isWritable: true }, // Associated bonding curve PDA
        { pubkey: GLOBAL, isSigner: false, isWritable: false }, // Global config
        { pubkey: MPL_TOKEN_METADATA, isSigner: false, isWritable: false }, // Metadata program ID
        { pubkey: metadata, isSigner: false, isWritable: true }, // Metadata PDA
        { pubkey: owner, isSigner: true, isWritable: true }, // Owner account
        { pubkey: SYSTEM_PROGRAM, isSigner: false, isWritable: false }, // System program
        { pubkey: TOKEN_PROGRAM_ID, isSigner: false, isWritable: false }, // Token program
        { pubkey: ASSOCIATED_TOKEN_PROGRAM_ID, isSigner: false, isWritable: false }, // Associated token account program
        { pubkey: RENT, isSigner: false, isWritable: false }, // Rent sysvar
        { pubkey: PUMP_FUN_ACCOUNT, isSigner: false, isWritable: false }, // Pump fun account
        { pubkey: PUMP_FUN_PROGRAM, isSigner: false, isWritable: false } // Pump fun program ID
    ];
    console.log("name:", name," symbol:", symbol, " uri:", uri);
    const nameBuffer = bufferFromString(name);
    const symbolBuffer = bufferFromString(symbol);
    const uriBuffer = bufferFromString(uri);
    const creatorBuffer = owner.toBuffer();

    const data = Buffer.concat([
        Buffer.from("181ec828051c0777", "hex"),
        nameBuffer,
        symbolBuffer,
        uriBuffer,
        creatorBuffer
    ]);

    const instruction = new web3.TransactionInstruction({
        keys: keys,
        programId: PUMP_FUN_PROGRAM,
        data: data
    });

    txBuilder.add(instruction);

    // buy in crete token account

    const tokenAccountAddress = await getAssociatedTokenAddress(
        mint.publicKey,
        owner,
        false
    );

    txBuilder.add(
        createAssociatedTokenAccountInstruction(
            payer.publicKey,
            tokenAccountAddress,
            payer.publicKey,
            mint.publicKey
        )
    );

    const solIn = sol_amount
    const slippageDecimal = 0.5;
    const solTokens = 33_500_000;
    const tokenOut = solIn * solTokens*1_000_000;
    const solInLamports = solIn * LAMPORTS_PER_SOL;
    const coinData = {
        "virtual_token_reserves": **********,
        "virtual_sol_reserves": **********
    }
    //const tokenOut = Math.floor(solInLamports * coinData["virtual_token_reserves"] / coinData["virtual_sol_reserves"]);
    //const tokenOut=3_000_000_000_000;
                 // 34_612_903_225_806   1sol

    // 35M , MaxSol = price * slippage
   //amount = int((sol_in_lamports * virtual_sol_reserves) / (real_token_reserves + sol_in_lamports)) 


const [vault,_] = PublicKey.findProgramAddressSync(
    [
        Buffer.from("creator-vault"),
        owner.toBuffer()
    ],
    PUMP_FUN_PROGRAM
);



    const solInWithSlippage = solIn * (1 + slippageDecimal);
    const maxSolCost = Math.floor(solInWithSlippage * LAMPORTS_PER_SOL);
    const ASSOCIATED_USER = tokenAccountAddress;
    const USER = owner;
    const BONDING_CURVE = bondingCurve;
    const ASSOCIATED_BONDING_CURVE = associatedBondingCurve;
    const FEE_RECIPIENT = new PublicKey("62qc2CNXwrYqQScmEdiZFFAnJR262PxWEuNQtxfafNgV");
    const keys2 = [
        { pubkey: GLOBAL, isSigner: false, isWritable: false },
        { pubkey: FEE_RECIPIENT, isSigner: false, isWritable: true },
        { pubkey: mint.publicKey, isSigner: false, isWritable: false },
        { pubkey: BONDING_CURVE, isSigner: false, isWritable: true },
        { pubkey: ASSOCIATED_BONDING_CURVE, isSigner: false, isWritable: true },
        { pubkey: ASSOCIATED_USER, isSigner: false, isWritable: true },
        { pubkey: USER, isSigner: false, isWritable: true },
        { pubkey: SYSPROGRAM, isSigner: false, isWritable: false },
        { pubkey: TOKEN_PROGRAM_ID, isSigner: false, isWritable: false },
        { pubkey: vault, isSigner: false, isWritable: true },
        { pubkey: PUMP_FUN_ACCOUNT, isSigner: false, isWritable: false },
        { pubkey: PUMP_FUN_PROGRAM, isSigner: false, isWritable: false }
    ];

    console.log(keys2);

    // keys2.forEach(accountMeta => {
    //     const pubkeyString = accountMeta;
    //     console.log(pubkeyString);
    //     }); 

    const data2 = Buffer.concat([
        bufferFromUInt64("16927863322537952870"),
        bufferFromUInt64(tokenOut),
        bufferFromUInt64(maxSolCost)
    ]);

    const instruction2 = new TransactionInstruction({
        keys: keys2,
        programId: PUMP_FUN_PROGRAM,
        data: data2
    });
    txBuilder.add(instruction2);

    const transaction = await createTransaction(connection, txBuilder.instructions, payer.publicKey);

///
let signature;
if (simulation == false) {
    const message = <object> {mint :mint.publicKey.toString(), wallet:USER.toString(), sellAtSol: Number(sol_amount_autosell), maxMinutes:5};
    if (sol_amount_autosell > sol_amount) {
        await sendMessageToQueue(message);
    }
    //return pubKey;

    signature = await sendAndConfirmTransactionWrapper(connection, transaction, [payer,mint]);
    console.log(`Tx confirmed with signature: ${signature} `);
    

} else if (simulation == true) {
    const simulatedResult = await connection.simulateTransaction(transaction);
    console.log(simulatedResult);


    await updateWalletStatus(pubKey,'used');

    const newFresh=await getPumpWallet('fresh');
  
    if (!newFresh) {
        throw new Error("No fresh wallet found in the database");
    }
    const newActivepubKey = (newFresh as any)['mintPubKey'];
    await updateWalletStatus(newActivepubKey,'active');


    return pubKey;
}


    //const signature = await sendAndConfirmTransactionWrapper(connection, transaction, [payer,mint]);

  //  console.log(`Tx confirmed with signature: ${signature} `);
  //  return signature || "";
//   await updateWalletStatus(pubKey,'used');

//   const newFresh=await getPumpWallet('fresh');

//   if (!newFresh) {
//       throw new Error("No fresh wallet found in the database");
//   }
//   const newActivepubKey = (newFresh as any)['mintPubKey'];
//   await updateWalletStatus(newActivepubKey,'active');

  return pubKey || "";

}

// Define the Example class
class Example {
    private deployerPrivatekey: string;
    private tokenUri: string;
    private tokenSymbol: string;
    private tokenName: string;
    private sol_amount: number;
    private simulation: boolean;
    private sol_amount_autosell: number;
 

    constructor(deployerPrivatekey: string, tokenUri: string, tokenSymbol: string, tokenName: string,sol_amount: number = 1.4, simulation: boolean = false,sol_amount_autosell: number = 0) {
        this.deployerPrivatekey = deployerPrivatekey;
        this.tokenUri = tokenUri;
        this.tokenSymbol = tokenSymbol;
        this.tokenName = tokenName;
        this.sol_amount = sol_amount;
        this.simulation = simulation;
        this.sol_amount_autosell = sol_amount_autosell;
    }

    async main() {
        try {

            const mint = await launchToken(this.deployerPrivatekey, this.tokenName, this.tokenSymbol, this.tokenUri, this.sol_amount,this.simulation,this.sol_amount_autosell);
            return { success: true, message: "Token deployed successfully" , mint: mint};
       } catch (error) {
           console.error('Error in main function:', error);
           return { success: false, message: error instanceof Error ? error.message : "Unknown error" };
       }
    }
}

app.get("/balance", (req: Request, res: Response): void => {
    const account: string = req.query["account"] as string;
    const balance=getAccountBalance(account).then((balance) => {
    res.status(200).json({ account: account ,balance: (Number(balance)/1e9).toFixed(3) });
    });
});

app.get("/test", (req: Request, res: Response): void => {
    //const pairFromDB=getPumpWallet('active');
    //const owner = getKeyPairFromPrivateKey(PRIVATE_KEY);
    // let wallet=tradeWalletPubKey;
    // if (req.query["type"] == "newborn"){
    //     wallet=wallets["newborn"];}
    // else if (req.query["type"] == "died"){
    //     wallet=wallets["died"];
    // }else{  
    //     wallet=wallets["other"];
    // }

    // const balance=getAccountBalance(wallet).then((balance) => {
    // const data = {"data": (Number(balance)/1e9).toFixed(3)};

    console.log(wallets)
    const data = {"name": "",
        "symbol": "",
        "description": "",
        "website": "",
        "twitter": "",
        "telegram": "",
        "image": ""
    };
    res.render("deploy",{wallets,data});
    });


    app.get("/clone", (req: Request, res: Response): void => {

        getTokenMetadata(req.query["token"] as string).then((metadata) => {
            
        if (req.query["token"]){
            const url=`${metadata.uri}`;
            
            // Using Node.js built-in https module instead of axios
            const fetch = (url: string): Promise<any> => {
            return new Promise((resolve, reject) => {
                https.get(url, (res: any) => {
                let data = '';
                
                // Accumulate data chunks
                res.on('data', (chunk: string) => {
                    data += chunk;
                });
                
                // Process complete response
                res.on('end', () => {
                    try {
                    resolve({
                        status: res.statusCode,
                        data: JSON.parse(data)
                    });
                    } catch (e) {
                    reject(e);
                    }
                });
                }).on('error', (err: Error) => {
                reject(err);
                });
            });
            };
            
            fetch(url)
            .then(response => {
            if (response.status === 200) {
                const data = response.data;
                console.log(data);

                
                //res.status(200).send(data);
                res.render("deploy",{wallets,data}); 
                
            } else {
                console.error('Failed to retrieve coin data:', response.status);
                res.status(500).send('Failed to retrieve coin data');
            }
            })
            .catch(error => {
            console.error('Error fetching coin data:', error);
            res.status(500).send('Failed to retrieve coin data');
            });
        }
        });

        });
//});

app.post('/download', async (req: Request, res: Response): Promise<void> => {
    const { url: video_url } = req.body;
    const video_id = uuidv4();
    const video_path = path.join(UPLOAD_FOLDER, `${video_id}.mp4`);

    try {
        const response = await axios.get(video_url, { responseType: 'stream' });
        const writer = fs.createWriteStream(video_path);
        response.data.pipe(writer);

        await new Promise((resolve, reject) => {
            writer.on('finish', resolve);
            writer.on('error', reject);
        });

        const frame_folder = path.join(IMAGES_FOLDER, video_id);
        if (!fs.existsSync(frame_folder)) {
            fs.mkdirSync(frame_folder, { recursive: true });
        }

        ffmpeg(video_path)
            .on('end', () => {
                const frame_files = fs.readdirSync(frame_folder);
                const image_paths = frame_files.map(file => `/static/images/${video_id}/${file}`);
                res.status(200).json({ images: image_paths, video_id });
            })
            .on('error', (err) => {
                res.status(500).json({ error: `Failed to process video: ${err.message}` });
            })
            .screenshots({
                count: 10,
                folder: frame_folder,
                filename: 'frame-%i.jpg',
            });
    } catch (err) {
        res.status(400).json({ error: `Failed to download video: ${(err as Error).message}` });
    }
});

app.post('/process_image_url', async (req: Request, res: Response): Promise<void> => {
    const { image_url } = req.body;

    if (!image_url) {
        res.status(400).json({ error: 'Image URL is required' });
        return;
    }

    try {
        const image_id = uuidv4();
        const image_path = path.join(IMAGES_FOLDER, `${image_id}.jpg`);

        const response = await axios({
            url: image_url,
            method: 'GET',
            responseType: 'stream',
        });

        const writer = fs.createWriteStream(image_path);
        response.data.pipe(writer);

        writer.on('finish', () => {
            res.json({ image_path: `/static/images/${image_id}.jpg` });
        });
        writer.on('error', (err) => {
            res.status(500).json({ error: `Error downloading image: ${err.message}` });
        });
    } catch (err) {
        res.status(500).json({ error: `Error downloading image: ${(err as Error).message}` });
    }
});

app.post('/shorten', async (req: Request, res: Response): Promise<void> => {
    try {
        const { long_url } = req.body;

        if (!long_url) {
            res.status(400).json({ error: 'Missing long_url' });
            return;
        }

        const short_url = shortUUID.generate().substring(0, 6);

        const conn = await createConnection(DB_CONFIG);
        try {
            const query = 'INSERT INTO urls (long_url, short_url) VALUES (?, ?)';
            const values = [long_url, short_url];
            await conn.execute(query, values);

            res.status(201).json({ short_url: `https://kroocoin.xyz/shortened/${short_url}` });
        } finally {
            await conn.end();
        }
    } catch (err) {
        res.status(500).json({ error: (err as Error).message });
    }
});

app.get('/shortened/:short_url', async (req: Request, res: Response): Promise<void> => {
    const { short_url } = req.params;

    const conn = await createConnection(DB_CONFIG);

    try {
        const query = 'SELECT long_url FROM urls WHERE short_url = ?';
        const [rows]: any = await conn.execute(query, [short_url]);

        if (!rows.length) {
            res.status(404).json({ error: 'URL not found' });
            return;
        }

        const long_url = rows[0].long_url;
        res.redirect(long_url);
    } catch (err) {
        res.status(500).json({ error: (err as Error).message });
    } finally {
        await conn.end();
    }
});

app.get('/static/images/*', (req: Request, res: Response): void => {
    const filePath = req.params[0]; // This captures the full path after /static/images/
    const fullPath = path.join(IMAGES_FOLDER, filePath); // Combine it with the images folder

    res.sendFile(fullPath, (err) => {
        if (err) {
            res.status(404).send('File not found');
        }
    });
});
app.get('/videos/:filename', (req: Request, res: Response): void => {
    const { filename } = req.params;
    res.sendFile(path.join(UPLOAD_FOLDER, filename));
});

/*
        token_name = request.form['token_name']
        token_symbol = request.form['token_symbol']
        description = request.form.get('token_description', '')
        website = request.form.get('website', '')
        twitter = request.form.get('twitter', '')
        telegram = request.form.get('telegram', '')
        sol_amount = float(request.form.get('sol_amount', 1.4))
        pasted_image_data = request.form.get('pasted_image_data', '')
        */

// Route to handle deployment
app.post('/create-token', async (req: Request, res: Response): Promise<void> => {
    //console.log(req.body);
    //const { deployerPrivatekey, tokenUri, tokenSymbol, tokenName } = req.body;
    const { token_name, token_symbol, token_description, website,twitter,telegram,sol_amount,sol_amount_autosell,pasted_image_data,trade_account } = req.body;

    // Validate request query parameters
    if (!pasted_image_data || !token_name || !token_symbol ) {
        res.status(400).json({ success: false, message: "Missing required parameters" });
        return;
    }
    //TODO: upload image to ipfs
    //TODO: upload manifest to ipfs
    //TODO: load private key from env

    // const manifest = {
    //     name: "RIP Shilling",
    //     symbol: "Shilling",
    //     description: " The Virginia Zoo is heartbroken to announce the passing of Shilling, our 17-year-old emu, during a veterinary procedure on the afternoon of Thursday, December 12th. The exam was being performed to investigate recent weight loss. Results are still pending from the necropsy, which may provide more insight into his condition.\r\n\r\nShilling arrived at the Virginia Zoo in 2010, where he lived with his companion Lester. Lester was sadly euthanized earlier this year at the age of 21 once his arthritis was no longer manageable with pain medication. Arthritis is common in zoo animals that have lived beyond their natural life expectancy. With wild emus typically living upwards of 10 years, both Shilling and Lester were well into their golden years at the time of their respective passings. It is because of the dedicated care of the Zoo’s Bird and Veterinary Teams that both were able to enjoy an enriching life well into their senior years. Some of Shilling’s favorite enrichment activities were puzzle feeders filled with grapes and bathing in water sprinklers to keep cool during the summer. According to his keeper Jaxx C. Shilling really loved playing with bubbles and pinwheels, too.\"\r\n\r\nWith this loss, the Zoo is currently not housing any emus. The habitat will be reevaluated by animal care staff before deciding next steps. We ask that you please keep the Bird and Veterinary Teams in your thoughts in their time of loss. ❤️\"",
    //     image: "https://ipfs.io/ipfs/QmTDVkBB9Y6ax2VFum3vo4QQhLSW9hX4XRLGwvQvpeAM3A",
    //     showName: true,
    //     createdOn: "https://pump.fun",
    //     twitter: "https://www.instagram.com/p/DD0Uy4ssCuF",
    //     telegram: "https://www.instagram.com/p/DD0Uy4ssCuF",
    //     website: "https://www.instagram.com/p/DD0Uy4ssCuF"
    // }

    // put in try/catch

  
        // upload image

        const base64Image = pasted_image_data.split(';base64,').pop();
        const binaryData = Buffer.from(base64Image, 'base64');
        const imageFilePath = `tmp/${token_symbol}_${uuidv4()}.png`;
        //await fs.promises.writeFile(imageFilePath, binaryData);
        //const ipfsImage=await addFileWithOptions(imageFilePath);
        //const ipfsImage = await uploadFileToIPFS(imageFilePath);

        const ipfsImage = new File([binaryData], token_symbol + ".png", { type: "text/plain" });

        const ImagePinHash = await pinata.upload.file(ipfsImage);
        console.log(pinataGW+"/"+ImagePinHash["IpfsHash"]);
        const imageHash=ImagePinHash["IpfsHash"];

        //const IpfsHash=ipfsImage["Hash"];
        //console.log("https://ipfs.io/ipfs/"+IpfsHash);
        const manifest2 = {
            name: token_name,
            symbol: token_symbol,
            description: token_description,
            image: pinataGW+'/'+imageHash,
            showName: true,
            createdOn: "https://pump.fun",
            twitter: twitter,
            telegram: telegram,
            website: website
        }

        //const metadataFilePath = `tmp/${token_symbol}_${uuidv4()}.json`;
        //await fs.promises.writeFile(metadataFilePath, JSON.stringify(manifest2, null, 4));


          //const ipfsMetadata=await addFileWithOptions(metadataFilePath);
          //const ipfsMetadata = await uploadFileToIPFS(metadataFilePath);
          //const IpfsHash2=ipfsMetadata["Hash"];


        const metafile = new File([JSON.stringify(manifest2, null, 4)], token_symbol + ".json", { type: "text/plain" });
        const pinHash = await pinata.upload.file(metafile);
        const IpfsHash2 = pinHash["IpfsHash"];
        
        
        const tokenUri1=pinataGW+"/"+IpfsHash2;
        console.log(pinataGW+"/"+IpfsHash2);

    //const deployerPrivatekey=PRIVATE_KEY;
    const deployerPrivatekey=getPrivateKey(trade_account);
    

 
    // const tokenUri1="https://ipfs.io/ipfs/QmdP8iRM2jQGhMruCJLiRQX1TPeoXeXoaNutSprWhCvUj5";
    // const tokenSymbol1="Shilling";
    // const tokenName1="RIP Shilling";
    // const tokenUri="test";
    try {
        // Create an instance of the Example class
        const example = new Example(
            deployerPrivatekey as string, 
            tokenUri1 as string, 
            token_symbol as string, 
            token_name as string,
            sol_amount,
            isSimulated,
            sol_amount_autosell
        );

        // Execute the main function
        const result = await example.main();
        
        
        // Send response back to the client
        //res.status(200).json(result);
        const mint=result.mint;
        console.log("mint:",mint);

        res.redirect(`https://neo.bullx.io/terminal?chainId=**********&address=${mint}`);
 
 
    } catch (error) {
        console.error('Error handling /deploy request:', error);
        res.status(500).json({ success: false, message: "Internal server error" });
    }
});
 
// Start the server
app.listen(port, () => {
    console.log(`Server is running on http://localhost:${port}`);
});

//http://127.0.0.1:3000/deploy?deployerPrivatekey=EnzmHsu21aALk6eC89SZCqDyxDR3X4FUSp5sjrARQQrXYZcLNzeGCG4zVZ4DV2CDjBNdsXUATbNS9USd5Y3cQQe&tokenUri=https://ipfs.io/ipfs/QmdP8iRM2jQGhMruCJLiRQX1TPeoXeXoaNutSprWhCvUj5&tokenSymbol=Shilling&tokenName=RIP%20Shilling