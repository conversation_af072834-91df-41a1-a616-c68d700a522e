"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getKeyPairFromPrivateKey = getKeyPairFromPrivateKey;
exports.createTransactionSwap = createTransactionSwap;
exports.createTransaction = createTransaction;
exports.sendAndConfirmTransactionWrapperOLD = sendAndConfirmTransactionWrapperOLD;
exports.sendAndConfirmTransactionWrapper = sendAndConfirmTransactionWrapper;
exports.bufferFromUInt64 = bufferFromUInt64;
exports.generatePubKey = generatePubKey;
exports.bufferFromString = bufferFromString;
const web3_js_1 = require("@solana/web3.js");
const web3_js_2 = require("@solana/web3.js");
const bs58_1 = __importDefault(require("bs58"));
const sha256_1 = require("@noble/hashes/sha256");
const spl_token_1 = require("@solana/spl-token");
async function getKeyPairFromPrivateKey(key) {
    return web3_js_1.Keypair.fromSecretKey(new Uint8Array(bs58_1.default.decode(key)));
}
async function createTransactionSwap(connection, instructions, payer, priorityFeeInSol = 100) {
    const modifyComputeUnits = web3_js_1.ComputeBudgetProgram.setComputeUnitLimit({
        units: 1000000,
    });
    const transaction = new web3_js_2.Transaction().add(modifyComputeUnits);
    if (priorityFeeInSol > 0) {
        const microLamports = priorityFeeInSol * 1_000_000_000; // convert SOL to microLamports
        const addPriorityFee = web3_js_1.ComputeBudgetProgram.setComputeUnitPrice({
            microLamports,
        });
        transaction.add(addPriorityFee);
    }
    transaction.add(...instructions);
    transaction.feePayer = payer;
    transaction.recentBlockhash = (await connection.getLatestBlockhash()).blockhash;
    return transaction;
}
async function createTransaction(connection, instructions, payer) {
    const transaction = new web3_js_2.Transaction().add(...instructions);
    transaction.feePayer = payer;
    transaction.recentBlockhash = (await connection.getLatestBlockhash()).blockhash;
    return transaction;
}
async function sendAndConfirmTransactionWrapperOLD(connection, transaction, signers) {
    try {
        const signature = await (0, web3_js_2.sendAndConfirmTransaction)(connection, transaction, signers, { skipPreflight: true, preflightCommitment: 'confirmed' });
        console.log('Transaction confirmed with signature:', signature);
        return signature;
    }
    catch (error) {
        console.error('Error sending transaction:', error);
        return null;
    }
}
async function sendAndConfirmTransactionWrapper(connection, transaction, signers) {
    try {
        const latestBlockhash = await connection.getLatestBlockhash();
        // Send without confirmation
        const signature = await connection.sendTransaction(transaction, signers, {
            skipPreflight: true, // or keep true if you really need to skip
            preflightCommitment: 'processed',
        });
        console.log('Transaction sent, signature:', signature);
        // Manually confirm
        // await connection.confirmTransaction(
        //   {
        //     signature,
        //     blockhash: latestBlockhash.blockhash,
        //     lastValidBlockHeight: latestBlockhash.lastValidBlockHeight,
        //   },
        //   'processed'
        // );
        //TODO: check account balance !! 
        console.log('Transaction confirmed with signature:', signature);
        return signature;
    }
    catch (error) {
        console.error('Error sending or confirming transaction:', error);
        return null;
    }
}
function bufferFromUInt64(value) {
    let buffer = Buffer.alloc(8);
    buffer.writeBigUInt64LE(BigInt(value));
    return buffer;
}
function generatePubKey({ fromPublicKey, programId = spl_token_1.TOKEN_PROGRAM_ID, }) {
    const seed = web3_js_1.Keypair.generate().publicKey.toBase58().slice(0, 32);
    const publicKey = createWithSeed(fromPublicKey, seed, programId);
    return { publicKey, seed };
}
function createWithSeed(fromPublicKey, seed, programId) {
    const buffer = Buffer.concat([fromPublicKey.toBuffer(), Buffer.from(seed), programId.toBuffer()]);
    const publicKeyBytes = (0, sha256_1.sha256)(buffer);
    return new web3_js_2.PublicKey(publicKeyBytes);
}
function bufferFromString(value) {
    const buffer = Buffer.alloc(4 + value.length);
    buffer.writeUInt32LE(value.length, 0);
    buffer.write(value, 4);
    return buffer;
}
