"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createTransaction = createTransaction;
const web3_js_1 = require("@solana/web3.js");
async function createTransaction(connection, instructions, wallet, priorityFee = 0) {
    const modifyComputeUnits = web3_js_1.ComputeBudgetProgram.setComputeUnitLimit({
        units: 1400000
    });
    const transaction = new web3_js_1.Transaction().add(modifyComputeUnits);
    if (priorityFee > 0) {
        const microLamports = priorityFee * 1_000_000_000; // convert SOL to microLamports
        const addPriorityFee = web3_js_1.ComputeBudgetProgram.setComputeUnitPrice({
            microLamports
        });
        transaction.add(addPriorityFee);
    }
    transaction.add(...instructions);
    transaction.feePayer = wallet;
    transaction.recentBlockhash = (await connection.getLatestBlockhash()).blockhash;
    return transaction;
}
