import * as fs from 'fs';
import axios from 'axios';
import FormData = require('form-data');

const pinataApiKey = "eae622f81b2b9d595268";
const pinataSecretApiKey = "****************************************************************";
const gateway = "https://ipfs.io/ipfs/";

export async function pinFileToIPFS(filePath: string): Promise<string> {
    try {
        const formData = new FormData();
        formData.append('file', fs.createReadStream(filePath));

        const response = await axios.post('https://api.pinata.cloud/pinning/pinFileToIPFS', formData, {
            maxContentLength: Infinity,
            headers: {
                'Content-Type': `multipart/form-data; boundary=${formData.getBoundary()}`,
                pinata_api_key: pinataApiKey,
                pinata_secret_api_key: pinataSecretApiKey
            }
        });

        const ipfsHash = response.data.IpfsHash;
        console.log(`IPFS Hash: ${ipfsHash}`);
        return ipfsHash;
    } catch (error) {
        console.error('Error uploading to IPFS:', error);
        throw error;
    }
}
