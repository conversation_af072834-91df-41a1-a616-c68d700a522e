Changelog for 10-04-2024:
  - Web UI
    - Added beta.viper.bot for testing new features
    - Added bundling option up to 25 wallets on beta.viper.bot
    - Bugfix for not showing proper error messages on both viper.bot and beta.viper.bot
    
Changelog for 26-07-2024:
  - Web UI
    - Added option to use one time coupon codes that remove fees
    - See https://t.me/viper_discussions for more info and to obtain a trial coupon
Changelog for 25-07-2024:
  - Backend (used by Telegram bot and Web UI)
    - Fixed bug where duplicate wallets would cause issue when using the sell all function
  - Web UI
    - Fixed bug where fee at the bottom of page wasn't updating properly
    - Added Changelog Tab to navigation
  - Telegram bot
    - Fixed bug where wallets were not deleting properly

Changelog for 24-07-2024:
  - Github repo's
    - Added new showcase videos to illustrate updated features
    - Clarified max limit of 17 buys instead of 16
  - Telegram bot
    - Bugfixes for transaction confirmations
    - Bugfixes for selling functionality
    - Improved UI for easier selling after launches

Changelog for 22-07-2024:
  - Telegram bot
    - Added Support and Discussions Group (https://t.me/viper_discussions)
    - Added dev wallet step for clarity
    - Split up buy amount steps to per wallet input to show max balance
    - Added max buy amount indications considering slippage + tip
