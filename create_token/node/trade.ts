import { pumpFunBuy, pumpFunSell} from './src/swap';
import { TransactionMode } from './src/types'
import fs from 'fs';
import path from 'path';
import { getPrice } from "./src/price"
import { getCoinData  }  from "./src/api"
import { getAssociatedTokenAddress } from "@solana/spl-token"
import { Connection, PublicKey, Keypair} from '@solana/web3.js';
const bs58 = require('bs58'); 
import amqp from 'amqplib';
import { compileFunction } from 'vm';
import  { ASSOCIATED_TOKEN_PROGRAM_ID,TOKEN_PROGRAM_ID } from "@solana/spl-token";

const PUMP_FUN_PROGRAM = new PublicKey("6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P");
const PUMP_FUN_ACCOUNT = new PublicKey("Ce6TQqeHC9p8KetsN6JsjHK7UTZk7nasjjnr7XxXp9F1");


const queue="autosell";
const username = 'pump2';
const password = 'pump2';
const host = 'kroocoin.xyz';
const port = 5672; // Default RabbitMQ port

let messageJson: { mint: string; wallet: string; sellAtSol: number ,maxMinutes: number}[] = [];

const  api_key = [
    "cf3aa81f-7796-401b-a170-5567272f5f65"
            ]
function getAssociatedBondingCurve(mintPublicKey: PublicKey): PublicKey {
    // Derive the bonding curve public key
    const [bondingCurve] = PublicKey.findProgramAddressSync(
        [
        Buffer.from("bonding-curve"),
        mintPublicKey.toBuffer(),
        ],
        PUMP_FUN_PROGRAM
    );
    
    // Derive the associated bonding curve public key
    const [associatedBondingCurve] = PublicKey.findProgramAddressSync(
        [
        bondingCurve.toBuffer(),
        PUMP_FUN_ACCOUNT.toBuffer(),
        mintPublicKey.toBuffer(),
        ],
        ASSOCIATED_TOKEN_PROGRAM_ID
    );
    
    return associatedBondingCurve;
    }

function getRandomKey() {
    const randomIndex = Math.floor(Math.random() * api_key.length);
    return api_key[randomIndex];
}

const jsonFilePath = path.join(__dirname, '.wallets.json');
let walletsJson: { type: string; pubkey: string; privatekey: string }[] = [];
// Read and parse the JSON file
fs.readFile(jsonFilePath, 'utf8', (err, data) => {
    if (err) {
        console.error('Error reading the JSON file:', err);
        return;
    }
    try {
        walletsJson = JSON.parse(data);
        console.log('JSON Data:', walletsJson);
    } catch (parseErr) {
        console.error('Error parsing JSON:', parseErr);
    }
});

function getPrivateKey(pubKey: string) {
    const wallet = walletsJson.find((w) => w.pubkey === pubKey);
    return wallet ? wallet.privatekey : '';
}


const RPC="https://mainnet.helius-rpc.com/?api-key="+getRandomKey();

class Sell {
    private payerPrivateKey: string;
    private payerKeyPair: Keypair;
    private mintAddress: string;
    private transactionMode: TransactionMode;
    private connection: Connection;
    private mintPubkey: PublicKey;
    private ownerPubkey: PublicKey;
    private associatedBondingCurve: PublicKey;
    private balance: number;
    private sellAtSol: number;
    private maxMinutes: number;
    private slippageDecimal:number;
    private priorityFeeInSol:number;

    constructor(privateKey: string, mintAddress: string,sellAtSol: number ,maxMinutes: number , mode: TransactionMode) {
        this.payerPrivateKey = privateKey;
        this.payerKeyPair= Keypair.fromSecretKey(bs58.decode(this.payerPrivateKey));
        this.mintAddress = mintAddress;
        this.mintPubkey= new PublicKey(this.mintAddress);
        this.ownerPubkey = this.payerKeyPair.publicKey;
        this.transactionMode = mode;
        this.connection = new Connection(RPC, 'processed');
        this.associatedBondingCurve = this.mintPubkey;
        this.balance = 0;
        this.sellAtSol = sellAtSol;
        this.maxMinutes = maxMinutes;
        this.slippageDecimal = 0.1; // Example value, adjust as needed
        this.priorityFeeInSol = 0.0001; // Example value for tip to get faster inclusion, adjust as needed
        

    }

    async  checkTokenMinted(publicKey: string) {
        try {
            // Connect to the Solana mainnet
            const connection = this.connection;
    
            // Convert the provided string to a PublicKey
            const tokenPublicKey = new PublicKey(publicKey);
    
            // Fetch account information for the public key
            const accountInfo = await connection.getParsedAccountInfo(tokenPublicKey);
    
            if (accountInfo.value) {
                // Check if the account is a token mint account
                const data = accountInfo.value.data;
                if (data && 'parsed' in data) {
                    const parsedData = data.parsed;
                    if (parsedData.type === 'mint' && parsedData.info.isInitialized) {
                        console.log('Token is minted. Details:', parsedData.info);
                        return true;
                    }
                }
                console.log('Public key exists but is not a token mint.');
                return false;
            } else {
                console.log('No account found for the given public key.');
                return false;
            }
        } catch (error) {
            console.error('Error checking token:', error);
            return false;
        }
    }




    async getBalance(){
        
        const balanceInfo = await this.connection.getTokenAccountBalance(this.associatedBondingCurve);
        const tokenBalance = Number(balanceInfo.value.amount);
        return tokenBalance;
       
    }

    async sellNow(maxRetries = 60) {
        const tx = await pumpFunSell(
            this.transactionMode,
            this.payerPrivateKey,
            this.mintAddress,
            Math.floor(this.balance),
            this.priorityFeeInSol,
            this.slippageDecimal,
            RPC
        ) as string;
    
        let retries = 0; // Initialize retry counter
    
        while (retries < maxRetries) {
            const transactionDetails = await new Connection(RPC).getTransaction(tx, {
                commitment: "confirmed",
            });
    
            if (transactionDetails && transactionDetails.meta && transactionDetails.meta.err === null) {
                console.log('https://solscan.io/tx/' + tx + ' Succeeded');
                break; // Exit the loop if transaction succeeds
            } else if (transactionDetails && transactionDetails.meta && transactionDetails.meta.err !== null) {
                console.log('Error in transaction:', transactionDetails.meta.err);
                break; // Handle the error and exit
            } else {
                console.log('https://solscan.io/tx/' + tx + ' retrying...');
            }
    
            retries++; // Increment retry counter
    
            await new Promise(resolve => setTimeout(resolve, 300)); // Wait 300ms before retrying
        }
    
        if (retries >= maxRetries) {
            console.log(`Max retries (${maxRetries}) reached. Transaction may have failed.`);
            // Optionally throw an error or handle the timeout
        }
    }
    

    async main() {
        const solIn = 0.0001; // Example value, adjust as needed

        let isMinted = false;
        const maxMintCheck = 30; // Seconds check if created
        let nowTime = new Date();
        let stopMintCheckTime = new Date();
        stopMintCheckTime.setSeconds(nowTime.getSeconds() + maxMintCheck);
        console.log("nowTime: ", nowTime,stopMintCheckTime);
        while ( isMinted === false && nowTime < stopMintCheckTime) {
            const status = await this.checkTokenMinted(this.mintAddress);
            if (status) {
                isMinted = true;
                break;
            }   
            await new Promise(resolve => setTimeout(resolve,    200));
            nowTime = new Date();
        }
        if (isMinted === false) {
            console.log("Token not minted");
            return;
        }
        // Get the token balance 

  
        try {
            //const coinData = await getCoinData(this.mintAddress);
            //const bondingCurve = coinData["bonding_curve"];
            const [bondingCurve1]  = PublicKey.findProgramAddressSync([Buffer.from("bonding-curve"), new PublicKey(this.mintAddress).toBytes()], PUMP_FUN_PROGRAM);
            const bondingCurve = bondingCurve1.toBase58();
            this.associatedBondingCurve = await getAssociatedTokenAddress(
            this.mintPubkey,
            this.ownerPubkey,
            true
            );

        
            this.balance = await this.getBalance() as number;
            console.log("Token Balance: ", await this.getBalance());
            
            let sold=false;

            let isSold = false;

            let stopTradeTime= new Date();
            stopTradeTime.setMinutes(nowTime.getMinutes() + this.maxMinutes);
            console.log("nowTime: ", nowTime,stopMintCheckTime);
            let valueSol = 0; 
            let price = 0;
            while ( isSold === false && nowTime < stopTradeTime) {
                price = await getPrice(bondingCurve,this.connection) as number;
                valueSol = this.balance * price/1e6;
                console.log("Token Value Balance: ", valueSol.toFixed(5)); 

                if (valueSol >= this.sellAtSol) {
                    this.sellNow();
                    console.log("Token sold at treshold. Value:", valueSol.toFixed(5));
                    // sellig token
                    isSold = true;
                    break;

                }

                await new Promise(resolve => setTimeout(resolve,    200));
                nowTime = new Date();
            }

            if (isSold === false) {
                // Force sell now: 
                this.sellNow();
                console.log("Token not sold at treshold. Time expired. sold as is at value:", valueSol.toFixed(5));
                return;
            }else{
                console.log("Success: Token sold at treshold. Value:", valueSol.toFixed(5));
            }


        } catch (error) {
            console.error('Error in main function:', error);
        }
    }
}

//const privateKey = '5cni8hCKdJUDxBUNHPpQ87ACrEZomRQaJDVxxG1UFDz2cs2LyKJNSFv98XXqyq5W1vKsp3PGfm8pfEhUQXpFvRAi'; // Replace with your actual private key
//const mintAddress = 'FKmXD25msBuYwB2weBxksCLMnAC4JX5tm2MBBnRjpump'; //Replace with actual token mint address

const txMode = TransactionMode.Execution; //Set to simulate to test, Execution to perform




// async function consumeMessages() {
//     const connectionUrl = `amqp://${username}:${password}@${host}:${port}`;

//     try {
//         // Connect to RabbitMQ with authentication
//         const connection = await amqp.connect(connectionUrl);
//         console.log('Connected to RabbitMQ');

//         // Create a channel
//         const channel = await connection.createChannel();

//         // Ensure the queue exists
//         await channel.assertQueue(queue, {
//             durable: true, // Set to true if messages should survive RabbitMQ restarts
//         });

//         console.log(`Waiting for messages in queue: ${queue}`);

//         // Consume messages from the queue
//         channel.consume(
//             queue,
//             async (message) => {
//                 if (message) {
//                     // Process the message in the background
//                     processMessage(message.content.toString())
//                         .then(() => {
//                             // Acknowledge the message after processing
//                             channel.ack(message);
//                             console.log('Message processed and acknowledged.');
//                         })
//                         .catch((error) => {
//                             console.error('Error processing message:', error);
//                             // Optional: Reject the message (could requeue or dead-letter)
//                             channel.nack(message, false, false); // Sends to dead-letter queue if configured
//                         });
//                 }
//             },
//             {
//                 noAck: false, // Ensure manual acknowledgment
//             }
//         );
//     } catch (error) {
//         console.error('Error:', error);
//     }
// }

// Background message processor


async function processMessage(messageContent: string) {
    console.log(`Processing message: ${messageContent}`);
    const walletsJson = JSON.parse(messageContent);
    console.log(walletsJson.mint,walletsJson.wallet, walletsJson.sellAtSol,walletsJson.maxMinutes);
    //current timestamp
    const now = new Date();
    //add minutes to current timestamp
    const stopTime = now.getMinutes() + walletsJson.maxMinutes;

    const privateKey = getPrivateKey(walletsJson.wallet);
    const trade = new Sell(privateKey,walletsJson.mint,walletsJson.sellAtSol,walletsJson.maxMinutes,txMode);
    trade.main();
}

//consumeMessages();


