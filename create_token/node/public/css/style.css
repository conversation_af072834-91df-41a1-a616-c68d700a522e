body {
    font-family: Arial, sans-serif;
    background-color: #f2f2f2;
    margin: 0;
    padding: 0;
}
h1 {
    text-align: center;
    color: #333;
    margin-top: 30px;
}
.container {
    max-width: 600px;
    margin: 20px auto;
    background-color: #fff;
    padding: 30px;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
    border-radius: 8px;
}
label {
    display: block;
    margin-top: 15px;
    font-weight: bold;
}
input[type="text"],
input[type="url"],
input[type="number"],
textarea {
    width: 100%;
    padding: 10px;
    margin-top: 5px;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-sizing: border-box;
    font-size: 16px;
}
textarea {
    resize: vertical;
}
button {
    width: 100%;
    padding: 15px;
    background-color: #28a745;
    color: #fff;
    border: none;
    border-radius: 4px;
    font-size: 18px;
    cursor: pointer;
    margin-top: 20px;
}
button:hover {
    background-color: #218838;
}
.image-preview {
    margin-top: 20px;
    text-align: center;
}
.image-preview img {
    max-width: 300px;
    max-height: 300px;
    border: 1px solid #ccc;
    border-radius: 4px;
}
@media (max-width: 600px) {
    .container {
        margin: 10px;
        padding: 20px;
    }
    button {
        font-size: 16px;
    }
}
/* Styles for the result page */
ul {
    list-style-type: none;
    padding: 0;
}
li {
    margin: 15px 0;
}
a {
    text-decoration: none;
    color: #007bff;
    font-weight: bold;
}
a:hover {
    text-decoration: underline;
}
.button {
    display: inline-block;
    padding: 10px 20px;
    background-color: #007bff;
    color: #fff;
    border-radius: 4px;
    text-decoration: none;
    margin-top: 20px;
}
.button:hover {
    background-color: #0056b3;
}
h1.success {
    color: #28a745;
}
h1.error {
    color: #dc3545;
}

.image-paste-area {
    border: 2px dashed #ccc;
    padding: 20px;
    min-height: 100px;
    cursor: text;
    background-color: #fafafa;
    margin-top: 10px;
}
.image-paste-area:focus {
    outline: none;
    border-color: #28a745;
}



.image-paste-area {
    border: 2px dashed #ccc;
    padding: 20px;
    min-height: 100px;
    cursor: text;
    background-color: #fafafa;
    margin-top: 10px;
}
.image-paste-area:focus {
    outline: none;
    border-color: #28a745;
}
.image-paste-area p {
    margin: 0;
    color: #777;
}

/* Adjust the margin of the image preview */
.image-preview {
    margin-top: 10px;
    text-align: center;
}

.image-paste-area {
    border: 2px dashed #ccc;
    padding: 20px;
    min-height: 100px;
    cursor: text;
    background-color: #fafafa;
    margin-top: 10px;
    outline: none; /* Remove default outline */
}
.image-paste-area:focus {
    border-color: #28a745;
}
.image-paste-area p {
    margin: 0;
    color: #777;
}
