from solders.pubkey import Pubkey
from solana.rpc.api import Client

# Solana Devnet endpoint
SOLANA_CLUSTER_URL = "https://api.devnet.solana.com"

# Initialize Solana client
client = Client(SOLANA_CLUSTER_URL)

# Recipient public key
recipient_public_key = Pubkey.from_string("2ygNY219Jt7DgAc9qHY8KyppAKpsew3faDshnF4Npump")

# 1 SOL in lamports
airdrop_amount = 1000000000

try:
    # This returns a solders.rpc.responses.RequestAirdropResp instance
    airdrop_response = client.request_airdrop(
        pubkey=recipient_public_key,
        lamports=airdrop_amount
    )

    # Check if the response has a signature in its .value property
    tx_signature = airdrop_response.value
    print("Airdrop response:", airdrop_response)
    if tx_signature:
        print(airdrop_response)
        print("Airdrop requested successfully!")
        print("Transaction signature:", tx_signature)
    else:
        print("Airdrop request failed. Response:", airdrop_response)

except Exception as e:
    print("An error occurred:", str(e))
