"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.transferSol = transferSol;
exports.getPumpWallet = getPumpWallet;
exports.getWalletByKey = getWalletByKey;
exports.updateWalletStatus = updateWalletStatus;
exports.launchToken = launchToken;
const express_1 = __importDefault(require("express"));
const utils_1 = require("./src/utils");
const web3_js_1 = __importStar(require("@solana/web3.js"));
const spl_token_1 = require("@solana/spl-token");
const spl_token_2 = require("@solana/spl-token");
const constants_1 = require("./src/constants");
const path_1 = __importDefault(require("path"));
const promise_1 = require("mysql2/promise");
const short_uuid_1 = __importDefault(require("short-uuid"));
const uuid_1 = require("uuid");
const axios_1 = __importDefault(require("axios"));
const fs = __importStar(require("fs"));
const fluent_ffmpeg_1 = __importDefault(require("fluent-ffmpeg"));
const FormData = require("form-data");
require('dotenv').config();
const pinata_web3_1 = require("pinata-web3");
const file_1 = require("@web-std/file");
const bs58_1 = __importDefault(require("bs58"));
//TODO: wallet management from sql 
//TODO: autosell from gui 
//TODO: (optional) chart in gui and price fetching
const isSimulated = false;
const tradeDiedWalletPubKey = "9qcRZ5DKoEJKD37dDo38kgvg3Qw8hvSpyh7WvJqat9Yn";
const tradeOtherWalletPubKey = "BVUS8oDZnq3RcqoZtKumX1GCGxjbrmujNAKF4qVDRapb";
const tradeNewbornWalletPubKey = "2hBhpyHtUdnZ6qyJc6Z92x5VrZrYR8y5k3mE419FS3mi";
const SYSPROGRAM = new web3_js_1.PublicKey("********************************");
const TRANSFER_FEE = 5000;
const UPLOAD_FOLDER = path_1.default.join(__dirname, 'static/videos');
const IMAGES_FOLDER = path_1.default.join(__dirname, 'static/images');
const pinataApiKey = "eae622f81b2b9d595268";
const pinataSecretApiKey = "****************************************************************";
const gateway = "https://ipfs.io/ipfs/";
const wallets = {
    "newborn": tradeNewbornWalletPubKey,
    "died": tradeDiedWalletPubKey,
    "other": tradeOtherWalletPubKey
};
const DB_CONFIG = {
    user: 'pump2',
    password: 'pump2',
    host: '**************',
    database: 'pump'
};
// Initialize Express application
const app = (0, express_1.default)(); // No explicit Application type needed
const port = 7777;
app.set("view engine", "ejs");
app.set("views", path_1.default.join(__dirname, "views"));
app.use(express_1.default.static('public'));
app.use(express_1.default.json({ limit: '50mb' }));
app.use(express_1.default.urlencoded({ limit: '50mb', extended: true }));
const PRIVATE_KEY = process.env.PRIVATE_KEY ? process.env.PRIVATE_KEY : "";
const PRIVATE_KEY_DIED = process.env.PRIVATE_KEY_DIED ? process.env.PRIVATE_KEY_DIED : "";
console.log("key:", PRIVATE_KEY);
console.log("key(died):", PRIVATE_KEY_DIED);
const pinataGW = "https://gateway.pinata.cloud/ipfs";
//const RPC="https://api.mainnet-beta.solana.com";
//clusterApiUrl("devnet")
const RPC = "https://mainnet.helius-rpc.com/?api-key=c29c0ee4-6308-4789-a694-e2dfa40ad7c7";
const jsonFilePath = path_1.default.join(__dirname, '.wallets.json');
let walletsJson = [];
// Read and parse the JSON file
fs.readFile(jsonFilePath, 'utf8', (err, data) => {
    if (err) {
        console.error('Error reading the JSON file:', err);
        return;
    }
    try {
        walletsJson = JSON.parse(data);
        console.log('JSON Data:', walletsJson);
    }
    catch (parseErr) {
        console.error('Error parsing JSON:', parseErr);
    }
});
// const wallets={ 
//     "newborn":tradeNewbornWalletPubKey,
//     "died":tradeDiedWalletPubKey,
//     "other":tradeOtherWalletPubKey
// }
// constructk wallets from json use type and pubkey
// let wallets: { [key: string]: string } = {};
// walletsJson.forEach((wallet) => {
//     wallets[wallet.type] = wallet.pubkey;
// });
// function to Get private key from public key
function getPrivateKey(pubKey) {
    const wallet = walletsJson.find((w) => w.pubkey === pubKey);
    return wallet ? wallet.privatekey : '';
}
async function getAccountBalance(publicKeyString) {
    const connection = new web3_js_1.Connection(RPC, 'processed');
    const publicKey = new web3_js_1.PublicKey(publicKeyString);
    const accountInfo = await connection.getAccountInfo(publicKey);
    if (accountInfo === null) {
        return 0; // Account not found
    }
    return accountInfo.lamports;
}
async function transferSol(fromPrivKey, toPubKey, amount, drain = false) {
    const privateKey = new Uint8Array(bs58_1.default.decode(fromPrivKey));
    const fromKey = web3_js_1.Keypair.fromSecretKey(privateKey);
    const txBuilder = new web3_js_1.Transaction();
    let final_amount = 0;
    if (drain) {
        const fromPubKey = fromKey.publicKey.toBase58();
        const balance = await getAccountBalance(fromPubKey);
        final_amount = (balance - TRANSFER_FEE);
    }
    else {
        final_amount = web3_js_1.LAMPORTS_PER_SOL * amount;
    }
    const connection = new web3_js_1.Connection(RPC, 'confirmed');
    const transaction = new web3_js_1.Transaction().add(web3_js_1.SystemProgram.transfer({
        fromPubkey: fromKey.publicKey,
        toPubkey: new web3_js_1.PublicKey(toPubKey),
        lamports: final_amount
    }));
    console.log('TRANSACTION', transaction);
    const signature = await (0, web3_js_1.sendAndConfirmTransaction)(connection, transaction, [fromKey], {
        skipPreflight: true,
        preflightCommitment: 'processed'
    });
    console.log('SIGNATURE', signature);
}
async function getPumpWallet(status = 'fresh') {
    const conn = await (0, promise_1.createConnection)(DB_CONFIG);
    let retObj = {};
    const query = 'select mintPubKey,mintPrivKey,tradePubKey,tradePrivKey from minting where status = ? order by id asc limit 1';
    const [rows, fields] = await conn.execute(query, [status]);
    conn.end();
    if (Array.isArray(rows) && rows.length > 0) {
        //console.log(rows[0]);
        return rows[0] || null;
    }
    else {
        // No rows found (or an OkPacket for some reason), return null
        return null;
    }
}
async function getWalletByKey(pubKey) {
    const conn = await (0, promise_1.createConnection)(DB_CONFIG);
    let retObj = {};
    const query = 'select mintPubKey,mintPrivKey,tradePubKey,tradePrivKey from minting where mintPubKey = ? or mintPrivKey = ? or tradePubKey = ? or tradePrivKey = ? order by id asc limit 1';
    const [rows, fields] = await conn.execute(query, [pubKey, pubKey, pubKey, pubKey]);
    conn.end();
    if (Array.isArray(rows) && rows.length > 0) {
        //console.log(rows[0]);
        return rows[0];
    }
    else {
        // No rows found (or an OkPacket for some reason), return null
        return null;
    }
}
async function updateWalletStatus(pubKey, status) {
    try {
        const conn = await (0, promise_1.createConnection)(DB_CONFIG);
        const query = 'update minting set status = ? , updated = NOW() where mintPubKey = ?';
        await conn.execute(query, [status, pubKey]);
        conn.end();
    }
    catch (error) {
        console.error('Error updating wallet status:', error);
    }
}
async function uploadFileToIPFS(filePath) {
    const url = 'http://localhost:5001/api/v0/add';
    const formData = new FormData();
    formData.append('file', fs.createReadStream(filePath));
    try {
        const response = await axios_1.default.post(url, formData, {
            headers: {
                ...formData.getHeaders(),
            },
        });
        console.log('File uploaded to IPFS:', response.data);
        return response.data;
    }
    catch (error) {
        console.error('Error uploading file to IPFS:', error);
    }
}
const pinata = new pinata_web3_1.PinataSDK({
    pinataJwt: process.env.JWT,
    pinataGateway: "amaranth-mad-elk-142.mypinata.cloud",
});
//   async function addFileWithOptions(filePath: String) {
//    // const ipfs = create({ url: 'http://localhost:5001' });
//     const fs = require('fs');
//     const fileContent = fs.readFileSync(filePath);
//     const { cid } = await ipfs.add(fileContent, {
//       pin: true, // Ensure the file is pinned
//       wrapWithDirectory: false, // Don't wrap in a directory
//     });
//     console.log(`File added with CID: ${cid}`);
//     return cid;
//   }
async function launchToken(deployerPrivatekey, name, symbol, uri, sol_amount = 1.4, simulation = false) {
    const connection = new web3_js_1.Connection(RPC, 'confirmed');
    const payer = await (0, utils_1.getKeyPairFromPrivateKey)(deployerPrivatekey);
    const owner = payer.publicKey;
    //Create new wallet to be used as mint
    const pairFromDB = await getPumpWallet('active');
    if (!pairFromDB) {
        throw new Error("No fresh wallet found in the database");
    }
    const privKey = pairFromDB['mintPrivKey'];
    const pubKey = pairFromDB['mintPubKey'];
    //const secretKeyBytes = bs58.decode("22YGR93sqrANTd5PgUx7JisrbHrCpvfc7xrKV8nYUDksrEP7Z2yTewPBMngeheAGXEQKjb9ZKhMgGXi4d6E6Gz1c"); //D8N3YSEF79QaBGjM4tpzsJDWjM8PTsk8VZpQXPbBpump
    //const mint = Keypair.fromSecretKey(secretKeyBytes);
    const secretKeyBytes = bs58_1.default.decode(privKey);
    const mint = web3_js_1.Keypair.fromSecretKey(secretKeyBytes);
    //const mint = Keypair.generate();
    //const mintPrivateKeyBase58 = "2D1xTbxqGLxoeL8tXHUJC5huJiUxG27bDCsNqgzTZE8gSxctmRfXwu1uZvdLRd3ong1t5NoNMfb49TgwuoqEomZG";
    //const mintPrivateKey = bs58.decode(mintPrivateKeyBase58);
    //const mint = Keypair.fromSecretKey(mintPrivateKey);
    const [bondingCurve, bondingCurveBump] = web3_js_1.PublicKey.findProgramAddressSync([Buffer.from("bonding-curve"), mint.publicKey.toBuffer()], constants_1.PUMP_FUN_PROGRAM);
    const [associatedBondingCurve, associatedBondingCurveBump] = web3_js_1.PublicKey.findProgramAddressSync([
        bondingCurve.toBuffer(),
        new web3_js_1.PublicKey("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA").toBuffer(),
        mint.publicKey.toBuffer()
    ], new web3_js_1.PublicKey("ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL"));
    const [metadata, metadataBump] = await web3_js_1.PublicKey.findProgramAddressSync([Buffer.from("metadata"), constants_1.MPL_TOKEN_METADATA.toBuffer(), mint.publicKey.toBuffer()], constants_1.MPL_TOKEN_METADATA);
    const txBuilder = new web3_js_1.default.Transaction();
    const txBuilder2 = new web3_js_1.default.Transaction();
    // Adding the Compute Budget instruction
    const computeBudgetInstruction = new web3_js_1.default.TransactionInstruction({
        keys: [],
        programId: constants_1.COMPUTE_BUDGET_PROGRAM_ID,
        data: Buffer.concat([
            Buffer.from(Uint8Array.of(3)), // discriminator for SetComputeUnitPrice
            (0, utils_1.bufferFromUInt64)(100000) // microLamports
        ])
    });
    txBuilder.add(computeBudgetInstruction);
    txBuilder2.add(computeBudgetInstruction);
    const keys = [
        { pubkey: mint.publicKey, isSigner: true, isWritable: true }, // Mint account
        { pubkey: constants_1.MINT_AUTHORITY, isSigner: false, isWritable: false }, // Mint authority
        { pubkey: bondingCurve, isSigner: false, isWritable: true }, // Bonding curve PDA
        { pubkey: associatedBondingCurve, isSigner: false, isWritable: true }, // Associated bonding curve PDA
        { pubkey: constants_1.GLOBAL, isSigner: false, isWritable: false }, // Global config
        { pubkey: constants_1.MPL_TOKEN_METADATA, isSigner: false, isWritable: false }, // Metadata program ID
        { pubkey: metadata, isSigner: false, isWritable: true }, // Metadata PDA
        { pubkey: owner, isSigner: true, isWritable: true }, // Owner account
        { pubkey: constants_1.SYSTEM_PROGRAM, isSigner: false, isWritable: false }, // System program
        { pubkey: spl_token_2.TOKEN_PROGRAM_ID, isSigner: false, isWritable: false }, // Token program
        { pubkey: spl_token_2.ASSOCIATED_TOKEN_PROGRAM_ID, isSigner: false, isWritable: false }, // Associated token account program
        { pubkey: constants_1.RENT, isSigner: false, isWritable: false }, // Rent sysvar
        { pubkey: constants_1.PUMP_FUN_ACCOUNT, isSigner: false, isWritable: false }, // Pump fun account
        { pubkey: constants_1.PUMP_FUN_PROGRAM, isSigner: false, isWritable: false } // Pump fun program ID
    ];
    const nameBuffer = (0, utils_1.bufferFromString)(name);
    const symbolBuffer = (0, utils_1.bufferFromString)(symbol);
    const uriBuffer = (0, utils_1.bufferFromString)(uri);
    const data = Buffer.concat([
        Buffer.from("181ec828051c0777", "hex"),
        nameBuffer,
        symbolBuffer,
        uriBuffer
    ]);
    const instruction = new web3_js_1.default.TransactionInstruction({
        keys: keys,
        programId: constants_1.PUMP_FUN_PROGRAM,
        data: data
    });
    txBuilder.add(instruction);
    // buy in crete token account
    const tokenAccountAddress = await (0, spl_token_1.getAssociatedTokenAddress)(mint.publicKey, owner, false);
    txBuilder.add((0, spl_token_1.createAssociatedTokenAccountInstruction)(payer.publicKey, tokenAccountAddress, payer.publicKey, mint.publicKey));
    const solIn = sol_amount;
    const slippageDecimal = 0.5;
    const solTokens = 34_000_000;
    const tokenOut = solIn * solTokens * 1_000_000;
    const solInLamports = solIn * web3_js_1.LAMPORTS_PER_SOL;
    const coinData = {
        "virtual_token_reserves": **********,
        "virtual_sol_reserves": **********
    };
    //const tokenOut = Math.floor(solInLamports * coinData["virtual_token_reserves"] / coinData["virtual_sol_reserves"]);
    //const tokenOut=3_000_000_000_000;
    // 34_612_903_225_806   1sol
    // 35M , MaxSol = price * slippage
    //amount = int((sol_in_lamports * virtual_sol_reserves) / (real_token_reserves + sol_in_lamports)) 
    const solInWithSlippage = solIn * (1 + slippageDecimal);
    const maxSolCost = Math.floor(solInWithSlippage * web3_js_1.LAMPORTS_PER_SOL);
    const ASSOCIATED_USER = tokenAccountAddress;
    const USER = owner;
    const BONDING_CURVE = bondingCurve;
    const ASSOCIATED_BONDING_CURVE = associatedBondingCurve;
    const FEE_RECIPIENT = new web3_js_1.PublicKey("CebN5WGQ4jvEPvsVU4EoHEpgzq1VV7AbicfhtW4xC9iM");
    const keys2 = [
        { pubkey: constants_1.GLOBAL, isSigner: false, isWritable: false },
        { pubkey: FEE_RECIPIENT, isSigner: false, isWritable: true },
        { pubkey: mint.publicKey, isSigner: false, isWritable: false },
        { pubkey: BONDING_CURVE, isSigner: false, isWritable: true },
        { pubkey: ASSOCIATED_BONDING_CURVE, isSigner: false, isWritable: true },
        { pubkey: ASSOCIATED_USER, isSigner: false, isWritable: true },
        { pubkey: USER, isSigner: false, isWritable: true },
        { pubkey: SYSPROGRAM, isSigner: false, isWritable: false },
        { pubkey: spl_token_2.TOKEN_PROGRAM_ID, isSigner: false, isWritable: false },
        { pubkey: constants_1.RENT, isSigner: false, isWritable: false },
        { pubkey: constants_1.PUMP_FUN_ACCOUNT, isSigner: false, isWritable: false },
        { pubkey: constants_1.PUMP_FUN_PROGRAM, isSigner: false, isWritable: false }
    ];
    // keys2.forEach(accountMeta => {
    //     const pubkeyString = accountMeta;
    //     console.log(pubkeyString);
    //     }); 
    const data2 = Buffer.concat([
        (0, utils_1.bufferFromUInt64)("16927863322537952870"),
        (0, utils_1.bufferFromUInt64)(tokenOut),
        (0, utils_1.bufferFromUInt64)(maxSolCost)
    ]);
    const instruction2 = new web3_js_1.TransactionInstruction({
        keys: keys2,
        programId: constants_1.PUMP_FUN_PROGRAM,
        data: data2
    });
    txBuilder.add(instruction2);
    const transaction = await (0, utils_1.createTransaction)(connection, txBuilder.instructions, payer.publicKey);
    ///
    let signature;
    if (simulation == false) {
        signature = await (0, utils_1.sendAndConfirmTransactionWrapper)(connection, transaction, [payer, mint]);
        console.log(`Tx confirmed with signature: ${signature} `);
    }
    else if (simulation == true) {
        const simulatedResult = await connection.simulateTransaction(transaction);
        console.log(simulatedResult);
        await updateWalletStatus(pubKey, 'used');
        const newFresh = await getPumpWallet('fresh');
        if (!newFresh) {
            throw new Error("No fresh wallet found in the database");
        }
        const newActivepubKey = newFresh['mintPubKey'];
        await updateWalletStatus(newActivepubKey, 'active');
        return pubKey;
    }
    ///
    //const signature = await sendAndConfirmTransactionWrapper(connection, transaction, [payer,mint]);
    //  console.log(`Tx confirmed with signature: ${signature} `);
    //  return signature || "";
    await updateWalletStatus(pubKey, 'used');
    const newFresh = await getPumpWallet('fresh');
    if (!newFresh) {
        throw new Error("No fresh wallet found in the database");
    }
    const newActivepubKey = newFresh['mintPubKey'];
    await updateWalletStatus(newActivepubKey, 'active');
    return pubKey || "";
}
// Define the Example class
class Example {
    deployerPrivatekey;
    tokenUri;
    tokenSymbol;
    tokenName;
    sol_amount;
    simulation;
    constructor(deployerPrivatekey, tokenUri, tokenSymbol, tokenName, sol_amount = 1.4, simulation = false) {
        this.deployerPrivatekey = deployerPrivatekey;
        this.tokenUri = tokenUri;
        this.tokenSymbol = tokenSymbol;
        this.tokenName = tokenName;
        this.sol_amount = sol_amount;
        this.simulation = simulation;
    }
    async main() {
        try {
            const mint = await launchToken(this.deployerPrivatekey, this.tokenName, this.tokenSymbol, this.tokenUri, this.sol_amount, this.simulation);
            return { success: true, message: "Token deployed successfully", mint: mint };
        }
        catch (error) {
            console.error('Error in main function:', error);
            return { success: false, message: error instanceof Error ? error.message : "Unknown error" };
        }
    }
}
app.get("/balance", (req, res) => {
    const account = req.query["account"];
    const balance = getAccountBalance(account).then((balance) => {
        res.status(200).json({ account: account, balance: (Number(balance) / 1e9).toFixed(3) });
    });
});
app.get("/test", (req, res) => {
    //const pairFromDB=getPumpWallet('active');
    //const owner = getKeyPairFromPrivateKey(PRIVATE_KEY);
    // let wallet=tradeWalletPubKey;
    // if (req.query["type"] == "newborn"){
    //     wallet=wallets["newborn"];}
    // else if (req.query["type"] == "died"){
    //     wallet=wallets["died"];
    // }else{  
    //     wallet=wallets["other"];
    // }
    // const balance=getAccountBalance(wallet).then((balance) => {
    // const data = {"data": (Number(balance)/1e9).toFixed(3)};
    res.render("deploy", { wallets });
});
//});
app.post('/download', async (req, res) => {
    const { url: video_url } = req.body;
    const video_id = (0, uuid_1.v4)();
    const video_path = path_1.default.join(UPLOAD_FOLDER, `${video_id}.mp4`);
    try {
        const response = await axios_1.default.get(video_url, { responseType: 'stream' });
        const writer = fs.createWriteStream(video_path);
        response.data.pipe(writer);
        await new Promise((resolve, reject) => {
            writer.on('finish', resolve);
            writer.on('error', reject);
        });
        const frame_folder = path_1.default.join(IMAGES_FOLDER, video_id);
        if (!fs.existsSync(frame_folder)) {
            fs.mkdirSync(frame_folder, { recursive: true });
        }
        (0, fluent_ffmpeg_1.default)(video_path)
            .on('end', () => {
            const frame_files = fs.readdirSync(frame_folder);
            const image_paths = frame_files.map(file => `/static/images/${video_id}/${file}`);
            res.status(200).json({ images: image_paths, video_id });
        })
            .on('error', (err) => {
            res.status(500).json({ error: `Failed to process video: ${err.message}` });
        })
            .screenshots({
            count: 10,
            folder: frame_folder,
            filename: 'frame-%i.jpg',
        });
    }
    catch (err) {
        res.status(400).json({ error: `Failed to download video: ${err.message}` });
    }
});
app.post('/process_image_url', async (req, res) => {
    const { image_url } = req.body;
    if (!image_url) {
        res.status(400).json({ error: 'Image URL is required' });
        return;
    }
    try {
        const image_id = (0, uuid_1.v4)();
        const image_path = path_1.default.join(IMAGES_FOLDER, `${image_id}.jpg`);
        const response = await (0, axios_1.default)({
            url: image_url,
            method: 'GET',
            responseType: 'stream',
        });
        const writer = fs.createWriteStream(image_path);
        response.data.pipe(writer);
        writer.on('finish', () => {
            res.json({ image_path: `/static/images/${image_id}.jpg` });
        });
        writer.on('error', (err) => {
            res.status(500).json({ error: `Error downloading image: ${err.message}` });
        });
    }
    catch (err) {
        res.status(500).json({ error: `Error downloading image: ${err.message}` });
    }
});
app.post('/shorten', async (req, res) => {
    try {
        const { long_url } = req.body;
        if (!long_url) {
            res.status(400).json({ error: 'Missing long_url' });
            return;
        }
        const short_url = short_uuid_1.default.generate().substring(0, 6);
        const conn = await (0, promise_1.createConnection)(DB_CONFIG);
        try {
            const query = 'INSERT INTO urls (long_url, short_url) VALUES (?, ?)';
            const values = [long_url, short_url];
            await conn.execute(query, values);
            res.status(201).json({ short_url: `https://kroocoin.xyz/shortened/${short_url}` });
        }
        finally {
            await conn.end();
        }
    }
    catch (err) {
        res.status(500).json({ error: err.message });
    }
});
app.get('/shortened/:short_url', async (req, res) => {
    const { short_url } = req.params;
    const conn = await (0, promise_1.createConnection)(DB_CONFIG);
    try {
        const query = 'SELECT long_url FROM urls WHERE short_url = ?';
        const [rows] = await conn.execute(query, [short_url]);
        if (!rows.length) {
            res.status(404).json({ error: 'URL not found' });
            return;
        }
        const long_url = rows[0].long_url;
        res.redirect(long_url);
    }
    catch (err) {
        res.status(500).json({ error: err.message });
    }
    finally {
        await conn.end();
    }
});
app.get('/static/images/*', (req, res) => {
    const filePath = req.params[0]; // This captures the full path after /static/images/
    const fullPath = path_1.default.join(IMAGES_FOLDER, filePath); // Combine it with the images folder
    res.sendFile(fullPath, (err) => {
        if (err) {
            res.status(404).send('File not found');
        }
    });
});
app.get('/videos/:filename', (req, res) => {
    const { filename } = req.params;
    res.sendFile(path_1.default.join(UPLOAD_FOLDER, filename));
});
/*
        token_name = request.form['token_name']
        token_symbol = request.form['token_symbol']
        description = request.form.get('token_description', '')
        website = request.form.get('website', '')
        twitter = request.form.get('twitter', '')
        telegram = request.form.get('telegram', '')
        sol_amount = float(request.form.get('sol_amount', 1.4))
        pasted_image_data = request.form.get('pasted_image_data', '')
        */
// Route to handle deployment
app.post('/create-token', async (req, res) => {
    //console.log(req.body);
    //const { deployerPrivatekey, tokenUri, tokenSymbol, tokenName } = req.body;
    const { token_name, token_symbol, token_description, website, twitter, telegram, sol_amount, pasted_image_data, trade_account } = req.body;
    // Validate request query parameters
    if (!pasted_image_data || !token_name || !token_symbol || !twitter) {
        res.status(400).json({ success: false, message: "Missing required parameters" });
        return;
    }
    //TODO: upload image to ipfs
    //TODO: upload manifest to ipfs
    //TODO: load private key from env
    // const manifest = {
    //     name: "RIP Shilling",
    //     symbol: "Shilling",
    //     description: " The Virginia Zoo is heartbroken to announce the passing of Shilling, our 17-year-old emu, during a veterinary procedure on the afternoon of Thursday, December 12th. The exam was being performed to investigate recent weight loss. Results are still pending from the necropsy, which may provide more insight into his condition.\r\n\r\nShilling arrived at the Virginia Zoo in 2010, where he lived with his companion Lester. Lester was sadly euthanized earlier this year at the age of 21 once his arthritis was no longer manageable with pain medication. Arthritis is common in zoo animals that have lived beyond their natural life expectancy. With wild emus typically living upwards of 10 years, both Shilling and Lester were well into their golden years at the time of their respective passings. It is because of the dedicated care of the Zoo’s Bird and Veterinary Teams that both were able to enjoy an enriching life well into their senior years. Some of Shilling’s favorite enrichment activities were puzzle feeders filled with grapes and bathing in water sprinklers to keep cool during the summer. According to his keeper Jaxx C. Shilling really loved playing with bubbles and pinwheels, too.\"\r\n\r\nWith this loss, the Zoo is currently not housing any emus. The habitat will be reevaluated by animal care staff before deciding next steps. We ask that you please keep the Bird and Veterinary Teams in your thoughts in their time of loss. ❤️\"",
    //     image: "https://ipfs.io/ipfs/QmTDVkBB9Y6ax2VFum3vo4QQhLSW9hX4XRLGwvQvpeAM3A",
    //     showName: true,
    //     createdOn: "https://pump.fun",
    //     twitter: "https://www.instagram.com/p/DD0Uy4ssCuF",
    //     telegram: "https://www.instagram.com/p/DD0Uy4ssCuF",
    //     website: "https://www.instagram.com/p/DD0Uy4ssCuF"
    // }
    // put in try/catch
    // upload image
    const base64Image = pasted_image_data.split(';base64,').pop();
    const binaryData = Buffer.from(base64Image, 'base64');
    const imageFilePath = `tmp/${token_symbol}_${(0, uuid_1.v4)()}.png`;
    //await fs.promises.writeFile(imageFilePath, binaryData);
    //const ipfsImage=await addFileWithOptions(imageFilePath);
    //const ipfsImage = await uploadFileToIPFS(imageFilePath);
    const ipfsImage = new file_1.File([binaryData], token_symbol + ".png", { type: "text/plain" });
    const ImagePinHash = await pinata.upload.file(ipfsImage);
    console.log(pinataGW + "/" + ImagePinHash["IpfsHash"]);
    const imageHash = ImagePinHash["IpfsHash"];
    //const IpfsHash=ipfsImage["Hash"];
    //console.log("https://ipfs.io/ipfs/"+IpfsHash);
    const manifest2 = {
        name: token_name,
        symbol: token_symbol,
        description: token_description,
        image: pinataGW + '/' + imageHash,
        showName: true,
        createdOn: "https://pump.fun",
        twitter: twitter,
        telegram: telegram,
        website: website
    };
    //const metadataFilePath = `tmp/${token_symbol}_${uuidv4()}.json`;
    //await fs.promises.writeFile(metadataFilePath, JSON.stringify(manifest2, null, 4));
    //const ipfsMetadata=await addFileWithOptions(metadataFilePath);
    //const ipfsMetadata = await uploadFileToIPFS(metadataFilePath);
    //const IpfsHash2=ipfsMetadata["Hash"];
    const metafile = new file_1.File([JSON.stringify(manifest2, null, 4)], token_symbol + ".json", { type: "text/plain" });
    const pinHash = await pinata.upload.file(metafile);
    const IpfsHash2 = pinHash["IpfsHash"];
    const tokenUri1 = pinataGW + "/" + IpfsHash2;
    console.log(pinataGW + "/" + IpfsHash2);
    //const deployerPrivatekey=PRIVATE_KEY;
    const deployerPrivatekey = getPrivateKey(trade_account);
    // const tokenUri1="https://ipfs.io/ipfs/QmdP8iRM2jQGhMruCJLiRQX1TPeoXeXoaNutSprWhCvUj5";
    // const tokenSymbol1="Shilling";
    // const tokenName1="RIP Shilling";
    // const tokenUri="test";
    try {
        // Create an instance of the Example class
        const example = new Example(deployerPrivatekey, tokenUri1, token_symbol, token_name, sol_amount, isSimulated);
        // Execute the main function
        const result = await example.main();
        // Send response back to the client
        //res.status(200).json(result);
        res.render("result", result);
    }
    catch (error) {
        console.error('Error handling /deploy request:', error);
        res.status(500).json({ success: false, message: "Internal server error" });
    }
});
// Start the server
app.listen(port, () => {
    console.log(`Server is running on http://localhost:${port}`);
});
//http://127.0.0.1:3000/deploy?deployerPrivatekey=EnzmHsu21aALk6eC89SZCqDyxDR3X4FUSp5sjrARQQrXYZcLNzeGCG4zVZ4DV2CDjBNdsXUATbNS9USd5Y3cQQe&tokenUri=https://ipfs.io/ipfs/QmdP8iRM2jQGhMruCJLiRQX1TPeoXeXoaNutSprWhCvUj5&tokenSymbol=Shilling&tokenName=RIP%20Shilling
