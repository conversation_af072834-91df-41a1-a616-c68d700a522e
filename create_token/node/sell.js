"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const swap_1 = require("./src/swap");
const types_1 = require("./src/types");
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const price_1 = require("./src/price");
const api_1 = require("./src/api");
const spl_token_1 = require("@solana/spl-token");
const web3_js_1 = require("@solana/web3.js");
const bs58 = require('bs58');
const amqplib_1 = __importDefault(require("amqplib"));
const queue = "autosell";
const username = 'pump2';
const password = 'pump2';
const host = 'kroocoin.xyz';
const port = 5672; // Default RabbitMQ port
let messageJson = [];
const api_key = [
    "8bff9957-1635-4a96-97bd-ee66697a4918",
    "4184847c-934e-437e-9c04-387c225c8033",
    "e639aac2-5de7-4096-8f2f-33bf54ae5665",
    "35c105e7-2b05-489a-a506-61c852272321",
    "3675ac39-6b5d-4986-8d47-a6f14b2eded5"
];
function getRandomKey() {
    const randomIndex = Math.floor(Math.random() * api_key.length);
    return api_key[randomIndex];
}
const jsonFilePath = path_1.default.join(__dirname, '.wallets.json');
let walletsJson = [];
// Read and parse the JSON file
fs_1.default.readFile(jsonFilePath, 'utf8', (err, data) => {
    if (err) {
        console.error('Error reading the JSON file:', err);
        return;
    }
    try {
        walletsJson = JSON.parse(data);
        console.log('JSON Data:', walletsJson);
    }
    catch (parseErr) {
        console.error('Error parsing JSON:', parseErr);
    }
});
function getPrivateKey(pubKey) {
    const wallet = walletsJson.find((w) => w.pubkey === pubKey);
    return wallet ? wallet.privatekey : '';
}
//const RPC="https://api.mainnet-beta.solana.com";
//const RPC="https://mainnet.helius-rpc.com/?api-key=c29c0ee4-6308-4789-a694-e2dfa40ad7c7";
//const RPC="https://go.getblock.io/c8d421e01d66409f88bdcde68057de0d";
//const RPC="https://capable-wispy-emerald.solana-mainnet.quiknode.pro/e14cad62ecf815fc184007d5d058dd7365cb9254";
const RPC = "https://mainnet.helius-rpc.com/?api-key=" + getRandomKey();
class Sell {
    payerPrivateKey;
    payerKeyPair;
    mintAddress;
    transactionMode;
    connection;
    mintPubkey;
    ownerPubkey;
    associatedBondingCurve;
    balance;
    sellAtSol;
    maxMinutes;
    slippageDecimal;
    priorityFeeInSol;
    constructor(privateKey, mintAddress, sellAtSol, maxMinutes, mode) {
        this.payerPrivateKey = privateKey;
        this.payerKeyPair = web3_js_1.Keypair.fromSecretKey(bs58.decode(this.payerPrivateKey));
        this.mintAddress = mintAddress;
        this.mintPubkey = new web3_js_1.PublicKey(this.mintAddress);
        this.ownerPubkey = this.payerKeyPair.publicKey;
        this.transactionMode = mode;
        this.connection = new web3_js_1.Connection(RPC, 'processed');
        this.associatedBondingCurve = this.mintPubkey;
        this.balance = 0;
        this.sellAtSol = sellAtSol;
        this.maxMinutes = maxMinutes;
        this.slippageDecimal = 0.1; // Example value, adjust as needed
        this.priorityFeeInSol = 0.0001; // Example value for tip to get faster inclusion, adjust as needed
    }
    async checkTokenMinted(publicKey) {
        try {
            // Connect to the Solana mainnet
            const connection = this.connection;
            // Convert the provided string to a PublicKey
            const tokenPublicKey = new web3_js_1.PublicKey(publicKey);
            // Fetch account information for the public key
            const accountInfo = await connection.getParsedAccountInfo(tokenPublicKey);
            if (accountInfo.value) {
                // Check if the account is a token mint account
                const data = accountInfo.value.data;
                if (data && 'parsed' in data) {
                    const parsedData = data.parsed;
                    if (parsedData.type === 'mint' && parsedData.info.isInitialized) {
                        console.log('Token is minted. Details:', parsedData.info);
                        return true;
                    }
                }
                console.log('Public key exists but is not a token mint.');
                return false;
            }
            else {
                console.log('No account found for the given public key.');
                return false;
            }
        }
        catch (error) {
            console.error('Error checking token:', error);
            return false;
        }
    }
    async getBalance() {
        const balanceInfo = await this.connection.getTokenAccountBalance(this.associatedBondingCurve);
        const tokenBalance = Number(balanceInfo.value.amount);
        return tokenBalance;
    }
    async sellNow() {
        const tx = await (0, swap_1.pumpFunSell)(this.transactionMode, this.payerPrivateKey, this.mintAddress, Math.floor(this.balance), this.priorityFeeInSol, this.slippageDecimal, RPC);
        while (true) {
            const transactionDetails = await new web3_js_1.Connection(RPC).getTransaction(tx, {
                commitment: "confirmed",
            });
            if (transactionDetails && transactionDetails.meta && transactionDetails.meta.err === null) {
                console.log('https://solscan.io/tx/' + tx + ' Succeeded');
                break; // Exit the loop or function
            }
            else if (transactionDetails && transactionDetails.meta && transactionDetails.meta.err !== null) {
                console.log('Error in transaction:', transactionDetails.meta.err);
                break; // Handle the error and exit
            }
            else {
                console.log('https://solscan.io/tx/' + tx + ' retrying...');
            }
            await new Promise(resolve => setTimeout(resolve, 300));
        }
    }
    async main() {
        const solIn = 0.0001; // Example value, adjust as needed
        let isMinted = false;
        const maxMintCheck = 30; // Seconds check if created
        let nowTime = new Date();
        let stopMintCheckTime = new Date();
        stopMintCheckTime.setSeconds(nowTime.getSeconds() + maxMintCheck);
        console.log("nowTime: ", nowTime, stopMintCheckTime);
        while (isMinted === false && nowTime < stopMintCheckTime) {
            const status = await this.checkTokenMinted(this.mintAddress);
            if (status) {
                isMinted = true;
                break;
            }
            await new Promise(resolve => setTimeout(resolve, 200));
            nowTime = new Date();
        }
        if (isMinted === false) {
            console.log("Token not minted");
            return;
        }
        // Get the token balance 
        try {
            const coinData = await (0, api_1.getCoinData)(this.mintAddress);
            const bondingCurve = coinData["bonding_curve"];
            this.associatedBondingCurve = await (0, spl_token_1.getAssociatedTokenAddress)(this.mintPubkey, this.ownerPubkey, true);
            console.log("Bonding Curve: ", bondingCurve);
            this.balance = await this.getBalance();
            console.log("Token Balance: ", await this.getBalance());
            let sold = false;
            let isSold = false;
            let stopTradeTime = new Date();
            stopTradeTime.setMinutes(nowTime.getMinutes() + this.maxMinutes);
            console.log("nowTime: ", nowTime, stopMintCheckTime);
            let valueSol = 0;
            let price = 0;
            while (isSold === false && nowTime < stopTradeTime) {
                price = await (0, price_1.getPrice)(bondingCurve, this.connection);
                valueSol = this.balance * price / 1e6;
                console.log("Token Value Balance: ", valueSol.toFixed(5));
                if (valueSol >= this.sellAtSol) {
                    this.sellNow();
                    console.log("Token sold at treshold. Value:", valueSol.toFixed(5));
                    // sellig token
                    isSold = true;
                    break;
                }
                await new Promise(resolve => setTimeout(resolve, 200));
                nowTime = new Date();
            }
            if (isSold === false) {
                // Force sell now: 
                this.sellNow();
                console.log("Token not sold at treshold. Time expired. sold as is at value:", valueSol.toFixed(5));
                return;
            }
            else {
                console.log("Success: Token sold at treshold. Value:", valueSol.toFixed(5));
            }
        }
        catch (error) {
            console.error('Error in main function:', error);
        }
    }
}
//const privateKey = '5cni8hCKdJUDxBUNHPpQ87ACrEZomRQaJDVxxG1UFDz2cs2LyKJNSFv98XXqyq5W1vKsp3PGfm8pfEhUQXpFvRAi'; // Replace with your actual private key
//const mintAddress = 'FKmXD25msBuYwB2weBxksCLMnAC4JX5tm2MBBnRjpump'; //Replace with actual token mint address
const txMode = types_1.TransactionMode.Execution; //Set to simulate to test, Execution to perform
async function consumeMessages() {
    const connectionUrl = `amqp://${username}:${password}@${host}:${port}`;
    try {
        // Connect to RabbitMQ with authentication
        const connection = await amqplib_1.default.connect(connectionUrl);
        console.log('Connected to RabbitMQ');
        // Create a channel
        const channel = await connection.createChannel();
        // Ensure the queue exists
        await channel.assertQueue(queue, {
            durable: true, // Set to true if messages should survive RabbitMQ restarts
        });
        console.log(`Waiting for messages in queue: ${queue}`);
        // Consume messages from the queue
        channel.consume(queue, async (message) => {
            if (message) {
                // Process the message in the background
                processMessage(message.content.toString())
                    .then(() => {
                    // Acknowledge the message after processing
                    channel.ack(message);
                    console.log('Message processed and acknowledged.');
                })
                    .catch((error) => {
                    console.error('Error processing message:', error);
                    // Optional: Reject the message (could requeue or dead-letter)
                    channel.nack(message, false, false); // Sends to dead-letter queue if configured
                });
            }
        }, {
            noAck: false, // Ensure manual acknowledgment
        });
    }
    catch (error) {
        console.error('Error:', error);
    }
}
// Background message processor
async function processMessage(messageContent) {
    console.log(`Processing message: ${messageContent}`);
    const walletsJson = JSON.parse(messageContent);
    console.log(walletsJson.mint, walletsJson.wallet, walletsJson.sellAtSol, walletsJson.maxMinutes);
    //current timestamp
    const now = new Date();
    //add minutes to current timestamp
    const stopTime = now.getMinutes() + walletsJson.maxMinutes;
    const privateKey = getPrivateKey(walletsJson.wallet);
    const trade = new Sell(privateKey, walletsJson.mint, walletsJson.sellAtSol, walletsJson.maxMinutes, txMode);
    trade.main();
}
consumeMessages();
