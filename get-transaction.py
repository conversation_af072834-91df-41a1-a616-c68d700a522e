#!/usr/bin/env python3
import json
import threading
import time
import requests
import sys
from modules.config import *
import modules.config
from modules.config import last_price
from modules.config import block_counter
from modules.config import redis_connection
import modules.decoders.pump as pump
import modules.redismq as redismq



JUPITER_FEE = [
    "45ruCyfdRkWpRNGEqWzjCiXRHkZs8WXCLQ67Pnpye7Hp"
]

SYSTEM_FEE_ACCOUNT="HWEoBxYs7ssKuudEjzjmpfJVX7Dvi7wescFsVx2L5yoY"
PUMP_FEE_ACCOUNT="CebN5WGQ4jvEPvsVU4EoHEpgzq1VV7AbicfhtW4xC9iM"
JITO_FEE = [
    "96gYZGLnJYVFmbjzopPSU6QiEV5fGqZNyN9nmNhvrZU5",
    "HFqU5x63VTqvQss8hp11i4wVV8bD44PvwucfZ2bU7gRe",
    "Cw8CFyM9FkoMi7K7Crf6HNQqf4uEMzpKw6QNghXLvLkY",
    "ADaUMid9yfUytqMBgopwjb2DTLSokTSzL1zt6iGPaS49",
    "DfXygSm4jCyNCybVYYK6DwvWqjKee8pbDmJGcLWNDXjh",
    "ADuUkR4vqLUMWXxW9gh6D6L8pMSawimctcNZ5pGwDcEt",
    "DttWaMuVvTiduZRnguLF7jNxTgiMBZ1hyAumKUiL2KRL",
    "3AVi9Tg9Uo68tJfuvoKvqKNWKkC5wPdSSdeBnizKZ6jT"
]
RAYDIUM = "5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1"
METEOR = "2MFoS3MPtvyQ4Wh4M9pdfPjz6UhVoNbFbGJAskCPCj3h"

AUTHORITIES=[RAYDIUM,METEOR]
FEE_ACCOUNTS=[SYSTEM_FEE_ACCOUNT,PUMP_FEE_ACCOUNT]+JITO_FEE+JUPITER_FEE



def decode_pump_program_instruction(tx):
    txs=[]
    for i in tx:
        for ii in i["instructions"]:
            if ii["programId"] =="6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P":
                encoded_data=ii["data"]
                pump_decode=pump.decode(encoded_data)
                if isinstance(pump_decode, list) and pump_decode[0] == 'TradeEvent':
                    txs.append(pump_decode[1])
    return txs

def set_last_price(token,price):
    global last_price
    last_price[token]=price   

def get_last_price(token):
    global last_price
    if token in last_price:
        return last_price[token]
    else:
        return 0

# Function to send a request to the WebSocket server



def process_solana_transaction(block):
    
    global last_price
    global block_counter
    global redismq
    global redis_connection
    pump_address=args[2]
    transfers=[]
    token_owner={}
    instructions = block['transaction']['message']['instructions']
    account_keys = block['transaction']['message']['accountKeys']
    signature=block["transaction"]["signatures"][0]
    meta=block["meta"]

    slot = block["slot"]
    timestamp=block['blockTime']
    owner=account_keys[0]['pubkey']
  

    dev=["AEDr4TBd6VSjMy1yb4YHst65wLBakr8dDhaAAktRaFkv",
        "AZt9KWsVpmcoH3Ka5nBMwyNhetJLW9Lj2C7V1dEnWAko",
        "BkBSrzFzwfkh1jk98zJTdw7Hrx2bNYeKnfumrD4NmPd8",
        "BqVKaTqaf8ePx1oDn2vJKhTAv6dyn2sfqX7N43L7Z35y",
        "EFoJLgXRTu9W7mgaAabkWhGDxgVdZWAk27ZWSefx7Gx2"]

    inner_instructions=block["meta"]["innerInstructions"]

    for log in meta["logMessages"]:
        if "RemoveLiquidity" in log:
            return

    pumptx=decode_pump_program_instruction(inner_instructions)
    print("pump len:",len(pumptx))
    if len(pumptx)>0:
        print("PUMP")
        for p in pumptx:
                mint=p["mint"]
                tokenAmount=p["token_amount"]
                solAmount=p["sol_amount"]
                is_buy=p["is_buy"]
                if is_buy:
                    trade="buy"
                else:
                    trade="sell"

                if solAmount == 0 or tokenAmount == 0:
                    return    
                price=(solAmount/tokenAmount/1000)

                mlp=get_last_price(pump_address) #last price
                if mlp == 0:
                    mlp=price
                if mlp > price:
                    high=mlp
                    low=price
                else:
                    high=price
                    low=mlp

                if low > high:
                    print(f"Error: low {low}  high {high}")
                color="orange"
                open_price=mlp
                close_price=price

                if slot in block_counter:
                    block_counter[slot]=block_counter[slot]+1
                else:
                    block_counter[slot]=1
                milliseconds=block_counter[slot]

                #millionseconds=int(timestamp)*1000   

                data={"broker":"PUMP", "color":color,"open":open_price,"close":close_price,   "high":high,"low":low,"address":owner,"trade":trade, "tokenAmount":tokenAmount,"solAmount":solAmount,"price":price,"timestamp":int(timestamp),"milliseconds":milliseconds,"signature":signature,"slot":slot}
                
                print(mlp,price)   
                set_last_price(pump_address,price)
                redis_connection=modules.config.redis_connection
                redismq.push_trade(redis_connection,data,pump_address,"rpush")

    else:
        print("RAYDIUM")
        def _check_chain_(transfers,pump_address): 
            final = transfers
            print(f"Final len:{len(final)}")

            if final[0]["mint"] != pump_address and final[1]["mint"] != pump_address:
                print("Error: no pump address.tx not related")
                return
            
            if len(final) == 2:


                # if final[0]["mint"] == "unknown" or final[1]["mint"] == "unknown":

                #     if final[0]["mint"]=="unknown":

                #         if final[1]["mint"]=="So11111111111111111111111111111111111111112":
                #             final[0]["mint"]=pump_address
                #             final[0]["amount"]=final[1]["amount"]/1e6 
                #         else:
                #             final[0]["mint"]="So11111111111111111111111111111111111111112"
                #             final[0]["amount"]=final[1]["amount"]/1e9 
                #     else:
                #         if final[0]["mint"]=="So11111111111111111111111111111111111111112":
                #             final[1]["mint"]=pump_address
                #             final[1]["amount"]=final[1]["amount"]/1e6 
                #         else:
                #             final[1]["mint"]="So11111111111111111111111111111111111111112"
                #             final[1]["amount"]=final[1]["amount"]/1e9                      


                print(json.dumps(final, indent=4))
                if final[0]["mint"]==pump_address:
                    trade="sell"
                    price=final[-1]["amount"]/final[0]["amount"]
                    volume=final[0]["amount"]
                    tokenAmount=final[0]["amount"]
                    solAmount=final[-1]["amount"]
                else:
                    trade="buy"
                    price=final[0]["amount"]/final[-1]["amount"]
                    volume=final[-1]["amount"]
                    tokenAmount=final[-1]["amount"]
                    solAmount=final[0]["amount"]

                
                mlp=get_last_price(pump_address) #last price
                if mlp == 0:
                    mlp=price
                if mlp > price:
                    high=mlp
                    low=price
                else:
                    high=price
                    low=mlp

                if low > high:
                    print(f"Error: low {low}  high {high}")
                color="orange"
                open_price=mlp
                close_price=price

                if slot in block_counter:
                    block_counter[slot]=block_counter[slot]+1
                else:
                    block_counter[slot]=1
                milliseconds=block_counter[slot]

                #millionseconds=int(timestamp)*1000   

                data={"broker":"RAYDIUM", "color":color,"open":open_price,"close":close_price,   "high":high,"low":low,"address":owner,"trade":trade, "tokenAmount":tokenAmount,"solAmount":solAmount,"price":price,"timestamp":int(timestamp),"milliseconds":milliseconds,"signature":signature,"slot":slot}
                print(json.dumps(data, indent=4))
                print(mlp,price)   
                set_last_price(pump_address,price)
                redis_connection=modules.config.redis_connection
                redismq.push_trade(redis_connection,data,pump_address,"rpush")
        # END of check_chain



        transfers=[]
        token_owner={}
        #instructions = row['transaction']['message']['instructions']
        #account_keys = row['transaction']['message']['accountKeys']

        #meta = row['meta']
        #post_token_balances = meta['postTokenBalances']
        #pre_token_balances = meta['preTokenBalances']


            

        def get_account_address_by_index(index):
            return account_keys[index]["pubkey"]
        
        def get_owner_by_token_account(tokenAccount):
            found=False
            for balance in meta["preTokenBalances"]:
                if get_account_address_by_index(balance["accountIndex"]) == tokenAccount:
                    return balance["owner"],balance["mint"]
            if tokenAccount in token_owner:
                return token_owner[tokenAccount]["owner"],token_owner[tokenAccount]["mint"]

            if found == False:
                return tokenAccount,"unknown"
            
        def process_tx(instruction):
            if instruction["program"] == "system" and instruction["parsed"]["type"] == "transfer":
                source = instruction["parsed"]["info"]["source"]
                destination = instruction["parsed"]["info"]["destination"]
                amount = instruction["parsed"]["info"]["lamports"]
                #print(f"Instruction: (transfer) Source: {source} Destination: {destination} Amount: {amount}  lamports")


            if instruction["program"] == "spl-associated-token-account" and  instruction["parsed"]["type"] == "createIdempotent":
                #print(f"Instruction: (createImpodent) Create associated token account owner:{instruction["parsed"]["info"]["source"]}  new splaccount:{instruction["parsed"]["info"]["account"]}  mint:{instruction["parsed"]["info"]["mint"]}")
                    token_owner[instruction["parsed"]["info"]["account"]]={"owner":instruction["parsed"]["info"]["source"],"mint":instruction["parsed"]["info"]["mint"]}
            
            if instruction["program"] == "spl-token" and instruction["parsed"]["type"] == "closeAccount":
                pass
                #print(f"Instruction: (closeAccount) owner:{instruction["parsed"]["info"]["owner"]}  closed account :{instruction["parsed"]["info"]["account"]}  ")
                    
            if instruction["program"] == "system" and instruction["parsed"]["type"] == "createAccount":
                #print(f"Instruction: (createAccount) owner:{instruction["parsed"]["info"]["source"]}  new account :{instruction["parsed"]["info"]["newAccount"]}  sum:{instruction["parsed"]["info"]["lamports"]}")
                pass
                #token_owner[instruction["parsed"]["info"]["newAccount"]]={"owner":instruction["parsed"]["info"]["source"],"mint":"So11111111111111111111111111111111111111112"}     
            if instruction["program"] == "spl-token" and instruction["parsed"]["type"] == "initializeAccount3":
                token_owner[instruction["parsed"]["info"]["account"]]={"owner":instruction["parsed"]["info"]["owner"],"mint":instruction["parsed"]["info"]["mint"]} 
                print(f"Instruction: (initializeAccount3)  Set token for account :{instruction["parsed"]["info"]["account"]}  mint:{instruction["parsed"]["info"]["mint"]}  owner:{instruction["parsed"]["info"]["owner"]}")
                
            if instruction["program"] == "spl-token" and instruction["parsed"]["type"] == "transfer":
                if "multisigAuthority" in instruction["parsed"]["info"]:
                    source_owner,_=get_owner_by_token_account(instruction["parsed"]["info"]["multisigAuthority"])
                else:
                    source_owner,_=get_owner_by_token_account(instruction["parsed"]["info"]["authority"])
                
                destination_owner,destination_mint=get_owner_by_token_account(instruction["parsed"]["info"]["destination"]) 

                #print(f"Instruction: (spl-transfer)  Source:{instruction["parsed"]["info"]["source"]}  Destination:{instruction["parsed"]["info"]["destination"]}  Amount:{instruction["parsed"]["info"]["amount"]} mint:{destination_mint}" )
                ###print(f"Instruction: (spl-transfer)  Source:{source_owner}  Destination:{destination_owner}  Amount:{instruction["parsed"]["info"]["amount"]} mint:{destination_mint}" )

                if destination_mint == pump_address:
                    amount=int(instruction["parsed"]["info"]["amount"])/1000000
                elif destination_mint == "So11111111111111111111111111111111111111112":
                    amount=int(instruction["parsed"]["info"]["amount"])/**********
                else:

                    amount=int(instruction["parsed"]["info"]["amount"])/1000000

                # transfers.append({
                #         'source': source_owner,
                #         'destination': destination_owner,
                #         'amount': amount,
                #         'mint': destination_mint
                #     })
                return {
                        'source': source_owner,
                        'destination': destination_owner,
                        'amount': amount,
                        'mint': destination_mint}

            if instruction["program"] == "spl-token" and instruction["parsed"]["type"] == "transferChecked":

                if "multisigAuthority" in instruction["parsed"]["info"]:
                    source_owner,_=get_owner_by_token_account(instruction["parsed"]["info"]["multisigAuthority"])
                else:
                    source_owner,_=get_owner_by_token_account(instruction["parsed"]["info"]["authority"])
                destination_owner,destination_mint=get_owner_by_token_account(instruction["parsed"]["info"]["destination"]) 
                ###print(f"Instruction: (spl-transferChecked)  Source:{source_owner}  Destination:{destination_owner}  Amount:{instruction["parsed"]["info"]["tokenAmount"]["uiAmount"]} mint:{instruction["parsed"]["info"]["mint"]}" )

                # transfers.append({
                #     'source': source_owner,
                #     'destination': destination_owner,
                #     'amount': instruction["parsed"]["info"]["tokenAmount"]["uiAmount"],
                #     'mint': instruction["parsed"]["info"]["mint"]
                # })
                return {
                        'source': source_owner,
                        'destination': destination_owner,
                        'amount': instruction["parsed"]["info"]["tokenAmount"]["uiAmount"],
                        'mint': instruction["parsed"]["info"]["mint"]}
#resolve new accounts

        for index,i in enumerate(instructions):
            if "program" in i and i["program"] == "spl-token" and ( i["parsed"]["type"] == "initializeAccount3" or i["parsed"]["type"] == "initializeAccount" ):
                    token_owner[i["parsed"]["info"]["account"]]={"owner":i["parsed"]["info"]["owner"],"mint":i["parsed"]["info"]["mint"]} 
                    print(f"Instruction: (initializeAccount3)  Set token for account :{i["parsed"]["info"]["account"]}  mint:{i["parsed"]["info"]["mint"]}  owner:{i["parsed"]["info"]["owner"]}")

        for inner_instruction in meta["innerInstructions"]:
            for ii in inner_instruction["instructions"]:
                if "program" in ii and ii["program"] == "spl-token" and ( ii["parsed"]["type"] == "initializeAccount3" or ii["parsed"]["type"] == "initializeAccount" ):
                    token_owner[ii["parsed"]["info"]["account"]]={"owner":ii["parsed"]["info"]["owner"],"mint":ii["parsed"]["info"]["mint"]} 
                    print(f"Instruction: (initializeAccount3)  Set token for account :{ii["parsed"]["info"]["account"]}  mint:{ii["parsed"]["info"]["mint"]}  owner:{ii["parsed"]["info"]["owner"]}")

# end resolve


        for index,instruction in enumerate(instructions):
   
            if instruction["programId"] == "675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8"  or instruction["programId"] == "LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo" or instruction["programId"] == "CAMMCzo5YL8w4VFF8KVHrK22GGUsp5VTaW7grrKgrWqK" :
                transfers=[]
                
                for inner_instruction in meta["innerInstructions"]:
                    if inner_instruction["index"] == index:
                        for ii in inner_instruction["instructions"]:
                            if "program" in ii and ii["program"] == "spl-token" and (ii["parsed"]["type"] == "transfer" or ii["parsed"]["type"] == "transferChecked"):
                                transfers.append(process_tx(ii))
                            if len(transfers) == 2:
                                _check_chain_(transfers,pump_address)
                                transfers=[]
                        transfers=[]                
            else:
                transfers=[]
                for inner_instruction in meta["innerInstructions"]:
                    if inner_instruction["index"] == index:    
            
                        transfers=[]
                        counter=0
                        for ii in inner_instruction["instructions"]:

                            if ii["programId"] == "675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8" or ii["programId"] == "LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo" or instruction["programId"] == "CAMMCzo5YL8w4VFF8KVHrK22GGUsp5VTaW7grrKgrWqK":
                                counter=1
                                if len(transfers) > 0:
                                    #print(f"Send previouse ({len(transfers)})transfers:{transfers}")
                                    _check_chain_(transfers,pump_address)
                                    transfers=[]
                            else:
                                if counter > 0 and  "program" in ii and ii["program"] == "spl-token" and (ii["parsed"]["type"] == "transfer" or ii["parsed"]["type"] == "transferChecked"):
                                    if counter < 3:
                                        counter=counter+1
                                        transfers.append(process_tx(ii))

                        if len(transfers) > 0:
                            _check_chain_(transfers,pump_address)
                        transfers=[]


def send_request(ws):
    request = {
        "jsonrpc": "2.0",
        "id": "1",
        "method": "blockSubscribe",
        "params": [
            {
            "mentionsAccountOrProgram": pump_address
            },
            {
            "commitment": "confirmed",
            "encoding": "jsonParsed",
            "showRewards": True,
            "transactionDetails": "full",
            "maxSupportedTransactionVersion": 0
            }
        ]
        }
    ws.send(json.dumps(request))

# Function to send a ping to the WebSocket server
def start_ping(ws):
    def ping():
        while True:
            if ws.sock and ws.sock.connected:
                ws.send('{"jsonrpc": "2.0", "method": "ping"}')
                #print('Ping sent')
            time.sleep(30)  # Ping every 30 seconds
    threading.Thread(target=ping).start()

# Define WebSocket event handlers
def on_open(ws):
    print('WebSocket is open')
    send_request(ws)  # Send a request once the WebSocket is open
    start_ping(ws)    # Start sending pings

def on_message(message):
    row = message
    if row["meta"]["err"]==None:
        print(f" tx written to file: {row["transaction"]["signatures"][0]}")
        file=open(f"tmp/tx/{row["transaction"]["signatures"][0]}.json","w")
        data=json.dumps(row, indent=4)
        file.write(data)
        file.close()
        print(f"========================= {row["transaction"]["signatures"][0]}  ================= { row['transaction']['message']['accountKeys'][0]['pubkey']} =====================")
        process_solana_transaction(row)
        return





def get_transaction(tx_id):
    rpc_url ='https://little-sleek-mansion.solana-mainnet.quiknode.pro/4b69dc2b49ac07ae787d9c65b8a33ccee7a904d2'

    request = {
        "jsonrpc": "2.0",
        "id": "1",
        "method": "getTransaction",
        "params": [tx_id, {"encoding": "jsonParsed","maxSupportedTransactionVersion": 0}]
        }
    request = requests.post(rpc_url, json=request)
    data = request.json()["result"]
    on_message(data)


#main rcp call  __main__

if __name__ == "__main__":
    args = sys.argv
    if len(args) < 2:
        print("Please provide a transaction ID")
        sys.exit
    get_transaction(args[1])
    modules.config.pump_address=args[2]



# source authority !!!