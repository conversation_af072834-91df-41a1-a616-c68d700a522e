import asyncio
from dataclasses import dataclass
from datetime import datetime
import json
from typing import Dict, List, Optional
import numpy as np
import logging
import grpc
from geyser_pb2 import SubscribeRequest, SubscribeRequestFilterAccounts, GetSlotRequest, CommitmentLevel
from geyser_pb2_grpc import GeyserStub

@dataclass
class Trade:
    timestamp: float
    price: float
    volume: float
    is_buy: bool
    market_cap: float

@dataclass
class TradingMetrics:
    price_velocity: float
    volume_surge: float
    market_cap_growth: float
    order_imbalance: float

class TokenTrader:
    def __init__(self, token_address: str, window_size: int = 30):
        self.token_address = token_address
        self.window_size = window_size  # Window size in seconds
        self.trades: List[Trade] = []
        self.position = None
        self.current_price = 0
        self.simulation_balance = 1000  # Starting with 1000 SOL
        self.in_position = False
        
        # Configure logging
        logging.basicConfig(
            filename=f'trades_{token_address}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log',
            level=logging.INFO,
            format='%(asctime)s - %(message)s'
        )
        
        # Trading thresholds
        self.PRICE_VELOCITY_THRESHOLD = 0.30  # 30% price increase
        self.VOLUME_SURGE_THRESHOLD = 5.0     # 5x volume increase
        self.MIN_MARKET_CAP = 20_000          # $20K
        self.MAX_MARKET_CAP = 1_000_000       # $1M
        self.ORDER_IMBALANCE_THRESHOLD = 0.5   # Positive order imbalance

    def calculate_metrics(self, window_trades: List[Trade]) -> TradingMetrics:
        if not window_trades or len(window_trades) < 2:
            return TradingMetrics(0, 0, 0, 0)

        # Price velocity
        time_diff = window_trades[-1].timestamp - window_trades[0].timestamp
        price_diff = window_trades[-1].price - window_trades[0].price
        price_velocity = (price_diff / window_trades[0].price) / time_diff if time_diff > 0 else 0

        # Volume surge
        recent_volume = sum(t.volume for t in window_trades[-5:])  # Last 5 trades
        avg_volume = np.mean([t.volume for t in window_trades[:-5]])  # Earlier trades
        volume_surge = recent_volume / avg_volume if avg_volume > 0 else 0

        # Market cap growth
        mc_growth = (window_trades[-1].market_cap - window_trades[0].market_cap) / window_trades[0].market_cap

        # Order imbalance
        buys = sum(1 for t in window_trades if t.is_buy)
        sells = len(window_trades) - buys
        order_imbalance = (buys - sells) / len(window_trades)

        return TradingMetrics(price_velocity, volume_surge, mc_growth, order_imbalance)

    def should_buy(self, metrics: TradingMetrics, current_trade: Trade) -> bool:
        if self.in_position:
            return False

        return (
            metrics.price_velocity > self.PRICE_VELOCITY_THRESHOLD and
            metrics.volume_surge > self.VOLUME_SURGE_THRESHOLD and
            self.MIN_MARKET_CAP <= current_trade.market_cap <= self.MAX_MARKET_CAP and
            metrics.order_imbalance > self.ORDER_IMBALANCE_THRESHOLD
        )

    def should_sell(self, metrics: TradingMetrics, current_trade: Trade) -> bool:
        if not self.in_position:
            return False

        return (
            metrics.price_velocity < 0.10 or  # Price velocity slowing
            metrics.volume_surge < 2.0 or     # Volume dropping
            current_trade.market_cap > 500_000 or  # Approaching peak
            metrics.order_imbalance < -0.3    # Negative order imbalance
        )

    def simulate_trade(self, trade: Trade, metrics: TradingMetrics):
        if self.should_buy(metrics, trade):
            self.in_position = True
            self.position = {
                'entry_price': trade.price,
                'entry_time': trade.timestamp,
                'entry_mcap': trade.market_cap
            }
            logging.info(f"BUY SIGNAL - Price: {trade.price:.6f}, MC: ${trade.market_cap:.2f}, "
                        f"PV: {metrics.price_velocity:.2f}, VS: {metrics.volume_surge:.2f}")

        elif self.should_sell(metrics, trade):
            if self.position:
                profit_pct = (trade.price - self.position['entry_price']) / self.position['entry_price'] * 100
                self.simulation_balance *= (1 + profit_pct/100)
                
                logging.info(f"SELL SIGNAL - Entry: {self.position['entry_price']:.6f}, Exit: {trade.price:.6f}, "
                           f"Profit: {profit_pct:.2f}%, Balance: {self.simulation_balance:.2f} SOL")
                
                self.position = None
                self.in_position = False

    async def process_trade(self, trade_data: dict):
        # Convert trade data to Trade object
        trade = Trade(
            timestamp=datetime.now().timestamp(),
            price=trade_data['price'],
            volume=trade_data['volume'],
            is_buy=trade_data['is_buy'],
            market_cap=trade_data['market_cap']
        )
        
        self.trades.append(trade)
        
        # Maintain sliding window
        current_time = trade.timestamp
        self.trades = [t for t in self.trades 
                      if current_time - t.timestamp <= self.window_size]
        
        # Calculate metrics and simulate trading
        metrics = self.calculate_metrics(self.trades)
        self.simulate_trade(trade, metrics)
        
        # Log trade data
        logging.info(f"TRADE - Price: {trade.price:.6f}, Volume: {trade.volume:.2f}, "
                    f"MC: ${trade.market_cap:.2f}, Is Buy: {trade.is_buy}")

    async def start_trading(self):
        try:
            # Initialize gRPC connection
            ENDPOINT = "grpc.solanavibestation.com:10000"
            channel = grpc.aio.insecure_channel(ENDPOINT)
            stub = GeyserStub(channel)
            
            # Create subscription request
            request = SubscribeRequest(
                accounts=SubscribeRequestFilterAccounts(
                    account_include=[self.token_address],
                    account_exclude=[],
                    account_required=[]
                ),
                commitment=CommitmentLevel.PROCESSED
            )
            
            # Subscribe and process stream
            async for response in stub.Subscribe(request):
                if response.HasField("account"):
                    await self.process_trade(response.account)
                
        except Exception as e:
            logging.error(f"Error in trading loop: {str(e)}")
            raise
        finally:
            if channel:
                await channel.close()

if __name__ == "__main__":
    # Example usage
    TOKEN_ADDRESS = "YOUR_TOKEN_ADDRESS"
    trader = TokenTrader(TOKEN_ADDRESS)
    
    asyncio.run(trader.start_trading())
