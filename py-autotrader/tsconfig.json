{
  "compilerOptions": {
    "target": "ES2020", // Target modern JavaScript
    "module": "CommonJS", // Use Node.js-compatible ES modules
    "moduleResolution": "node", // Use Node.js module resolution
    "outDir": "./dist", // Output directory for compiled files
    "rootDir": "./", // Root directory for source files
    "strict": true, // Enable all strict type-checking options
    "esModuleInterop": true, // Enable ES module interop for CommonJS modules
    "skipLibCheck": true, // Skip type checking of declaration files
    "forceConsistentCasingInFileNames": true, // Ensure consistent casing in file names
    "resolveJsonModule": true // Allow importing JSON files
  },
  "include": ["**/*.ts", "test/test.js"], // Include all TypeScript files
  "exclude": ["node_modules"] // Exclude node_modules directory
}