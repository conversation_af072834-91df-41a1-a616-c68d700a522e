import Client, {
  CommitmentLevel,
  SubscribeRequest,
  SubscribeUpdate
} from "@triton-one/yellowstone-grpc";
import { ClientDuplexStream } from "@grpc/grpc-js";
import { PublicKey } from '@solana/web3.js';
import { BorshCoder } from '@coral-xyz/anchor';
import * as fs from 'fs';
import { format } from 'date-fns';
import bs58 from 'bs58'

interface Trade {
  timestamp: number;
  price: number;
  volume: number;
  isBuy: boolean;
  marketCap: number;
}

// Removed from here and moved inside the TokenTrader class

interface TradingMetrics {
  priceVelocity: number;
  volumeSurge: number;
  marketCapGrowth: number;
  orderImbalance: number;
}

interface Position {
  entryPrice: number;
  entryTime: number;
  entryMcap: number;
}

interface BuyInstruction {
  lpTokenAmountIn: bigint;
  minBaseAmountOut: bigint;
  minQuoteAmountOut: bigint;
}

interface SellInstruction {
  baseAmountIn: bigint;
  quoteAmountIn: bigint;
  minLpTokenAmountOut: bigint;
}

function decodeBuyInstruction(data: Uint8Array | string): BuyInstruction | null {
  try {
    // Convert string to Uint8Array if needed
    const dataBytes = typeof data === 'string' ? bs58.decode(data) : data;
    
    const idl = require('./pumpswap_idl.json');
    const coder = new BorshCoder(idl);
    
    // Skip first 8 bytes (discriminator)
    const instructionData = dataBytes.slice(8);
    
    // Decode the remaining data
    const decoded = coder.instruction.decode(Buffer.from(instructionData)) as { name: string; data: { lpTokenAmountIn: number; minBaseAmountOut: number; minQuoteAmountOut: number } };
    console.log(decoded);
    if (decoded && decoded.name === 'buy') {
      return {
        lpTokenAmountIn: BigInt(decoded.data.lpTokenAmountIn),
        minBaseAmountOut: BigInt(decoded.data.minBaseAmountOut.toString()),
        minQuoteAmountOut: BigInt(decoded.data.minQuoteAmountOut.toString())
      };
    }
    return null;
  } catch (error) {
    console.error('Error decoding buy instruction:', error);
    return null;
  }
}

function decodeSellInstruction(data: Uint8Array | string): SellInstruction | null {
  try {
    // Convert string to Uint8Array if needed
    const dataBytes = typeof data === 'string' ? bs58.decode(data) : data;
    
    const idl = require('./pumpswap_idl.json');
    const coder = new BorshCoder(idl);
    
    // Skip first 8 bytes (discriminator)
    const instructionData = dataBytes.slice(8);
    
    // Decode the remaining data
    const decoded = coder.instruction.decode(Buffer.from(instructionData)) as { name: string; data: { baseAmountIn: number; quoteAmountIn: number; minLpTokenAmountOut: number } };
    console.log(decoded);
    if (decoded && decoded.name === 'sell') {
      return {
        baseAmountIn: BigInt(decoded.data.baseAmountIn.toString()),
        quoteAmountIn: BigInt(decoded.data.quoteAmountIn.toString()),
        minLpTokenAmountOut: BigInt(decoded.data.minLpTokenAmountOut.toString())
      };
    }
    return null;
  } catch (error) {
    console.error('Error decoding sell instruction:', error);
    return null;
  }
}

// Helper function for sending subscribe request
async function sendSubscribeRequest(
  stream: ClientDuplexStream<SubscribeRequest, SubscribeUpdate>,
  request: SubscribeRequest
): Promise<void> {
  return new Promise((resolve, reject) => {
    stream.write(request, (error: Error | null | undefined) => {
      if (error) {
      reject(error);
      } else {
      resolve();
      }
    });
  });
}

// Removed duplicate class definition
class TokenTrader {
  private readonly tokenAddress: string;
  private readonly windowSize: number;
  private trades: Trade[];
  private position: Position | null;
  private currentPrice: number;
  private simulationBalance: number;
  private inPosition: boolean;
  private readonly logger: fs.WriteStream;

  private calculateMarketCap(amount: number): number {
    // Placeholder logic for calculating market cap
    // Replace this with the actual formula for your use case
    return amount * this.currentPrice;
  }
  private readonly MIN_MARKET_CAP = 20_000;          // $20K
  private readonly MAX_MARKET_CAP = 1_000_000;       // $1M
  private readonly ORDER_IMBALANCE_THRESHOLD = 0.5;   // Positive order imbalance
  private readonly PRICE_VELOCITY_THRESHOLD = 0.05;   // Example threshold for price velocity
  private readonly VOLUME_SURGE_THRESHOLD = 2.0;      // Example threshold for volume surge

  constructor(tokenAddress: string, windowSize: number = 30) {
    this.tokenAddress = tokenAddress;
    this.windowSize = windowSize;
    this.trades = [];
    this.position = null;
    this.currentPrice = 0;
    this.simulationBalance = 1000;  // Starting with 1000 SOL
    this.inPosition = false;

    // Configure logging
    const logFileName = `trades_${tokenAddress}_${format(new Date(), 'yyyyMMdd_HHmmss')}.log`;
    this.logger = fs.createWriteStream(logFileName, { flags: 'a' });
  }

  private log(message: string): void {
    const timestamp = format(new Date(), 'yyyy-MM-dd HH:mm:ss');
    this.logger.write(`${timestamp} - ${message}\n`);
  }

  private calculateMetrics(windowTrades: Trade[]): TradingMetrics {
    if (!windowTrades || windowTrades.length < 2) {
      return {
        priceVelocity: 0,
        volumeSurge: 0,
        marketCapGrowth: 0,
        orderImbalance: 0
      };
    }

    // Price velocity
    const timeDiff = windowTrades[windowTrades.length - 1].timestamp - windowTrades[0].timestamp;
    const priceDiff = windowTrades[windowTrades.length - 1].price - windowTrades[0].price;
    const priceVelocity = timeDiff > 0 ? (priceDiff / windowTrades[0].price) / timeDiff : 0;

    // Volume surge
    const recentVolume = windowTrades.slice(-5).reduce((sum, t) => sum + t.volume, 0);
    const earlierTrades = windowTrades.slice(0, -5);
    const avgVolume = earlierTrades.length > 0 
      ? earlierTrades.reduce((sum, t) => sum + t.volume, 0) / earlierTrades.length 
      : 0;
    const volumeSurge = avgVolume > 0 ? recentVolume / avgVolume : 0;

    // Market cap growth
    const mcGrowth = (windowTrades[windowTrades.length - 1].marketCap - windowTrades[0].marketCap) / windowTrades[0].marketCap;

    // Order imbalance
    const buys = windowTrades.filter(t => t.isBuy).length;
    const orderImbalance = (buys - (windowTrades.length - buys)) / windowTrades.length;

    return { priceVelocity, volumeSurge, marketCapGrowth: mcGrowth, orderImbalance };
  }

  private shouldBuy(metrics: TradingMetrics, currentTrade: Trade): boolean {
    if (this.inPosition) return false;

    return (
      metrics.priceVelocity > this.PRICE_VELOCITY_THRESHOLD &&
      metrics.volumeSurge > this.VOLUME_SURGE_THRESHOLD &&
      currentTrade.marketCap >= this.MIN_MARKET_CAP &&
      currentTrade.marketCap <= this.MAX_MARKET_CAP &&
      metrics.orderImbalance > this.ORDER_IMBALANCE_THRESHOLD
    );
  }

  private shouldSell(metrics: TradingMetrics, currentTrade: Trade): boolean {
    if (!this.inPosition) return false;

    return (
      metrics.priceVelocity < 0.10 ||  // Price velocity slowing
      metrics.volumeSurge < 2.0 ||     // Volume dropping
      currentTrade.marketCap > 500_000 ||  // Approaching peak
      metrics.orderImbalance < -0.3    // Negative order imbalance
    );
  }

  private simulateTrade(trade: Trade, metrics: TradingMetrics): void {
    if (this.shouldBuy(metrics, trade)) {
      this.inPosition = true;
      this.position = {
        entryPrice: trade.price,
        entryTime: trade.timestamp,
        entryMcap: trade.marketCap
      };
      this.log(`BUY SIGNAL - Price: ${trade.price.toFixed(6)}, MC: $${trade.marketCap.toFixed(2)}, ` +
               `PV: ${metrics.priceVelocity.toFixed(2)}, VS: ${metrics.volumeSurge.toFixed(2)}`);
    }
    else if (this.shouldSell(metrics, trade)) {
      if (this.position) {
        const profitPct = (trade.price - this.position.entryPrice) / this.position.entryPrice * 100;
        this.simulationBalance *= (1 + profitPct/100);
        
        this.log(`SELL SIGNAL - Entry: ${this.position.entryPrice.toFixed(6)}, Exit: ${trade.price.toFixed(6)}, ` +
                 `Profit: ${profitPct.toFixed(2)}%, Balance: ${this.simulationBalance.toFixed(2)} SOL`);
        
        this.position = null;
        this.inPosition = false;
      }
    }
  }

  private async processBuyInstruction(instruction: any): Promise<boolean> {
    try {
      if (!instruction.data) return false;
      
      const decodedBuy = decodeBuyInstruction(instruction.data);
      console.log(decodedBuy);
      if (!decodedBuy) return false;

      this.log(`Buy Order Detected:
        LP Token Amount In: ${decodedBuy.lpTokenAmountIn.toString()}
        Min Base Amount Out: ${decodedBuy.minBaseAmountOut.toString()}
        Min Quote Amount Out: ${decodedBuy.minQuoteAmountOut.toString()}`);
      
      const trade: Trade = {
        timestamp: Date.now() / 1000,
        price: Number(decodedBuy.minQuoteAmountOut) / Number(decodedBuy.minBaseAmountOut),
        volume: Number(decodedBuy.lpTokenAmountIn),
        isBuy: true,
        marketCap: this.calculateMarketCap(Number(decodedBuy.minBaseAmountOut))
      };
      
      this.trades.push(trade);
      return true;
    } catch (error) {
      console.error('Error in processBuyInstruction:', error);
      return false;
    }
  }

  private async processSellInstruction(instruction: any): Promise<boolean> {
    try {
      if (!instruction.data) return false;
      
      const decodedSell = decodeSellInstruction(instruction.data);
      if (!decodedSell) return false;

      this.log(`Sell Order Detected:
        Base Amount In: ${decodedSell.baseAmountIn.toString()}
        Quote Amount In: ${decodedSell.quoteAmountIn.toString()}
        Min LP Token Amount Out: ${decodedSell.minLpTokenAmountOut.toString()}`);
      
      const trade: Trade = {
        timestamp: Date.now() / 1000,
        price: Number(decodedSell.quoteAmountIn) / Number(decodedSell.baseAmountIn),
        volume: Number(decodedSell.baseAmountIn),
        isBuy: false,
        marketCap: this.calculateMarketCap(Number(decodedSell.baseAmountIn))
      };
      
      this.trades.push(trade);
      return true;
    } catch (error) {
      console.error('Error in processSellInstruction:', error);
      return false;
    }
  }

  private async processTrade(update: SubscribeUpdate): Promise<void> {
    try {
      if (!update || !update.transaction || !update.transaction.transaction) {
        return;
      }

      const transaction = update.transaction.transaction;
      
      // Ensure we have a valid message before processing
      if (!transaction.meta || !Array.isArray(transaction.meta.innerInstructions)) {
        return;
      }

      for (const instruction of transaction.meta?.innerInstructions || []) {
        // Ensure instruction data is valid before processing
        if (!instruction || !instruction.instructions || instruction.instructions.length === 0) {
          continue;
        }

        // Convert instruction data to Uint8Array if it isn't already
        const instructionData = instruction.instructions[0].data instanceof Uint8Array 
          ? instruction.instructions[0].data 
          : Buffer.from(instruction.instructions[0].data, 'base64');

        try {
          // Try to decode as buy instruction
          const buyResult = await this.processBuyInstruction({
            ...instruction,
            data: instructionData
          });
          if (buyResult) continue;

          // Try to decode as sell instruction
          const sellResult = await this.processSellInstruction({
            ...instruction,
            data: instructionData
          });
          if (sellResult) continue;

        } catch (decodeError) {
          console.error('Error processing instruction:', decodeError);
          continue;
        }
      }
    } catch (error) {
      console.error('Error in processTrade:', error);
    }
  }

  public async startTrading(): Promise<void> {
    try {
      const ENDPOINT = "http://grpc.solanavibestation.com:10000";
      const client = new Client(ENDPOINT, undefined, {});
      
      // Create and get stream
      const stream = await client.subscribe();
      
      // Create subscription request
      const request: SubscribeRequest = {
        accounts: {},
        slots: {},
        transactions: {
          accountSubscribe: {
            accountInclude: [this.tokenAddress],
            accountExclude: [],
            accountRequired: [],
          }
        },
        transactionsStatus: {},
        entry: {},
        blocks: {},
        blocksMeta: {},
        commitment: CommitmentLevel.PROCESSED,
        accountsDataSlice: [],
        ping: undefined
      };

      // Send subscribe request
      await sendSubscribeRequest(stream, request);
      console.log("Geyser connection established - watching token trades");

      // Setup event handlers
      stream.on('data', async (data: SubscribeUpdate) => {
        await this.processTrade(data);
      });

      stream.on('error', (error: Error) => {
        console.error('Stream error:', error);
        // Attempt to reconnect after delay
        setTimeout(() => this.startTrading(), 5000);
      });

      // Keep connection alive
      await new Promise(() => {});

    } catch (error) {
      this.log(`Error in trading loop: ${error}`);
      throw error;
    }
  }
}

// Example usage
const TOKEN_ADDRESS = "Yup9W6Z35RM9gfoRiEk4XKDV2GSZPfegHfvKtQKpump";
const trader = new TokenTrader(TOKEN_ADDRESS);
trader.startTrading().catch(console.error);
