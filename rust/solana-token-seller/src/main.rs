use anyhow::{Context, Result};
use clap::Parser;
use serde_json::{json, Value};
use solana_client::rpc_client::RpcClient;
use solana_program::pubkey::Pubkey;
use solana_sdk::{
    commitment_config::CommitmentConfig,
    instruction::{AccountMeta, Instruction},
    signature::{Keypair, Signer, Signature, SeedDerivable},
    system_instruction,
    transaction::Transaction,
};

use std::{str::FromStr, time::{Duration, Instant}};
use reqwest::Client as HttpClient;
use std::env;
use bs58;

// Constants

const JITO_TIP_ACCOUNT: &str = "DttWaMuVvTiduZRnguLF7jNxTgiMBZ1hyAumKUiL2KRL";
const JITO_ENDPOINT: &str = "https://mainnet.block-engine.jito.wtf/api/v1/transactions";
const TOKEN_PROGRAM_ID: &str = "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA";
const ASSOCIATED_TOKEN_PROGRAM_ID: &str = "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL";
const PUMP_FUN_PROGRAM: &str = "PumpFUnZqMYxucmzm8xAkYNgBBhkLGvcVrshCRwJCqL";
const PUMP_FUN_ACCOUNT: &str = "GlobalvgWek9Lx6H2nHHAGyVR3LByE2nu5jVYnNPEhF3";
const FEE_RECIPIENT: &str = "6VGNJRMWACv6c7Q2Co46sZ6vMDhzgyHXXyZKQ8YhUYed";
const GLOBAL: &str = "GlobalvgWek9Lx6H2nHHAGyVR3LByE2nu5jVYnNPEhF3";
const SYSTEM_PROGRAM_ID: &str = "11111111111111111111111111111111";


// CLI Args
#[derive(Parser, Debug)]
#[command(name = "solana-token-seller")]
#[command(about = "CLI tool to sell Solana tokens")]
struct Cli {
    #[arg(short, long, help = "Token mint address")]
    mint: String,

    #[arg(short, long, help = "Amount of tokens to sell")]
    amount: Option<f64>,

    #[arg(short, long, help = "Check token balance")]
    balance: bool,
}

#[tokio::main]
async fn main() -> Result<()> {
    // Load environment variables
    dotenv::dotenv().ok();
    
    // Parse command line arguments
    let cli = Cli::parse();

    // Setup RPC connection
    let rpc_url = "https://solana-mainnet.api.syndica.io/api-key/21KG2L6E3kGURokSr4VwfrtYMbqnqVNYKJKCBTQv2jsiLs97iW8TQv98fcz5kvVDhS3MjVmGt91jZ3UGQpGD7zaPCuxpwgCJbek";
    let client = RpcClient::new_with_commitment(rpc_url.to_string(), CommitmentConfig::processed());

    let mint_pubkey = Pubkey::from_str(&cli.mint)
        .context("Invalid mint address")?;

    if cli.balance {
        // Check token balance
        let balance = get_token_balance(&client, &mint_pubkey)?;
        println!("Balance for mint {}: {}", cli.mint, balance);
    } else if let Some(amount) = cli.amount {
        println!("Selling now...");
        let start_time = Instant::now();

        match sell_tokens_jito(&client, &mint_pubkey, amount).await {
            Ok(tx_id) => {
                let submit_time = Instant::now();
                let submit_duration = submit_time.duration_since(start_time).as_millis();
                println!("Sold: {} - {}", cli.mint, tx_id);
                println!("Time to submit transaction: {}ms", submit_duration);

                // Check transaction status
                let success = check_transaction_status(&client, &tx_id, start_time).await?;
                std::process::exit(if success { 0 } else { 1 });
            },
            Err(err) => {
                let error_time = Instant::now();
                let error_duration = error_time.duration_since(start_time).as_millis();
                eprintln!("Failed to sell tokens for mint: {} - {}", cli.mint, err);
                eprintln!("Time until error: {}ms", error_duration);
                std::process::exit(1);
            }
        }
    } else {
        eprintln!("Either --amount or --balance option is required");
        std::process::exit(1);
    }

    Ok(())
}

async fn check_transaction_status(client: &RpcClient, tx_id: &str, start_time: Instant) -> Result<bool> {
    let max_attempts = 30; // Maximum number of attempts (30 seconds)
    
    // Convert string tx_id to Signature
    let signature = Signature::from_str(tx_id)
        .context("Invalid transaction signature")?;
    
    for attempt in 1..=max_attempts {
        match client.get_signature_status_with_commitment(
            &signature, 
            CommitmentConfig::processed()
        ) {
            Ok(Some(status)) => {
                if status.is_err() {
                    eprintln!("Transaction failed: {:?}", status);
                    return Ok(false);
                } else {
                    let end_time = Instant::now();
                    let processing_time = end_time.duration_since(start_time).as_millis();
                    println!("Transaction succeeded! Processing time: {}ms", processing_time);
                    return Ok(true);
                }
            },
            Ok(None) => {
                println!("Transaction still processing (attempt {}/{})", attempt, max_attempts);
                
                if attempt >= max_attempts {
                    println!("Max attempts reached. Transaction status uncertain.");
                    return Ok(false);
                }
                
                // Wait 1 second before the next attempt
                tokio::time::sleep(Duration::from_secs(1)).await;
            },
            Err(err) => {
                eprintln!("Error checking transaction: {}", err);
                return Ok(false);
            }
        }
    }
    
    Ok(false)
}

fn get_token_balance(client: &RpcClient, mint: &Pubkey) -> Result<f64> {
    // Get private key from environment
    let private_key = env::var("PRIVATE_KEY")
        .context("Missing PRIVATE_KEY environment variable")?;
    
    let keypair = match get_keypair_from_base58(&private_key) {
        Ok(kp) => kp,
        Err(err) => {
            eprintln!("Error decoding private key: {}", err);
            return Err(anyhow::anyhow!("Failed to decode private key"));
        }
    };
    
    // Find the associated token account for the mint
    let token_account = get_associated_token_address(&keypair.pubkey(), mint);
    
    // Get token account balance
    let balance_result = client.get_token_account_balance(&token_account);
    
    match balance_result {
        Ok(balance) => {
            let amount = balance.ui_amount.unwrap_or(0.0);
            Ok(amount)
        },
        Err(err) => {
            eprintln!("Error getting token balance: {}", err);
            Ok(0.0)
        }
    }
}

async fn sell_tokens_jito(client: &RpcClient, mint: &Pubkey, amount: f64) -> Result<String> {
    // Get private key from environment
    let private_key = env::var("PRIVATE_KEY")
        .context("Missing PRIVATE_KEY environment variable")?;
    
    // Log partial key for debugging (only first 5 characters for security)
    let visible_part = if private_key.len() > 5 {
        &private_key[0..5]
    } else {
        &private_key
    };
    println!("Using private key starting with: {}...", visible_part);
    
    let keypair = get_keypair_from_base58(&private_key)?;
    
    // Log public key for verification
    println!("Public key: {}", keypair.pubkey());
    
    // Jito tip amount
    let tip_lamports = 1_000;
    let jito_tip_account = Pubkey::from_str(JITO_TIP_ACCOUNT)?;
    
    // Build transaction
    let mut instructions = vec![
        system_instruction::transfer(
            &keypair.pubkey(),
            &jito_tip_account,
            tip_lamports,
        )
    ];
    
    // Add sell instruction
    let token_amount = (amount * 1.0) as u64;
    println!("Selling {} tokens", token_amount);
    let sell_instruction = get_sell_instruction_no_rpc(&keypair, mint, token_amount)?;
    instructions.push(sell_instruction);
    
    // Create and sign transaction
    let recent_blockhash = client.get_latest_blockhash()?;
    let transaction = Transaction::new_signed_with_payer(
        &instructions,
        Some(&keypair.pubkey()),
        &[&keypair],
        recent_blockhash
    );
    
    // Convert transaction to wire format - use the correct serialization method
    let serialized_transaction = transaction.message.serialize();
    let encoded_transaction = bs58::encode(serialized_transaction).into_string();
    
    println!("Encoded transaction length: {}", encoded_transaction.len());
    
    // Send transaction to Jito
    let http_client = HttpClient::new();
    let response = http_client.post(JITO_ENDPOINT)
        .json(&json!({
            "jsonrpc": "2.0",
            "id": 1,
            "method": "sendTransaction",
            "params": [encoded_transaction]
        }))
        .send()
        .await?;
    
    let response_json: Value = response.json().await?;
    println!("Response: {}", response_json);
    
    if let Some(result) = response_json.get("result") {
        if let Some(tx_id) = result.as_str() {
            Ok(tx_id.to_string())
        } else {
            Err(anyhow::anyhow!("Invalid response format from Jito: {:?}", result))
        }
    } else if let Some(error) = response_json.get("error") {
        Err(anyhow::anyhow!("Error from Jito: {:?}", error))
    } else {
        Err(anyhow::anyhow!("Unknown response from Jito: {:?}", response_json))
    }
}

fn get_keypair_from_base58(base58_key: &str) -> Result<Keypair> {
    // Check if the key is already without leading/trailing whitespace
    let trimmed_key = base58_key.trim();
    
    // Try to decode the key directly
    match bs58::decode(trimmed_key).into_vec() {
        Ok(decoded) => {
            // Log length for debugging
            println!("Decoded private key length: {} bytes", decoded.len());
            
            // Check if we got enough bytes for a keypair
            if decoded.len() == 64 {
                // We have a full keypair
                Ok(Keypair::from_bytes(&decoded)?)
            } else if decoded.len() == 32 {
                // This is a secret key only (32 bytes)
                println!("Detected 32-byte secret key, converting to keypair");
                let seed_array: [u8; 32] = decoded.try_into()
                    .map_err(|_| anyhow::anyhow!("Failed to convert seed to 32-byte array"))?;
                
                // Handle the error conversion explicitly
                Keypair::from_seed(&seed_array)
                    .map_err(|e| anyhow::anyhow!("Failed to create keypair from seed: {}", e))
            } else {
                Err(anyhow::anyhow!("Invalid key length: {}, expected 64 bytes for keypair or 32 bytes for secret key", decoded.len()))
            }
        },
        Err(e) => {
            // Try to parse as a JSON array of numbers
            if trimmed_key.starts_with('[') && trimmed_key.ends_with(']') {
                let json_result: Result<Vec<u8>, serde_json::Error> = serde_json::from_str(trimmed_key);
                if let Ok(bytes) = json_result {
                    if bytes.len() == 64 {
                        return Ok(Keypair::from_bytes(&bytes)?);
                    } else if bytes.len() == 32 {
                        let seed_array: [u8; 32] = bytes.try_into()
                            .map_err(|_| anyhow::anyhow!("Failed to convert JSON seed to 32-byte array"))?;
                        
                        // Handle the error conversion explicitly
                        return Keypair::from_seed(&seed_array)
                            .map_err(|e| anyhow::anyhow!("Failed to create keypair from seed: {}", e));
                    }
                }
            }
            
            // If we get here, the key format is not recognized
            Err(anyhow::anyhow!("Failed to decode private key: {}. Make sure it's in base58 format.", e))
        }
    }
}

fn get_associated_token_address(owner: &Pubkey, mint: &Pubkey) -> Pubkey {
    let token_program_id = Pubkey::from_str(TOKEN_PROGRAM_ID).unwrap();
    let associated_token_program_id = Pubkey::from_str(ASSOCIATED_TOKEN_PROGRAM_ID).unwrap();
    
    let seeds = &[
        owner.as_ref(),
        token_program_id.as_ref(),
        mint.as_ref(),
    ];
    
    let (address, _) = Pubkey::find_program_address(
        seeds,
        &associated_token_program_id,
    );
    
    address
}

fn get_bonding_curve_addresses(mint: &Pubkey) -> Result<(Pubkey, Pubkey)> {
    let pump_fun_program = Pubkey::from_str(PUMP_FUN_PROGRAM)?;
    let associated_token_program_id = Pubkey::from_str(ASSOCIATED_TOKEN_PROGRAM_ID)?;
    let token_program_id = Pubkey::from_str(TOKEN_PROGRAM_ID)?;
    
    // Derive bonding curve address
    let seeds = &[
        b"bonding-curve".as_ref(),
        mint.as_ref(),
    ];
    let (bonding_curve, _) = Pubkey::find_program_address(seeds, &pump_fun_program);
    
    // Derive associated bonding curve address
    let seeds = &[
        bonding_curve.as_ref(),
        token_program_id.as_ref(),
        mint.as_ref(),
    ];
    let (associated_bonding_curve, _) = Pubkey::find_program_address(seeds, &associated_token_program_id);
    
    Ok((bonding_curve, associated_bonding_curve))
}

fn get_sell_instruction_no_rpc(keypair: &Keypair, mint: &Pubkey, token_balance: u64) -> Result<Instruction> {
    let owner = keypair.pubkey();
    
    // Get token account address
    let token_account = get_associated_token_address(&owner, mint);
    
    // Get bonding curve addresses
    let (bonding_curve, associated_bonding_curve) = get_bonding_curve_addresses(mint)?;
    
    // Minimum expected output (set to 0 for demonstration)
    let min_sol_output: u64 = 0;
    
    // Create accounts for the instruction
    let accounts = vec![
        AccountMeta::new_readonly(Pubkey::from_str(GLOBAL)?, false),
        AccountMeta::new(Pubkey::from_str(FEE_RECIPIENT)?, false),
        AccountMeta::new_readonly(*mint, false),
        AccountMeta::new(bonding_curve, false),
        AccountMeta::new(associated_bonding_curve, false),
        AccountMeta::new(token_account, false),
        AccountMeta::new(owner, false),
        AccountMeta::new_readonly(Pubkey::from_str(SYSTEM_PROGRAM_ID)?, false),
        AccountMeta::new_readonly(Pubkey::from_str(ASSOCIATED_TOKEN_PROGRAM_ID)?, false),
        AccountMeta::new_readonly(Pubkey::from_str(TOKEN_PROGRAM_ID)?, false),
        AccountMeta::new_readonly(Pubkey::from_str(PUMP_FUN_ACCOUNT)?, false),
        AccountMeta::new_readonly(Pubkey::from_str(PUMP_FUN_PROGRAM)?, false),
    ];
    
    // Create data for the instruction
    let mut data = vec![];
    
    // Operation code for sell: "12502976635542562355"
    let op_code = 12502976635542562355u64;
    data.extend_from_slice(&op_code.to_le_bytes());
    
    // Token balance to sell
    data.extend_from_slice(&token_balance.to_le_bytes());
    
    // Minimum SOL output
    data.extend_from_slice(&min_sol_output.to_le_bytes());
    
    // Create the instruction
    let sell_instruction = Instruction {
        program_id: Pubkey::from_str(PUMP_FUN_PROGRAM)?,
        accounts,
        data,
    };
    
    // Note: Close token account instruction would be added separately to the transaction
    Ok(sell_instruction)
}

// Helper function to create a close token account instruction





