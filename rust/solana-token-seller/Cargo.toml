[package]
name = "solana-token-seller"
version = "0.1.0"
edition = "2021"

[dependencies]
anyhow = "1.0"
clap = { version = "4.3", features = ["derive"] }
tokio = { version = "1.28", features = ["full"] }
solana-client = "1.16"
solana-program = "1.16"
solana-sdk = "1.16"
spl-token = "4.0"
spl-associated-token-account = "2.0"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
bs58 = "0.5"
reqwest = { version = "0.11", features = ["json"] }
dotenv = "0.15"
bincode = "1.3.3"
base64 = "0.13.1"
