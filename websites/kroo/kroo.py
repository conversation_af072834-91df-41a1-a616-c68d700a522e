from flask import Flask, render_template

app = Flask(__name__)




@app.route('/')
def index():
    lyrics = [
        {"time": 0, "text": "Kangaroo hop from the outback, droppin' stacks"},
        {"time": 10, "text": "Pocket full of green, wealth growin' like a cheetah fax"},
        {"time": 20, "text": "Diamonds in his pouch, watch glitz gleam"},
        {"time": 30, "text": "Hoppin' up the Forbes list, livin' that dream"},
        {"time": 40, "text": "Kroo Kroo Kangaroo, makin' money moves"},
        {"time": 50, "text": "Hop to the bank, cashin' checks so smooth"},
        {"time": 60, "text": "Kroo Kroo Kangaroo, wealth on the rise"},
        {"time": 70, "text": "From the bush to the bling, eyes on the prize"},
        {"time": 80, "text": "Started from the dirt, now ridin' high"},
        {"time": 90, "text": "Paws on the wheel, hittin’ limits in the sky"},
        {"time": 100, "text": "Rollin’ in the limo, swag so elite"},
        {"time": 110, "text": "Got a gold chain hangin', kangaroo can't be beat"},
        {"time": 120, "text": "<PERSON>roo <PERSON>roo <PERSON>aroo, makin' money moves"},
        {"time": 130, "text": "Hop to the bank, cashin' checks so smooth"},
        {"time": 140, "text": "Kroo Kroo Kangaroo, wealth on the rise"},
        {"time": 150, "text": "From the bush to the bling, eyes on the prize"},
        {"time": 160, "text": "Skippin' through investments, like a pro on the scene"},
        {"time": 170, "text": "Crypto, stocks, bonds, turnin’ green to green"},
        {"time": 180, "text": "CEO status, suited for the biz"},
        {"time": 190, "text": 'Wall Street questions, "Who hopped in like this?"'},
        {"time": 200, "text": "Kangaroo mansion, opulence display"},
        {"time": 210, "text": "Pool shaped like Australia, loungin’ in the day"},
        {"time": 220, "text": "Art on the walls, rare as ancient scroll"},
        {"time": 230, "text": "Bank vault kangaroo, with stacks to console"},
    ]
    return render_template('index.html', lyrics=lyrics)

if __name__ == '__main__':
    app.run(debug=True,host="0.0.0.0",port=6700)
