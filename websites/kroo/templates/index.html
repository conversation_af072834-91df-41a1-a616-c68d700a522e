<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>$KROO - Kangaroo Cash</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">

 
    <link href="https://fonts.googleapis.com/css2?family=Rowdies:wght@400;700&family=Oswald:wght@500&family=Pacifico&display=swap" rel="stylesheet">

    <link href="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-p/VXcWZHfJw7i1D/H+N6m80ZxGL4Pw0jpi9UQKZrzVM0Cdx4o4Mqgx0gqVn3f1b5YJdXBn4VY88yc2f0sP3PDA==" crossorigin="anonymous" referrerpolicy="no-referrer" />

    <!-- Add before </body> -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.js"></script>
    <script>
        AOS.init({
            duration: 1000, // Animation duration
            once: true, // Whether animation should happen only once
        });
    </script>

</head>
<body>
    <!-- Top Toolbar -->
    <div class="background-wrapper"></div>

    <!-- Top Toolbar -->
    <div class="toolbar">
        <a href="#home" onclick="showContent('home')">Home</a>
        <a href="#about" onclick="showContent('about')">About Us</a>
        <a href="#how-to-buy" onclick="showContent('how-to-buy')">How to Buy</a>
        <a href="#tokenomics" onclick="showContent('tokenomics')">Tokenomics</a>
        <a href="#tokenomics" onclick="showContent('facts')">Facts</a>
    </div>

    <!-- Main Content -->
    <div class="content">
        <audio id="background-music" src="/static/kroo.mp3" loop="loop" autoplay muted></audio>
        <button id="music-control" onclick="toggleMusic()"></button>

        <!-- Left Side Text Section -->
        <div class="left-content" id="main-content">
            <!-- Dynamic Content Injected Here -->
        </div>

        <!-- Right Side Kangaroo Image with Lyrics Overlay -->
        <div class="right-content">
            <div class="image-lyrics-container">
                <img src="/static/kangaroo3.png" alt="KROO Kangaroo" id="kangaroo-image" class="hover-zoom" data-aos="fade-left">
                <div class="lyrics">
                    <!-- Verse -->
                    <p data-time="6">Kangaroo hop from the outback, droppin' stacks</p>
                    <p data-time="10">Pocket full of green, wealth growin' like a cheetah fax</p>
                    <p data-time="13">Diamonds in his pouch, watch glitz gleam</p>
                    <p data-time="15">Hoppin' up the Forbes list, livin' that dream</p>
                    
                    <!-- Chorus -->
                    <p data-time="18">Kroo Kroo Kangaroo, makin' money moves</p>
                    <p data-time="21">Hop to the bank, cashin' checks so smooth</p>
                    <p data-time="24">Kroo Kroo Kangaroo, wealth on the rise</p>
                    <p data-time="27">From the bush to the bling, eyes on the prize</p>
                    
                    <!-- Verse 2 -->
                    <p data-time="31">Started from the dirt, now ridin' high</p>
                    <p data-time="34">Paws on the wheel, hittin’ limits in the sky</p>
                    <p data-time="37">Rollin’ in the limo, swag so elite</p>
                    <p data-time="40">Got a gold chain hangin', kangaroo can't be beat</p>
                    
                    <!-- Chorus -->
                    <p data-time="43">Kroo Kroo Kangaroo, makin' money moves</p>
                    <p data-time="46">Hop to the bank, cashin' checks so smooth</p>
                    <p data-time="49">Kroo Kroo Kangaroo, wealth on the rise</p>
                    <p data-time="52">From the bush to the bling, eyes on the prize</p>
                    
                    <!-- Bridge -->
                    <p data-time="56">Skippin' through investments, like a pro on the scene</p>
                    <p data-time="61">Crypto, stocks, bonds, turnin’ green to green</p>
                    <p data-time="62">CEO status, suited for the biz</p>
                    <p data-time="65">Wall Street questions, "Who hopped in like this?"</p>
                    
                    <!-- Verse 3 -->
                    <p data-time="69">Kangaroo mansion, opulence display</p>
                    <p data-time="71">Pool shaped like Australia, loungin’ in the day</p>
                    <p data-time="75">Art on the walls, rare as ancient scroll</p>
                    <p data-time="77">Bank vault kangaroo, with stacks to console</p>
                    <p data-time="87">Kroo Kroo</p>

                    <p data-time="93">Kroo Kroo Kangaroo, makin' money moves</p>
                    <p data-time="96">Hop to the bank, cashin' checks so smooth</p>
                    <p data-time="99">Kroo Kroo Kangaroo, wealth on the rise</p>
                    <p data-time="102">From the bush to the bling, eyes on the prize</p>
                    
                    <p data-time="119">Skippin' through investments, like a pro on the scene</p>
                    <p data-time="124">Crypto, stocks, bonds, turnin’ green to green</p>
                    <p data-time="125">CEO status, suited for the biz</p>
                    <p data-time="128">Wall Street questions, "Who hopped in like this?"</p>

                </div>
            </div>
        </div>
    </div>

    <!-- Bottom Toolbar -->
    <div class="bottom-toolbar">
        <span>CA: AP4EnJMuLTUzXCD7DKuShGPywqR5bcLq5gskHCDpump</span> <span><a href="https://x.com/Kroo_coin" > &nbsp;&nbsp;&nbsp; <img height="15" src="https://upload.wikimedia.org/wikipedia/commons/c/ce/X_logo_2023.svg"/></a></span>
    </div>

    <!-- JavaScript -->
    <script src="/static/script.js"></script>
  <!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-18KCVHKQ11"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-18KCVHKQ11');
</script>

    <script>
        showContent('home');
        window.onload = function() {
                document.getElementById("background-music").play();
            }
    </script>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    const audio = document.getElementById('background-music');
    const lyrics = document.querySelectorAll('.lyrics p');
    const lyricsContainer = document.querySelector('.lyrics');

    // Create an array of lyric objects with time and element reference
    const lyricLines = Array.from(lyrics).map(line => {
        return {
            time: parseFloat(line.getAttribute('data-time')),
            element: line
        };
    });

    // Sort the lyrics by time in ascending order
    lyricLines.sort((a, b) => a.time - b.time);

    let currentLyricIndex = 0;
    let previousTime = 0;

    // Function to reset lyrics
    const resetLyrics = () => {
        lyrics.forEach(lyric => lyric.classList.remove('active'));
        currentLyricIndex = 0;
    };

    // Function to update lyrics based on current time
    const updateLyrics = () => {
        const currentTime = audio.currentTime;

        // Detect if the audio has looped
        if (currentTime < previousTime) {
            // Audio has looped
            resetLyrics();
            lyricsContainer.classList.add('visible'); // Ensure lyrics are visible on loop
        }

        previousTime = currentTime;

        // Check if we've reached the next lyric line
        if (currentLyricIndex < lyricLines.length && currentTime >= lyricLines[currentLyricIndex].time) {
            // Remove 'active' class from all lyrics
            lyrics.forEach(lyric => lyric.classList.remove('active'));

            // Add 'active' class to the current lyric
            lyricLines[currentLyricIndex].element.classList.add('active');

            // Optional: Scroll the active lyric into view
            lyricLines[currentLyricIndex].element.scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            });

            // Move to the next lyric line
            currentLyricIndex++;
        }
    };

    // Listen to the timeupdate event on the audio
    audio.addEventListener('timeupdate', updateLyrics);

    // Listen to the ended event to ensure lyrics reset
    audio.addEventListener('ended', () => {
        resetLyrics();
        lyricsContainer.classList.remove('visible'); // Hide lyrics when audio ends
    });

    // Listen to the play event to show lyrics
    audio.addEventListener('play', () => {
        lyricsContainer.classList.add('visible');
    });

    // Listen to the pause event to hide lyrics
    audio.addEventListener('pause', () => {
        lyricsContainer.classList.remove('visible');
    });

    // Handle seek events to maintain synchronization
    audio.addEventListener('seeked', () => {
        resetLyrics();
        updateLyrics();
    });
});
</script>
</body>
</html>
