/* Reset basic styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

@keyframes gradientAnimation {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

/* Container for the background animation */
.background-wrapper {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden; /* Prevent any overflow from affecting the page layout */
    z-index: -1; /* Place it behind all other content */
    pointer-events: none; /* Ignore clicks on the background */
}

.background-wrapper::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('/static/kroo.svg');
    background-size: 40% 90%;
    background-repeat: no-repeat;
    background-position: center;
    opacity: 0.1;
    animation: rotateAndExpand 10s ease-in-out infinite;
}

/* Updated keyframe animation for background rotation and scaling */
@keyframes rotateAndExpand {
    0% {
        transform: rotate(20deg) scale(1); /* Start at original size and rotation */
    }
    50% {
        transform: rotate(40deg) scale(1.2); /* Rotate and scale up */
    }
    100% {
        transform: rotate(20deg) scale(1); /* Return to original size and rotation */
    }
}

/* Body styling */
body {
    font-family: 'Rowdies', cursive;
    background: linear-gradient(135deg, #f5f1e6, #816442); /* Fallback for gradient */
    background: linear-gradient(-45deg, #f5f1e6, #e0d4c3, #b8a897, #a0908b);
    background-size: 400% 400%;
    animation: gradientAnimation 15s ease infinite;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    overflow-x: hidden;
}


/* Body styling */
body {
    font-family: 'Rowdies', cursive;
    background: linear-gradient(135deg, #f5f1e6, #816442);
    color: #333;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    overflow-x: hidden;
    transition: background 2s ease;


    background: linear-gradient(-45deg, #f5f1e6, #e0d4c3, #b8a897, #a0908b);
    background-size: 400% 400%;
    animation: gradientAnimation 15s ease infinite;

    
}


/* Top toolbar */
.toolbar {
    width: 100%;
    background-color: #4a3d2a; /* Deep brown */
    padding: 10px 0;
    text-align: center;
    font-family: 'Oswald', sans-serif;
}

.toolbar a {
    color: #e8c078; /* Gold text */
    font-size: 1.2em;
    margin: 0 20px;
    text-decoration: none;
}

.toolbar a:hover {
    color: #fff;
}

/* Main content layout */
.content {
    display: flex;
    align-items: flex-start;
    width: 90%;
    height:100%;
    max-width: 1200px;
    margin-top: 20px;
    /* hide scrollbar */
    scrollbar-width: none;
    
}

/* Left content area (Scrollable text) */
.left-content {
    width: 70%;
    padding: 20px;
    max-height: 90vh;
    overflow-y: auto;
    font-size: 33px;
    font-variant: Bold 700;
    color: #8d7756;
       /* Hide scrollbar */

}
.left-content h1 {
    font-size: 1.5em;
    font-weight: bold;
    margin-bottom: 10px;
    color: #44290c;
}

.left-content h2 {
    font-size: 4.5em;
    font-weight: bold;
    margin-bottom: 10px;
    color: #44290c;
}


/* Right content area for kangaroo image (Fixed position) */
.right-content {
    width: 30%;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}
.left-image {
    width: 30%;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}
#kangaroo-image {
    width: 100%;
    max-width: 500px;
    position: fixed;
    right: 5%;
    top: 20%; /* Adjust position for fixed placement */
    
}

/* Bottom toolbar styling */
.bottom-toolbar {
    width: 100%;
    background-color: #4a3d2a; /* Same deep brown as the top toolbar */
    padding: 10px 0;
    text-align: center;
    font-family: 'Oswald', sans-serif;
    position: fixed;
    bottom: 0;
    left: 0;
}

.bottom-toolbar span {
    color: #e8c078; /* Gold text color */
    font-size: 1.2em;
}

#music-control {
    position: fixed;
    top: 20px;
    right: 20px;
    background: none;
    border: none;
    cursor: pointer;
}

#play-pause-icon {
    width: 50px; /* Adjust size as needed */
    height: 50px;
}



@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.left-content {
    animation: fadeIn 1s ease-in-out;
}

/* Slight hop animation for heading */
@keyframes hop {
    0% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
    100% { transform: translateY(0); }
}

h2 {
    animation: hop 1s ease-in-out infinite;
}

/* Sparkle effect for chains (if added) */
@keyframes sparkle {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.chain-sparkle {
    animation: sparkle 0.8s ease-in-out infinite;
}





/* Fade-in animation */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.left-content {
    animation: fadeIn 1s ease-in-out;
}

/* Slight hop animation for heading */
@keyframes hop {
    0% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
    100% { transform: translateY(0); }
}

h1 {
    animation: hop 1s ease-in-out infinite;
}

/* Sparkle effect for chains (if added) */
@keyframes sparkle {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.chain-sparkle {
    animation: sparkle 0.8s ease-in-out infinite;
}

.fade-section {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.5s, transform 0.5s;
}

.fade-section.visible {
    opacity: 1;
    transform: translateY(0);
}

#music-control {
    width: 29px; /* Adjust size as needed */
    height: 29px;
    background-image: url('/static/playpause.png'); /* Path to your image */
    background-size: 59px 29px; /* Set full width of image and height */
    background-position: right; /* Start with pause icon */
    border: none;
    cursor: pointer;
    position: fixed;
    top: 10px;
    right: 20px;
}

#music-control.play {
    background-position: right; /* Shift to show the play icon */
}

#music-control.pause {
    background-position: left; /* Shift to show the pause icon */
}






@keyframes fadeSlideUp {
    0% {
        opacity: 0;
        transform: translateY(20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

#kangaroo-image {
    width: 100%;
    max-width: 500px;
    position: fixed;
    right: 5%;
    top: 20%; /* Adjust position for fixed placement */
}

.animate {
    animation: fadeSlideUp 1s ease forwards;
}



/* Toolbar Links Hover Effect */
.toolbar a {
    position: relative;
    transition: color 0.3s ease;
}

.toolbar a::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    display: block;
    margin-top: 5px;
    right: 0;
    background: #e8c078;
    transition: width 0.3s ease;
    -webkit-transition: width 0.3s ease;
}

.toolbar a:hover::after {
    width: 100%;
    left: 0;
    background: #fff;
}

/* Button Hover Effect */
#music-control:hover {
    transform: scale(1.1);
    transition: transform 0.3s ease;
}

/* Add a subtle shadow on image hover */
#kangaroo-image:hover {
    transform: scale(1.35);
    transition: transform 0.3s ease, box-shadow 0.3s ease;

}


.hover-zoom {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    transform-origin: center center; /* Ensures scaling happens from the center */
}

.hover-zoom:hover {
    transform: scale(1.2); /* Scales the image to 120% of its original size */
   
}

/* Apply the animation to the image */

.fancy-list {
    list-style: none; /* Remove default bullets */
    padding: 20px;
    margin: 20px 0;
    background: rgba(255, 255, 255, 0.1); /* Semi-transparent background */
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.fancy-list li {
    position: relative;
    padding-left: 40px; /* Space for the custom icon */
    margin-bottom: 15px;
    font-size: 1.2em;
    color: #333;
    transition: transform 0.3s ease, color 0.3s ease;
}

.fancy-list li:last-child {
    margin-bottom: 0;
}

/* Custom Icon */
.fancy-list li::before {
    content: '\f10c'; /* Default icon (replace with desired Font Awesome icon code) */
    font-family: 'Font Awesome 6 Free'; /* Ensure Font Awesome is loaded */
    font-weight: 900; /* Solid style */
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    color: #e8c078; /* Gold color */
    font-size: 1.2em;
}

/* Different Icons for Each List Item (Optional) */
.fancy-list li:nth-child(1)::before {
    content: '\f0c0'; /* Users icon */
}

.fancy-list li:nth-child(2)::before {
    content: '\f0a9'; /* Star icon */
}

.fancy-list li:nth-child(3)::before {
    content: '\f2ed'; /* Fire icon */
}

.fancy-list li:nth-child(4)::before {
    content: '\f0c6'; /* Chart Bar icon */
}

/* Hover Effects */
.fancy-list li:hover {
    color: #e27d60; /* Change text color on hover */
    transform: translateX(10px); /* Slight movement to the right */
}

/* Responsive Design */
@media (max-width: 600px) {
    .fancy-list {
        padding: 15px;
    }

    .fancy-list li {
        font-size: 1em;
        padding-left: 35px;
    }

    .fancy-list li::before {
        font-size: 1em;
    }
}

.fancy-list li:nth-child(1)::before {
    content: '\f0c2'; /* fa-coins */
}

.fancy-list li:nth-child(2)::before {
    content: '\f201'; /* fa-sync-alt */
}

.fancy-list li:nth-child(3)::before {
    content: '\f06d'; /* fa-fire */
}

.fancy-list li:nth-child(4)::before {
    content: '\f0a1'; /* fa-bullhorn */
}


.image-lyrics-container {
    position: relative;
    width: 100%;
    height: 100%;
 
}

.lyrics {
    position: absolute;
    top: 0;
    left: 0;
    width: 150%;
    height: 130px;

    color: rgba(255, 255, 255, 0.9); /* Semi-transparent white */
    font-family: 'Pacifico', cursive;
    font-size: 1.2em;
    line-height: 1.0em;
    overflow: hidden; /* Hide overflowing lyrics */
    text-align: center;
    pointer-events: none; /* Allows clicks to pass through to the image */
    /* Optional: Add a subtle background overlay for better readability */
    background: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    align-items: center;
    justify-content: center;

}

.lyrics p {
    margin: 10px 0;
    opacity: 0; /* Hidden by default */
    transform: translateY(-0px); /* Positioned slightly below */
    transition: opacity 0.6s ease, transform 0.6s ease;
    
}

.lyrics p.active {
    opacity: 1; /* Fully visible */
    transform: translateY(0px); /* Slide into place */
}

@media (max-width: 800px) {
    .lyrics {
        font-size: 1em;
    }
}

@media (max-width: 500px) {
    .lyrics {
        font-size: 0.9em;
        padding: 10px;
    }
}

.lyrics p.active {
    color: #e8c078; /* Highlight color */
    font-weight: bold;
    text-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
}

/* Lyrics Styling */
.lyrics {
    opacity: 0; /* Fully transparent */
    visibility: hidden; /* Hidden from layout */
    transition: opacity 0.5s ease, visibility 0.5s ease; /* Smooth transition */
}

/* Visible State */
.lyrics.visible {
    opacity: 1; /* Fully visible */
    visibility: visible; /* Present in layout */
}

@keyframes fadeOut {
    0% {
        opacity: 1;
        transform: scale(1); /* Start at original size */
    }
    100% {
        opacity: 0;
        transform: scale(1.2); /* Slightly enlarge as it fades out */
    }
}

.kroo-effect {
    position: absolute;
    width: 150px; /* Set the size of the SVG */
    height: 150px;
    pointer-events: none; /* Prevents the SVG from interfering with clicks */
    animation: fadeOut 0.8s ease-out forwards; /* Fades out in 0.8 seconds */
    opacity: 0; /* Start with 0 opacity */
    transform-origin: center;
}


@keyframes fadeInUp {
    0% {
        opacity: 0;
        transform: translateY(20px); /* Start slightly below */
    }
    100% {
        opacity: 1;
        transform: translateY(0); /* End at the original position */
    }
}

.fade-in {
    opacity: 0; /* Start invisible */
    animation: fadeInUp 1s ease forwards; /* Animation lasts 1 second */
}




/* Style for the close link */
.close-link {
    position: absolute;
    right: 20px; /* Adjust right spacing as needed */
    color: #e8c078;
    font-size: 1.2em;
    text-decoration: none;
}

