// JavaScript function to display content dynamically and update the image based on section


function showContent(section) {
    const mainContent = document.getElementById('main-content');
    const kangarooImage = document.getElementById('kangaroo-image');
    let content = '';

    // Update content and image based on section
    if (section === 'about') {
        content = `
            <h1>About Us</h1>
            <p><img src="/static/kroo_small.png"> is a meme-powered, community-driven token designed for those who want to have fun while investing. <br><br>
            Our token brings together people who love the energy of memes and the spirit of financial freedom. Join us on this exciting journey, and become part of a movement that's bold, adventurous, and ready to hop into the future!</p>
        `;
        kangarooImage.src = '/static/kangaroo6.png'; // Update with your image path for "About Us"
    } else if (section === 'how-to-buy') {
        content = `
            <h1>How to Buy</h1>
            <p>Buying <img src="/static/kroo_small.png"> is simple and straightforward! Follow these steps to join our Kangaroo Cash community:</p><br>
            <ul  class="fancy-list">
                <li>Download a crypto wallet </li>
                <li>Fund wallet</li>
                <li>Go to pump.fun and search for  <img src="/static/kroo_small.png"></li>
                <li>Ape and join our crew!</li>
            </ul>
        `;
        kangarooImage.src = '/static/kangaroo5.png'; // Update with your image path for "How to Buy"
    } else if (section === 'tokenomics') {
        content = `
            <h1>Tokenomics</h1>
            <p>Our tokenomics ensure that <img src="/static/kroo_small.png"> remains both sustainable and rewarding for our holders:</p><br>
            <ul class="fancy-list">
                <li> Supply: 1 Billion Tokens</li>
                <li> Taxes: 0% on buys and sells </li>
                <li> Liquidity burned</li>
            </ul>
            <br>
            <p>With these mechanisms in place, we aim to create long-term value for our community and sustain the <img src="/static/kroo_small.png"> culture.</p>
        `;
        kangarooImage.src = '/static/kangaroo4.png'; // Update with your image path for "Tokenomics"
    } else if (section === 'home') {
        content = `
     
            <h2>KROO</h2>

                <div class="intro-text">
        <h1>Welcome to <img src="/static/kroo_small.png"><br> The Kangaroo Cash Crew!</h1>
        <p>It's not just a pump.fun token – it’s a movement for the wild-hearted and the bold.</p><br>
        <p>With our fierce and stylish kangaroo leading the way, join us as we leap towards a new crypto adventure. Embrace the meme, join the fun, and watch your portfolio hop to new heights with <strong>$KROO!</strong></p><br>
        <p><strong>🎶 Heads up!</strong> You’re one click away from experiencing the full vibe. We’ve got a super cool track that brings the $KROO energy to life. <strong>Click anywhere to play the anthem, and let the kangaroo groove take over!</strong></p><br>
    </div>
            
      
        `;
        kangarooImage.src = '/static/kangaroo3.png'; // Update with your image path for "Home"
    }else if (section === 'facts') {
        content = `
     
            <h1>Kangaroo Facts</h1>
    <div class="facts-container">
    <p class="kangaroo-fact">
        🦘 <strong>They Can’t Walk Backwards:</strong> Just like a kangaroo, crypto markets, especially $KROO, are all about moving forward! 🚀 With $KROO, there’s no room for regrets – hodl strong and keep looking ahead because $KROO is all about the next big leap, not stepping back.
    </p>
    <br>
    <p class="kangaroo-fact">
        🥊 <strong>Boxers by Nature:</strong> Kangaroos box to show dominance, and $KROO does the same in the crypto ring! 💥 With contenders like $KROO, even the top coins need to step up their game to keep up with this power-packed token!
    </p>
    <br>
    <p class="kangaroo-fact">
        💪 <strong>They "Flex" to Impress:</strong> Male kangaroos flex to attract females, and $KROO flexes its awesome community and meme power to attract new holders. With flashy logos and exciting updates, $KROO is flexing its way to the top!
    </p>
    <br>
    <p class="kangaroo-fact">
        👜 <strong>Built-In Pockets for Babies:</strong> Kangaroo pouches are like secure wallets, just like $KROO's secure ecosystem keeps your assets safe and snug. Remember, keep your "pouch" secure with $KROO and let it grow in peace!
    </p>
    <br>
    <p class="kangaroo-fact">
        🦷 <strong>Kangaroo Teeth Are on a Conveyor Belt:</strong> Just as kangaroos get fresh teeth, $KROO keeps fresh opportunities rolling in for its holders. Whether it’s rewards, growth, or community perks, $KROO never stops delivering!
    </p>
    <br>
    <p class="kangaroo-fact">
        👂 <strong>They Have Superhuman Hearing:</strong> Kangaroos can hear everything around them, and $KROO holders stay tuned to all market news. The $KROO community is always aware, listening for the next big jump. Don’t miss out!
    </p>
    <br>
    <p class="kangaroo-fact">
        🦿 <strong>Their Tail Acts Like a Fifth Leg:</strong> Just like kangaroos use their tail for balance, $KROO provides a stable foundation for investors. Having $KROO in your portfolio gives you that extra boost of stability during wild market swings.
    </p>
    <br>
    <p class="kangaroo-fact">
        ⏸️ <strong>They Can Put Themselves on "Pause":</strong> Female kangaroos can pause pregnancy if needed, just like $KROO is ready to adapt during market changes. $KROO knows when to wait for the right moment to leap to new heights!
    </p>
    <br>
    <p class="kangaroo-fact">
        ✋ <strong>They’re Lefties:</strong> Many kangaroos are left-handed, and $KROO holders know that sometimes going against the grain is the winning move. Whether it’s buying the dip or holding strong, $KROO holders know how to play it smart.
    </p>
    <br>
    <p class="kangaroo-fact">
        🗣️ <strong>They Can "Talk" to Humans:</strong> Kangaroos can "communicate" with humans, and $KROO keeps its community engaged and informed. With transparency and constant updates, $KROO makes sure its holders are always in the know. It’s all about building trust!
    </p><br><br>
</div>
     `;
        kangarooImage.src = '/static/kangaroo.png'; // Update with your image path for "Home"
    }

    // Update content and add a fade-in effect
    mainContent.innerHTML = content;
    mainContent.style.opacity = 0;

    const paragraphs = mainContent.querySelectorAll('p');
    paragraphs.forEach((paragraph, index) => {
        paragraph.style.animationDelay = `${index * 0.3}s`;
        paragraph.classList.add('fade-in');
    });


    setTimeout(() => {
        mainContent.style.opacity = 1;
    }, 100);

    // Trigger fade and slide-up animation for kangaroo image
    kangarooImage.classList.remove('animate'); // Remove animation class temporarily
    setTimeout(() => {
        kangarooImage.classList.add('animate'); // Re-add the class after a short delay
    }, 10); // Small delay to ensure the animation restarts
}


function toggleMusic() {
    const music = document.getElementById('background-music');
    const musicControl = document.getElementById('music-control');
    
    if (music.paused) {
        music.play();
        musicControl.classList.add('pause');
        musicControl.classList.remove('play');
    } else {
        music.pause();
        musicControl.classList.add('play');
        musicControl.classList.remove('pause');
    }
}

document.addEventListener("scroll", () => {
    const sections = document.querySelectorAll(".fade-section");
    const scrollPosition = window.scrollY + window.innerHeight;
    
    sections.forEach(section => {
        if (scrollPosition > section.offsetTop + 100) {
            section.classList.add("visible");
        }
    });
});

function enableAudio() {
    const music = document.getElementById('background-music');
    music.muted = false; // Unmute the audio
    music.play(); // Start playing the audio

    // Remove the event listener after the first interaction
    document.removeEventListener('click', enableAudio);
}
document.addEventListener('click', enableAudio);

document.addEventListener('click', (event) => {
    // Create a new SVG element
    const svgElement = document.createElement('img');
    svgElement.src = '/static/kroo.svg'; // Path to your SVG file
    svgElement.classList.add('kroo-effect');

    // Position the SVG at the cursor position
    svgElement.style.left = `${event.pageX - 25}px`; // Offset by half width
    svgElement.style.top = `${event.pageY - 25}px`;  // Offset by half height

    // Append SVG to the body
    document.body.appendChild(svgElement);

    // Remove the SVG after the animation ends
    svgElement.addEventListener('animationend', () => {
        svgElement.remove();
    });
});


document.addEventListener('DOMContentLoaded', () => {
    const paragraphs = document.querySelectorAll('#main-content p'); // Select all paragraphs in the main content
    paragraphs.forEach((paragraph, index) => {
        // Add the fade-in class with a delay
        paragraph.style.animationDelay = `${index * 0.3}s`; // Stagger by 0.3 seconds
        paragraph.classList.add('fade-in');
    });
});