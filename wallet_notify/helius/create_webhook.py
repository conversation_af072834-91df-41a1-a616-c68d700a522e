import requests

address="BkLsbxUeUEP3Yytdt2m84BzSKMnex9qn5HtERmYWqv8Q"
api="8ede527e-abea-4177-ba97-d614266c5f8d"


response = requests.post(
    f"https://api.helius.xyz/v0/webhooks?api-key={api}",
    headers={"Content-Type":"application/json"},
    json={
        "webhookURL": "http://**************:7002/webhook",
        "transactionTypes": ["Any"],
        "accountAddresses": [address],
        "webhookType": "enhanced"
        }
)



#// Use ["ACCOUNT_ADDRESS", "ACCOUNT_ADDRESS"] for multiple accountAddresses. 


data = response.json()
print(data.text)



#curl -L 'https://api.helius.xyz/v0/webhooks?api-key=8ede527e-abea-4177-ba97-d614266c5f8d'
