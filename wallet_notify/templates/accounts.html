<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Account Management</title>
    <style>
        body {
            background-color: #121212;
            color: #e0e0e0;
            font-family: Arial, sans-serif;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #333;
        }
        th {
            background-color: #333;
        }
        td.links a{
            white-space: nowrap;
            color: #979797;
            text-decoration: none;
        }
        tr:hover {
            background-color: #444;
        }
        input, select, button {
            padding: 8px;
            margin: 5px;
            color: #e0e0e0;
            background-color: #333;
            border: 1px solid #555;
            border-radius: 4px;
        }
        button {
            cursor: pointer;
        }
        form {
    position: sticky;
    top: 0;
    background-color: #121212;
    padding: 20px;
    border-bottom: 1px solid #333;
    z-index: 10;
}

    </style>
</head>
<body>
    <h1>Account Management</h1>
    <form method="post" action="/accounts">
        <input type="hidden" name="id" id="account-id">
        
        <label for="address">Address:</label>
        <input type="text" name="address" id="address" required>
        
        <label for="name">Name:</label>
        <input type="text" name="name" id="name" required>
    
        <label for="notify">Notify:</label>
        <select name="notify" id="notify">
            <option value="1" selected>Yes</option>
            <option value="0">No</option>
        </select>
    
        <label for="trader">Trader:</label>
        <select name="trader" id="trader">
            <option value="1" selected>Yes</option>
            <option value="0">No</option>
        </select>
    
        <label for="enabled">Enabled:</label>
        <select name="enabled" id="enabled">
            <option value="1" selected>Yes</option>
            <option value="0">No</option>
        </select>
    
        <label for="description">Description:</label>
        <input type="text" name="description" id="description">
    
        <label for="url">URL:</label>
        <input type="text" name="url" id="url">
    
        <button type="submit">Save Account</button>
    </form>

    <table>
        <thead>
            <tr>
                <th>ID</th>
                <th>Address</th>
                <th>Name</th>
                <th>Notify</th>
                <th>Trader</th>
                <th>Enabled</th>
                <th>Description</th>
                <th>URL</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for account in accounts %}
            <tr>
                <td>{{ account.id }}</td>
                <td class="links">[&nbsp;<a href="https://app.cielo.finance/profile/{{ account.address }}">cielo</a>|<a href="https://gmgn.ai/sol/address/{{ account.address }}">gmgn</a>|<a href="https://solscan.io/account/{{ account.address }}#defiactivities">solscan</a>&nbsp;]&nbsp;{{ account.address }}</td>
                <td>{{ account.name }}</td>
                <td>{{ account.notify }}</td>
                <td>{{ account.trader }}</td>
                <td>{{ account.enabled }}</td>
                <td>{{ account.description }}</td>
                <td>{{ account.url }}</td>
                <td>
                    <button onclick="editAccount({{ account.id }}, '{{ account.address }}', '{{ account.name }}', {{ account.notify }}, {{ account.trader }}, {{ account.enabled }}, '{{ account.description }}', '{{ account.url }}')">Edit</button>
                    <button onclick="deleteAccount({{ account.id }})">Delete</button>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>

    <script>
    function editAccount(id, address, name, notify, trader, enabled, description, url) {
        document.getElementById('account-id').value = id;
        document.getElementById('address').value = address;
        document.getElementById('name').value = name;
        document.getElementById('notify').value = notify;
        document.getElementById('trader').value = trader;
        document.getElementById('enabled').value = enabled;
        document.getElementById('description').value = description;
        document.getElementById('url').value = url;

        // Scroll to the top of the page
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }

        function deleteAccount(id) {
            if (confirm("Are you sure you want to delete this account?")) {
                fetch(`/accounts/delete/${id}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const row = document.querySelector(`button[onclick="deleteAccount(${id})"]`).closest('tr');
                        row.remove();
                    } else {
                        alert("Failed to delete account. Please try again.");
                    }
                })
                .catch(error => console.error('Error:', error));
            }
        }
    </script>
</body>
</html>
