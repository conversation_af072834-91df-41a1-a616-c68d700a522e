<!DOCTYPE html>
<html lang="en">

<head>
    <!-- Meta and Title -->
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Latest Token Swaps</title>
    <!-- Load multiple fonts from Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto&family=Open+Sans&family=Lato&family=Montserrat&family=Oswald&family=Raleway&family=Merriweather&family=Ubuntu&family=Playfair+Display&family=Lora&display=swap" rel="stylesheet">

    <!-- Styles -->
    <style>
        body {
            font-family: 'Roboto', sans-serif;
            background-color: #1c1e22;
            color: #fff;
            margin: 0;
            padding: 20px;
        }
        .social {
            color: #434343;
            font-weight: italic;
            text-decoration: none;
            cursor: pointer;
        }
        h1 {
            color: #fff;
            text-align: center;
        }

        .last-updated {
            position: absolute;
            top: 10px;
            left: 20px;
            color: #ccc;
            font-size: 14px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background-color: #2a2c31;
            border-radius: 8px;
            overflow: hidden;
        }

        table th,
        table td {
            padding: 12px;
            border: 1px solid #444;
            text-align: center;
            height: 60px;
        }

        table th {
            background-color: #1c1e22;
            font-weight: bold;
            text-transform: uppercase;
        }

        table td {
            font-size: 14px;
            color: #fff;
        }

        .sol-low {
            color: #797979;
            font-weight: normal;
        }
        .sol-mid {
            color: #7dff8d;
            font-weight: bold;
        }
        .sol-high {
            color: #fa5bff;
            font-weight: bold;
        }

 



        .freshTime1 {
            color: #c3faa1;
            font-weight: bold;
        }
        .freshTime2 {
            color: #56b01e;
            font-weight: bold;
        }

        .freshTime3 {
            color: #254a0f;
            font-weight: bold;
        }


        .buy {
            color: #34d399;
            font-weight: bold;
        }

        .sell {
            color: #c65907;
            font-weight: bold;
        }

        .neutral {
            color: #14640f;
        }

        .low-mcap {
            color: #92d9ff;
            font-weight: bold;
         }

         .mid-mcap {
            color: #3374b8;
            font-weight: bold;
         }

         .high-mcap {
            color: #1c3d75;
            font-weight: bold;
         }
        .action a {
            color: #6b47dc;
            font-weight: bold;
            text-decoration: none;
            cursor: pointer;
        }

        .action a:hover {
            text-decoration: underline;
        }

        .time {
            font-size: 12px;
            color: #999;
        }

        img {
            min-width: 50px;
            max-width: 50px;
            max-height: 50px;
        }

        .avatar {
            min-width: 30px;
            max-width: 30px;
            max-height: 30px;
        }

        .filter-container {
            text-align: center;
            margin-bottom: 20px;
        }

        .filter-input {
            padding: 8px;
            border-radius: 5px;
            border: 1px solid #ccc;
            width: 250px;
            font-size: 14px;
        }

        .clear-button {
            padding: 8px 12px;
            margin-left: 10px;
            border-radius: 5px;
            border: none;
            background-color: #6b47dc;
            color: #fff;
            cursor: pointer;
            font-size: 14px;
        }

        .clear-button:hover {
            background-color: #5a37c0;
        }

        .circle {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 5px;
        }

        .black {
            background-color: black;
        }

        .dark-red {
            background-color: rgb(95, 0, 0);
        }

        .red {
            background-color: red;
        }

        .gray {
            background-color: gray;
        }

        .light-blue {
            background-color: rgb(74, 180, 129);
        }

        .blue {
            background-color: rgb(7, 126, 35);
        }

        .purple {
            background-color: rgb(10, 66, 23);
        }

        /* Added CSS for account-name */
        .account-name {
            display: flex;
            align-items: center;
            justify-content: left;
        }

        .account-name img {
            margin-right: 8px;
            width: 30px;
            height: 30px;
            border-radius: 4px;
        }

        /* Style for clickable symbol */
        .symbol-link {
            color: #6b47dc;
            cursor: pointer;
            text-decoration: none;
        }

        .symbol-link:hover {
            text-decoration: underline;
        }
    </style>

    <!-- Scripts -->
    <!-- Include identicon.js -->
    <script src="https://cdn.jsdelivr.net/npm/identicon.js@2.3.3/identicon.min.js"></script>
    <!-- Include md5.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/blueimp-md5/2.19.0/js/md5.min.js"></script>

    <script>
        let latestData = [];
        let previousTopEntryBlockTime = null; // To track the previous top entry's block_time

        const fonts = [
            "'Roboto', sans-serif",
            "'Open Sans', sans-serif",
            "'Lato', sans-serif",
            "'Montserrat', sans-serif",
            "'Oswald', sans-serif",
            "'Raleway', sans-serif",
            "'Merriweather', serif",
            "'Ubuntu', sans-serif",
            "'Playfair Display', serif",
            "'Lora', serif",
            "'PT Sans', sans-serif",
            "'Roboto Condensed', sans-serif",
            "'Source Sans Pro', sans-serif",
            "'Poppins', sans-serif",
            "'Nunito', sans-serif",
            "'Rubik', sans-serif",
            "'Inconsolata', monospace",
            "'Work Sans', sans-serif",
            "'Nunito Sans', sans-serif",
            "'Comfortaa', cursive"
        ];

        // Function to get a font for a given account name
        function getFontForAccount(accountName) {
            // Generate a hash from the account name
            const hash = md5(accountName);
            // Convert hash to integer
            const hashInt = parseInt(hash.substring(0, 8), 16);
            // Map hash to a font index
            const fontIndex = hashInt % fonts.length;
            // Return the font family
            return fonts[fontIndex];
        }

        function handleImageError(image) {
            // Prevent infinite loop if the placeholder image also fails
            image.onerror = null;
            // Replace with a placeholder image or data URI
            image.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIiBoZWlnaHQ9IjUwIiB2aWV3Qm94PSIwIDAgNTAgNTAiPjxyZWN0IHdpZHRoPSI1MCIgaGVpZ2h0PSI1MCIgZmlsbD0iI0I0QjRCRCIgLz48dGV4dCB4PSIyNSIgeT0iMzAiIGZvbnQtc2l6ZT0iMTBweCIgZm9udC1mYW1pbHk9IkFyaWFsIiBmaWxsPSIjRkZGIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj5ObyBJbWFnZTwvdGV4dD48L3N2Zz4=';
        }

        function createColoredCirclesHTML(arr) {
            let html = ''; // Initialize an empty string to hold the HTML

            arr.forEach(value => {
                let colorClass = '';

                // Determine the color class based on the value
                switch (value) {
                    case -3: colorClass = 'black'; break;
                    case -2: colorClass = 'dark-red'; break;
                    case -1: colorClass = 'red'; break;
                    case 0: colorClass = 'gray'; break;
                    case 1: colorClass = 'light-blue'; break;
                    case 2: colorClass = 'blue'; break;
                    case 3: colorClass = 'purple'; break;
                    default: colorClass = 'gray'; // Default to gray if value is unknown
                }

                // Append a span element with the circle class and the determined color
                html += `<span class="circle ${colorClass}"></span>`;
            });

            return html; // Return the constructed HTML string
        }

        // Function to generate a color code from a string
        function stringToColor(str) {
            let hash = 0;
            for (let i = 0; i < str.length; i++) {
                hash = str.charCodeAt(i) + ((hash << 5) - hash);
            }
            let color = '#';
            for (let i = 0; i < 3; i++) {
                const value = (hash >> (i * 8)) & 0xFF;
                color += ('00' + value.toString(16)).substr(-2);
            }
            return color;
        }

        // Sound notification function
        function beep() {
            var snd = new Audio("data:audio/wav;base64,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");
            snd.play();
        }

        // Update last updated time
        function updateLastUpdatedTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', second: '2-digit' });
            document.getElementById('last-updated').textContent = 'Last updated: ' + timeString;
        }

        // Calculate time ago
        function timeAgo(timestamp) {
            const now = Date.now();
            const secondsAgo = Math.floor((now - timestamp) / 1000);

            if (secondsAgo < 60) return `${secondsAgo} seconds ago`;
            const minutesAgo = Math.floor(secondsAgo / 60);
            if (minutesAgo < 60) return `${minutesAgo} minutes ago`;
            const hoursAgo = Math.floor(minutesAgo / 60);
            if (hoursAgo < 24) return `${hoursAgo} hours ago`;
            const daysAgo = Math.floor(hoursAgo / 24);
            return `${daysAgo} days ago`;
        }

        function filterRows() {
            const filterValue = document.getElementById('filter-input').value.toLowerCase();

            let newHTML = '';

            latestData.forEach(function (swap) {
                // Check if filter matches account_name, token_name, or token_symbol
                if (
                    swap['account_name'].toLowerCase().includes(filterValue) ||
                    swap['token_name'].toLowerCase().includes(filterValue) ||
                    swap['token_symbol'].toLowerCase().includes(filterValue)
                ) {
                    let rowHTML = '<tr>';

                    // Define the order of columns
                    const columns = ['trade', 'account_name', 'sol_amount', 'token_name', 'token_symbol'];

                    columns.forEach(function (key) {
                        let cellContent = '';

                        if (key === 'account_name') {
                            const fontFamily = getFontForAccount(swap[key]);

                            // Generate identicon
                            const hash = md5(swap[key]);
                            const data = new Identicon(hash, { size: 40, format: 'svg' }).toString();
                            const imgSrc = 'data:image/svg+xml;base64,' + data;

                            // Apply the font style inline
                            cellContent = `
                                <div class="account-name" style="font-family: ${fontFamily};">
                                    <img src="${imgSrc}" class="avatar" alt="Identicon"/>
                                    <a href="https://gmgn.ai/sol/address/${swap['account']}" target="_blank" style="color: lightgray;">${swap[key]}</a>
                                </div>`;
                        } else if (key === 'sol_amount') {
                            cellContent = parseFloat(swap[key]).toFixed(2);
                        } else if (key === 'token_symbol') {
                            // Make the symbol clickable
                            cellContent = `<a href="#" class="symbol-link" onclick="filterBySymbol('${swap[key]}'); return false;">${swap[key]}</a>`;
                        } else {
                            cellContent = swap[key];
                        }

                        // Add class for BUY or SELL based on the trade type
                        //https://gmgn.ai/sol/token/H2L2BMV2451hDynXzyngu4ktg2HfSNCa2CcKAnRppump?maker=HDk1zYGfmwpU5wqUtFm5NNY6RdBvxHELaP8kqXjzsAkD

                        let className = '';
                        if (key === 'trade' && swap[key].toUpperCase() === 'BUY') {
                            className = 'buy';
                        } else if (key === 'trade' && swap[key].toUpperCase() === 'SELL') {
                            className = 'sell';
                        }
                        if (swap['num_other_accounts_bought'] > 0 && key === 'trade') {
        
                            rowHTML += `<td class="${className}"><a href="https://gmgn.ai/sol/token/${swap['mint']}?maker=${swap['account']}" target="_blank" class="${className}">${cellContent}(${swap['num_other_accounts_bought']})</a></td>`;
                        } else if (key === 'sol_amount') {

                            if ( swap['sol_amount'] <1 ) {
                                rowHTML += `<td class="sol-low">${cellContent}</td>`;
                            } else if ( swap['sol_amount'] >= 1 && swap['sol_amount'] < 10 ) {
                                rowHTML += `<td class="sol-mid">${cellContent}</td>`;
                            } else {
                                rowHTML += `<td class="sol-high">${cellContent}</td>`;
                            }
                        } else if (key === 'trade') {
                            rowHTML += `<td class="${className}"><a href="https://gmgn.ai/sol/token/${swap['mint']}?maker=${swap['account']}" target="_blank" class="${className}">${cellContent}</a></td>`;
                            
                        } else {
                            
                            rowHTML += `<td class="${className}">${cellContent}</td>`;
                        }
                    });

                    // Generate border color from account_name
                    const borderColor = stringToColor(swap['account_name']);

                    // Add the token image column with onerror handler
                    rowHTML += `<td><img src="${swap['token_url']}" alt="Token Image" style="max-width:50px; max-height:50px; border: 3px solid ${borderColor};" onerror="handleImageError(this)"></td>`;

                    // Add dynamic "token created" time
                    const tokenCreatedTime = swap['token_timestamp'] * 1000; // Convert from seconds to milliseconds
                    // if earlier than 10 mionuts
                    if ( swap['token_timestamp'] > 0 && (Date.now() - tokenCreatedTime) < 600000 ) {
        
                        rowHTML += `<td class="freshTime1">${timeAgo(tokenCreatedTime)}</td>`;
                    // time between 10 minutes and 1 hour
                    } else if ( swap['token_timestamp'] > 0 && (Date.now() - tokenCreatedTime) < 3600000 ) {
                        rowHTML += `<td class="freshTime2">${timeAgo(tokenCreatedTime)}</td>`;
                    //time between 1 hour and 1 day 
                    
                    } else {
                        rowHTML += `<td class="freshTime3">${timeAgo(tokenCreatedTime)}</td>`;
                    }

                    // Add the social media links
                    let socialsHTML = '';

                    if (swap['twitter']) {
                        socialsHTML += `<a href="${swap['twitter']}" target="_blank" class="social" >[Twitter]</a> `;
                    }
                    if (swap['website']) {
                        socialsHTML += `<a href="${swap['website']}" target="_blank" class="social">[Website]</a> `;
                    }
                    if (swap['telegram']) {
                        socialsHTML += `<a href="${swap['telegram']}" target="_blank" class="social">[Telegram]</a> `;
                    }

                    socialsHTML = socialsHTML || 'N/A';
                    rowHTML += `<td>${socialsHTML}</td>`;

                    // Add MarketCap column
                    function formatMarketCap(value) {
                        if (value >= 1e9) {
                            return `$${(value / 1e9).toFixed(1)}B`; // Billions
                        } else if (value >= 1e6) {
                            return `$${(value / 1e6).toFixed(1)}M`; // Millions
                        } else if (value >= 1e3) {
                            return `$${(value / 1e3).toFixed(1)}k`; // Thousands
                        } else {
                            return `$${value}`; // Less than a thousand
                        }
                    }

                    function pnl(value) {
                        if (value > 0) {
                            return `<span style="color: green;">+${value.toFixed(2)}%</span>`;
                        } else if (value < 0) {
                            return `<span style="color: red;">${value.toFixed(2)}%</span>`;
                        } else {
                            return `<span>N/A</span>`;
                        }
                    }

                    if (swap['mcap'] < 40000) {
                        rowHTML += `<td class="low-mcap">${formatMarketCap(swap['mcap'])}</td>`;
                    // mid between 40 and 1000
                    } else if (swap['mcap'] >= 40000 && swap['mcap'] < 150000) {
                        rowHTML += `<td class="mid-mcap">${formatMarketCap(swap['mcap'])}</td>`;

                    } else {
                        rowHTML += `<td class="high-mcap">${formatMarketCap(swap['mcap'])}</td>`;
                    }                


                    rowHTML += `<td>${pnl(swap['pnl_pct'])}</td>`;

                    let pnlArray = JSON.parse(swap['pnl_array']);

                    rowHTML += `<td>${createColoredCirclesHTML(pnlArray)}</td>`;

                    // Add dynamic "time ago"
                    const blockTime = swap['block_time'] * 1000; // Convert from seconds to milliseconds
                    rowHTML += `<td class="time" data-block-time="${swap['block_time']}">${timeAgo(blockTime)}</td>`;

                    // Add Action column with URL
                    rowHTML += `<td class="action"><a href="https://bullx.io/terminal?chainId=1399811149&address=${swap['mint']}" target="_blank">BullX</a></td>`;

                    rowHTML += '</tr>';

                    newHTML += rowHTML;
                }
            });

            // Replace the table body content
            let tableBody = document.getElementById('swaps-table-body');
            tableBody.innerHTML = newHTML;
        }

        // Function to filter by symbol when symbol is clicked
        function filterBySymbol(symbol) {
            const filterInput = document.getElementById('filter-input');
            filterInput.value = symbol;
            filterRows();
        }

        // Function to clear the filter
        function clearFilter() {
            const filterInput = document.getElementById('filter-input');
            filterInput.value = '';
            filterRows();
        }

        // Update "time ago" every second
        function updateTimes() {
            const timeCells = document.querySelectorAll('.time');
            timeCells.forEach(function (timeCell) {
                const blockTime = timeCell.getAttribute('data-block-time') * 1000;  // Get block time in milliseconds
                timeCell.textContent = timeAgo(blockTime);
            });
        }

        // Fetch latest swaps from the server
        function fetchLatestSwaps() {
            fetch('/api/latest-swaps')
                .then(response => response.json())
                .then(data => {
                    latestData = data; // Store the latest data for filtering
                    filterRows(); // Apply filtering when data is fetched

                    // Play sound if new top entry appears
                    if (latestData.length > 0) {
                        const currentTopEntryBlockTime = latestData[0]['block_time'];
                        if (previousTopEntryBlockTime !== null && currentTopEntryBlockTime !== previousTopEntryBlockTime) {
                            beep();
                        }
                        previousTopEntryBlockTime = currentTopEntryBlockTime;
                    }

                    // Update the last updated time after data is fetched
                    updateLastUpdatedTime();
                })
                .catch(error => console.error('Error fetching data:', error));
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function () {
            document.getElementById('filter-input').addEventListener('input', filterRows); // Apply filter on input
            document.getElementById('clear-button').addEventListener('click', clearFilter); // Clear filter on button click
            fetchLatestSwaps(); // Initial fetch
            setInterval(fetchLatestSwaps, 1000); // Fetch every 1 second

            // Set interval to update "time ago" every second
            setInterval(updateTimes, 1000);
        });
    </script>
</head>

<body>
    <!-- Last Updated Time -->
    <div class="last-updated" id="last-updated">Last updated: </div>

    <!-- Page Title -->
    <h1>Latest Token Swaps</h1>

    <!-- Filter Input -->
    <div class="filter-container">
        <input type="text" id="filter-input" class="filter-input" placeholder="Filter by account name, token name, or symbol...">
        <button id="clear-button" class="clear-button">Clear</button>
    </div>

    <!-- Data Table -->
    <table>
        <thead>
            <tr>
                <th>Trade</th>
                <th>Account Name</th>
                <th>Amount (SOL)</th>
                <th>Token Name</th>
                <th>Symbol</th>
                <th>Token Image</th>
                <th>Token Created</th>
                <th>Socials</th>
                <th>MarketCap</th>
                <th>PNL</th>
                <th>PNL ARRAY</th>
                <th>Time Ago</th>
                <th>Action</th>
            </tr>
        </thead>
        <tbody id="swaps-table-body">
            <!-- Data will be inserted here -->
        </tbody>
    </table>
</body>

</html>
