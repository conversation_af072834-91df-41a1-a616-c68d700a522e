<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Trade Data Visualization</title>
  <style>
    /* Global Styles */
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
      background-color: #121212; /* Dark background */
      color: #e0e0e0; /* Light text */
    }

    h1 {
      text-align: center;
      color: #ffffff; /* White text for the header */
    }

    /* Container for the input and button */
    .input-container {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: 20px;
      gap: 10px;
      flex-wrap: wrap; /* Allow wrapping on smaller screens */
    }
    
    .Buy{
        background-color: #2e7d32;
    }
    .Sell {
        background-color: #c62828;
    }

    .input-container label {
      color: #ffffff; /* White labels */
    }

    .input-container input {
      padding: 8px;
      border: 1px solid #333; /* Dark border */
      border-radius: 4px;
      background-color: #1e1e1e; /* Dark input background */
      color: #e0e0e0; /* Light input text */
    }

    .input-container button {
      padding: 8px 16px;
      border: none;
      border-radius: 4px;
      background-color: #3f51b5; /* Dark blue button */
      color: #ffffff; /* White button text */
      cursor: pointer;
      transition: background-color 0.3s;
    }

    .input-container button:hover {
      background-color: #303f9f; /* Darker blue on hover */
    }

    /* Chart Container */
    #chart-container {
      width: 100%;
      height: 400px;
      margin-bottom: 20px;
      background-color: #1e1e1e; /* Dark chart background */
      border-radius: 8px;
      padding: 10px;
    }

    /* Table Container */
    .table-container {
      max-height: 400px; /* Adjust as needed */
      overflow-y: auto;
      border: 1px solid #333; /* Dark border */
      border-radius: 8px;
      background-color: #1e1e1e; /* Dark table background */
    }

    /* Table Styling */
    #data-table {
      width: 100%;
      border-collapse: collapse;
    }

    #data-table th, #data-table td {
      border: 1px solid #333; /* Dark border */
      padding: 8px;
      text-align: center;
      color: #e0e0e0; /* Light text */
    }

    #data-table th {
      background-color: #2c2c2c; /* Dark header background */
      position: sticky;
      top: 0;
      z-index: 2;
    }

    #data-table tr:hover {
      background-color: #333333; /* Darker hover */
    }

    #data-table tr.highlight {
      background-color: #ffd700; /* Gold highlight */
      color: #000000; /* Black text for contrast */
    }

    #data-table tr.selected {
      background-color: #3f51b5 !important; /* Darker blue for selection */
      color: #ffffff; /* White text */
    }

    /* Cursor Pointer for Table Rows */
    #data-table tbody tr {
      cursor: pointer;
    }

    /* Scrollbar Styling for Table */
    .table-container::-webkit-scrollbar {
      width: 8px;
    }

    .table-container::-webkit-scrollbar-track {
      background: #1e1e1e; /* Dark track */
    }

    .table-container::-webkit-scrollbar-thumb {
      background-color: #555; /* Dark thumb */
      border-radius: 4px;
    }

    /* Responsive Adjustments */
    @media (max-width: 768px) {
      .input-container {
        flex-direction: column;
      }

      .input-container input {
        width: 100%;
        max-width: 300px;
      }

      .input-container button {
        width: 100%;
        max-width: 150px;
      }
    }
  </style>
</head>
<body>

  <h1>Trade Data Visualization</h1>

  <div class="input-container">
    <label for="mint-address">Mint Address:</label>
    <input type="text" id="mint-address" value="2Zw6kxtNXgVCA7atqnHBUWJMfwzffDpah18ELqMupump" size="50">
    <label for="minimum-size">Min sol:</label>
    <input type="text" id="minimum-size" value="1" size="5">
    <button id="load-data-btn">Load Data</button>
  </div>

  <div id="chart-container">
    <canvas id="trade-chart" style="background-color: #1e1e1e; border-radius: 8px;"></canvas>
  </div>

  <div class="table-container">
    <table id="data-table">
      <thead>
        <tr>
          <th>Links</th>
          <th>User Address</th>
          <th>Buy/Sell</th>
          <th>Amount (SOL)</th>
          <th>Amount (Token)</th>
          <th>Time</th>

        </tr>
      </thead>
      <tbody>
        <!-- Data rows will be inserted here -->
      </tbody>
    </table>
  </div>

  <!-- Include Chart.js library -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <!-- Include date-fns adapter -->
  <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns@3"></script>

  <script>
    const ctx = document.getElementById('trade-chart').getContext('2d');
    let chart;
    let chartData = [];
    let buyData = [];
    let sellData = [];
    let selectedRow = null; // To keep track of the currently selected row
    let selectedPoint = null; // To keep track of the currently selected chart point

    document.getElementById('load-data-btn').addEventListener('click', loadData);

    async function loadData() {
      const mintAddress = document.getElementById('mint-address').value.trim();
      const minimumSize = parseFloat(document.getElementById('minimum-size').value.trim());
      if (!mintAddress) {
        alert('Please enter a valid Mint Address.');
        return;
      }

      const url = `/api/fetch-chart?mint=${mintAddress}&minimumSize=${minimumSize * 1e9}`;
      console.log('Fetching data for Mint Address:', mintAddress);

      try {
        const response = await fetch(url);
        if (!response.ok) {
          throw new Error(`API request failed with status ${response.status}`);
        }

        const data = await response.json();

        chartData = data.map(trade => ({
          x: new Date(trade.timestamp * 1000),
          y: trade.sol_amount / trade.token_amount * 170 * 1000000000,
          isBuy: trade.is_buy,
          user: trade.user,
          solAmount: trade.sol_amount,
          tokenAmount: trade.token_amount,
          mint: trade.mint,
          timestamp: trade.timestamp
        }));

        // Split data into Buy and Sell datasets
        buyData = chartData.filter(d => d.isBuy);
        sellData = chartData.filter(d => !d.isBuy);

        updateChart();
        updateTable();
      } catch (error) {
        console.error('Error fetching data:', error);
        alert('Failed to load data. Please check the console for more details.');
      }
    }

    function updateChart() {
      if (chart) chart.destroy();

      chart = new Chart(ctx, {
        type: 'scatter',
        data: {
          datasets: [
            {
              label: 'Buy Trades',
              data: buyData,
              backgroundColor: 'rgba(76, 175, 80, 0.8)', // Green with opacity
              borderColor: 'rgba(76, 175, 80, 1)', // Solid green border
              borderWidth: 1,
              pointRadius: 5,
              pointHoverRadius: 7,
              pointStyle: 'circle',
            },
            {
              label: 'Sell Trades',
              data: sellData,
              backgroundColor: 'rgba(244, 67, 54, 0.8)', // Red with opacity
              borderColor: 'rgba(244, 67, 54, 1)', // Solid red border
              borderWidth: 1,
              pointRadius: 5,
              pointHoverRadius: 7,
              pointStyle: 'circle',
            }
          ]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            x: {
              type: 'time',
              title: { display: true, text: 'Time', color: '#e0e0e0' },
              ticks: { color: '#e0e0e0' },
              grid: { color: '#333333' }
              // Removed the adapters.locale configuration to fix the error
            },
            y: {
              title: { display: true, text: 'Price (Token/SOL)', color: '#e0e0e0' },
              ticks: { color: '#e0e0e0' },
              grid: { color: '#333333' }
            }
          },
          plugins: {
            tooltip: {
              backgroundColor: '#2c2c2c', // Dark tooltip background
              titleColor: '#ffffff', // White tooltip title
              bodyColor: '#e0e0e0', // Light tooltip body
              callbacks: {
                label: context => {
                  const data = context.raw;
                  return `Price: ${data.y.toFixed(6)}`;
                }
              }
            },
            legend: {
              labels: {
                color: '#e0e0e0' // Light legend text
              }
            }
          },
          // Prevent the chart from animating during updates for better performance
          animation: false,
        }
      });
    }

    function updateTable() {
      const tbody = document.querySelector('#data-table tbody');
      tbody.innerHTML = '';

      chartData.forEach((trade, index) => {
        const row = document.createElement('tr');

        // Determine datasetIndex and dataIndex based on trade type
        const datasetIndex = trade.isBuy ? 0 : 1;
        const dataIndex = trade.isBuy ? buyData.indexOf(trade) : sellData.indexOf(trade);

        // Assign data attributes for later reference
        row.dataset.datasetIndex = datasetIndex;
        row.dataset.dataIndex = dataIndex;

        // Add event listeners for hover highlighting
        row.addEventListener('mouseenter', () => highlightPoint(datasetIndex, dataIndex));
        row.addEventListener('mouseleave', () => resetHighlight());

        // Add event listener for click selection
        row.addEventListener('click', () => selectRow(row, datasetIndex, dataIndex));

        row.innerHTML = `
          <td>[<a href="https://gmgn.ai/sol/address/${trade.user}" target="_blank" style="color: lightgray;">GMGN</a>] |[<a href="https://solscan.io/account/${trade.user}#defiactivities" target="_blank" style="color: lightgray;">Solscan</a>]</td>
          <td>${trade.user}</td>
          <td class="${trade.isBuy ? 'Buy' : 'Sell'}">${trade.isBuy ? 'Buy' : 'Sell'}</td>
          <td>${(trade.solAmount / 1e9).toFixed(2)}</td>
          <td>${(trade.tokenAmount / 1e9).toFixed(2)}</td>
          <td>${new Date(trade.timestamp * 1000).toLocaleString()}</td>

        `;

        tbody.appendChild(row);
      });
    }

    function highlightPoint(datasetIndex, dataIndex) {
      // Highlight on hover (temporary)
      chart.setActiveElements([{ datasetIndex, index: dataIndex }]);
      chart.tooltip.setActiveElements([{ datasetIndex, index: dataIndex }], {});

      // Update the point radius for visual emphasis
      updatePointStyles(datasetIndex, dataIndex, true);

      chart.update();
    }

    function resetHighlight() {
      // Remove hover highlighting
      chart.setActiveElements([]);
      chart.tooltip.setActiveElements([], {});

      // Reset point styles
      if (selectedPoint) {
        updatePointStyles(selectedPoint.datasetIndex, selectedPoint.dataIndex, false);
        // Re-apply the selected point style
        updatePointStyles(selectedPoint.datasetIndex, selectedPoint.dataIndex, true, true);
      } else {
        // Reset all points to default if no selection
        resetAllPointStyles();
      }

      chart.update();
    }

    function selectRow(row, datasetIndex, dataIndex) {
      // Deselect previously selected row
      if (selectedRow) {
        selectedRow.classList.remove('selected');
        // Reset previously selected point's style
        updatePointStyles(selectedPoint.datasetIndex, selectedPoint.dataIndex, false);
      }

      // If clicking the already selected row, deselect it
      if (selectedRow === row) {
        chart.setActiveElements([]);
        chart.tooltip.setActiveElements([], {});
        resetAllPointStyles();
        chart.update();
        selectedRow = null;
        selectedPoint = null;
        return;
      }

      // Select the new row
      row.classList.add('selected');
      selectedRow = row;
      selectedPoint = { datasetIndex, dataIndex };

      // Highlight the corresponding chart point
      chart.setActiveElements([{ datasetIndex, index: dataIndex }]);
      chart.tooltip.setActiveElements([{ datasetIndex, index: dataIndex }], {});

      // Update the point style for the selected point
      updatePointStyles(datasetIndex, dataIndex, true, true);

      chart.update();
    }

    function updatePointStyles(datasetIndex, dataIndex, highlight, selected = false) {
      const dataset = chart.data.datasets[datasetIndex];
      const point = dataset.data[dataIndex];

      if (highlight && !selected) {
        // On hover: increase the radius slightly and add a border
        dataset.pointRadius = dataset.data.map((_, idx) => idx === dataIndex ? 8 : 5);
        dataset.pointBorderWidth = dataset.data.map((_, idx) => idx === dataIndex ? 2 : 1);
        dataset.pointBorderColor = dataset.data.map((_, idx) => idx === dataIndex ? '#ffffff' : dataset.borderColor);
      } else if (highlight && selected) {
        // On selection: make the point significantly larger and add a distinct border
        dataset.pointRadius = dataset.data.map((_, idx) => idx === dataIndex ? 12 : 5);
        dataset.pointBorderWidth = dataset.data.map((_, idx) => idx === dataIndex ? 3 : 1);
        dataset.pointBorderColor = dataset.data.map((_, idx) => idx === dataIndex ? '#ffeb3b' : dataset.borderColor);
      } else {
        // Reset to default
        resetAllPointStyles();
      }
    }

    function resetAllPointStyles() {
      chart.data.datasets.forEach(dataset => {
        dataset.pointRadius = 5;
        dataset.pointBorderWidth = 1;
        // Reset border color to original (assuming solid colors)
        if (dataset.label === 'Buy Trades') {
          dataset.pointBorderColor = dataset.data.map(() => 'rgba(76, 175, 80, 1)'); // Green
        } else if (dataset.label === 'Sell Trades') {
          dataset.pointBorderColor = dataset.data.map(() => 'rgba(244, 67, 54, 1)'); // Red
        }
      });
    }

    // Ensure the table container is scrollable without affecting the chart visibility
    // This is already handled by CSS with the .table-container class
  </script>

</body>
</html>
