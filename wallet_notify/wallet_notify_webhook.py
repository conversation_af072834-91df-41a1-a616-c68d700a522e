#!/usr/bin/env python
from flask import Flask, request, jsonify
import json
import pika,os

from mysql.connector import pooling
import modules.utils as utils
from modules.config import *
import modules.db as db
import modules.bot as bot
import modules.config
import modules.decoders.pump as pump
import logging


MYSQL_SERVER="pumpfun.mooo.com"
MYSQL_PORT=3306
MYSQL_USER="pump2"
MYSQL_PASSWORD="pump2"
MYSQL_DB="pump"
MYSQL_POOL="mypool"
MYSQL_POOL_SIZE="10"


pool = pooling.MySQLConnectionPool(
    pool_name=MYSQL_POOL, 
    user=MYSQL_USER,
    password=MYSQL_PASSWORD,
    host=MYSQL_SERVER,
    db=MYSQL_DB)

modules.config.pool=pool

RABBITMQ_SERVER="pumpfun.mooo.com"
increment=0



app = Flask(__name__)
log = logging.getLogger('werkzeug')
log.disabled = True

# Optionally set up your own logger
logging.basicConfig(level=logging.INFO)

# Example usage
file_path = 'last_tokens.txt'  # Path to your file

def send_message(message,queue="trades"):


    credentials = pika.PlainCredentials('pump', 'pump2pump')
    parameters = pika.ConnectionParameters(RABBITMQ_SERVER, 5672, '/', credentials)
    connection = pika.BlockingConnection(parameters)
    channel = connection.channel()

    # Create a queue named 'task_queue'
    channel.queue_declare(queue=queue, durable=True)

    # Publish the message to the queue
    channel.basic_publish(
        exchange='',
        routing_key=queue,
        body=message,
        properties=pika.BasicProperties(
            delivery_mode=2,  # Make message persistent
        ))
    print(" [x] Sent %r" % message)


def get_multi_trades(data):
    #print(data)
    #print(json.dumps(data))
    send_message(json.dumps(data))

def check_past_tokens(file_path, word,numlines = -5):
    if not os.path.exists(file_path):
        with open(file_path, 'w'):
            pass


    with open(file_path, 'r') as file:
        lines = file.readlines()  # Read all lines from the file
    
    # Get the last 5 lines
    last_lines = lines[numlines:]
    
    # Check if the word exists in any of the last 5 lines
    for line in last_lines:
        if word in line:
            return True
    
    return False




# Webhook endpoint
@app.route('/webhook', methods=['POST'])
def helius_webhook():
    # try:
    # Get JSON data from the incoming request
    data = request.json



    #dd='[{"accountData": [{"account": "8deJ9xeUvXSJwicYptA9mHsU2rN2pDx37KWzkDkEXhU6", "nativeBalanceChange": -**********, "tokenBalanceChanges": []}, {"account": "nJSpLaePAakKrRWyixMLGEZokjxmS3A72cpevnP2H4A", "nativeBalanceChange": 0, "tokenBalanceChanges": []}, {"account": "4FwgpK2B44vNLxFxu27VWfmBE97DJyZ7ai1WEoXy7qWx", "nativeBalanceChange": 2039280, "tokenBalanceChanges": [{"mint": "9b6Z3tQeEKYRkwhRez8GApEnActyQYUARnYouGEFpump", "rawTokenAmount": {"decimals": 6, "tokenAmount": "*********547262"}, "tokenAccount": "4FwgpK2B44vNLxFxu27VWfmBE97DJyZ7ai1WEoXy7qWx", "userAccount": "8deJ9xeUvXSJwicYptA9mHsU2rN2pDx37KWzkDkEXhU6"}]}, {"account": "********************************************", "nativeBalanceChange": 0, "tokenBalanceChanges": []}, {"account": "9ccE8cTsCY3qwUvUNpoFxubihS7NPYayYcd7X9EYe6sC", "nativeBalanceChange": 0, "tokenBalanceChanges": []}, {"account": "FZU6WSRkymdAtCsqTChWbtvXqCEP9rpzgov971xinweE", "nativeBalanceChange": 0, "tokenBalanceChanges": []}, {"account": "a4J8ZdStt6WiZiad9LQtPmL94xufichgoH38TP5LwoV", "nativeBalanceChange": 49********, "tokenBalanceChanges": [{"mint": "So11111111111111111111111111111111111111112", "rawTokenAmount": {"decimals": 9, "tokenAmount": "49********"}, "tokenAccount": "a4J8ZdStt6WiZiad9LQtPmL94xufichgoH38TP5LwoV", "userAccount": "5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1"}]}, {"account": "4M2tfFagJnoqAFxBV6GqNH4QG3a3z2pjPtBGLmVJCzSb", "nativeBalanceChange": 0, "tokenBalanceChanges": [{"mint": "9b6Z3tQeEKYRkwhRez8GApEnActyQYUARnYouGEFpump", "rawTokenAmount": {"decimals": 6, "tokenAmount": "-*********547262"}, "tokenAccount": "4M2tfFagJnoqAFxBV6GqNH4QG3a3z2pjPtBGLmVJCzSb", "userAccount": "5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1"}]}, {"account": "2hGve1xTcxzNSLwwGroycT5AYsz4GBknbjzt1geZwGs7", "nativeBalanceChange": 0, "tokenBalanceChanges": []}, {"account": "5QYzwpkQwTgQXHmLDMCA9FYdG68kXBRBC2PEG9YzMZur", "nativeBalanceChange": 0, "tokenBalanceChanges": []}, {"account": "C5E71Eq5FvmV4wEfjASzyg7S1zVAR5wEvHfBS8YFHGc6", "nativeBalanceChange": 0, "tokenBalanceChanges": []}, {"account": "EHiDfXSierLn2c9PeBJAxbNadtULuPc69UARCLCodReg", "nativeBalanceChange": 0, "tokenBalanceChanges": []}, {"account": "AAzn3KzMzTcKipqd5vDxADxDNrSfjynuhmJtjhanxRvo", "nativeBalanceChange": 0, "tokenBalanceChanges": []}, {"account": "8PsQnn3emFBm6XAvTUdaNG2xDxD4qEfKy5LYQUPScUxT", "nativeBalanceChange": 0, "tokenBalanceChanges": []}, {"account": "HWEoBxYs7ssKuudEjzjmpfJVX7Dvi7wescFsVx2L5yoY", "nativeBalanceChange": ********, "tokenBalanceChanges": []}, {"account": "ComputeBudget111111111111111111111111111111", "nativeBalanceChange": 0, "tokenBalanceChanges": []}, {"account": "11111111111111111111111111111111", "nativeBalanceChange": 0, "tokenBalanceChanges": []}, {"account": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA", "nativeBalanceChange": 0, "tokenBalanceChanges": []}, {"account": "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL", "nativeBalanceChange": 0, "tokenBalanceChanges": []}, {"account": "9b6Z3tQeEKYRkwhRez8GApEnActyQYUARnYouGEFpump", "nativeBalanceChange": 0, "tokenBalanceChanges": []}, {"account": "675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8", "nativeBalanceChange": 0, "tokenBalanceChanges": []}, {"account": "99y71uU1oL4Dm63bD4fjVCnG7JP4KXKRMqCmixHhZH6H", "nativeBalanceChange": 0, "tokenBalanceChanges": []}, {"account": "BSfD6SHZigAfDWSjzD5Q41jw8LmKwtmjskPH9XW1mrRW", "nativeBalanceChange": 0, "tokenBalanceChanges": []}, {"account": "AVUCZyuT35YSuj4RH7fwiyPu82Djn2Hfg7y2ND2XcnZH", "nativeBalanceChange": ********, "tokenBalanceChanges": []}, {"account": "So11111111111111111111111111111111111111112", "nativeBalanceChange": 0, "tokenBalanceChanges": []}, {"account": "SysvarRent111111111111111111111111111111111", "nativeBalanceChange": 0, "tokenBalanceChanges": []}, {"account": "5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1", "nativeBalanceChange": 0, "tokenBalanceChanges": []}, {"account": "srmqPvymJeFKQ4zGQed1GFppgkRHL9kaELCbyksJtPX", "nativeBalanceChange": 0, "tokenBalanceChanges": []}], "description": "8deJ9xeUvXSJwicYptA9mHsU2rN2pDx37KWzkDkEXhU6 swapped 4.95 SOL for *********.********* 9b6Z3tQeEKYRkwhRez8GApEnActyQYUARnYouGEFpump", "events": {"swap": {"innerSwaps": [], "nativeFees": [], "nativeInput": {"account": "8deJ9xeUvXSJwicYptA9mHsU2rN2pDx37KWzkDkEXhU6", "amount": "49********"}, "nativeOutput": null, "tokenFees": [], "tokenInputs": [], "tokenOutputs": [{"mint": "9b6Z3tQeEKYRkwhRez8GApEnActyQYUARnYouGEFpump", "rawTokenAmount": {"decimals": 6, "tokenAmount": "*********547262"}, "tokenAccount": "4M2tfFagJnoqAFxBV6GqNH4QG3a3z2pjPtBGLmVJCzSb", "userAccount": "5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1"}]}}, "fee": ********, "feePayer": "8deJ9xeUvXSJwicYptA9mHsU2rN2pDx37KWzkDkEXhU6", "instructions": [{"accounts": [], "data": "LEJDE7", "innerInstructions": [], "programId": "ComputeBudget111111111111111111111111111111"}, {"accounts": [], "data": "3TijwetXP3QK", "innerInstructions": [], "programId": "ComputeBudget111111111111111111111111111111"}, {"accounts": ["8deJ9xeUvXSJwicYptA9mHsU2rN2pDx37KWzkDkEXhU6", "nJSpLaePAakKrRWyixMLGEZokjxmS3A72cpevnP2H4A"], "data": "3ipZWpDfY2voAEx3VHD1nPcpmWp2VLhX2r1HjGyc3H5FP6fxNmccmMTtpUBMQG6R7z8xZi1mGe7gtUm7LPMWHdWZkZ84ZXwLh8nVsStVJ8kWyzEHF6B2yrvLbe9S5F1KNssjdbAFXGA2TDjhP8KyB9Am8tXdDWD9TtrbGBgtY", "innerInstructions": [], "programId": "11111111111111111111111111111111"}, {"accounts": ["nJSpLaePAakKrRWyixMLGEZokjxmS3A72cpevnP2H4A", "So11111111111111111111111111111111111111112", "8deJ9xeUvXSJwicYptA9mHsU2rN2pDx37KWzkDkEXhU6", "SysvarRent111111111111111111111111111111111"], "data": "2", "innerInstructions": [], "programId": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"}, {"accounts": ["8deJ9xeUvXSJwicYptA9mHsU2rN2pDx37KWzkDkEXhU6", "4FwgpK2B44vNLxFxu27VWfmBE97DJyZ7ai1WEoXy7qWx", "8deJ9xeUvXSJwicYptA9mHsU2rN2pDx37KWzkDkEXhU6", "9b6Z3tQeEKYRkwhRez8GApEnActyQYUARnYouGEFpump", "11111111111111111111111111111111", "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"], "data": "2", "innerInstructions": [{"accounts": ["9b6Z3tQeEKYRkwhRez8GApEnActyQYUARnYouGEFpump"], "data": "84eT", "programId": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"}, {"accounts": ["8deJ9xeUvXSJwicYptA9mHsU2rN2pDx37KWzkDkEXhU6", "4FwgpK2B44vNLxFxu27VWfmBE97DJyZ7ai1WEoXy7qWx"], "data": "11119os1e9qSs2u7TsThXqkBSRVFxhmYaFKFZ1waB2X7armDmvK3p5GmLdUxYdg3h7QSrL", "programId": "11111111111111111111111111111111"}, {"accounts": ["4FwgpK2B44vNLxFxu27VWfmBE97DJyZ7ai1WEoXy7qWx"], "data": "P", "programId": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"}, {"accounts": ["4FwgpK2B44vNLxFxu27VWfmBE97DJyZ7ai1WEoXy7qWx", "9b6Z3tQeEKYRkwhRez8GApEnActyQYUARnYouGEFpump"], "data": "6UmLy6ABHDNi9DCi4v3MVFGJrXjwrDyDjpEgrEoS3pqu4", "programId": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"}], "programId": "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL"}, {"accounts": ["TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA", "********************************************", "5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1", "9ccE8cTsCY3qwUvUNpoFxubihS7NPYayYcd7X9EYe6sC", "FZU6WSRkymdAtCsqTChWbtvXqCEP9rpzgov971xinweE", "a4J8ZdStt6WiZiad9LQtPmL94xufichgoH38TP5LwoV", "4M2tfFagJnoqAFxBV6GqNH4QG3a3z2pjPtBGLmVJCzSb", "srmqPvymJeFKQ4zGQed1GFppgkRHL9kaELCbyksJtPX", "2hGve1xTcxzNSLwwGroycT5AYsz4GBknbjzt1geZwGs7", "5QYzwpkQwTgQXHmLDMCA9FYdG68kXBRBC2PEG9YzMZur", "C5E71Eq5FvmV4wEfjASzyg7S1zVAR5wEvHfBS8YFHGc6", "EHiDfXSierLn2c9PeBJAxbNadtULuPc69UARCLCodReg", "AAzn3KzMzTcKipqd5vDxADxDNrSfjynuhmJtjhanxRvo", "8PsQnn3emFBm6XAvTUdaNG2xDxD4qEfKy5LYQUPScUxT", "99y71uU1oL4Dm63bD4fjVCnG7JP4KXKRMqCmixHhZH6H", "nJSpLaePAakKrRWyixMLGEZokjxmS3A72cpevnP2H4A", "4FwgpK2B44vNLxFxu27VWfmBE97DJyZ7ai1WEoXy7qWx", "8deJ9xeUvXSJwicYptA9mHsU2rN2pDx37KWzkDkEXhU6"], "data": "6BKE5M3EVvkznzVk3jkh6co", "innerInstructions": [{"accounts": ["nJSpLaePAakKrRWyixMLGEZokjxmS3A72cpevnP2H4A", "a4J8ZdStt6WiZiad9LQtPmL94xufichgoH38TP5LwoV", "8deJ9xeUvXSJwicYptA9mHsU2rN2pDx37KWzkDkEXhU6"], "data": "3asMzWFJqWXH", "programId": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"}, {"accounts": ["4M2tfFagJnoqAFxBV6GqNH4QG3a3z2pjPtBGLmVJCzSb", "4FwgpK2B44vNLxFxu27VWfmBE97DJyZ7ai1WEoXy7qWx", "5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1"], "data": "3aZoFY7qR5vo", "programId": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"}], "programId": "675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8"}, {"accounts": ["8deJ9xeUvXSJwicYptA9mHsU2rN2pDx37KWzkDkEXhU6", "nJSpLaePAakKrRWyixMLGEZokjxmS3A72cpevnP2H4A", "AVUCZyuT35YSuj4RH7fwiyPu82Djn2Hfg7y2ND2XcnZH", "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA", "11111111111111111111111111111111"], "data": "RR93MXKVQPmHayn8CruTsDCE8owLY5azGF", "innerInstructions": [{"accounts": ["nJSpLaePAakKrRWyixMLGEZokjxmS3A72cpevnP2H4A", "8deJ9xeUvXSJwicYptA9mHsU2rN2pDx37KWzkDkEXhU6", "8deJ9xeUvXSJwicYptA9mHsU2rN2pDx37KWzkDkEXhU6"], "data": "A", "programId": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"}, {"accounts": ["8deJ9xeUvXSJwicYptA9mHsU2rN2pDx37KWzkDkEXhU6", "AVUCZyuT35YSuj4RH7fwiyPu82Djn2Hfg7y2ND2XcnZH"], "data": "3Bxs4NRZ15a54oAf", "programId": "11111111111111111111111111111111"}], "programId": "BSfD6SHZigAfDWSjzD5Q41jw8LmKwtmjskPH9XW1mrRW"}, {"accounts": ["8deJ9xeUvXSJwicYptA9mHsU2rN2pDx37KWzkDkEXhU6", "HWEoBxYs7ssKuudEjzjmpfJVX7Dvi7wescFsVx2L5yoY"], "data": "3Bxs4NRZ15a54oAf", "innerInstructions": [], "programId": "11111111111111111111111111111111"}], "nativeTransfers": [{"amount": ********, "fromUserAccount": "8deJ9xeUvXSJwicYptA9mHsU2rN2pDx37KWzkDkEXhU6", "toUserAccount": "HWEoBxYs7ssKuudEjzjmpfJVX7Dvi7wescFsVx2L5yoY"}, {"amount": 2039280, "fromUserAccount": "8deJ9xeUvXSJwicYptA9mHsU2rN2pDx37KWzkDkEXhU6", "toUserAccount": "4FwgpK2B44vNLxFxu27VWfmBE97DJyZ7ai1WEoXy7qWx"}, {"amount": ********, "fromUserAccount": "8deJ9xeUvXSJwicYptA9mHsU2rN2pDx37KWzkDkEXhU6", "toUserAccount": "AVUCZyuT35YSuj4RH7fwiyPu82Djn2Hfg7y2ND2XcnZH"}], "signature": "3aqF3BK1ZbNkMA7Kw1pE4TmWQAnBLiFbqvdW79trEnhkmLwmLwt7Zi7zV4tR5JjhiQ7HzzdfSLcnGJZiavc5obhW", "slot": *********, "source": "RAYDIUM", "timestamp": **********, "tokenTransfers": [{"fromTokenAccount": "nJSpLaePAakKrRWyixMLGEZokjxmS3A72cpevnP2H4A", "fromUserAccount": "8deJ9xeUvXSJwicYptA9mHsU2rN2pDx37KWzkDkEXhU6", "mint": "So11111111111111111111111111111111111111112", "toTokenAccount": "a4J8ZdStt6WiZiad9LQtPmL94xufichgoH38TP5LwoV", "toUserAccount": "5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1", "tokenAmount": 4.95, "tokenStandard": "Fungible"}, {"fromTokenAccount": "4M2tfFagJnoqAFxBV6GqNH4QG3a3z2pjPtBGLmVJCzSb", "fromUserAccount": "5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1", "mint": "9b6Z3tQeEKYRkwhRez8GApEnActyQYUARnYouGEFpump", "toTokenAccount": "4FwgpK2B44vNLxFxu27VWfmBE97DJyZ7ai1WEoXy7qWx", "toUserAccount": "8deJ9xeUvXSJwicYptA9mHsU2rN2pDx37KWzkDkEXhU6", "tokenAmount": *********.547262, "tokenStandard": "Fungible"}], "transactionError": null, "type": "SWAP"}]'
    
    #sell
    #dd='[{"accountData": [{"account": "AVAZvHLR2PcWpDf8BXY4rVxNHYRBytycHkcB5z5QNXYm", "nativeBalanceChange": *********, "tokenBalanceChanges": []}, {"account": "7deZLi1qaYWtihEo13MrERTwMEaNPoAQfr7DcNRovXH1", "nativeBalanceChange": 0, "tokenBalanceChanges": []}, {"account": "CUQjqGeJ7Y5jXhCe5BYZg21FKxmM1wBRJjwrhZ4q213A", "nativeBalanceChange": 0, "tokenBalanceChanges": [{"mint": "522MXsrq2fPp8gpDA6uDpJBZtYeUR1Wt8hQgcnyipump", "rawTokenAmount": {"decimals": 6, "tokenAmount": "-********901277"}, "tokenAccount": "CUQjqGeJ7Y5jXhCe5BYZg21FKxmM1wBRJjwrhZ4q213A", "userAccount": "AVAZvHLR2PcWpDf8BXY4rVxNHYRBytycHkcB5z5QNXYm"}]}, {"account": "11111111111111111111111111111111", "nativeBalanceChange": 0, "tokenBalanceChanges": []}, {"account": "ComputeBudget111111111111111111111111111111", "nativeBalanceChange": 0, "tokenBalanceChanges": []}, {"account": "JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4", "nativeBalanceChange": 0, "tokenBalanceChanges": []}, {"account": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA", "nativeBalanceChange": 0, "tokenBalanceChanges": []}, {"account": "D8cy77BBepLMngZx6ZukaTff5hCt1HrWyKk3Hnd9oitf", "nativeBalanceChange": 0, "tokenBalanceChanges": []}, {"account": "JRHmiebVsMeTU4FhjkK7s3HWUFq5qfU24fWBujNGvaW", "nativeBalanceChange": 0, "tokenBalanceChanges": [{"mint": "522MXsrq2fPp8gpDA6uDpJBZtYeUR1Wt8hQgcnyipump", "rawTokenAmount": {"decimals": 6, "tokenAmount": "********901277"}, "tokenAccount": "JRHmiebVsMeTU4FhjkK7s3HWUFq5qfU24fWBujNGvaW", "userAccount": "5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1"}]}, {"account": "9F2qWYeQH4xveLnEXirZnGjSmZeRD64UmgCFxkkSfQJH", "nativeBalanceChange": -*********, "tokenBalanceChanges": [{"mint": "So11111111111111111111111111111111111111112", "rawTokenAmount": {"decimals": 9, "tokenAmount": "-*********"}, "tokenAccount": "9F2qWYeQH4xveLnEXirZnGjSmZeRD64UmgCFxkkSfQJH", "userAccount": "5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1"}]}, {"account": "Cm3GxvN9wj7BnPJs3AzvrPPhhHyBHSDV3EAy4RrZYCcA", "nativeBalanceChange": 0, "tokenBalanceChanges": []}, {"account": "So11111111111111111111111111111111111111112", "nativeBalanceChange": 0, "tokenBalanceChanges": []}, {"account": "5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1", "nativeBalanceChange": 0, "tokenBalanceChanges": []}, {"account": "675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8", "nativeBalanceChange": 0, "tokenBalanceChanges": []}], "description": "AVAZvHLR2PcWpDf8BXY4rVxNHYRBytycHkcB5z5QNXYm swapped ********.901277 Dewy for 0.******** SOL", "events": {"swap": {"innerSwaps": [{"nativeFees": [], "programInfo": {"account": "675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8", "instructionName": "SwapEvent", "programName": "RAYDIUM_LIQUIDITY_POOL_V4", "source": "RAYDIUM"}, "tokenFees": [], "tokenInputs": [{"fromTokenAccount": "CUQjqGeJ7Y5jXhCe5BYZg21FKxmM1wBRJjwrhZ4q213A", "fromUserAccount": "AVAZvHLR2PcWpDf8BXY4rVxNHYRBytycHkcB5z5QNXYm", "mint": "522MXsrq2fPp8gpDA6uDpJBZtYeUR1Wt8hQgcnyipump", "toTokenAccount": "JRHmiebVsMeTU4FhjkK7s3HWUFq5qfU24fWBujNGvaW", "toUserAccount": "5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1", "tokenAmount": ********.901277, "tokenStandard": "Fungible"}], "tokenOutputs": [{"fromTokenAccount": "9F2qWYeQH4xveLnEXirZnGjSmZeRD64UmgCFxkkSfQJH", "fromUserAccount": "5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1", "mint": "So11111111111111111111111111111111111111112", "toTokenAccount": "7deZLi1qaYWtihEo13MrERTwMEaNPoAQfr7DcNRovXH1", "toUserAccount": "AVAZvHLR2PcWpDf8BXY4rVxNHYRBytycHkcB5z5QNXYm", "tokenAmount": 0.********, "tokenStandard": "Fungible"}]}], "nativeFees": [], "nativeInput": null, "nativeOutput": {"account": "AVAZvHLR2PcWpDf8BXY4rVxNHYRBytycHkcB5z5QNXYm", "amount": "*********"}, "tokenFees": [], "tokenInputs": [{"mint": "522MXsrq2fPp8gpDA6uDpJBZtYeUR1Wt8hQgcnyipump", "rawTokenAmount": {"decimals": 6, "tokenAmount": "********901277"}, "tokenAccount": "CUQjqGeJ7Y5jXhCe5BYZg21FKxmM1wBRJjwrhZ4q213A", "userAccount": "AVAZvHLR2PcWpDf8BXY4rVxNHYRBytycHkcB5z5QNXYm"}], "tokenOutputs": []}}, "fee": 5000, "feePayer": "AVAZvHLR2PcWpDf8BXY4rVxNHYRBytycHkcB5z5QNXYm", "instructions": [{"accounts": [], "data": "LBAA95", "innerInstructions": [], "programId": "ComputeBudget111111111111111111111111111111"}, {"accounts": [], "data": "3DTZbgwsozUF", "innerInstructions": [], "programId": "ComputeBudget111111111111111111111111111111"}, {"accounts": ["7deZLi1qaYWtihEo13MrERTwMEaNPoAQfr7DcNRovXH1", "AVAZvHLR2PcWpDf8BXY4rVxNHYRBytycHkcB5z5QNXYm", "So11111111111111111111111111111111111111112", "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA", "11111111111111111111111111111111"], "data": "2tDqDdUmhLW1t", "innerInstructions": [{"accounts": ["AVAZvHLR2PcWpDf8BXY4rVxNHYRBytycHkcB5z5QNXYm", "7deZLi1qaYWtihEo13MrERTwMEaNPoAQfr7DcNRovXH1"], "data": "11119os1e9qSs2u7TsThXqkBSRVFxhmYaFKFZ1waB2X7armDmvK3p5GmLdUxYdg3h7QSrL", "programId": "11111111111111111111111111111111"}, {"accounts": ["7deZLi1qaYWtihEo13MrERTwMEaNPoAQfr7DcNRovXH1", "So11111111111111111111111111111111111111112"], "data": "6WcsErUsDKEtM5hkeGgjQLUPknS11PeFJzfn2aZmDffyj", "programId": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"}], "programId": "JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4"}, {"accounts": ["TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA", "AVAZvHLR2PcWpDf8BXY4rVxNHYRBytycHkcB5z5QNXYm", "CUQjqGeJ7Y5jXhCe5BYZg21FKxmM1wBRJjwrhZ4q213A", "7deZLi1qaYWtihEo13MrERTwMEaNPoAQfr7DcNRovXH1", "JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4", "So11111111111111111111111111111111111111112", "JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4", "D8cy77BBepLMngZx6ZukaTff5hCt1HrWyKk3Hnd9oitf", "JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4", "675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8", "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA", "Cm3GxvN9wj7BnPJs3AzvrPPhhHyBHSDV3EAy4RrZYCcA", "5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1", "Cm3GxvN9wj7BnPJs3AzvrPPhhHyBHSDV3EAy4RrZYCcA", "9F2qWYeQH4xveLnEXirZnGjSmZeRD64UmgCFxkkSfQJH", "JRHmiebVsMeTU4FhjkK7s3HWUFq5qfU24fWBujNGvaW", "Cm3GxvN9wj7BnPJs3AzvrPPhhHyBHSDV3EAy4RrZYCcA", "Cm3GxvN9wj7BnPJs3AzvrPPhhHyBHSDV3EAy4RrZYCcA", "Cm3GxvN9wj7BnPJs3AzvrPPhhHyBHSDV3EAy4RrZYCcA", "Cm3GxvN9wj7BnPJs3AzvrPPhhHyBHSDV3EAy4RrZYCcA", "Cm3GxvN9wj7BnPJs3AzvrPPhhHyBHSDV3EAy4RrZYCcA", "Cm3GxvN9wj7BnPJs3AzvrPPhhHyBHSDV3EAy4RrZYCcA", "Cm3GxvN9wj7BnPJs3AzvrPPhhHyBHSDV3EAy4RrZYCcA", "Cm3GxvN9wj7BnPJs3AzvrPPhhHyBHSDV3EAy4RrZYCcA", "CUQjqGeJ7Y5jXhCe5BYZg21FKxmM1wBRJjwrhZ4q213A", "7deZLi1qaYWtihEo13MrERTwMEaNPoAQfr7DcNRovXH1", "AVAZvHLR2PcWpDf8BXY4rVxNHYRBytycHkcB5z5QNXYm"], "data": "PrpFmsY4d26dKbdKMAXs4o9ACSdDV65EvBsJVvCUESmNHa2K", "innerInstructions": [{"accounts": ["TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA", "Cm3GxvN9wj7BnPJs3AzvrPPhhHyBHSDV3EAy4RrZYCcA", "5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1", "Cm3GxvN9wj7BnPJs3AzvrPPhhHyBHSDV3EAy4RrZYCcA", "9F2qWYeQH4xveLnEXirZnGjSmZeRD64UmgCFxkkSfQJH", "JRHmiebVsMeTU4FhjkK7s3HWUFq5qfU24fWBujNGvaW", "Cm3GxvN9wj7BnPJs3AzvrPPhhHyBHSDV3EAy4RrZYCcA", "Cm3GxvN9wj7BnPJs3AzvrPPhhHyBHSDV3EAy4RrZYCcA", "Cm3GxvN9wj7BnPJs3AzvrPPhhHyBHSDV3EAy4RrZYCcA", "Cm3GxvN9wj7BnPJs3AzvrPPhhHyBHSDV3EAy4RrZYCcA", "Cm3GxvN9wj7BnPJs3AzvrPPhhHyBHSDV3EAy4RrZYCcA", "Cm3GxvN9wj7BnPJs3AzvrPPhhHyBHSDV3EAy4RrZYCcA", "Cm3GxvN9wj7BnPJs3AzvrPPhhHyBHSDV3EAy4RrZYCcA", "Cm3GxvN9wj7BnPJs3AzvrPPhhHyBHSDV3EAy4RrZYCcA", "CUQjqGeJ7Y5jXhCe5BYZg21FKxmM1wBRJjwrhZ4q213A", "7deZLi1qaYWtihEo13MrERTwMEaNPoAQfr7DcNRovXH1", "AVAZvHLR2PcWpDf8BXY4rVxNHYRBytycHkcB5z5QNXYm"], "data": "6ExevG4Eoy6iEpJvDjKetd5", "programId": "675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8"}, {"accounts": ["CUQjqGeJ7Y5jXhCe5BYZg21FKxmM1wBRJjwrhZ4q213A", "JRHmiebVsMeTU4FhjkK7s3HWUFq5qfU24fWBujNGvaW", "AVAZvHLR2PcWpDf8BXY4rVxNHYRBytycHkcB5z5QNXYm"], "data": "3fokN9HB85n7", "programId": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"}, {"accounts": ["9F2qWYeQH4xveLnEXirZnGjSmZeRD64UmgCFxkkSfQJH", "7deZLi1qaYWtihEo13MrERTwMEaNPoAQfr7DcNRovXH1", "5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1"], "data": "3izK4C2LdMiw", "programId": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"}, {"accounts": ["D8cy77BBepLMngZx6ZukaTff5hCt1HrWyKk3Hnd9oitf"], "data": "QMqFu4fYGGeUEysFnenhAvR83g86EDDNxzUskfkWKYCBPWe1hqgD6jgKAXr6aYoEQbYMstn6YdSUFV4kbEjgPXTv1npKmF2pNmjC3tSb4mRu9eMWm9XGW2yNruA47raXX7MSzu6fimVQSYjeodZecVaA3c3yjGtx3LKwpST4s2sLij1", "programId": "JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4"}], "programId": "JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4"}, {"accounts": ["7deZLi1qaYWtihEo13MrERTwMEaNPoAQfr7DcNRovXH1", "AVAZvHLR2PcWpDf8BXY4rVxNHYRBytycHkcB5z5QNXYm", "AVAZvHLR2PcWpDf8BXY4rVxNHYRBytycHkcB5z5QNXYm"], "data": "A", "innerInstructions": [], "programId": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"}], "nativeTransfers": [{"amount": 2039280, "fromUserAccount": "AVAZvHLR2PcWpDf8BXY4rVxNHYRBytycHkcB5z5QNXYm", "toUserAccount": "7deZLi1qaYWtihEo13MrERTwMEaNPoAQfr7DcNRovXH1"}, {"amount": *********, "fromUserAccount": "7deZLi1qaYWtihEo13MrERTwMEaNPoAQfr7DcNRovXH1", "toUserAccount": "AVAZvHLR2PcWpDf8BXY4rVxNHYRBytycHkcB5z5QNXYm"}], "signature": "2a5w9XevETf9mrCrQYRVAVuEQ9P2uHhY76fj3yEpi3cjWhLiUCNCauHFpbi2XQ1EPxFNVMGitYnMEmki8s43F5B2", "slot": *********, "source": "JUPITER", "timestamp": **********, "tokenTransfers": [{"fromTokenAccount": "CUQjqGeJ7Y5jXhCe5BYZg21FKxmM1wBRJjwrhZ4q213A", "fromUserAccount": "AVAZvHLR2PcWpDf8BXY4rVxNHYRBytycHkcB5z5QNXYm", "mint": "522MXsrq2fPp8gpDA6uDpJBZtYeUR1Wt8hQgcnyipump", "toTokenAccount": "JRHmiebVsMeTU4FhjkK7s3HWUFq5qfU24fWBujNGvaW", "toUserAccount": "5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1", "tokenAmount": ********.901277, "tokenStandard": "Fungible"}, {"fromTokenAccount": "9F2qWYeQH4xveLnEXirZnGjSmZeRD64UmgCFxkkSfQJH", "fromUserAccount": "5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1", "mint": "So11111111111111111111111111111111111111112", "toTokenAccount": "7deZLi1qaYWtihEo13MrERTwMEaNPoAQfr7DcNRovXH1", "toUserAccount": "AVAZvHLR2PcWpDf8BXY4rVxNHYRBytycHkcB5z5QNXYm", "tokenAmount": 0.********, "tokenStandard": "Fungible"}], "transactionError": null, "type": "SWAP"}]'
    #data=json.loads(dd)




    description = data[0]["description"]

    if "multiple" in description or "transferred 0.00" in description: 
        return jsonify({"status": "success"}), 200

    f = open(f"data/{data[0]["signature"]}", "w")
    f.write(json.dumps(data))
    f.close()

    sig=data[0]["signature"]
    #print(f"sig: {sig}")
    type=data[0]["type"]
    source=data[0]["source"]
    timestamp=data[0]["timestamp"] 
    slot=data[0]["slot"]
    owner = data[0]["feePayer"]
    account_info=db.get_account_name(pool,owner)
    account_name=""
    swap="" 
    if account_info:
        if len(account_info)>0:
            account_name=account_info[0]
        else:
        #    print(f"account not found: {owner}")
            return jsonify({"status": "success"}), 200
    else:
       # print(f"account not found: {owner}")
        return jsonify({"status": "success"}), 200
    mint=""

    desc_arr=description.split(" ")
    #8deJ9xeUvXSJwicYptA9mHsU2rN2pDx37KWzkDkEXhU6 swapped 4836349.122304 HyJcz1VmtnvDq7XTXNqwXBdKjYe2ri8NM8x2eKpSpump for 0.******** SOL"     < sell
    # 8deJ9xeUvXSJwicYptA9mHsU2rN2pDx37KWzkDkEXhU6 swapped 4.95 SOL for *********.********* 9b6Z3tQeEKYRkwhRez8GApEnActyQYUARnYouGEFpump  < buy


    if type != "UNKNOWN":
        if "swapped" in description:
            if  "SOL for" in description:
                trade="buy"
                amount_sol=desc_arr[2]
                amount_token=desc_arr[5]
                #token=desc_arr[6]
            else:
                trade="sell"
                amount_sol=desc_arr[5]
                amount_token=desc_arr[2]
                #token=desc_arr[3]


            owner=desc_arr[0]


            mint=""
            for token in data[0]["tokenTransfers"]: 
                if token["fromTokenAccount"] != "" and token["mint"] != "So11111111111111111111111111111111111111112":
                    mint=token["mint"]    
            token_data={}
            token=mint  #  get api token name
            if "pump" not in token:
                return jsonify({"status": "success"}), 200
           
            if token == "8CW7uE3HxwHUdcLMVPyVpMu4dkTnoEob7h9YSu44pump":
                return jsonify({"status": "success"}), 200
            print(f"UNKNOWN:token_data: {token}  owner: {owner} sig: {sig}")
            token_data=bot.get_coin_data(token)
            if token_data is None:
                return jsonify({"status": "success"}), 200
            
            token_name=token_data["name"]
            token_url=token_data["image_uri"]
            token_description=token_data["description"]
            token_created=int(token_data["created_timestamp"]/1000) 
            token_symbol=token_data["symbol"]	# get from helius
            twitter=token_data["twitter"]# skip info
            website=token_data["website"] # skip info
            telegram=token_data["telegram"]	# skip info 
            mcap=token_data["usd_market_cap"] # get by calculation

            db.add_token_swap_name(owner,mint,trade,amount_token,amount_sol,0.0,timestamp,sig,slot,"pump",account_name,token_name,token_url,token_description,token_symbol,token_created,twitter,website,telegram,mcap)
            if trade=="sell":
                print(f"swap: {account_name} sold {amount_token} {token_name} for {amount_sol} SOL")
            else:
                print(f"swap: {account_name} bought {amount_token} {token_name} for {amount_sol} SOL")

            ddd=db.get_multi_account_swaps(pool,mint)
            get_multi_trades(ddd)

        elif "transferred" in description:
            for instruction in data[0]["instructions"]:
                for innerinstructions in instruction["innerInstructions"]:
                        if innerinstructions["programId"]=="6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P":
                            if innerinstructions["accounts"][0]=="Ce6TQqeHC9p8KetsN6JsjHK7UTZk7nasjjnr7XxXp9F1":
                                pump_decode=pump.decode(innerinstructions["data"])
                                p=pump_decode[1]

                
                                mint=p["mint"]
                                amount_token=p["token_amount"]/1e6
                                amount_sol=p["sol_amount"]/1e9
                                is_buy=p["is_buy"]
                                if is_buy:
                                    trade="buy"
                                else:
                                    trade="sell"
                                if amount_sol == 0 or amount_token == 0:
                                    return    

                        
                                token_data={}
                                token=mint  #  get api token name
                                print(f"Transferred :token_data: {token}")
                                token_data=bot.get_coin_data(token)
                                print(token_data)
                        
                                token_name=token_data["name"]
                                token_url=token_data["image_uri"]
                                token_description=token_data["description"]
                                token_created=int(token_data["created_timestamp"]/1000)
                                token_symbol=token_data["symbol"]	
                                twitter=token_data["twitter"]
                                website=token_data["website"]
                                telegram=token_data["telegram"]	
                                mcap=token_data["usd_market_cap"]
                    



                                db.add_token_swap_name(owner,mint,trade,amount_token,amount_sol,0.0,timestamp,sig,slot,"pump",account_name,token_name,token_url,token_description,token_symbol,token_created,twitter,website,telegram,mcap)
                                if trade=="sell":
                                    print(f"tx: {account_name} sold {amount_token} {token_name} for {amount_sol} SOL")
                                else:
                                    print(f"tx: {account_name} bought {amount_token} {token_name} for {amount_sol} SOL")
                                ddd=db.get_multi_account_swaps(pool,mint)
                                get_multi_trades(ddd)

    else:
        if source == "RAYDIUM":
            amount_sol=0
            amount_token=0
            mint=""
            if len(data[0]["tokenTransfers"])>1:
                for tokenTransfer in data[0]["tokenTransfers"]:
                    if tokenTransfer["fromTokenAccount"] != "" and tokenTransfer["mint"] != "So11111111111111111111111111111111111111112":
                        mint=tokenTransfer["mint"]
                
                for tokenTransfer in data[0]["tokenTransfers"]:
                    # buy tokne
                    if tokenTransfer["fromUserAccount"] == owner and  tokenTransfer["toUserAccount"] == "5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1" and tokenTransfer["mint"] == "So11111111111111111111111111111111111111112":
                        trade="buy"
                        amount_sol=tokenTransfer["tokenAmount"]   
                    elif tokenTransfer["fromUserAccount"] == "5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1" and tokenTransfer["toUserAccount"] == owner and tokenTransfer["mint"] == mint:
                        trade="buy" 
                        amount_token=tokenTransfer["tokenAmount"]
                    # sell token
                    elif tokenTransfer["fromUserAccount"] == owner and  tokenTransfer["toUserAccount"] == "5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1" and tokenTransfer["mint"] == mint:
                        trade="sell"
                        amount_token=tokenTransfer["tokenAmount"]
                    elif tokenTransfer["fromUserAccount"] == "5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1" and tokenTransfer["toUserAccount"] == owner and tokenTransfer["mint"] == "So11111111111111111111111111111111111111112":
                        trade="sell"
                        amount_sol=tokenTransfer["tokenAmount"]


                print(f"Raydium: swap: {account_name} {trade} {amount_token} {mint} for {amount_sol} SOL")

                token_data={}
                token=mint  #  get api token name
                print(f"RAYDIUM:token_data: {token}")
                token_data=bot.get_coin_data(token)
        
                token_name=token_data["name"]
                token_url=token_data["image_uri"]
                token_description=token_data["description"]
                token_created=int(token_data["created_timestamp"]/1000)
                token_symbol=token_data["symbol"]	
                twitter=token_data["twitter"]
                website=token_data["website"]
                telegram=token_data["telegram"]	
                mcap=token_data["usd_market_cap"]




                db.add_token_swap_name(owner,mint,trade,amount_token,amount_sol,0.0,timestamp,sig,slot,"raydium",account_name,token_name,token_url,token_description,token_symbol,token_created,twitter,website,telegram,mcap)
                if trade=="sell":
                    print(f"raydium: {account_name} sold {amount_token} {token_name} for {amount_sol} SOL")
                else:
                    print(f"raydium: {account_name} bought {amount_token} {token_name} for {amount_sol} SOL")
                ddd=db.get_multi_account_swaps(pool,mint)
                get_multi_trades(ddd)         


    return jsonify({"status": "success"}), 200
    

if __name__ == '__main__':
    # Start the Flask web server
    app.run(host='0.0.0.0', port=7002)