from flask import Flask, render_template, jsonify, request, redirect, url_for, session
import pymysql
import pymysql.cursors
from functools import wraps
import threading
import time
import modules.wallet_tracker as tracker


wallet_tracking_api=[
        "8ede527e-abea-4177-ba97-d614266c5f8d",
        "094cf3fa-bf7c-46cd-9b40-df49beb9b6c3",
   "8bff9957-1635-4a96-97bd-ee66697a4918",
    "4184847c-934e-437e-9c04-387c225c8033",
    "868166b5-f9bf-4771-8923-7bd4be842daa",
   ## "c5d1b8dd-8fc8-44d3-bebf-7f6214e177a8",
   ## "c3cff3eb-a5ea-4b4d-a7c8-95ad5d44445f",
   ## "8c76f8f5-954f-4b58-a1af-2897acf22598",
   ## "ff4f607b-2327-4f55-8e42-6dab753681e2",
]


def list_tracked_wallets():
    for api in wallet_tracking_api:
        webhook_data=tracker.get_helius_webhookid(api)
        if webhook_data is not None:
            addresses=webhook_data["accountAddresses"]
            for address in addresses:
                print(f"api:{api} address:{address}")



list_tracked_wallets()