from flask import Flask, render_template, jsonify, request, redirect, url_for, session
import pymysql
import pymysql.cursors
from functools import wraps
import threading
import time
import modules.wallet_tracker as tracker

app = Flask(__name__)
app.secret_key = 'your_secret_key_here'  # Replace with your actual secret key

# Database connection parameters
db_host = 'pumpfun.mooo.com'  # Update if your database is hosted elsewhere
db_user = 'pump2'
db_password = 'pump2'
db_name = 'pump'

def get_db_connection():
    """Establish a connection to the MySQL database."""
    connection = pymysql.connect(
        host=db_host,
        user=db_user,
        password=db_password,
        database=db_name,
        cursorclass=pymysql.cursors.DictCursor
    )
    return connection

def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'logged_in' in session and session['logged_in']:
            return f(*args, **kwargs)
        else:
            return redirect(url_for('login'))
    return decorated_function

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        # Get the username and password from the form
        username = request.form['username']
        password = request.form['password']
        
        # Check if the credentials are correct (hardcoded for simplicity)
        if username == 'admin' and password == 'pumpfun':
            # Set the user as logged in
            session['logged_in'] = True
            return redirect(url_for('index'))
        else:
            error = 'Invalid Credentials. Please try again.'
            return render_template('login.html', error=error)
    else:
        return render_template('login.html')

@app.route('/')
@login_required
def index():
    """Render the main page."""
    return render_template('index.html')

# Global cache variable and lock
cache_data = []
cache_lock = threading.Lock()



def update_cache():
    """Background thread function to update the cache every second."""
    while True:
        try:
            connection = get_db_connection()
            with connection:
                with connection.cursor() as cursor:
                    sql = """
    WITH token_buys AS (
                        SELECT
                            mint,
                            account,
                            SUM(token_amount) AS total_tokens_bought,
                            SUM(sol_amount) AS total_sol_spent
                        FROM
                            token_swaps
                        WHERE
                            trade = 'buy' 

                        GROUP BY mint, account
                    ),
                    token_sells AS (
                        SELECT
                            id,
                            mint,
                            account,
                            sol_amount,
                            token_amount,
                            block_time
                        FROM
                            token_swaps
                        WHERE
                            trade = 'sell'
                    ),
                    remaining_tokens AS (
                        SELECT
                            tb.mint,
                            tb.account,
                            tb.total_tokens_bought - COALESCE(SUM(ts.token_amount), 0) AS tokens_remaining
                        FROM
                            token_buys tb
                        LEFT JOIN
                            token_sells ts ON tb.mint = ts.mint AND tb.account = ts.account
                        GROUP BY tb.mint, tb.account, tb.total_tokens_bought
                    ),
                    other_buyers AS (
                        SELECT
                            t1.mint,
                            t1.account,
                            COUNT(DISTINCT t2.account) AS num_other_accounts_bought
                        FROM
                            token_swaps t1
                        INNER JOIN
                            token_swaps t2 ON t1.mint = t2.mint
                            AND t2.trade = 'buy'
                            AND t1.account != t2.account
                        WHERE
                            t1.trade = 'buy'
                        GROUP BY
                            t1.mint, t1.account
                    )
                    SELECT
                        ts.id,
                        ts.mint,
                        ts.account,
                        ts.sol_amount,
                        ts.token_amount,
                        ts.trade,
                        ts.block_time,
                        ts.account_name,
                        ts.token_name,
                        ts.token_url,
                        ts.token_description,
                        ts.token_symbol,
                        ts.token_timestamp,
                        ts.twitter,
                        ts.website,
                        ts.telegram,
                        ts.mcap,
                        ts.pnl_array,
                        ts.pnl_from_avg2,
                        CASE 
                            WHEN ts.trade = 'sell' 
                                AND remaining_tokens.tokens_remaining = 0
                            THEN 
                                (ts.sol_amount - (tb.total_sol_spent * (ts.token_amount / tb.total_tokens_bought)))
                            ELSE NULL 
                        END AS pnl,
                        CASE 
                            WHEN ts.trade = 'sell' 
                                AND remaining_tokens.tokens_remaining = 0
                            THEN 
                                ((ts.sol_amount - (tb.total_sol_spent * (ts.token_amount / tb.total_tokens_bought))) / tb.total_sol_spent) * 100
                            ELSE NULL 
                        END AS pnl_pct,
                        COALESCE(other_buyers.num_other_accounts_bought, 0) AS num_other_accounts_bought
                    FROM
                        token_swaps ts
                    LEFT JOIN
                        token_buys tb ON ts.mint = tb.mint AND ts.account = tb.account
                    LEFT JOIN
                        remaining_tokens ON ts.mint = remaining_tokens.mint AND ts.account = remaining_tokens.account
                    LEFT JOIN
                        other_buyers ON ts.mint = other_buyers.mint AND ts.account = other_buyers.account
where account_name != ""
                    ORDER BY
                        ts.id DESC
                    LIMIT 100;
                    """
                    cursor.execute(sql)
                    result = cursor.fetchall()
            
            # Update the cache
            with cache_lock:
                cache_data[:] = result  # Update the list in place
            
            time.sleep(1)  # Wait for 1 second before fetching data again
        except Exception as e:
            print(f"Error updating cache: {e}")
            time.sleep(5)  # Wait longer before retrying in case of error

@app.route('/api/latest-swaps')
def get_latest_swaps():
    """API endpoint to fetch the latest swaps from the cache."""
    with cache_lock:
        data = list(cache_data)  # Make a copy to ensure thread safety
    return jsonify(data)

@app.route('/chart')
@login_required
def chart():
    """Render the main page."""
    return render_template('chart.html')
import requests

import requests

import requests

def fetch_chart(mint_address, minimum_size=**********,):
    """
    Fetches all chart data from the specified URL based on the mint address and parameters,
    using pagination with a maximum of 200 records per request.

    Parameters:
        mint_address (str): The mint address to fetch data for.
        minimum_size (int): The minimum size filter for the data.
        limit (int): The maximum number of records to retrieve per request (default is 200).

    Returns:
        list: A combined list of all trade data dictionaries.
    """
    url_template = 'https://frontend-api.pump.fun/trades/all/{mint_address}?limit={limit}&offset={offset}&minimumSize={minimum_size}'
    headers = {'accept': '*/*'}
    
    all_data = []
    offset = 0  # Starting point for data retrieval
    limit=200
    while True:
        url = url_template.format(
            mint_address=mint_address,
            limit=limit,
            offset=offset,
            minimum_size=minimum_size
        )
        print(f"Fetching data with offset {offset}...")

        try:
            response = requests.get(url, headers=headers)
            response.raise_for_status()  # Raises HTTPError for bad responses (4xx or 5xx)
            data = response.json()
            
            if not isinstance(data, list):
                print(f"Unexpected data format received: {data}")
                break  # Exit the loop if data is not a list

            if not data:
                print("No more records to fetch.")
                break  # Exit the loop if no data is returned

            all_data.extend(data)
            print(f"Fetched {len(data)} records.")

            if len(data) < limit:
                print("Reached the end of available records.")
                break  # Exit the loop if fewer records than the limit are returned

            offset += limit  # Increment the offset for the next batch

        except requests.exceptions.RequestException as e:
            print(f"Error fetching chart data at offset {offset}: {e}")
            break  # Exit the loop on any request-related errors

    print(f"Total records fetched: {len(all_data)}")
    return all_data


@app.route('/api/fetch-chart')
def get_chart_data():
    """API endpoint to fetch chart data."""
    mint_address = request.args.get('mint', '2Zw6kxtNXgVCA7atqnHBUWJMfwzffDpah18ELqMupump')
    limit = request.args.get('limit', 1000, type=int)
    offset = request.args.get('offset', 0, type=int)
    minimum_size = request.args.get('minimumSize', **********, type=int)

    data = fetch_chart(mint_address, minimum_size)
    return jsonify(data)

@app.route('/accounts/delete/<int:account_id>', methods=['POST'])
@login_required
def delete_account(account_id):
    """Delete an account based on the provided ID."""
    connection = get_db_connection()
    with connection:
        with connection.cursor() as cursor:
            sql = "DELETE FROM accounts WHERE id = %s"
            cursor.execute(sql, (account_id,))
            connection.commit()
    return jsonify({"success": True})


@app.route('/accounts', methods=['GET', 'POST'])
@login_required
def manage_accounts():
    connection = get_db_connection()
    with connection:
        with connection.cursor() as cursor:
            if request.method == 'POST':
                # Handle form submission for adding/editing accounts
                account_id = request.form.get('id')
                address = request.form.get('address')
                name = request.form.get('name')
                notify = request.form.get('notify')
                trader = request.form.get('trader')
                enabled = request.form.get('enabled')
                description = request.form.get('description')
                url = request.form.get('url')

                # Insert or update account
                if account_id:
                    # Update existing account
                    sql = """
                    UPDATE accounts SET address=%s, name=%s, notify=%s, trader=%s, enabled=%s, description=%s, url=%s WHERE id=%s
                    """
                    cursor.execute(sql, (address, name, notify, trader, enabled, description, url, account_id))
                else:
                    # Insert new account
                    # add 
                    helius_api=tracker.add_helius_address(address)
                    print("==============",helius_api)
                    if helius_api:
                        sql = """
                        INSERT INTO accounts (address, name, notify, trader, enabled, description, url,helius_api)
                        VALUES (%s, %s, %s, %s, %s, %s, %s,%s)
                        """
                        cursor.execute(sql, (address, name, notify, trader, enabled, description, url,helius_api["api"]))
                    else:
                        return None
                    
                connection.commit()

            # Fetch all accounts to display in the table
            cursor.execute("SELECT * FROM accounts where trader = 1")
            accounts = cursor.fetchall()

    return render_template('accounts.html', accounts=accounts)







if __name__ == '__main__':
    # Start the cache update thread
    cache_thread = threading.Thread(target=update_cache)
    cache_thread.daemon = True
    cache_thread.start()
    
    app.run(debug=True, host="0.0.0.0", port=5500)
