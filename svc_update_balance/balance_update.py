#!/usr/bin/env python3

from datetime import datetime, timedelta
from mysql.connector import pooling
from modules import db, transfer, utils
import time
import pandas as pd
from neo4j import GraphDatabase
from modules.neo import Neo4jClient
from modules.config import *
import pika, json, os
import dotenv

dotenv.load_dotenv()

neo4j_server = os.getenv('NEO4J_SERVER')
neo4j_user = os.getenv('NEO4J_USER')
neo4j_password = os.getenv('NEO4J_PASSWORD')

MYSQL_SERVER = os.getenv('MYSQL_SERVER')
MYSQL_USER = os.getenv('MYSQL_USER')
MYSQL_PASSWORD = os.getenv('MYSQL_PASSWORD')
MYSQL_DB = os.getenv('MYSQL_DB')
MYSQL_POOL = os.getenv('MYSQL_POOL')

MIN_BALANCE_CHANGE = os.getenv('MIN_BALANCE_CHANGE')

import modules.utils as utils
log = utils.get_logger()

def process_account_balances(pool, neo_client):
    addr = os.getenv('BOOTSTRAP_ADDRESS')
    BALANCE_UPDATE_BATCH = int(os.getenv('BALANCE_UPDATE_BATCH'))
    BALANCE_SLEEP = int(os.getenv('BALANCE_SLEEP'))

    while True:
        try:
            print(" ------------------------- new cycle -------------------------")

            accounts, current_balances, _ = db.get_all_accounts_update(pool, "new|exist")
            log.info(f"Processing {len(accounts)} accounts")

            balances_df = pd.DataFrame()  # Initialize an empty DataFrame to store balances

            # Process accounts in batches
            for start in range(0, len(accounts), BALANCE_UPDATE_BATCH):
                end = start + BALANCE_UPDATE_BATCH
                account_batch = accounts[start:end]
                current_balance_batch = current_balances[start:end]

                try:
                    # Get account balances for the current batch
                    batch_balances_df = transfer.get_account_balances(account_batch, current_balance_batch, int(MIN_BALANCE_CHANGE))
                    balances_df = pd.concat([balances_df, batch_balances_df], ignore_index=True)
                except Exception as e:
                    log.error(f"Error in batch processing: {e}")
                    continue  # Skip to the next batch

            # Update balances in the database and Neo4j
            if not balances_df.empty:
                db.update_account_balances_many(pool, balances_df)
                neo_client.update_balances(neo_client, balances_df)

        except KeyboardInterrupt:
            log.info("Graceful shutdown initiated.")
            break
        except Exception as e:
            log.error(f"Unexpected error in main cycle: {e}")
        finally:
            time.sleep(BALANCE_SLEEP)

if __name__ == "__main__":
    neo_client = Neo4jClient(neo4j_server, neo4j_user, neo4j_password)
    log.info("Start balance updates")

    # MySQL Connection Pool
    pool = pooling.MySQLConnectionPool(
        pool_name=MYSQL_POOL, 
        user=MYSQL_USER,
        password=MYSQL_PASSWORD,
        host=MYSQL_SERVER,
        database=MYSQL_DB
    )

    process_account_balances(pool, neo_client)
