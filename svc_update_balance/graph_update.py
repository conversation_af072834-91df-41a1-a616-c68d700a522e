#!/usr/bin/env python3


from datetime import datetime, timedelta
from mysql.connector import pooling
from modules import db,transfer,utils
import time,os
import pandas as pd
from neo4j import GraphDatabase
from modules.neo import Neo4jClient
from modules.config import *



neo4j_server = os.getenv('NEO4J_SERVER')
neo4j_user = os.getenv('NEO4J_USER')
neo4j_password = os.getenv('NEO4J_PASSWORD')


MYSQL_SERVER=os.getenv('MYSQL_SERVER')
MYSQL_USER=os.getenv('MYSQL_USER')
MYSQL_PASSWORD=os.getenv('MYSQL_PASSWORD')
MYSQL_DB=os.getenv('MYSQL_DB')
MYSQL_POOL=os.getenv('MYSQL_POOL')


MIN_BALANCE_CHANGE=os.getenv('MIN_BALANCE_CHANGE')

import modules.utils as utils
log=utils.get_logger()

if __name__ == "__main__":
    neo_client = Neo4jClient(neo4j_server, neo4j_user, neo4j_password)
    log.info("start balace updates")
     # my account
    pool = pooling.MySQLConnectionPool(
        pool_name=MYSQL_POOL, 
        user=MYSQL_USER,
        password=MYSQL_PASSWORD,
        host=MYSQL_SERVER,
        db=MYSQL_DB)


    addr="9yMwSPk9mrXSN7yDHUuZurAh1sjbJsfpUqjZ7SvVtdco"



    accounts, current_balances, _ = db.get_all_accounts(pool, "new|exist")

    log.info(f"Processing {len(accounts)} accounts")
    

    balances_df = pd.DataFrame()  # Initialize an empty DataFrame to store balances

    start=0
    # Process accounts in batches of 100
    for start in range(0, len(accounts)):

        print(f"update NODE balance: {accounts[start]}  new balance: {current_balances[start]}")
        neo_client.update_node("Address", accounts[start], {"balance": current_balances[start]})
        start+=1
