SYSTEM_FEE_ACCOUNT="HWEoBxYs7ssKuudEjzjmpfJVX7Dvi7wescFsVx2L5yoY"
PUMP_FEE_ACCOUNT="CebN5WGQ4jvEPvsVU4EoHEpgzq1VV7AbicfhtW4xC9iM"
JITO_FEE = [
    "96gYZGLnJYVFmbjzopPSU6QiEV5fGqZNyN9nmNhvrZU5",
    "HFqU5x63VTqvQss8hp11i4wVV8bD44PvwucfZ2bU7gRe",
    "Cw8CFyM9FkoMi7K7Crf6HNQqf4uEMzpKw6QNghXLvLkY",
    "ADaUMid9yfUytqMBgopwjb2DTLSokTSzL1zt6iGPaS49",
    "DfXygSm4jCyNCybVYYK6DwvWqjKee8pbDmJGcLWNDXjh",
    "ADuUkR4vqLUMWXxW9gh6D6L8pMSawimctcNZ5pGwDcEt",
    "DttWaMuVvTiduZRnguLF7jNxTgiMBZ1hyAumKUiL2KRL",
    "3AVi9Tg9Uo68tJfuvoKvqKNWKkC5wPdSSdeBnizKZ6jT"
]
JUPITER_FEE = [
    "45ruCyfdRkWpRNGEqWzjCiXRHkZs8WXCLQ67Pnpye7Hp"
]

CEX_ACCOUNTS = [
    "5tzFkiKscXHK5ZXCGbXZxdw7gTjjD1mBwuoFbhUvuAi9",
    "9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM",
    "Amf2mf2Ciap5wYAEKDtGoQHfHPWaKbyFYhutMK46hTRF",
    "2ojv9BAiHUrvsm9gxDe7fJSzbNZSJcxZvf8dqmWGHG8S",
    "DRpbCBMxVnDK7maPM5tGv6MvB3v1sRMC86PZ8okm21hy",
    "H8sMJSCQxfKiFTCfDR3DUMLPwcRbM61LGFJ8N4dK3WjS",
    "2AQdpHJ2JpcEgPiATUXjQxA8QmafFegfQwSLWSprPicm",
    "GJRs4FwHtemZ5ZE9x3FNvJ8TMwitKTh21yxdRPqn7npE",
    "6FEVkH17P9y8Q9aCkDdPcMDjvj7SVxrTETaYEm8f51Jy",
    "AobVSwdW9BbpMdJvTqeCN4hPAmh4rHm7vwLnQ5ATSyrS",
    "FWznbcNXWQuHTawe9RxvQ2LdCENssh12dsznf4RiouN5",
    "krakeNd6ednDPEXxHAmoBs1qKVM8kLg79PvWF2mhXV1",
    "bc1q75tsfq2c5cqp2ss32qksmnzd9yea2mjsjktdmrz9",
    "ft4vm2xm3fyfvurrkende46hpmwwnzpctfu3szxpve2",
    "ASTyfSima4LLAdDgoFGkgqoKowG1LZFDr9fAQrg7iaJZ",
    "8tp9ffkz2kcrblydtunxo98ez6ojgb6mzepxfgddebzg",
    "6brjeznfspqjwoo16z1ybywkguaruxzhnz9bjmvze8pd",
    "9ftgm6hjulcpa8an4sfg5yshuexdzbtmedcxsyntnwh5",
    "aebwztwxscynnuqcedhs54wttrqrw3nj1utqddzb4c7b",
    "57vSaRTqN9iXaemgh4AoDsZ63mcaoshfMK8NP3Z5QNbs",
    "BmFdpraQhkiDQE6SnfG5omcA1VwzqfXrwtNYBwWTymy6",
    "HVh6wHNBAsG3pq1Bj5oCzRjoWKVogEDHwUHkRz3ekFgt",
    "5LT2BQ7FFYBXW2PEDVB6KBX2F3C77WXBJ2FPVERBXXBU",
    "EkUy8BB574iEVAQE9dywEiMhp9f2mFBuFu6TBKAkQxFY",
    "6b4aypBhH337qSzzkbeoHWzTLt4DjG2aG8GkrrTQJfQA",
    "6wEMcwrcF5AP9jpHWQcPxHXciWA2g217Qq81CTWjbgBw",
    "9uyDy9VDBw4K7xoSkhmCAm8NAFCwu4pkF6JeHUCtVKcX",
    "6ZRCB7AAqGre6c72PRz3MHLC73VMYvJ8bi9KHf1HFpNk",
    "JBpj7yp4Afvb71TmanVwJZXGeX4kqbGFvjCFCRo3EbTM",
    "12T1tgaYZzEkFpnPvyqttmPRJxbGbR4uDx49cvZR5SRF",
    "13AE11jLvxcxsjqaSoWFXCTGUfbjXb1gmZTY8x3TXJzW",
    "1UJSCYLh44UYhkm1WwXAwT2W8nirTD74VzPsdhfsstY8",
    "BZHBVLCCYGERFGQ3ZRQPSJ7R6UB3NXFBF552BGYAGX4",
    "DdzFFzCqrhstFM5XQYA28G2ekCkvpb6bPdhUT5vcsZtq",
    "JDQ7EW3VY2ZHK4DKUHMNP35XLFPRJBND6M7SZ7W5RCFD",
    "YT3i7wAtjNPCTEGNbUmgL4ym9udeV5k6Utc3vCmAbky6",
    "erd1a56dkgcpwwx6grmcvw9w5vpf9zeq53w3w7n6dmxc",
    "FxteHmLwG9nk1eL4pjNve3Eub2goGkkz6g6TbvdmW46a",
    "J4rzLDLhLWFpjSgCMCcxTU84bQ8AH5vhgjwq7SjYVk8Q",
    "FyJBKcfcEBzGN74uNxZ95GxnCxeuJJujQCELpPv14ZfN",
    "GnCRxKqUEPouYMvTb5nJMGrDB3VkTXZnDTaDuVZdnWA3",
    "1P7cDFGeWm6ezez6XGXTAjvm8qcsGiMXe7",
    "4DgunMfBb19GaMZ1Z48oqcymZ4eBA4v5SUdRnapzj66G",
    "Cet3t77x2BBVSmiEFm8ZPoDSngbpso2RuWPL79Ky7SpA",
    "Fe7SEekiKygziaEGKxsDsgLVzrCfNvVBvAYsaJBwFA8s",
    "2XxP4kS2vfkiMvpLpGNxry3fPUYimsuAmSbqL1KnuwZ8",
    "9qoUcyhKSWMbk6tqGUYQUpeosPcdUnJszG4eQKwfe4gL",
    "2E1UKoiiZPwsp4vn6tUh5k61kG2UqYpT7oBrFaJUJXXd",
    "15Fg7p6pzLo6uinCFdsx3HTWdAx4vFt8nnw2E3JWHHwh",
    "15mENJiKxtbxE2PNcB8qTaatYKjFTN4kitEzZ5eiHFGW",
    "1xXbYy1V5Sc3EQZ76wmcWy4gXTSyLbzgdDNJtGT6jEcL",
    "dc521827821f1695538ff9312df15f32837bbfe95bc9",
    "7SRyi7urg28j1XtioKbcJLtkL9Pqu39Y4CTAXwmu2Hts",
    "821dTn3JEhhqg11Q24ijxRBYXfKWngGMibTqHS8hWjZb",
    "6iVBAsquJRaLsXbojb18kqTW1d5iVLspVjCtsReZBKhY",
    "EKpQGSJtjMFqKZ9KQanSqYXRcF8fBopzLHYxdM65zcjm",
    "HZ1JovNiVvGrGNiiYvEozEVgZ58xaU3RKwX8eACQBCt3",
    "JUPyiwrYJFskUPiHa7hkeR8VUtAeFoSYbKedZNsDvCN",
    "hntyVP6YFm1Hg25TN9WGLqM12b8TQmcknKrdu1oxWux",
    "jtojtomepa8beP8AuQc6eXt5FriJwfFMwQx2v2f9mCL",
    "mb1eu7TzEc71KxDpsmsKoucSSuuoGLv1drys1oP2jh6",
    "rndrizKT3MK1iimdxRdWabcF7Zg7AR5T4nud4EkHBof",
    "BbHG9GvPActFGogv3iNrpDAj4qpXr8t3jF16uGxXcKci",
    "43DbAvKxhXh1oSxkJSqGosNw3HpBnmsWiak6tB5wpecN",
    "13VagdYbCRMSBSbmz4UivPpS9SwmTTRiPtMkjoEHRm8v",
    "887234fe5ed5e77272465317978ae86c25a71531d933",
    "BZ3kabSsMzbuJUguYxtmkRtzw7ACqw1DUMH8PcbvXiUr",
    "HXsKP7wrBWaQ8T2Vtjry3Nj3oUgwYcqq9vrHDM12G664",
    "MTCEM5YJJSYGW2RCXYXGE4SXLSPUUEJKQAWG2GUX6CNN",
    "enC1zkqfU5X4x84LMKSzcRdsSSiF7M1Nt7ovm62jRXr7",
    "x5bd7de5c56d5691f32ea86c973c73fec7b1445e5973",
    "16ZL8yLyXv3V3L3z9ofR1ovFLziyXaN1DPq4yffMAZ9c",
    "1743nDTMZisPgBCYSAgkUn1kVG7MePc9rvMEjoRNf4ip",
    "1P6bgxZi42kYYV545c3RSp7NJLUgASDpMP1ifXJazVR1",
    "1qnJN7FViy3HZaxZK9tGAA71zxHSBeUweirKqCaox4t8",
    "28nYGHJyUVcVdxZtzKByBXEj127XnrUkrE3VaGuWj1ZU",
    "3gd3dqgtJ4jWfBfLYTX67DALFetjc5iS72sCgRhCkW2u",
    "3yFwqXBfZY4jBVUafQ1YEXw189y2dN3V5KQq9uzBDy1E",
    "6QJzieMYfp7yr3EdrePaQoG3Ghxs2wM98xSLRu8Xh56U",
    "A77HErqtfN1hLLpvZ9pCtu66FEtM8BveoaKbbMoZ4RiR",
    "E1EfU3iKUS16mB2vdgj6mi3ssV32dm2Pxh7W74XxKuYu",
    "ETZY5TjMKdV2KdHVmUNTN56pWhMc8TyjrXtQ7YexDCmG",
    "HBxZShcE86UMmF93KUM8eWJKqeEXi5cqWCLYLMMhqMYm",
    "bc1qgc446er7nn9t92hnk5untxg2ad4hvxaanpvwmrex",
    "i57ExrKB2i4mSgjSuq2xz617mQXmu33WG2WEYypmdvX",
    "42brAgAVNzMBP7aaktPvAmBSPEkehnFQejiZc53EpJFd",
    "AC5RDfQFmDS1deWZos921JfqscXdByf8BKHs5ACWjtW2",
    "2vzgquwagv84t9xcjekauja5cz4wsat2uc2fe4mqfdza",
    "3Nvb6UeEMVLf9DsehhqT8B5SNHrNLEpHdf42nXi9aLK8",
    "4E73T5Zubx3kPtheJ5DB6QMEaadMQowhUcdRV6f9rvVt",
    "5bjZPeHbdGUCduyY8i2dZkkgLWkG3u1P2sbt1Jb5kFxW",
    "6XuYJv4PyauP2WKdgd4fZYvjK7uBdVkEySoy89fXV9XT",
    "6fuHoq5Tr2j6dFTGWHmLUhVBUj8XvDBgdGGzgfSwe5DN",
    "6p8LxMvHUZtdiik28TQy483WaCaDGsAUFm3PKjpUpLB",
    "8W4u11KJYhmCY9rs2MCVHmMrgpeHLgsbkwAarbNvzr2Y",
    "8nFGjbJNdarBouEC856Qmhrm8fptuEUnZrpNKgougsLi",
    "8zSWFy8wvHEuFJ7CZ81wy2wn4fPaju6jc6tbtbv5x9f8",
    "92qQCjprXoJ5GYji5it8PJTneBFxzSJ6mYosQWxYKxx4",
    "9buzBcTujdQLyf1XkyLaSrGNbkyNxg8qML3bpKWCz9P6",
    "AjCHG7CYJST379Eh7xLqhRaUyBzurFKjsX7z8DTrfGZp",
    "Bq6NnW55ToJrAM7NxUYDmSMbKKCQ6f742ZemLiypqjzY",
    "EUXVDPCwXe9sJt3VucYfveB1kk5piSWJqhWQEsFtE2CR",
    "EhZLSqyWrazhqR5bmyyEfFRa3ycWu9KPgQgQ7G1R9zC9",
    "HwgxMWxaq5cDbdVmoH24Gy8F9m9DkU7Mwgdbgh4mh2m7",
    "addr1qy5dqjx4mtegqu6kx5gu6upkk96c8haf38eksj5",
    "at7s987xtv86xx265k42gtvuvkt4vyjjxgqn9wat2zmd",
    "CLNEVwuSAiGsvPtE74yLhda4beNfd8qfZXVKkUcAJZDL",
    "DdzFFzCqrhseMuShaXzLDGDBa8jGEjdEjNc83jouqdqB",
    "DdzFFzCqrhtBatWqyFge4w6M6VLgNUwRHiXTAg3xfQCU",
    "Qzk5R52MedutUq3QGdMPiauR5SjbttqdBjDA5g6rf3H6",
    "dTcjJxSrPHVZJBsQprUEc5pRhgMWQaGciTssoZVwrSKm",
    "u6PJ8DtQuPFnfmwHbGFULQ4u4EgjDiyYKjVEsynXq2w",
    "HiRpdAZifEsZGdzQ5Xo5wcnaH3D2Jj9SoNsUzcYNK78J",
    "5bJcc9eb2XE7mqcET2xDuAdMGuXWybb4YPmAHLjKLhQG",
    "88xTWZMeKfiTgbfEmPLdsUCQcZinwUfk25EBQZ21XMAZ",
    "8NBEbxLknGv5aRYefFrW2qFXoDZyi9fSHJNiJRvEcMBE",
    "BY4StcU9Y2BpgH8quZzorg31EGE4L1rjomN8FNsCBEcx",
    "8wkq8ywmughxkm7n75d2fmrgnmvwun6rhepyknjxqm99",
    "bc1q33m8td986p3vcnap9zqpx3d8v8zujtkvqacsya5x",
    "bc1qku6z53kuyaj9r898kj6esqnwz7wke82mwgw43vhu",
    "bc1qnerwvz93pcj653r5yd4hnd2d7np2drhdhyruj7qd",
    "bc1qs9ut74nue7vjknz2eqxegmtuzqhjzx9y8tzjymv",
    "1347e3PfJKKcJL4XJhFeZ5UmZYRnk26Vs9aGjZ8RZLPk",
    "51AASorYCLPcUHnuQQaau6DfsfFRixzh4HsoQwsc5Ara",
    "erd1z5xjeu4xw32jkckhj9jpc9dymj6a9h8yxtch96e4",
    "u7q4ju4eyrd8ykvvdehryn6qyz3n4nh8ucfr8s3pgesk",
    "s9dsgkgwk759fxrnpae7f4q5d3uk2aw97ypjvvf3kjy4",
    "14B3z6xL9vGgKz8WptoZabPrgH6adH1ev2Ven4SiTcdz",
    "6Attw9NcFspCAreufvQ3mW8aqXEk6MDceeoC8obw45cJ",
    "6NnzUC7mrM7ZgKdcMq1jPAdqh9TMgdsaVR1DNq6kce3A",
    "7CtzEcGeYpMfR1aEi1Gbt8GRHDPH9uDB6GNTsBABqSmo",
    "7fGw3UURsxk1szQ4buxQyEkiF4P6z7vx7sN1MHEguTJg",
    "7o9ukGhWvc71yYcjinKnrxMn2kFMyQD1iCG3romrzg7r",
    "8dUc88Nss8uhqzzFvUQhepkwZaVrfzpoCfKjCXGkdzAG",
    "945SJTwsBSqwEEgtKMnhcLDnJowP6YpJUTEykMVK5k6q",
    "9un5wqE3q4oCjyrDkwsdD48KteCJitQX5978Vh7KKxHo",
    "APJmXrtC9TUAg5gcjbcsVUiVzeDv85xLZRLad1GiQTNE",
    "BEaiMhcc4Kao7B4hoq6r9m8neUpfimUMwhzugKxtkZw6",
    "15abPBmJrMY7QJeCEQJQbQ9a62A7ndfTo8KC7Wn4dzt9",
    "BWZqi67kXsvf5crjd19Fb3gmf3DCipGE8eWWJT2YHDXQ",
    "BhJrLQEyFyrcf1746pHATzAZQpC1uk3SJf2AHHK7LW3K",
    "CE8joA143dBjsCFTeBxBs3kNGNU87WH6Q7GMMfxPzSr2",
    "CGZAHxBannZzYK3rzVdG1e3uNjMxrmaXDhLEZxQBbueq",
    "CeGyfZdtbjzC5FeVXCBdYE1v397yxxqYgmMcUwtouJUu",
    "EBVigEhxvkWUwreG98kTh5FbZEjnXTXC5ANZtLz4YpHQ",
    "FJS2rTjAQmFFRmVqtnkceGvLJrMwUPgCUfLjciuMYkSC",
    "FefAbVt2EgXMGxeJb6sB1k5pQJhiGPxw4mB1zeUohZLk",
    "GcFZHQVJ2icPGLrA9qPLq88b7eqBXSUcdL1utUtUfcSD",
    "HnLezgQkNVWMp2AV6mcHM1Ljst32kfsoVnYUkvXuQpjg",
    "16hp43x8DUZtU8L3cJy9Z8JMwTzuu8ZZRWqDZnpMhp46",
    "bc1qdk55vq5vrzt44pn93t9e3rwecw2dy4pcwc5gwugd",
    "uheu4997ufs84efw7jw248mk9v83sgpmvsr6nawhaadx",
    "1Dkx7zjy4pRMwLQwWkbhb9Jxy7EXLfkdHVRXufwvdLV7",
    "1xpD24SQ9UgeFPQ2P9eRc7dppjgU9hiHDULqvWNfH3g3",
    "2gp4phmq6mcz995xc6m8n6fn8nygjfg8p63ncy4gsp3x",
    "44DwSkZbY2PVo9wfFP21D7HG7xf5rw8QaF6xSLGSfm7P",
    "5URmUviWsAtcciFXqgM7f1jmhvBULdQDT47dqDkrJUi",
    "5kiqH41rz2wPoNH5FuDJ3x8dB3EEe3XGoSWy2485tAp9",
    "5VCwKtCXgCJ6kit5FybXjvriW3xELsFDhYrPSqtJNmcD"
    ]
METAPLEX_ACCOUNTS = "metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s"

FEE_ACCOUNTS=[SYSTEM_FEE_ACCOUNT,PUMP_FEE_ACCOUNT]+JITO_FEE+JUPITER_FEE

SKIP_OTHER=["3qJwxTR7jE6RSFUVQkViWtxwgvYwLVkLft2XsWoDHrxm","2xCKVe8QDtE4DHoPRywbmTKEJCjYeHRCvM5wp7HXBovL"]   # father acount
SKIP_ACCOUNTS=[SYSTEM_FEE_ACCOUNT,PUMP_FEE_ACCOUNT,METAPLEX_ACCOUNTS]+JITO_FEE+JUPITER_FEE+CEX_ACCOUNTS+SKIP_OTHER
SYSTEM_ACCOUNT="11111111111111111111111111111111"
TOKENPROGRAM_ACCOUNT="TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"
PUMP_PROGRAM="6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P"

RAYDIUM = "5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1"
METEOR = "2MFoS3MPtvyQ4Wh4M9pdfPjz6UhVoNbFbGJAskCPCj3h"

omnia_keys = [
    "1e0ca1a2a6114584b5416f7c2a615dd8",
    "bf8f530c3d624f1889e1d0c720901fb1",
    "203f81f52e7c4cd1a5fcc8e60b09137a",
    "ea93c7bbd2814462a157b94e13dbb8a8",
    "292d0a08f13f426cbc08b2a4f71e06ee",
    "ea515850a5474d698bebea9d988da92f",
    "96b60195-b6d9-405a-bbb7-4693ae541c5f",
]

api_key = [
"8bff9957-1635-4a96-97bd-ee66697a4918",
"4184847c-934e-437e-9c04-387c225c8033",
"e639aac2-5de7-4096-8f2f-33bf54ae5665",
"35c105e7-2b05-489a-a506-61c852272321",
"3675ac39-6b5d-4986-8d47-a6f14b2eded5",
"c5558abf-0a37-4464-805f-9ed7a71bae5b",
"868166b5-f9bf-4771-8923-7bd4be842daa",
"868166b5-f9bf-4771-8923-7bd4be842daa",
"c5d1b8dd-8fc8-44d3-bebf-7f6214e177a8",
"c3cff3eb-a5ea-4b4d-a7c8-95ad5d44445f",
"8c76f8f5-954f-4b58-a1af-2897acf22598",
"ff4f607b-2327-4f55-8e42-6dab753681e2",
"1c8e2406-113b-41bb-8029-20cc9790ab46",
"95165943-c7af-44b8-aea5-5e0fcb57af47",
"c4b358d4-7298-4c2d-9d96-0b9ff31bb4e2",
"bd89402a-f47f-4c04-b6b2-39428a840cad",
"2528acff-ea03-4e36-b9af-eed11e791d29",
"f33d8624-a4dc-4fa7-9cc3-3b6718da72a9",
"a3b2aa15-f943-4f4b-a500-36b289c2c6f3",
"262ce58f-1374-4443-be6d-fcc5902d41ec",
"3de61475-a023-4fc9-a4fb-07e5c7586a97",
"93d43d74-f2d2-4f9c-8d44-558399588dce",
"0ab07156-128b-4742-99db-38ee578c60dc",
"22fa1550-8775-4dd1-b45e-b49a83cf8af2",
"5706a939-e98c-447d-8c8c-b3f08e3e65ff",
"12e5df17-b71d-4465-8a1d-78229414c78e",
"def83b56-0989-4e96-a7b6-4e3896e084e4",
"b30d89bf-3860-4cbd-8da0-f2835232c818",
"02f10592-8eba-4b09-bd1d-523b558494c7",
"f0b9c436-6036-44a1-9501-36ffa2d5d5f0",
"9c6af33d-2bac-42d8-838d-ed58db64a2de",
"13b573a6-3eff-4478-b2b1-0b7c99044c40",
"1d942438-63e4-4d84-adfb-fd7dbd837428",
"bb846fef-b20d-4355-9766-4c3a1ff3f69c",
"bb846fef-b20d-4355-9766-4c3a1ff3f69c",
"f77b1c29-79ab-4eeb-b287-711b7b8f0730",
"d90c9c2e-ae00-40e8-95d7-9c05e253c7c6",
"e78c411a-f6db-4323-8a8f-fe05285ca394",
"a934075e-b3ad-4bc4-a5e6-328dad56f08c",
"0adaa884-34d8-4299-a60b-9ce29d68c8c8",
        "384915a8-7223-42c2-90c0-f28542870655",
        "3a6c9bf2-9ee8-4d19-a59e-5b1f6726d959",
        "7b2bcde9-5345-4719-a12a-b1a7cb6ed93e",
        "9770ec28-45b3-4f2e-b05a-9484d94c34c5",
        "f9a0a1cf-2276-4f02-8c28-9f4c8e6e6dd4",
        "e3d60de6-07f3-49ca-be5b-2c7784cd6b09",
        "756ea740-7c29-4258-aafa-b8bb72cb25cd",
        "279f0638-a926-4a8e-ba7c-f8f7b374784a",
        "73852fcf-4dd1-4b00-925d-7b1db15f5571",
        "4954978f-1e89-4f7b-9d14-3aa4ad5b0025",
        "27e88e99-673d-43c1-81f6-2c1c27873c64",
        "f3aa1460-c40e-470d-866b-cefeadc05c79",
        "b2896fa0-a02e-4c38-b99d-4e534d54c5d5",
        "3c3eed09-1263-42ae-b7b1-49fba615baa7",
        "96a35211-f50b-4a64-a9c0-474844647002",
        "ec553663-cac5-4927-8600-05860a729113",
        "6a363fd8-6928-4721-9335-af4f0196dbd9",
        "3c279132-f839-4917-9c67-30f8a727ed64",
        "be63531c-8f43-470d-b609-92973d0e5a92",
        "7c2bafda-2529-4d1e-b61c-2fa73fb41405",
        "c29c0ee4-6308-4789-a694-e2dfa40ad7c7",
        "c297538a-47ae-4000-9a42-3b4e0c0a9331",
        "21cc2ba2-ce8d-4e56-bdb8-13d7a4dad4fd",
        "ca71c4c7-11e0-4199-aea4-746f502f4fa9",
        "3ff9e818-112a-4b99-bc98-87e48f167962",
        "ba294920-bc04-4fea-8267-35d62e7d82f4",
        "2846ace7-e0a6-4832-b10b-8da5870e7d87",
        "91c81c62-7580-47b7-8587-e5cf5abc3602",
        "fe69ecea-bc63-4b56-90eb-474c7db7a4d3",
        "bf66cf1e-28eb-4c39-b84b-0552eb93d027",
        "556770dc-df91-48c2-9da2-e7cc0c5452a1",
        "8ad8d3da-c7de-42b2-b7d6-2d94ca0aa654",
        "4705cd7a-f4a6-4993-adb4-20d416b7c038",
        "69463031-b3c1-455a-addd-0a8d100dca90",
        "c29c0ee4-6308-4789-a694-e2dfa40ad7c7",
        "814e2855-a7f8-4d84-b0bc-82cedf5694e3",
        "e10ad31d-205d-4bd2-ad35-e261cd901f38",
        "6d8bfbc2-44f3-4c30-b31e-c1322741dce8",
        "4a53c62c-2be7-4030-8e51-7f4867bf16c4",
        "f5678270-3a1a-4868-ba6a-1d70a0891966",
        "8c5ca67a-9aa2-4986-905f-2a95b1927b34",
        "12728bae-550f-4f59-a42c-94b89d9e86b8",
        "0c496773-5e98-428a-90da-4b24095db327",  
        "fc0f373b-3547-4878-bc4c-c406adf17211", #13oct
        "e2e83734-8b3c-44c4-a53a-a8cd548c5b2c",
        "022511fe-c527-4b15-bf49-1ba49439e81f",
        "f67df597-d1fb-40ed-9182-cb002fbf5510",
        "96b60195-b6d9-405a-bbb7-4693ae541c5f",
        "7ca211b2-928f-4c7f-b7fc-dc8703393c16",
        "43a78d3f-84c1-41bc-a763-b94cfd568b28",
        "80cd5950-bfe7-47fb-873f-2c7a9f4d697c",
        "2350d68c-1e6d-4c21-9c31-84e71987cfb2",
        "8baecf77-203c-4da0-a78c-7571729cdec7",
        "23761ad3-f828-47c0-a9ab-259196cdf54a",
        "9fcebc71-dc4b-4a89-b8fc-e571543a0064",
        "d36821ee-c828-49c6-a1fb-174ded36a7aa",
        "1ca6b9ce-5738-4669-bf1b-0d5838a54b06",
        "29f6374c-7a34-4225-88b8-dd3bfb49d2f2"
        ]
api_key = [
   "094cf3fa-bf7c-46cd-9b40-df49beb9b6c3", # github
   "f9816c8f-1101-4a98-9972-1ca8796c4e0b", #<EMAIL>
   "f9816c8f-1101-4a98-9972-1ca8796c4e0b",  #<EMAIL>
   "c29c0ee4-6308-4789-a694-e2dfa40ad7c7"   #<EMAIL>
]

api_key = [ "cf3aa81f-7796-401b-a170-5567272f5f65" ]

db_config = {
    'user': 'pump2',
    'password': 'pump2',
    'host': '***************',
    'db': 'pump'
}
omni_url="https://endpoints.omniatech.io/v1/sol/mainnet/"
omni_keys =[
        "b6efe195db51499ca3719ad3eb838aa6",
        "438ffec158c5401e9b00d2de309a86a5",
        "4fd8f6aa33f443ebb14c52e25551890a"
        ]

mysql_pool_name="mypool"
mysql_pool_size=10
mysql_user='pump2'
mysql_password='pump2'
mysql_host='**************'
mysql_db='pump'


neo_uri = "bolt://**************:7687"
neo_user = "neo4j"
neo_password = "pump2pump"

master_address="E197uwUXRjLMQBEZ4PYazDq5rGXwRB6xuTJ7f56UaLDC"

#max number of hops to consider
max_hops=5
#tx_max_age=172800
tx_max_age=86400*7
#tx_max_age=3600
tx_dir="out"  # in,out,both
#minimum balance to consider
minsol=1_500_000_000

mint_minsol=4_000_000_000
mint_maxsol=6_000_000_000
mint_amount=5_000_000_000
mint_amount2=30_000_000_000
#maximum number of tx to process
max_sig=1000

bootstrap=False
pushover_token="arbs7ccq98rqoz78pagtcqyc523hro"
pushover_api="utmxhixkgtchrdahx3tfj6sxgfkbw5"


pushover=[{
        "pushover_token":"arbs7ccq98rqoz78pagtcqyc523hro",
        "pushover_api":"utmxhixkgtchrdahx3tfj6sxgfkbw5"  
    },
    {
        "pushover_token":"af5c3jtswdoan29x92sycyjqz76rbq",
        "pushover_api":"ug7adwprvzzmzo133c678cz85vpc9m"  
    }
]

RABBITMQ_SERVER="**************"
API_SERVER="**************:2222" 


currency={
    "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v":"USDC",
    "********************************************":"WBTC",
    "So11111111111111111111111111111111111111112":"WSOL",
    "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB":"USDT",
    }

#cureencty price for 1 sol 
rate={
    "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v":147,
    "********************************************":0.0024,
    "So11111111111111111111111111111111111111112":1,
    "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB":147,
    }
solana_price=142.0
last_price={}
block={}
token_sigs={}
token_supply={}

trade_colors={"buy":"#00FF00","sell":"#FF0000","insider":"#0000FF"}

token_update_ws_url ='wss://little-sleek-mansion.solana-mainnet.quiknode.pro/4b69dc2b49ac07ae787d9c65b8a33ccee7a904d2'
#token_update_ws_url2='wss://mainnet.helius-rpc.com/?api-key=094cf3fa-bf7c-46cd-9b40-df49beb9b6c3'
#token_update_ws_url2='wss://endpoints.omniatech.io/v1/ws/sol/mainnet/bf8f530c3d624f1889e1d0c720901fb1'
token_update_ws_url="wss://go.getblock.io/9c188ee75a47487ca8f5508c18ae6cdd"
pump_address="metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s"
token_address=""
block_counter={ }
redis_connection=None
mint_scan_address={}  # mint_scan_address[mint]=mint|account

ankr_url='https://rpc.ankr.com/solana/4020c5ecce79f530e46c5adce15ef75d1b0e13d5732c15107db309b3b1207931'
alchemy_url='https://solana-mainnet.g.alchemy.com/v2/********************************'


rugcheck_api_key="8b2f634d-6220-4b93-a1fa-3def5a59775a"
#mysql shared pool
pool=None
mail_key="imlecdhhciwhlfjt"
sender = "<EMAIL>"
recipients = ["<EMAIL>","<EMAIL>"]
#recipients = ["<EMAIL>"]
quicknode_rpc="https://light-skilled-violet.solana-mainnet.quiknode.pro/fe06024365496464f231a10a777b0a61c3b65f58"
discord_webhook="https://discord.com/api/webhooks/1293608534507917333/PDeRhf0C875F-e3Q8YFMu8jO7scAhYRlapKgbb0qEoNVJPrLPgkr5uEFakn0gvjkoiNc"

discord_webhook_scanner="https://discord.com/api/webhooks/1328648852110180393/ZXxCLkZcPtD5Ul92vbmcO0hw8xTVxW1WoExacGJFRsJupmW1n9Iu2IcoYRwo93lK8vGV"

proxy="http://a81ad8141f53fcbd72ac:<EMAIL>:823"
proxies = {
    'http': proxy,
    'https': proxy,
}
proxy_host = 'gw.dataimpulse.com'
proxy_port = '823'
proxy_user = 'a81ad8141f53fcbd72ac'
proxy_pass = 'a3be642487aed06f'

webhook_emails=["<EMAIL>"]
min_holding=1
minimum_holding=1000
max_wallets=22
wallet_tracking_api=[
   # "8bff9957-1635-4a96-97bd-ee66697a4918",
    #"4184847c-934e-437e-9c04-387c225c8033",
    "868166b5-f9bf-4771-8923-7bd4be842daa",
    "c5d1b8dd-8fc8-44d3-bebf-7f6214e177a8",
    "c3cff3eb-a5ea-4b4d-a7c8-95ad5d44445f",
    "8c76f8f5-954f-4b58-a1af-2897acf22598",
    "ff4f607b-2327-4f55-8e42-6dab753681e2",
]

failed_tokens=[]

