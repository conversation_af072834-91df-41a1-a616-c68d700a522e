from modules.config import *
import requests
import random,json
import concurrent.futures
import random
import json
import requests
import time
import asyncio
import aiohttp, re
import modules.config as config

sol_price=150

def time_to_exec(start_time):
    return f"{round(time.time()-start_time,2)}s"

def get_token_liquidity(bonding_curve):
    #========  get liquidity liquidity  + 4200 ==========

    asset_id=bonding_curve

    randNum = random.randint(0, len(api_key) - 1)
    url = f"https://mainnet.helius-rpc.com/?api-key={api_key[randNum]}"
    headers = {"Content-Type": "application/json"}
    payload = {
        "jsonrpc": "2.0",
        "id": "M4tCEjM9B7k8kNMVBtAfktmRJShMjVrdPWZDZboXfQG",
        "method": "getBalance",
        "params": 
            [ asset_id]
        
    }
    response = requests.post(url, headers=headers, data=json.dumps(payload))
    print(response)
    response_data = response.json()
    liquidity=response_data['result']['value']
    return liquidity

def get_trades_all(token, limit=200):
    total_trades = get_trades_count(token)
    if total_trades is None:
        return None
    if total_trades == 0:
        return None
    total_trades = int(total_trades)    

    # If the provided limit exceeds total_trades, use total_trades as the limit
    limit = min(limit, total_trades)

    all_trades = []  # List to collect all the trades
    offset = 0
    
    while offset < total_trades:
        # Use the updated limit for each request
        url = f"https://frontend-api.pump.fun/trades/all/{token}?limit={limit}&offset={offset}&minimumSize=0"
        headers = {
            "accept": "*/*"
        }
        
        response = requests.get(url, headers=headers)
        
        if response.status_code == 200:
            trades = response.json()  # Assuming the response is JSON-formatted
            if not trades:
                break
            all_trades.extend(trades)  # Add the new trades to the collection
            offset += len(trades)  # Increment offset by the number of trades retrieved
        else:
            print(f"Failed to fetch data at offset {offset}. Status code: {response.status_code}")
            break
    if all_trades:
        data={}
        data["users"]=[]
        data["unique_users"]=[]
        data["buys"]=0
        data["sells"]=0
        data["user_buys"]=0
        data["user_sells"]=0
        data["user_volume_buys"]=0
        data["user_volume_sells"]=0
        data["volume_buys"]=0
        data["volume_sells"]=0
        data["trades_in_first_slog"]=0
        data["trade_amount_in_first+slog"]=0
        first_slot=all_trades[-1]["slot"]   


        for trade in all_trades:
            if trade["slot"] == first_slot:
                data["trades_in_first_slog"] += 1
                data["trade_amount_in_first+slog"] += trade["sol_amount"]
            if trade["user"] not in data["unique_users"]:
                data["unique_users"].append(trade["user"])
            data["users"].append(trade["user"])
            if trade["is_buy"]:
                data["buys"] += 1
                if trade["username"] != None:
                    data["user_buys"] += 1
                    data["volume_buys"] += trade["sol_amount"]
                    data["user_volume_buys"] += trade["sol_amount"]
                else:
                    data["volume_buys"] += trade["sol_amount"]
            else:
                data["sells"] += 1
                if trade["username"] != None:
                    data["user_sells"] += 1
                    data["volume_sells"] += trade["sol_amount"]
                    data["user_volume_sells"] += trade["sol_amount"]
                else:
                    data["volume_sells"] += trade["sol_amount"]

        return data
    return  None

    """
    bundl deals in first block ???
    get total trades, num buys/sells,  user buys/sells , volume buys+sells 
    {
        "signature": "4vWYxtUhWzb5myCkFeosvhpF5sRHfsW3roC8qg322KgkmfuGoRd1sSHnyF5X2m48XV9jkApW4wh5GXTx18ruF5MW",
        "mint": "DZ4NyWvUFrbTLvPErdWPwXe5VeUKHKHrT46ht3phpump",
        "sol_amount": ********,
        "token_amount": ************,
        "is_buy": false,
        "user": "D26x6TTTDjCKsH5c9AjX9rgqjAwghoffwuwLw6ypfWTL",
        "timestamp": **********,
        "tx_index": 2,
        "username": null,
        "profile_image": null,
        "slot": *********
    },
    """

def get_top_accounts(token,complete=False):
    supply=1e9
    url="https://go.getblock.io/b7a22554b2c64eac87eafdb5a144fd2e"
    #url="https://api.mainnet-beta.solana.com"
    randNum = random.randint(0, len(api_key) - 1)
    url=f"https://mainnet.helius-rpc.com/?api-key={api_key[randNum]}"

    request_payload = {
        "jsonrpc": "2.0",
        "id": "1",
        "method": "getTokenLargestAccounts",
        "params": [token]
    }
    headers={"Content-Type": "application/json"}

    
    response = requests.get(url, headers=headers, data=json.dumps(request_payload))
    if response.status_code == 200:
        data=json.loads(response.text)
        top10=data["result"]["value"][:20]

        values=[]
        for value in top10:
            if value["address"] != "5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1":
                values.append({"address":value["address"],"amount":round(value["uiAmount"]/supply*100,2)})
        if complete:
            return values[1:20]  # Return the JSON response if the request was successful
        else:
            return values[1:20]
    else:
        print(f"Failed to fetch data. Status code: {response.status_code}")
        return None
    
def get_trades_count(token):
    url = f"https://frontend-api.pump.fun/trades/count/{token}?minimumSize=0"
    headers = {
        "accept": "*/*"
    }
    
    response = requests.get(url, headers=headers)
    
    if response.status_code == 200:
        return response.text  # Return the JSON response if the request was successful
    else:
        print(f"Failed to fetch data. Status code: {response.status_code}")
        return None

def get_dex_paid(token):

    url = f"https://api.dexscreener.com/orders/v1/solana/{token}"
    headers = {
        "accept": "*/*"
    }
    
    response = requests.get(url, headers=headers)
    #print("dex:",response.json())
    if response.status_code == 200:
        data=response.json()  # Return the JSON response if the request was successful
        if len(data)>0:
            if "status" in data[0]:
                if data[0]["status"]=="approved":
                    #print(data[0]["paymentTimestamp"])
                    return f"{human_time_diff(data[0]["paymentTimestamp"])} ago"
                        
        return "No"

    else:
        print(f"Failed to fetch data. Status code: {response.status_code}")
        return None

def dev_balance(address,token):
    data=get_balance(address)
    if len(data)>0:
        for row in data:
            if row["mint"]==token:
                #print(row["balance"])
                usd=row["value"]*sol_price
                return f"{round(row["balance"]/1e6/1e9*100,2)}%  ({round(usd,2)}$)"
    return "0%"


def get_balance(address):
    url=f"https://frontend-api.pump.fun/balances/{address}?offset=0&limit=100&minBalance=0"
    headers = {
        "accept": "*/*"
    }
    
    response = requests.get(url, headers=headers)
    
    if response.status_code == 200:
        data=json.loads(response.text)  # Return the JSON response if the request was successful    
        return data
    else:
        print(f"Failed to fetch data. Status code: {response.status_code}")
        return None


def get_coin_data(token, max_retries=1, backoff_factor=1):
    """
    Fetches coin data from the Pump API with retry logic.

    Args:
        token (str): The token identifier for the coin.
        max_retries (int, optional): Maximum number of retry attempts. Default is 3.
        backoff_factor (int, optional): Factor for calculating sleep time between retries. Default is 1.

    Returns:
        dict or None: Processed coin data if successful, else None.
    """
    if token in config.failed_tokens:
        print(f"Skipping failed token: {token}")
        return None

    url = f"https://frontend-api.pump.fun/coins/{token}"
    headers = {
        "accept": "*/*"
    }

    for attempt in range(1, max_retries + 1):
        try:
            response = requests.get(url, headers=headers , timeout=10)
            if response.status_code == 200:
                data = response.json()
                # Process raydium_pool
                data["raydium_pool"] = "Yes" if data.get("raydium_pool") else "No"

                # Process market cap
                usd_mcap = data.get('usd_market_cap', 0)
                if usd_mcap > 1_000_000:
                    mcap = f"${round(usd_mcap / 1_000_000, 2)}M"
                else:
                    mcap = f"${round(usd_mcap / 1_000, 2)}K"
                data["mcap"] = mcap
                print(f"RC:{response.status_code} token: {token}")
                return data  # Successful response
            else:
                if response.status_code == 500:
                    return None
        except requests.RequestException as e:
            print(f"Attempt {attempt}: An error occurred: {e} token: {token}")

        if attempt < max_retries:
            sleep_time = backoff_factor * attempt
            print(f"Retrying in {sleep_time} seconds... RC: {response.status_code} token: {token}")
            time.sleep(sleep_time)

    print(f"Failed to fetch data after multiple attempts: token: {token}")
    config.failed_tokens.append(token)
    return None

async def get_token_replies(token,trades):
    url = f"https://frontend-api.pump.fun/replies/{token}?limit=10000&offset=0&user=all&reverseOrder=false"
    headers = {
        "accept": "*/*"
    }
    # TODO: get all replays]
    replies_start_time = time.time()
    response = requests.get(url, headers=headers)
    
    if response.status_code == 200:


        if response.json() == []:
            return None
        replies=response.json()

        data={}
        data["users"]=[]
        data["total_comments"]=0
        data["real_comments"]=0
        data["trade_users"]=0


        for reply in replies:
            data["total_comments"] += 1
            if reply["user"] not in data["users"]:
                data["users"].append(reply["user"])

            if reply["username"] != None:
                data["real_comments"] += 1
            if reply["user"] in trades["users"]:
                data["trade_users"] += 1
        print(f"Replies took: {time_to_exec(replies_start_time)}")
        return data
    else:
        print(f"Failed to fetch data. Status code: {response.status_code}")
        return None
    """
    get total comments, commented buy buyers, real comments, bot comments
    [
    {
        "signature": null,
        "is_buy": null,
        "sol_amount": null,
        "id": 16719867,
        "mint": "AEXbqWmEHY4wXS1XeLfieBDh3ZEvVqkAdDzdaMAwpump",
        "file_uri": null,
        "text": "HOLD my boys and join the tg , dex being updated",
        "user": "48zWMpZcxZ5PsNhJGm5DwEc6FDBwUc5PBhrTysTaQhEq",
        "timestamp": 1728505572653,
        "total_likes": 0,
        "username": null,
        "profile_image": null,
        "liked_by_user": false
    },
    """

def get_user_balances(user):
    url = f"https://frontend-api.pump.fun/balances/{user}?offset=0&limit=1000&minBalance=0"
    headers = {
        "accept": "*/*"
    }
    
    response = requests.get(url, headers=headers)
    
    if response.status_code == 200:
        return response.json()  # Return the JSON response if the request was successful
    else:
        print(f"Failed to fetch data. Status code: {response.status_code}")
        return None
    """
    get account balance in lamports, 
	report : did it buy compoare to all trades 

    [
    {
        "address": "CCecKo1uiNbb3UKGVtivvmJ6d7YzLYusY63EJevuN57E",
        "mint": "G1LBmZYHmy22wxNBsFMCDhcr6MBtLcm9wycUmgQkpump",
        "balance": 637435,
        "image_uri": "https://ipfs.io/ipfs/Qmbcp4kHWjAZYgZTrLyTBgTauDjr3j54BhM9n4ShPxSWfM",
        "symbol": "bambi",
        "name": "The famous cat",
        "market_cap": 28.*********,
        "value": 1.815118181162231e-8
    },
    """

def get_candles(token, timeframe=None, limit=200):
    url = f"https://frontend-api.pump.fun/candlesticks/{token}?offset=1&limit=100&timeframe={timeframe}"
    headers = {
        "accept": "*/*"
    }
    
    response = requests.get(url, headers=headers)
    
    if response.status_code == 200:
        return response.json()  # Return the JSON response if the request was successful
    else:
        print(f"Failed to fetch data. Status code: {response.status_code}")
        return None
    """
    get account balance in lamports, 
	report : did it buy compoare to all trades 

	
Response body
Download
[
  {
    "mint": "DZ4NyWvUFrbTLvPErdWPwXe5VeUKHKHrT46ht3phpump",
    "timestamp": **********,
    "open": 1.2516750334618983e-7,
    "high": 1.2525555955630015e-7,
    "low": 1.2516750334618983e-7,
    "close": 1.2516750335802124e-7,
    "volume": *********,
    "slot": *********,
    "is_5_min": true,
    "is_1_min": true
  },
    """

def html_to_image(data):
    """
        input:data  <- contains all data we need, contains links to dependencie images or urls. 
        generate html send it to crome browser and save image
        output: image link from server. 
    """
    pass

def user_coins(user):
    url = f"https://frontend-api.pump.fun/coins/user-created-coins/{user}?limit=1000&offset=0&includeNsfw=true"
    headers = {
        "accept": "*/*"
    }
    
    response = requests.get(url, headers=headers)
    
    if response.status_code == 200:


        if response.json() == []:
            return None
        replies=response.json()

        data={}
        data["users"]=[]
        data["total_comments"]=0
        data["real_comments"]=0
        data["trade_users"]=0


        for reply in replies:
            data["total_comments"] += 1
            data["users"].append(reply["user"])
            if reply["username"] != None:
                data["real_comments"] += 1
            if reply["user"] in trades["users"]:
                data["trade_users"] += 1

        return data
    else:
        print(f"Failed to fetch data. Status code: {response.status_code}")
        return None
    """
    [
  {
    x "mint": "76DiMM7FgUyhAxdyAwXVLL7ixg1hGpizxCA7evLLpump",
    x "name": "Wop Cat",
    x "symbol": "WC",
    x "image_uri": "https://ipfs.io/ipfs/QmQ9inSQwGDsrLvE4VxQSsHNYr2Jp6K8reE4p4x3ZcLCa3",
    x "created_timestamp": 1728672120225,
    x "raydium_pool": null,
    x "king_of_the_hill_timestamp": null,
    x "reply_count": 53,
    x "usd_market_cap": 4188.108543858731
    # need get last trade data
  }
]
    """


async def get_wallet_sol_balances(addresses):

    max_retries = 10  # Maximum number of retries
    delay = 1  # Delay between retries in seconds

    randNum = random.randint(0, len(api_key) - 1)
    url=f"https://mainnet.helius-rpc.com/?api-key={api_key[randNum]}"

    proxy_url = f'http://{proxy_host}:{proxy_port}'
    proxy_auth = aiohttp.BasicAuth(proxy_user, proxy_pass)

    print(f"Fetching balances for {len(addresses)} addresses")
    request_payload = {
        "jsonrpc": "2.0",
        "id": "1",
        "method": "getMultipleAccounts",
        "params": [addresses,{"encoding":"jsonParsed"}]
    }
    header={"Content-Type": "application/json"}
   
    for attempt in range(1, max_retries + 1):
        try:

            async with aiohttp.ClientSession() as session:
                async with session.post(url,headers=header, data=json.dumps(request_payload)) as response:    
                    response_data = await response.json()
                    all_owners = []
                    bal_arr=[]
                    if "value" in response_data["result"] and len(response_data["result"]["value"]) > 0:
                        for account in response_data["result"]["value"]:
                            if account is not None:
                                if "owner" in account and "lamports" in account:
                                    all_owners.append({"owner":account["owner"],"amount": round(account["lamports"]/1e9,2) } )
                            else:
                                all_owners.append({"owner":"1111","amount": 0 } )

                        counter=0
                        
                        #print(all_owners)
                        if len(all_owners) > 0:
                            for account in all_owners:
                                if account["owner"] == "11111111111111111111111111111111":
                                    bal_arr.append({"owner":addresses[counter],"amount":account["amount"]})
                                counter+=1
                        
                        return bal_arr  # Success, so exit the function

        except Exception as e:
            print(f"Balances tx Attempt {attempt} failed for token : {e}")
            #print(response_data)
            print(response_data["result"]["value"])
            if attempt == max_retries:
                print(f"Balances All {max_retries} attempts failed .")
                return None  # Or raise an exception if you prefer
            else:
                await asyncio.sleep(delay)  # Wait before retrying




async def get_token_wallets(token):

    max_retries = 10  # Maximum number of retries
    delay = 1  # Delay between retries in seconds

    randNum = random.randint(0, len(api_key) - 1)
    url=f"https://mainnet.helius-rpc.com/?api-key={api_key[randNum]}"

    proxy_url = f'http://{proxy_host}:{proxy_port}'
    proxy_auth = aiohttp.BasicAuth(proxy_user, proxy_pass)
    request_payload = {
        "jsonrpc": "2.0",
        "id": "1",
        "method": "getTokenAccounts",
        "params": {"mint":token,"limit": 1000}
    }
    header={"Content-Type": "application/json"}
   
    for attempt in range(1, max_retries + 1):
        try:

            async with aiohttp.ClientSession() as session:
                async with session.post(url,headers=header, data=json.dumps(request_payload)) as response:    
                    response_data = await response.json()
                    all_owners = []
                    for account in response_data["result"]["token_accounts"]:
                        all_owners.append({"owner":account["owner"],"amount":account["amount"]})


                    all = sorted(all_owners, key=lambda d: d['amount'],reverse=True)
                    return all  # Success, so exit the function

        except Exception as e:
            #print(f"ADDR tx Attempt {attempt} failed for token {token}: {e}")
            #print(response_data)
            if attempt == max_retries:
                print(f"All {max_retries} attempts failed for token {token}.")
                return None  # Or raise an exception if you prefer
            else:
                await asyncio.sleep(delay)  # Wait before retrying

async def fetch_top_holder(session, address):

    max_retries = 10  # Maximum number of retries
    delay = 1  # Delay between retries in seconds
    randNum = random.randint(0, len(api_key) - 1)
    url=f"https://mainnet.helius-rpc.com/?api-key={api_key[randNum]}"

    proxy_url = f'http://{proxy_host}:{proxy_port}'
    proxy_auth = aiohttp.BasicAuth(proxy_user, proxy_pass)
    request_payload = {
        "jsonrpc": "2.0",
        "id": "1",
        "method": "getAssetsByOwner",
    "params": 
        { "ownerAddress": address , "displayOptions":{ "showNativeBalance":False,"showZeroBalance":False,"showFungible":True } }
    }


    header={"Content-Type": "application/json"}
    for attempt in range(1, max_retries + 1):
        try:
            #async with session.post(url,headers=header, proxy=proxy_url,proxy_auth=proxy_auth,data=json.dumps(request_payload)) as response:
            async with session.post(url,headers=header, data=json.dumps(request_payload)) as response:    
                response_data = await response.json()

                data=response_data["result"]["items"]
                holder_data={}
                top_holders=[]
                for item in data:
                    if item["interface"] == "FungibleToken":
                        if "token_info" in item and "price_info" in item["token_info"] and "total_price" in item["token_info"]["price_info"]:
                            price=item["token_info"]["price_info"]["total_price"]
                            if price>min_holding and item["content"]["metadata"]["symbol"] != "USDC":
                                top_holders.append({"symbol":item["content"]["metadata"]["symbol"],"price":price})

                if len(top_holders) > 0:
                    top_holdings = sorted(top_holders, key=lambda d: d['price'],reverse=True)
                    holder_data={"address":address,"top_holdings":top_holdings[0:3]}
                return holder_data  # Success, so exit the function

        except Exception as e:
            #print(f"HOLDERS tx Attempt {attempt} failed for address {address}: {e}")
            #print(response_data)
            if attempt == max_retries:
                print(f"Holders All {max_retries} attempts failed for address {address}.")
                return None  # Or raise an exception if you prefer
            else:
                await asyncio.sleep(delay)  # Wait before retrying


async def fetch_top_holdings(addresses):
    async with aiohttp.ClientSession() as session:
        holdings_start_time = time.time()
        tasks = [fetch_top_holder(session, address) for address in addresses]
        results=await asyncio.gather(*tasks)
        print(f"Top holders took: {time_to_exec(holdings_start_time)}")
        return results


async def get_address_tx(session, address):

    max_retries = 10  # Maximum number of retries
    delay = 1  # Delay between retries in seconds
    url = f"https://api.mainnet-beta.solana.com"
    url="https://go.getblock.io/b7a22554b2c64eac87eafdb5a144fd2e"

    randNum = random.randint(0, len(api_key) - 1)
    url=f"https://mainnet.helius-rpc.com/?api-key={api_key[randNum]}"

    proxy_url = f'http://{proxy_host}:{proxy_port}'
    proxy_auth = aiohttp.BasicAuth(proxy_user, proxy_pass)
    request_payload = {
        "jsonrpc": "2.0",
        "id": "1",
        "method": "getSignaturesForAddress",
        "params": [address, {"limit": 1000, "commitment": "confirmed"}]
    }
    header={"Content-Type": "application/json"}
    for attempt in range(1, max_retries + 1):
        try:
            #async with session.post(url,headers=header, proxy=proxy_url,proxy_auth=proxy_auth,data=json.dumps(request_payload)) as response:
            async with session.post(url,headers=header, data=json.dumps(request_payload)) as response:    
                response_data = await response.json()
                return len(response_data["result"])  # Success, so exit the function

        except Exception as e:
            print(f"ADDR tx Attempt {attempt} failed for address {address}: {e}")
            print(response_data)
            if attempt == max_retries:
                print(f"ADDR All {max_retries} attempts failed for address {address}.")
                return None  # Or raise an exception if you prefer
            else:
                await asyncio.sleep(delay)  # Wait before retrying

async def fetch_all_tx(addresses):
    async with aiohttp.ClientSession() as session:
        tasks = [get_address_tx(session, address) for address in addresses]
        return await asyncio.gather(*tasks)
    
async def get_address_age(session, address):

    max_retries = 10  # Maximum number of retries
    delay = 1  # Delay between retries in seconds
    url = f"https://api.mainnet-beta.solana.com"
    url="https://go.getblock.io/b7a22554b2c64eac87eafdb5a144fd2e"
    randNum = random.randint(0, len(api_key) - 1)
    url=f"https://mainnet.helius-rpc.com/?api-key={api_key[randNum]}"

    proxy_url = f'http://{proxy_host}:{proxy_port}'
    proxy_auth = aiohttp.BasicAuth(proxy_user, proxy_pass)
    request_payload = {
        "jsonrpc": "2.0",
        "id": "1",
        "method": "getSignaturesForAddress",
        "params": [address, {"limit": 1000, "commitment": "confirmed"}]
    }
    header={"Content-Type": "application/json"}
    for attempt in range(1, max_retries + 1):
        try:
            async with session.post(url,headers=header,data=json.dumps(request_payload)) as response:
            #async with session.post(url,headers=header, proxy=proxy_url,proxy_auth=proxy_auth,data=json.dumps(request_payload)) as response:
                response_data = await response.json()
                if len(response_data["result"])  < 900 and  len(response_data["result"]) > 1:  
                    #print(f"====== {len(response_data["result"])}   tx: {address}")                   
                    block = response_data["result"][-1]["blockTime"]
                    return {"blockTime":block,"address":address}  # Success, so exit the function

        except Exception as e:
            print(f"Age Attempt {attempt} failed for address {address}: {e}")
            print(response_data)
            if attempt == max_retries:
                print(f"Age All {max_retries} attempts failed for address {address}.")
                return None  # Or raise an exception if you prefer
            else:
                await asyncio.sleep(delay)  # Wait before retrying

async def fetch_all_age(trades):
    addresses=trades["unique_users"]

    async with aiohttp.ClientSession() as session:
        age_start = time.time()
        tasks = [get_address_age(session, address) for address in addresses]
        results=await asyncio.gather(*tasks)
        #print time took
        print(f"Age took: {time_to_exec(age_start)}")
        return results
    
async def get_address_transactions(session, address, mint, bonding_curve):
    PUMP_FEE = "CebN5WGQ4jvEPvsVU4EoHEpgzq1VV7AbicfhtW4xC9iM"
    max_retries = 10  # Maximum number of retries
    delay = 1  # Delay between retries in seconds

    randNum = random.randint(0, len(api_key) - 1)
    url = f"https://api.helius.xyz/v0/addresses/{address}/transactions?api-key={api_key[randNum]}"

    proxy_url = f'http://{proxy_host}:{proxy_port}'
    proxy_auth = aiohttp.BasicAuth(proxy_user, proxy_pass)

    for attempt in range(1, max_retries + 1):
        try:

            #async with session.get(url, proxy=proxy_url,proxy_auth=proxy_auth) as response:   
            async with session.get(url) as response:
                response_data = await response.json()

                data = {}
                data["address"] = address
                data["count"] = len(response_data)
                data["valid_tx"] = 0
                data["first_tx"] = response_data[-1]["timestamp"] if len(response_data) > 0 else None
                data["native_recieved"] = []
                data["native_sent"] = []
                data["token_buy"] = 0
                data["token_sell"] = 0
                data["token_sended_to"] = []
                data["token_recieved_from"] = []
                data["buy_signer_for_addresses"] = []
                data["sell_signer_for_addresses"] = []

                for tx in response_data:
                    if tx.get("transactionError") is None:
                        feePayer = tx["feePayer"]
                        data["valid_tx"] += 1
                        if len(tx.get("tokenTransfers", [])) > 0:
                            for token in tx["tokenTransfers"]:
                                if token["mint"] == mint:
                                    if token["fromUserAccount"] == feePayer and token["toUserAccount"] == bonding_curve:
                                        data["token_sell"] += 1
                                    if token["toUserAccount"] == feePayer and token["fromUserAccount"] == bonding_curve:
                                        data["token_buy"] += 1

                                    if token["fromUserAccount"] != feePayer and token["toUserAccount"] == bonding_curve:
                                        data["buy_signer_for_addresses"].append(token["fromUserAccount"])
                                    if token["toUserAccount"] != feePayer and token["fromUserAccount"] == bonding_curve:
                                        data["sell_signer_for_addresses"].append(token["toUserAccount"])

                                    if token["fromUserAccount"] == address and token["toUserAccount"] != bonding_curve and token["toUserAccount"] != PUMP_FEE:
                                        data["token_sended_to"].append(token["toUserAccount"])
                                    if token["toUserAccount"] == address and token["fromUserAccount"] != bonding_curve:
                                        data["token_recieved_from"].append(token["fromUserAccount"])
                        else:
                            for native in tx["nativeTransfers"]:
                                if native["fromUserAccount"] == address:
                                    if native["toUserAccount"] not in data["native_sent"] and native["amount"] > 1000000 and native["toUserAccount"] not in CEX_ACCOUNTS and native["toUserAccount"] not in JITO_FEE:
                                        data["native_sent"].append(native["toUserAccount"])
                                if native["toUserAccount"] == address:
                                    if native["fromUserAccount"] not in data["native_recieved"] and native["amount"] > 1000000 and native["fromUserAccount"] not in CEX_ACCOUNTS and native["fromUserAccount"] not in JITO_FEE:
                                        data["native_recieved"].append(native["fromUserAccount"])

                #print(f"{address} : {len(response_data)}")
                return data  # Success, so exit the function

        except Exception as e:
            print(f"TX Attempt {attempt} failed for address {address}: {e}")
            if attempt == max_retries:
                print(f"TX All {max_retries} attempts failed for address {address}.")
                return None  # Or raise an exception if you prefer
            else:
                await asyncio.sleep(delay)  # Wait before retrying

async def fetch_all_transactions(trades, mint, bonding_curve):
    addresses=trades["unique_users"]
    tx_start_time = time.time()
    async with aiohttp.ClientSession() as session:
        tasks = [get_address_transactions(session, address, mint, bonding_curve) for address in addresses]
        results=await asyncio.gather(*tasks)
        print(f"TX took: {time_to_exec(tx_start_time)}")
        return results

    """
    input: array of addresses

    output: array of objects:
    - pct holding token
    - age !!
    - balance
    - total tx
    - num of buys and sells of token
    - success rate
    - token transfers - this and other
    - holding in other tokens 
    - array of trade history : 
        - list of addres transfered with amount ( token and sol)
        - listof addres transfered from with amount ( token and sol)


    
    """

def human_time_diff(timestamp):
    if timestamp is None:
        return "N/A"
    import datetime
    now = datetime.datetime.now()
    then = datetime.datetime.fromtimestamp(timestamp / 1000)
    diff = now - then
    days = diff.days
    hours = diff.seconds // 3600
    minutes = (diff.seconds // 60) % 60
    
    if days > 0:
        return f"{days}d {hours}h {minutes}m"
    elif hours > 0:
        return f"{hours}h {minutes}m"
    else:
        return f"{minutes}m"


def convert_to_usd_to_usd_human(usd):
    
    if usd is None:
        return "N/A"
  
    usd=int(usd)
    if int(usd) > 1000 and int(usd) < 1000000:
        amount=usd/1000
        amount=round(amount, 2)
        amount=f"${amount}k"
    elif int(usd) > 1000000:
        amount=usd/1000000
        amount=round(amount, 2)
        amount=f"${amount}M"
    else:
        amount=round(usd, 2)
        amount=f"${amount}"
    return amount


def convert_to_usd(sol):
    if sol is None:
        return "N/A"
    amount=(sol/1000000000 * sol_price)+4200
    if amount > 1000000:
        amount=amount/1000000
        amount=round(amount, 2)
        amount=f"${mcap}M"
    else:
        amount=amount/1000
        amount=round(amount, 2)
        amount=f"${amount}K"
    return amount

def format_social_links(data):
    # Get the social links, defaulting to "none" if they are not present
    telegram = f"[TG](<{data['telegram']}>)" if data.get('telegram') else "none"
    twitter = f"[X](<{data['twitter']}>)" if data.get('twitter') else "none"
    website = f"[WEB](<{data['website']}>)" if data.get('website') else "none"
    return f"> Socials: {telegram} {twitter} {website}"

async def get_pump_data(token):
    # general pump coin data
    data=get_coin_data(token)
    start_time = time.time()
    print(f"====== started: CA ({data['mint']} - {data['symbol']}) =========")


    if data["raydium_pool"] == "Yes":
        return data
        #return f"**{data['name']}** has migrated to Raydium pool. :rocket:"
    print("1a")
    # Construct the social links string
    liquidity=get_token_liquidity(data['bonding_curve'])
    print("1b")
    liquidity=convert_to_usd(liquidity)


    print("Fetch trades")
    trade_start_time = time.time()
    trades=get_trades_all(token)
    print("Trades fetched:",time_to_exec(trade_start_time))

    # TODO: process users, get user balances, get user trades, get user comments, get user likes, get user dislikes
    bonding_curve=data["bonding_curve"]
    
    # big requests
    #all_ages=await fetch_all_age(trades["unique_users"])
    #all_user_transactions = await fetch_all_transactions(trades["unique_users"], token, bonding_curve)
    #replies=get_token_replies(token,trades)
    topbalance_start_time = time.time()
    topbalances=get_top_accounts(data['mint'],data["complete"])
    print("Top balances fetched:",time_to_exec(topbalance_start_time))
    top10=[]
    for balance in topbalances:
        top10.append(balance["amount"])

  
  
    wall_start_time = time.time()
    wallets_in = await asyncio.gather(
        get_token_wallets(token)
        )
    print("Wallets fetched:",time_to_exec(wall_start_time))
    top20=[]
    print("wallets_in",len(wallets_in),len(wallets_in[0]))
    for w in wallets_in[0][:10]:
        top20.append(w["owner"])

    many_start_time = time.time()
    all_ages, all_user_transactions,replies,top_holders = await asyncio.gather(
        fetch_all_age(trades),  # Convert to list if needed
        fetch_all_transactions(trades, token, bonding_curve),
        get_token_replies(token, trades),
        fetch_top_holdings(top20)
        )
    print("Many fetched:",time_to_exec(many_start_time))
    top_20_start_time = time.time()
    top_20_balances = await asyncio.gather(
        get_wallet_sol_balances(top20)
        )
    print("Top 20 balances fetched:",time_to_exec(top_20_start_time))


    ctime=int(time.time())
    new_wallet=0
    max_age=60*60*24*5 # 5 days
    new_wallets_all=[]
    for age in all_ages:
        if age is not None:
            if age["blockTime"]:
                tdiff=ctime-age["blockTime"]
                if tdiff < max_age:
                    new_wallet+=1   
                    new_wallets_all.append(age["address"])
     
    new_wallets_still_in=[]
    new_wallets_amount=0
    other_wallets_amount=0
    if len(new_wallets_all) > 0:
        for wallet in wallets_in[0]:
            if wallet["owner"] in new_wallets_all:
                new_wallets_still_in.append(wallet["owner"])
                new_wallets_amount+=wallet["amount"]
            else:
                other_wallets_amount+=wallet["amount"]
    
    #proportion in percentage new_wallets vs otehr wallets
    new_wallets_pct=round(new_wallets_amount/(new_wallets_amount+other_wallets_amount)*100,2)
    other_wallets_pct=round(other_wallets_amount/(new_wallets_amount+other_wallets_amount)*100,2)

    
    print(f"alltx:{len(all_user_transactions)}")
    native_received_count = {}
    native_sent_count = {}
    tokens_sent_count={}

    for entry in all_user_transactions:
        if entry:
            if "token_sended_to" in entry:
                for token in entry["token_sended_to"]:
                    if token in tokens_sent_count:
                        tokens_sent_count[token] += 1
                    else:
                        tokens_sent_count[token] = 1



    # Iterate over the data and count occurrences of each address in native_recieved lists
    for entry in all_user_transactions:
        if entry:
            if "native_recieved" in entry:
                for native in entry["native_recieved"]:
                    if native in native_received_count:
                        native_received_count[native] += 1
                    else:
                        native_received_count[native] = 1
    # native_recieved
    for entry in all_user_transactions:
        if entry:
            if "native_sent" in entry:
                for native in entry["native_sent"]:
                    if native in native_sent_count:
                        native_sent_count[native] += 1
                    else:
                        native_sent_count[native] = 1
    
    #filtered_recievied = {value for key, value in native_received_count.items() if value > 1}
    wallets_recieved=[]
    for key,value in native_received_count.items():
        if value > 1:
            wallets_recieved.append(value)
    wallets_sent=[]
    #filtered_sent = {value for key, value in native_sent_count.items() if value > 1}
    for key,value in native_sent_count.items():
        if value > 1:
            wallets_sent.append(value)

    # print(filtered_recievied)
    # print(filtered_sent)

    print("fetch comments")   
    fresh_commenters=0
    if "users" in replies:
        all_tx=await fetch_all_tx(replies["users"])
        
        for tx in all_tx:
            if tx:
                if tx < 10:
                    fresh_commenters+=1
    print(f"Fresh Comments: {fresh_commenters}")
    

    def _sum(arr):
        sum = 0
        for i in arr:
            sum = sum + i
        return(sum)


    print("print table")   
    dex=get_dex_paid(data["mint"])
    top10=top10[1:10]
    top10_sum=_sum(top10)
    dev_holdings=dev_balance(data["creator"],data["mint"])
    end_time = time.time()
    print(f"====== finished: CA ({data['mint']} - {data['symbol']}) =========")
    execution_time = end_time - start_time


    #top_holders  #29 (0.90%) (597 SOL) | WORM ($29.16K), E/ACC ($32.63K), ALP ($31.89K)
    # num=1
    # pct=2
    # symbol=3
    # sol=3
    # array=[{"num":num,
    #         "pct":pct,
    #         "sol":sol,
    #         "symbol":[
    #                     {"symbol":symbol,"value":value}
    #                 ] 
    #         }
    #         ]
    # holder entry: {'address': 'Ecp5wZfZUHZFrj51cd5sZm2c2fbeiW53McKSFbg3LzRP', 'top_holdings': [{'symbol': 'meolloween', 'price': 1032.564161146981}]}, {}, {'address': 'AzEqWVf7oF7ZnRgmXB7HscWmgbpdDXibzrxp9V2NAnXM', 'top_holdings': [{'symbol': 'Ouf', 'price': 243.2362764126361}]}, {}, {},
    holder_arr=[]
    position=0
    for holder in top_holders:
        position+=1
        holder_entry={}
        if holder:
            holder_entry["position"]=position
            holder_entry["address"]=holder["address"]
            holder_entry["top_holdings"]=[]
            for pct in topbalances:
                if pct["address"] == holder["address"]:
                    holder_entry["pct"]=pct["amount"]
                    break


            for bal in top_20_balances[0]:
                if bal["owner"] == holder["address"]:
                    holder_entry["sol"]=bal["amount"]
                    break

            for holding in holder["top_holdings"]:
                if holding["symbol"] != data["symbol"]:
                    if holding["price"] > minimum_holding:
                        holder_entry["top_holdings"].append({"symbol":holding["symbol"],"price":convert_to_usd_to_usd_human(holding["price"])})
            if len(holder_entry["top_holdings"]) > 0:
                holder_arr.append(holder_entry)

    formated_holders=[]
    for holder in holder_arr:
        gmgn=f"https://gmgn.ai/sol/address/{holder['address']}"
        part1=f"[#{holder['position']}  ({holder['sol']} SOL)](<{gmgn}>) | "
        part2=""
        for holding in holder["top_holdings"]:
            part2+=f"***{holding['symbol']}*** (`{holding['price']}`), "
        part2=part2[:-2]
        formated_holders.append(part1+part2+"\n")

#  telegram = f"[TG](<{data['telegram']}>)" if data.get('telegram') else "none"
    top_holders_str = "".join(formated_holders)

    bot_data={
        "ca":data["mint"],
        "socials":format_social_links(data),
        "symbol":data["symbol"],
        "name":data["name"],
        "image_uri":data["image_uri"],
        "mcap":data["mcap"],
        "dex":dex,
        "liquidity":liquidity,
        "volume":convert_to_usd(trades['volume_sells']+trades['volume_buys']),
        "age":human_time_diff(data['created_timestamp']),
        "koth":human_time_diff(data['king_of_the_hill_timestamp']),
        "migrated":data["raydium_pool"],
        "raydium_pool":data["raydium_pool"],
        "is_currently_live":data['is_currently_live'],
        "total_comments":replies['total_comments'],
        "trade_users":replies['trade_users'],
        "real_comments":replies['real_comments'],
        "fresh_commenters":fresh_commenters,
        "dev_holdings":dev_holdings,
        "trades_in_first_slot":trades['trades_in_first_slog'],
        "trade_amount_in_first_slot":round(trades['trade_amount_in_first+slog']/1e9*140,2),
        "user_buys":trades['user_buys'],
        "user_sells":trades['user_sells'],
        "buys":trades['buys'],
        "sells":trades['sells'],
        "wallets_recieved":wallets_recieved,
        "wallets_sent":wallets_sent,
        "unique_users":len(trades['unique_users']),
        "new_wallet":new_wallet,
        "wallets_in":len(wallets_in[0]),
        "new_wallets_still_in":len(new_wallets_still_in),
        "new_wallets_pct":new_wallets_pct,
        "top10_sum":round(top10_sum,2),
        "top10":top10,
        "thumnail":data["image_uri"],
        "top_holders":top_holders_str,
        "execution_time":round(execution_time,2)


    }

    return bot_data

    data_old= f""":green_circle: **{data['symbol']}** ({data['name']})
    > :key: CA: `{data['mint']}`
    > :bird: Socials: {format_social_links(data)}  
    > :link: Dex paid: `{dex}`
    > :chart_with_upwards_trend: MCap: `{data["mcap"]}` :droplet:  Liquidity: `{liquidity}`  :dollar:  Volume: `{convert_to_usd(trades['volume_sells']+trades['volume_buys'])}` 
    > :clock1:  Age: `{human_time_diff(data['created_timestamp'])}`  :crown: KOTH: `{human_time_diff(data['king_of_the_hill_timestamp'])}` :earth_africa: Migrated: `{data["raydium_pool"]}`
    > :clapper:  StreamingLive: `{data['is_currently_live']}`
    > :speaking_head: Comments: Total:`{replies['total_comments']}` Traders: `{replies['trade_users']}` Pump.fun(users): `{replies['real_comments']}` :ghost: Fake: `{fresh_commenters}`
    > :man_technologist: Dev holdings: `{dev_holdings}`
    > :first_place: Trades in 1 slot: `{trades['trades_in_first_slog']}`
    > :coin: Trade amount in 1 slot: `{round(trades['trade_amount_in_first+slog']/1e9*140,2)}$`
    > :bar_chart: Pump.fun user trades: :b: `{trades['user_buys']}` :regional_indicator_s:  `{trades['user_sells']}`. 
    > :bar_chart: Total Trades:`{trades['buys']+trades['sells']}` :b: `{trades['buys']}` :regional_indicator_s:  `{trades['sells']}` 
    > :inbox_tray: No of wallets recieved tranacactions from same outsider wallet: `{wallets_recieved}`
    > :outbox_tray: Number or wallets  send fund to same outsider account: `{wallets_sent}`
    > :man_standing: Uniq traders since mint: `{len(trades['unique_users'])}`
    > :baby: New wallets since mint: `{new_wallet}`
    > :people_holding_hands: current Holders: `{len(wallets_in)}`
    > :baby: New wallets holding: `{len(new_wallets_still_in)}`  Toknens holding: (`{new_wallets_pct}%`)  
    > :keycap_ten: TOP 10 holdings: (`{round(top10_sum,2)}%`)  Distribution: `{top10}`
    Execution time:{round(execution_time,2)}s

    
    [Thumbnail]({data['image_uri']})
    """



def is_website_reachable(url,mint):
    try:
        response = requests.get(url, timeout=3)  # Set timeout to 1 second
        if response.status_code == 200:
            ca="CA not found"
            if mint.lower() in response.text.lower():
                ca="CA found"

            pattern = r"\b[a-zA-Z0-9]{41}pump\b"
            # Finding all matches
            if ca == "CA not found":
                matches = re.findall(pattern, response.text.lower(), flags=re.IGNORECASE)
                if matches:
                    ca="wrong CA found"
            
            pattern2 = r"\b(?:solana|pump)\b"
            matches = re.findall(pattern2, response.text, flags=re.IGNORECASE)
            solana="No Pump or Solana in mention found"
            if matches:
                solana = "Found Solana or Pump in website text"      


            return {"reachable":"Yes","ca":ca,"solana":solana}
    except requests.RequestException:
        return {"reachable":"No","ca":"N/A","solana":"N/A"}
        

    

async def get_pump_data_short(token):
    # general pump coin data
    data=get_coin_data(token)
    start_time = time.time()
    print(f"====== started: CA ({data['mint']} - {data['symbol']}) =========")


    liquidity=get_token_liquidity(data['bonding_curve'])
    print("1b")
    liquidity=convert_to_usd(liquidity)




    reachable=is_website_reachable(data["website"],data["mint"])

    bot_data={
        "ca":data["mint"],
        "socials":format_social_links(data),
        "website":data["website"],
        "twitter":data["twitter"],
        "reachable":reachable["reachable"],
        "solana":reachable["solana"],
        "cafound":reachable["ca"],
        "telegram":data["telegram"],
        "symbol":data["symbol"],
        "name":data["name"],
        "image_uri":data["image_uri"],
        "mcap":data["mcap"],
        "liquidity":liquidity,
        "age":human_time_diff(data['created_timestamp']),
        "thumnail":data["image_uri"]



    }

    return bot_data

    data_old= f""":green_circle: **{data['symbol']}** ({data['name']})
    > :key: CA: `{data['mint']}`
    > :bird: Socials: {format_social_links(data)}  
    > :link: Dex paid: `{dex}`
    > :chart_with_upwards_trend: MCap: `{data["mcap"]}` :droplet:  Liquidity: `{liquidity}`  :dollar:  Volume: `{convert_to_usd(trades['volume_sells']+trades['volume_buys'])}` 
    > :clock1:  Age: `{human_time_diff(data['created_timestamp'])}`  :crown: KOTH: `{human_time_diff(data['king_of_the_hill_timestamp'])}` :earth_africa: Migrated: `{data["raydium_pool"]}`
    > :clapper:  StreamingLive: `{data['is_currently_live']}`
    > :speaking_head: Comments: Total:`{replies['total_comments']}` Traders: `{replies['trade_users']}` Pump.fun(users): `{replies['real_comments']}` :ghost: Fake: `{fresh_commenters}`
    > :man_technologist: Dev holdings: `{dev_holdings}`
    > :first_place: Trades in 1 slot: `{trades['trades_in_first_slog']}`
    > :coin: Trade amount in 1 slot: `{round(trades['trade_amount_in_first+slog']/1e9*140,2)}$`
    > :bar_chart: Pump.fun user trades: :b: `{trades['user_buys']}` :regional_indicator_s:  `{trades['user_sells']}`. 
    > :bar_chart: Total Trades:`{trades['buys']+trades['sells']}` :b: `{trades['buys']}` :regional_indicator_s:  `{trades['sells']}` 
    > :inbox_tray: No of wallets recieved tranacactions from same outsider wallet: `{wallets_recieved}`
    > :outbox_tray: Number or wallets  send fund to same outsider account: `{wallets_sent}`
    > :man_standing: Uniq traders since mint: `{len(trades['unique_users'])}`
    > :baby: New wallets since mint: `{new_wallet}`
    > :people_holding_hands: current Holders: `{len(wallets_in)}`
    > :baby: New wallets holding: `{len(new_wallets_still_in)}`  Toknens holding: (`{new_wallets_pct}%`)  
    > :keycap_ten: TOP 10 holdings: (`{round(top10_sum,2)}%`)  Distribution: `{top10}`
    Execution time:{round(execution_time,2)}s

    
    [Thumbnail]({data['image_uri']})
    """







#
# TODO:
#  net worth holders  !!!
#  token transfers   !!!
#  Google lens screenshot - later
#  chart screenshot from candles - later 
# dev holdings 
# ATH pup api 
