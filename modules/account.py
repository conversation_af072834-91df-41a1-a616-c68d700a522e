import requests,random,json,time,re
import pandas as pd
import modules.db as db 
import modules.utils as utils
from modules.config import *
import redis,sys
import modules.redismq as redismq
from modules.transfer import get_token_info
import modules.decoders.pump as pump
import modules.decoders.system as system
from modules.config import last_price
from modules.config import block
from modules.config import trade_colors
from modules.config import token_supply
from modules.config import solana_price
from modules.redismq import get_redis_latest_sig
from modules.utils import fetch_token_price

log=utils.get_logger()


# Return: sig num,fist sig, last sig, where money from 
def get_account_age(address):
        request = {
            "jsonrpc": "2.0",
            "id": "1",
            "method": "getSignaturesForAddress",
            "params": [address, {"limit": 100}]
        }

        randNum = random.randint(0, len(api_key) - 1)

        url = f"https://mainnet.helius-rpc.com/?api-key={api_key[randNum]}"      

        request = requests.post(url, json=request)

        data = request.json()["result"]
        if len(data) == 0:
            return None
        return data[0]["blockTime"],data[-1]["blockTime"]



def fetch_all_transactions(address):
    max_tx=100
    last_signature = None 
    transactions = []
 
    while True:
        print("start")
        randNum = random.randint(0, len(api_key) - 1)
        url = f"https://api.helius.xyz/v0/addresses/{address}/transactions?api-key={api_key[randNum]}"

        if last_signature:
            url_with_signature = f"{url}&before={last_signature}"
        else:
            url_with_signature = url
        response = requests.get(url_with_signature)
        print("got data:")

        if response.status_code != 200:
            print("Error code：", response.status_code)
            time.sleep(1)
            continue

      
        data = response.json()
        
        if "error" in data:
            if "exceeded limit for api" in data:
                print(data)
                time.sleep(3)
                continue   
            else:
                return  transactions  
        
        transactions = transactions + data

        if data and len(data) < 100:
             return transactions
        if data and len(data) > 0:
            print("Transaction count：", len(transactions))
            last_signature = data[-1]["signature"]
            print(f"transaction count:{len(transactions)}")
          
        elif len(transactions)>max_tx:
            break
          
        else:
            print("End of check")
            print(f"transaction count:{len(transactions)}")
            break
        
        if len(transactions)>max_tx:
          break
          
    return  transactions  
