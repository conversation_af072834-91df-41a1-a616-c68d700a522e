from neo4j import GraphDatabase
import pandas as pd
from modules.config import * 
from tabulate import tabulate
from modules.utils import get_logger
from modules.utils import send_event
import json
log=get_logger()

class Neo4jClient:

    def __init__(self, uri, user, password):
        self.driver = GraphDatabase.driver(uri, auth=(user, password))

    def close(self):
        self.driver.close()

    def add_unique_node(self, label, properties):
        with self.driver.session() as session:
            result = session.write_transaction(self._create_unique_node, label, properties)
            return result

    @staticmethod
    def _create_unique_node(tx, label, properties):
        # Check if the node exists
        match_query = f"MATCH (n:{label} {{name: $name}}) RETURN n"
        result = tx.run(match_query, name=properties['name']).single()
        
        if result:
            return result[0]
        
        # If the node does not exist, create it
        create_query = f"CREATE (n:{label} {{"
        create_query += ", ".join([f"{key}: ${key}" for key in properties.keys()])
        create_query += "}) RETURN n"
        result = tx.run(create_query, **properties)
        return result.single()[0]

    def update_node(self, label, name, properties):
        with self.driver.session() as session:
            result = session.write_transaction(self._update_node, label, name, properties)
            return result

    @staticmethod
    def _update_node(tx, label, name, properties):
        set_clause = ", ".join([f"n.{key} = ${key}" for key in properties.keys()])
        query = f"MATCH (n:{label} {{name: $name}}) SET {set_clause} RETURN n"
        params = {"name": name}
        params.update(properties)
        result = tx.run(query, **params)
        #return result.single()[0]

    def add_unique_relationship(self, label1, properties1, label2, properties2, rel_type, rel_properties):
        with self.driver.session() as session:
            result = session.write_transaction(self._create_unique_relationship, label1, properties1, label2, properties2, rel_type, rel_properties)
            return result

    @staticmethod
    def _create_unique_relationship(tx, label1, properties1, label2, properties2, rel_type, rel_properties):
        query = (
            f"MATCH (a:{label1} {{name: $a_name}}), (b:{label2} {{name: $b_name}}) "
            f"MERGE (a)-[r:{rel_type}]->(b) "
            f"ON CREATE SET "
            + ", ".join([f"r.{key} = ${'r_' + key}" for key in rel_properties.keys()]) +
            f" ON MATCH SET "
            + ", ".join([f"r.{key} = COALESCE(r.{key}, ${'r_' + key})" for key in rel_properties.keys()]) +
            " RETURN r"
        )
        params = {'a_name': properties1['name'], 'b_name': properties2['name']}
        params.update({f'r_{key}': value for key, value in rel_properties.items()})
        result = tx.run(query, **params)

        #return result.single()[0]

    def read_data(self, query):
        with self.driver.session() as session:
            result = session.read_transaction(self._execute_read_query, query)
            return result

    @staticmethod
    def _execute_read_query(tx, query):
        result = tx.run(query)
        return [record.data() for record in result]

    def drop_all(self):
        with self.driver.session() as session:
            session.write_transaction(self._drop_all)

    @staticmethod
    def _drop_all(tx):
        tx.run("MATCH (n) DETACH DELETE n")

    def count_hops(self, name1, name2):
        with self.driver.session() as session:
            result = session.read_transaction(self._count_hops, name1, name2)
            return result

    @staticmethod
    def _count_hops(tx, name1, name2):
        query = (
            "MATCH (a {name: $name1}), (b {name: $name2}), "
            "p = shortestPath((a)-[*]-(b)) "
            "RETURN length(p) as hops"
        )
        result = tx.run(query, name1=name1, name2=name2).single()
        if result:
            return result["hops"]
        return None
    

    @staticmethod
    def add_tx_to_neo(neo_client, df):
        for _, tx in df.iterrows():
            if tx["coin"] == "sol" and float(tx["amount"]) < minsol:
                continue
            elif tx["type"] == "BURN":
                continue
            else:
                address1 = neo_client.add_unique_node("Address", {"name": tx["source"], "balance": 0, "total_tx": 20, "role": "user", "group": "B", "age": 30, "type": "wallet"})
                address2 = neo_client.add_unique_node("Address", {"name": tx["destination"], "balance": 0, "total_tx": 20, "role": "user", "group": "B", "age": 30, "type": "wallet"})
                print(f"edgelabel: {tx["source"]}-{tx["destination"]}")
                relationship = neo_client.add_unique_relationship("Address", {"name": tx["source"]}, "Address", {"name": tx["destination"]}, "TRANSFERRED", {"amount": 0,"amount_send": 0, "amount_received": 0})
                        
    @staticmethod
    def print_nodes(neo_client):
        node_query = """
        MATCH (n)
        RETURN n.name AS Name, n.balance AS Balance
        """
        relationship_query = """
        MATCH (a)-[r]->(b)
        RETURN a.name AS From, type(r) AS Relationship, b.name AS To, r.amount_send AS Amount_Sent, r.amount_received AS Amount_Received, r.amount AS amount
        """
        
        # Fetch nodes and relationships
        nodes_data = neo_client.read_data(node_query)
        relationships_data = neo_client.read_data(relationship_query)
        
        # Convert to DataFrames
        nodes_df = pd.DataFrame(nodes_data)
        relationships_df = pd.DataFrame(relationships_data)
        
        # Print nodes
        print("Nodes:")
        print(tabulate(nodes_df, headers='keys', tablefmt='psql'))
        
        # Print relationships
        print("\nRelationships:")
        print(tabulate(relationships_df, headers='keys', tablefmt='psql'))

    @staticmethod
    def update_balances(neo_client, df):
        for index, row in df.iterrows():
            print(f"update NODE balance: {row['account']}  new balance: {row['balance']}")
            neo_client.update_node("Address", row["account"], {"balance": row["balance"]})
            log.info(f"Seding event to message: account: {row["account"]}   balance: {row['balance']}")
            



