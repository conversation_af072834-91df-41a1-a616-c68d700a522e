import requests,json,random
from modules.config import ankr_url
import json
import threading
import time
import requests
import sys
from modules.config import *
import modules.config
from modules.config import last_price
from modules.config import block_counter
from modules.config import redis_connection
from modules.redismq import get_redis_latest_sig
import modules.decoders.pump as pump
import modules.redismq as redismq
import modules.db as db



JUPITER_FEE = [
    "45ruCyfdRkWpRNGEqWzjCiXRHkZs8WXCLQ67Pnpye7Hp"
]

SYSTEM_FEE_ACCOUNT="HWEoBxYs7ssKuudEjzjmpfJVX7Dvi7wescFsVx2L5yoY"
PUMP_FEE_ACCOUNT="CebN5WGQ4jvEPvsVU4EoHEpgzq1VV7AbicfhtW4xC9iM"
JITO_FEE = [
    "96gYZGLnJYVFmbjzopPSU6QiEV5fGqZNyN9nmNhvrZU5",
    "HFqU5x63VTqvQss8hp11i4wVV8bD44PvwucfZ2bU7gRe",
    "Cw8CFyM9FkoMi7K7Crf6HNQqf4uEMzpKw6QNghXLvLkY",
    "ADaUMid9yfUytqMBgopwjb2DTLSokTSzL1zt6iGPaS49",
    "DfXygSm4jCyNCybVYYK6DwvWqjKee8pbDmJGcLWNDXjh",
    "ADuUkR4vqLUMWXxW9gh6D6L8pMSawimctcNZ5pGwDcEt",
    "DttWaMuVvTiduZRnguLF7jNxTgiMBZ1hyAumKUiL2KRL",
    "3AVi9Tg9Uo68tJfuvoKvqKNWKkC5wPdSSdeBnizKZ6jT"
]
RAYDIUM = "5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1"
METEOR = "2MFoS3MPtvyQ4Wh4M9pdfPjz6UhVoNbFbGJAskCPCj3h"

AUTHORITIES=[RAYDIUM,METEOR]
FEE_ACCOUNTS=[SYSTEM_FEE_ACCOUNT,PUMP_FEE_ACCOUNT]+JITO_FEE+JUPITER_FEE



def decode_pump_program_instruction(tx):
    txs=[]
    for i in tx:
        for ii in i["instructions"]:
            if ii["programId"] =="6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P":
                encoded_data=ii["data"]
                pump_decode=pump.decode(encoded_data)
                if isinstance(pump_decode, list) and pump_decode[0] == 'TradeEvent':
                    txs.append(pump_decode[1])
    return txs

def set_last_price(token,price):
    global last_price
    last_price[token]=price   

def get_last_price(token):
    global last_price
    if token in last_price:
        return last_price[token]
    else:
        return 0

# Function to send a request to the WebSocket server

def find_pump_raydium_pair(tx):
    block=tx
    found_pump=False
    found_raydium=False
    for account in block['transaction']['message']['accountKeys']: 
        if account['pubkey'] == "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P":
            found_pump=True
        if account['pubkey'] == "5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1":
            found_raydium=True
    if found_pump and found_raydium:
        return True 
    else:
        return False
    

def get_network(tx,mint):
    block=tx

    # for account in block['transaction']['message']['accountKeys']: 
    #     if account['pubkey'] == "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P":
    #         modules.config.mint_scan_address[mint]=mint
    #         return mint

    instructions = block['transaction']['message']['instructions']
    meta=block["meta"]

    for i in instructions:
        
        if "programId" in i and i["programId"] == "675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8":

            if len(i["accounts"]) == 21:
                modules.config.mint_scan_address[mint]=i["accounts"][4]
                #print("returningmian liquiditypool:",i["accounts"][4])
                return i["accounts"][4]
            if len(i["accounts"]) == 17 or len(i["accounts"]) == 18:
                modules.config.mint_scan_address[mint]=i["accounts"][1]
                #print("returning main 17 18:",i["accounts"][1])
                return i["accounts"][1]

    for i in meta["innerInstructions"]:
        if "programId" in i and i["programId"] == "675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8":
            
            if len(i["accounts"]) == 17 or len(i["accounts"]) == 18:
                modules.config.mint_scan_address[mint]=i["accounts"][1]
                #print("returning inner 17 18:",i["accounts"][1])
                return i["accounts"][1]
            
    #check pump
    for account in block['transaction']['message']['accountKeys']: 
        if account['pubkey'] == "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P":
            modules.config.mint_scan_address[mint]=mint
            #print(f"returning:{mint}")
            return mint
    
    #print("Programm PUMP of RAYDIUM not found ")
    return modules.config.mint_scan_address[mint]



def process_solana_transaction(block,mint):


    global last_price
    global block_counter
    global redismq
    global redis_connection
    pump_address=mint
    transfers=[]
    token_owner={}
    instructions = block['transaction']['message']['instructions']
    account_keys = block['transaction']['message']['accountKeys']
    signature=block["transaction"]["signatures"][0]
    meta=block["meta"]

    slot = block["slot"]
    timestamp=block['blockTime']
    owner=account_keys[0]['pubkey']
  

    if find_pump_raydium_pair(block):
        return modules.config.mint_scan_address[mint],signature
    


    dev=["AEDr4TBd6VSjMy1yb4YHst65wLBakr8dDhaAAktRaFkv",
        "AZt9KWsVpmcoH3Ka5nBMwyNhetJLW9Lj2C7V1dEnWAko",
        "BkBSrzFzwfkh1jk98zJTdw7Hrx2bNYeKnfumrD4NmPd8",
        "BqVKaTqaf8ePx1oDn2vJKhTAv6dyn2sfqX7N43L7Z35y",
        "EFoJLgXRTu9W7mgaAabkWhGDxgVdZWAk27ZWSefx7Gx2"]

    inner_instructions=block["meta"]["innerInstructions"]

    for log in meta["logMessages"]:
        if "Liquidity" in log:
            print ("liquidity")
            return
    try:
        pumptx=decode_pump_program_instruction(inner_instructions)
    except Exception as e:
        print(f"Error: {e}")
        return

    # skip if raydium and pump in same transaction
    
    
    if len(pumptx)>0:
        
        for p in pumptx:
                mint=p["mint"]
                tokenAmount=p["token_amount"]
                solAmount=p["sol_amount"]
                is_buy=p["is_buy"]
                if is_buy:
                    trade="buy"
                else:
                    trade="sell"
                
                if solAmount == 0 or tokenAmount == 0:
                    print(f"Error: solAmount or tokenAmount is 0  sig: {signature}")
                    return None,signature
                
                price=(solAmount/tokenAmount/1000)

                mlp=get_last_price(pump_address) #last price
                if mlp == 0:
                    mlp=price
                if mlp > price:
                    high=mlp
                    low=price
                else:
                    high=price
                    low=mlp

                if low > high:
                    print(f"Error: low {low}  high {high}")
                color="orange"
                open_price=price
                close_price=mlp

                if slot in block_counter:
                    block_counter[slot]=block_counter[slot]+1
                else:
                    block_counter[slot]=1
                milliseconds=block_counter[slot]

                #millionseconds=int(timestamp)*1000   

                ttime=modules.utils.get_tx_time(mint,int(timestamp),"prepend")

                data={"broker":"PUMP", "color":color,"open":open_price,"close":close_price,   "high":high,"low":low,"address":owner,"trade":trade, "tokenAmount":tokenAmount,"solAmount":solAmount,"price":price,"timestamp":float(ttime),"milliseconds":milliseconds,"signature":signature,"slot":slot}
                
                #print(mlp,price)   
                set_last_price(pump_address,price)
                redis_connection=modules.config.redis_connection
                redismq.push_trade(redis_connection,data,pump_address,"lpush")
                program="PUMP"
                db.add_token_swap(owner,mint,trade,tokenAmount,solAmount,price,timestamp,signature,slot,program)
    

    else:

        def _check_chain_(transfers,pump_address): 
            final = transfers
            if len(final) == 2:
                if final[0]["mint"] != pump_address and final[1]["mint"] != pump_address:
                    print("Error: mint not found")
                    return
            
                if final[-1]["amount"] == 0 or final[0]["amount"] == 0:
                    print("Error: amount is 0")
                    return

                if final[0]["mint"]==pump_address:
                    trade="sell"
                    price=final[-1]["amount"]/final[0]["amount"]
                    volume=final[0]["amount"]
                    tokenAmount=final[0]["amount"]
                    solAmount=final[-1]["amount"]
                else:
                    trade="buy"
                    price=final[0]["amount"]/final[-1]["amount"]
                    volume=final[-1]["amount"]
                    tokenAmount=final[-1]["amount"]
                    solAmount=final[0]["amount"]

                
                mlp=get_last_price(pump_address) #last price
                if mlp == 0:
                    mlp=price
                if mlp > price:
                    high=mlp
                    low=price
                else:
                    high=price
                    low=mlp

                if low > high:
                    print(f"Error: low {low}  high {high}")
                color="orange"
                open_price=price
                close_price=mlp

                if slot in block_counter:
                    block_counter[slot]=block_counter[slot]+1
                else:
                    block_counter[slot]=1
                milliseconds=block_counter[slot]

                #millionseconds=int(timestamp)*1000   
                unix_timestamp = int(time.time())
                time_diff = unix_timestamp - int(timestamp)
                print(f"Time diff: {time_diff}")
                ttime=modules.utils.get_tx_time(mint,int(timestamp),"prepend")
                data={"broker":"RAYDIUM", "color":color,"open":open_price,"close":close_price,   "high":high,"low":low,"address":owner,"trade":trade, "tokenAmount":tokenAmount,"solAmount":solAmount,"price":price,"timestamp":float(ttime),"milliseconds":milliseconds,"signature":signature,"slot":slot}
                
                #print(mlp,price)   
                set_last_price(pump_address,price)
                redis_connection=modules.config.redis_connection
                redismq.push_trade(redis_connection,data,pump_address,"lpush")
                program="RAYDIUM"
                #db.add_token_swap(pool,owner,mint,trade,tokenAmount,solAmount,price,timestamp,signature,slot,program)

        # END of check_chain


        transfers=[]
        token_owner={}
        #instructions = row['transaction']['message']['instructions']
        #account_keys = row['transaction']['message']['accountKeys']

        #meta = row['meta']
        #post_token_balances = meta['postTokenBalances']
        #pre_token_balances = meta['preTokenBalances']


            

        def get_account_address_by_index(index):
            return account_keys[index]["pubkey"]
        
        def get_owner_by_token_account(tokenAccount):
            found=False
            for balance in meta["preTokenBalances"]:
                if get_account_address_by_index(balance["accountIndex"]) == tokenAccount:
                    return balance["owner"],balance["mint"]
            if tokenAccount in token_owner:
                return token_owner[tokenAccount]["owner"],token_owner[tokenAccount]["mint"]

            if found == False:
                return tokenAccount,"unknown"
            
        def process_tx(instruction):
            if instruction["program"] == "system" and instruction["parsed"]["type"] == "transfer":
                source = instruction["parsed"]["info"]["source"]
                destination = instruction["parsed"]["info"]["destination"]
                amount = instruction["parsed"]["info"]["lamports"]
                #print(f"Instruction: (transfer) Source: {source} Destination: {destination} Amount: {amount}  lamports")


            if instruction["program"] == "spl-associated-token-account" and  instruction["parsed"]["type"] == "createIdempotent":
                #print(f"Instruction: (createImpodent) Create associated token account owner:{instruction["parsed"]["info"]["source"]}  new splaccount:{instruction["parsed"]["info"]["account"]}  mint:{instruction["parsed"]["info"]["mint"]}")
                    token_owner[instruction["parsed"]["info"]["account"]]={"owner":instruction["parsed"]["info"]["source"],"mint":instruction["parsed"]["info"]["mint"]}
            
            if instruction["program"] == "spl-token" and instruction["parsed"]["type"] == "closeAccount":
                pass
                #print(f"Instruction: (closeAccount) owner:{instruction["parsed"]["info"]["owner"]}  closed account :{instruction["parsed"]["info"]["account"]}  ")
                    
            if instruction["program"] == "system" and instruction["parsed"]["type"] == "createAccount":
                #print(f"Instruction: (createAccount) owner:{instruction["parsed"]["info"]["source"]}  new account :{instruction["parsed"]["info"]["newAccount"]}  sum:{instruction["parsed"]["info"]["lamports"]}")
                pass
                #token_owner[instruction["parsed"]["info"]["newAccount"]]={"owner":instruction["parsed"]["info"]["source"],"mint":"So11111111111111111111111111111111111111112"}     
            if instruction["program"] == "spl-token" and instruction["parsed"]["type"] == "initializeAccount3":
                token_owner[instruction["parsed"]["info"]["account"]]={"owner":instruction["parsed"]["info"]["owner"],"mint":instruction["parsed"]["info"]["mint"]} 
                #print(f"Instruction: (initializeAccount3)  Set token for account :{instruction["parsed"]["info"]["account"]}  mint:{instruction["parsed"]["info"]["mint"]}  owner:{instruction["parsed"]["info"]["owner"]}")
                
            if instruction["program"] == "spl-token" and instruction["parsed"]["type"] == "transfer":
                if "multisigAuthority" in instruction["parsed"]["info"]:
                    source_owner,_=get_owner_by_token_account(instruction["parsed"]["info"]["multisigAuthority"])
                else:
                    source_owner,_=get_owner_by_token_account(instruction["parsed"]["info"]["authority"])
                
                destination_owner,destination_mint=get_owner_by_token_account(instruction["parsed"]["info"]["destination"]) 

                #print(f"Instruction: (spl-transfer)  Source:{instruction["parsed"]["info"]["source"]}  Destination:{instruction["parsed"]["info"]["destination"]}  Amount:{instruction["parsed"]["info"]["amount"]} mint:{destination_mint}" )
                ###print(f"Instruction: (spl-transfer)  Source:{source_owner}  Destination:{destination_owner}  Amount:{instruction["parsed"]["info"]["amount"]} mint:{destination_mint}" )

                if destination_mint == pump_address:
                    amount=int(instruction["parsed"]["info"]["amount"])/1000000
                elif destination_mint == "So11111111111111111111111111111111111111112":
                    amount=int(instruction["parsed"]["info"]["amount"])/**********
                else:

                    amount=int(instruction["parsed"]["info"]["amount"])/1000000

                # transfers.append({
                #         'source': source_owner,
                #         'destination': destination_owner,
                #         'amount': amount,
                #         'mint': destination_mint
                #     })
                return {
                        'source': source_owner,
                        'destination': destination_owner,
                        'amount': amount,
                        'mint': destination_mint}

            if instruction["program"] == "spl-token" and instruction["parsed"]["type"] == "transferChecked":

                if "multisigAuthority" in instruction["parsed"]["info"]:
                    source_owner,_=get_owner_by_token_account(instruction["parsed"]["info"]["multisigAuthority"])
                else:
                    source_owner,_=get_owner_by_token_account(instruction["parsed"]["info"]["authority"])
                destination_owner,destination_mint=get_owner_by_token_account(instruction["parsed"]["info"]["destination"]) 
                ###print(f"Instruction: (spl-transferChecked)  Source:{source_owner}  Destination:{destination_owner}  Amount:{instruction["parsed"]["info"]["tokenAmount"]["uiAmount"]} mint:{instruction["parsed"]["info"]["mint"]}" )

                # transfers.append({
                #     'source': source_owner,
                #     'destination': destination_owner,
                #     'amount': instruction["parsed"]["info"]["tokenAmount"]["uiAmount"],
                #     'mint': instruction["parsed"]["info"]["mint"]
                # })
                return {
                        'source': source_owner,
                        'destination': destination_owner,
                        'amount': instruction["parsed"]["info"]["tokenAmount"]["uiAmount"],
                        'mint': instruction["parsed"]["info"]["mint"]}
#resolve new accounts

        for index,i in enumerate(instructions):
            if "program" in i and i["program"] == "spl-token" and ( i["parsed"]["type"] == "initializeAccount3" or i["parsed"]["type"] == "initializeAccount" ):
                    token_owner[i["parsed"]["info"]["account"]]={"owner":i["parsed"]["info"]["owner"],"mint":i["parsed"]["info"]["mint"]} 
                    #print(f"Instruction: (initializeAccount3)  Set token for account :{i["parsed"]["info"]["account"]}  mint:{i["parsed"]["info"]["mint"]}  owner:{i["parsed"]["info"]["owner"]}")

        for inner_instruction in meta["innerInstructions"]:
            for ii in inner_instruction["instructions"]:
                if "program" in ii and ii["program"] == "spl-token" and ( ii["parsed"]["type"] == "initializeAccount3" or ii["parsed"]["type"] == "initializeAccount" ):
                    token_owner[ii["parsed"]["info"]["account"]]={"owner":ii["parsed"]["info"]["owner"],"mint":ii["parsed"]["info"]["mint"]} 
                    #print(f"Instruction: (initializeAccount3)  Set token for account :{ii["parsed"]["info"]["account"]}  mint:{ii["parsed"]["info"]["mint"]}  owner:{ii["parsed"]["info"]["owner"]}")



# end resolve


        for index,instruction in enumerate(instructions):
   
            if instruction["programId"] == "675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8"  or instruction["programId"] == "LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo" or instruction["programId"] == "CAMMCzo5YL8w4VFF8KVHrK22GGUsp5VTaW7grrKgrWqK" :
                transfers=[]
                
                for inner_instruction in meta["innerInstructions"]:
                    if inner_instruction["index"] == index:
                        for ii in inner_instruction["instructions"]:
                            if "program" in ii and ii["program"] == "spl-token" and (ii["parsed"]["type"] == "transfer" or ii["parsed"]["type"] == "transferChecked"):
                                transfers.append(process_tx(ii))
                            if len(transfers) == 2:
                                _check_chain_(transfers,pump_address)
                                transfers=[]
                        transfers=[]                
            else:
                transfers=[]
                for inner_instruction in meta["innerInstructions"]:
                    if inner_instruction["index"] == index:    
            
                        transfers=[]
                        counter=0
                        for ii in inner_instruction["instructions"]:

                            if ii["programId"] == "675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8" or ii["programId"] == "LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo" or instruction["programId"] == "CAMMCzo5YL8w4VFF8KVHrK22GGUsp5VTaW7grrKgrWqK":
                                counter=1
                                if len(transfers) > 0:
                                    #print(f"Send previouse ({len(transfers)})transfers:{transfers}")
                                    _check_chain_(transfers,pump_address)
                                    transfers=[]
                            else:
                                if counter > 0 and  "program" in ii and ii["program"] == "spl-token" and (ii["parsed"]["type"] == "transfer" or ii["parsed"]["type"] == "transferChecked"):
                                    if counter < 3:
                                        counter=counter+1
                                        transfers.append(process_tx(ii))

                        if len(transfers) > 0:
                            _check_chain_(transfers,pump_address)
                        transfers=[]
    token_network=get_network(block,mint)
    return token_network,signature


def batchTransactions(mint,token_address):
    if mint not in modules.config.mint_scan_address:
        modules.config.mint_scan_address[mint]=mint
        last_mint_scan_address=mint

    rpc_url = ankr_url
    #rpc_url = "https://little-sleek-mansion.solana-mainnet.quiknode.pro/4b69dc2b49ac07ae787d9c65b8a33ccee7a904d2"
    rpc_url ="https://endpoints.omniatech.io/v1/sol/mainnet/bf8f530c3d624f1889e1d0c720901fb1"
    redis_connection=modules.config.redis_connection
    lsig=get_redis_latest_sig(redis_connection, mint,"lpush")
    token_address=mint
    
    while True:
        if lsig:
                request = {
                    "jsonrpc": "2.0",
                    "id": "1",
                    "method": "getSignaturesForAddress",
                    "params": [token_address, {"limit":100,"before":lsig,"commitment":"confirmed"}]
                }
        else:
            request = {
                    "jsonrpc": "2.0",
                    "id": "1",
                    "method": "getSignaturesForAddress",
                    "params": [token_address, {"limit":100,"commitment":"confirmed"}]
                }
            
        randNum = random.randint(0, len(omnia_keys) - 1)
        url = f"https://endpoints.omniatech.io/v1/sol/mainnet/{omnia_keys[randNum]}"


        request = requests.post(rpc_url, json=request)

        if "result" in request.json():
            data = request.json()["result"]
        else:
            data = []

        if len(data) == 0:
            #print("No more transactions:last signature",lsig)
            return

        good_sigs = []
        for sig in data:
            if sig["err"] == None:
                good_sigs.append(sig['signature']) 
                unix_timestamp = int(time.time())
                time_diff = int(unix_timestamp)- int(sig["blockTime"]) 
                #print(sig['signature'],time_diff)
        #print(f"Good signatures: {len(good_sigs)}")
        if len(good_sigs) == 0:
            #print(" no good signatures: ")
            return
        if good_sigs:
            batch_transactions = []
            counter=1
            for sig in good_sigs:
                batch_transactions.append({"jsonrpc": "2.0", "id": counter, "method": "getTransaction", "params": [sig, {"maxSupportedTransactionVersion":0,"encoding": "jsonParsed","commitment":"confirmed"}]})
                counter+=1
            batch_request_json = json.dumps(batch_transactions)


            randNum = random.randint(0, len(omnia_keys) - 1)
            rpc_url = f"https://endpoints.omniatech.io/v1/sol/mainnet/{omnia_keys[randNum]}"

            response = requests.post(rpc_url, headers={"Content-Type": "application/json"}, data=batch_request_json)

            batch_data=json.loads(response.text)
            #print(f"Processing {len(batch_data)} transactions: {mint}")
            token_address,lsig=process_transactions(batch_data,mint)
            #print(f"Token address: {token_address} last signature: {lsig}")

        #lsig=data[-1]["signature"]


def process_transactions(data,mint):

    for tx in data:
        mint_address=mint
        sig=""
      
        if "result" in tx and tx["result"] != None:
            row=tx["result"]
            if "meta" in row and row["meta"]["err"]==None:
                #print(f"{ row['transaction']['message']['accountKeys'][0]['pubkey']} -------sig: {row["transaction"]["signatures"][0]}")

                mint_address,sig=process_solana_transaction(row,mint)
                if  modules.config.mint_scan_address[mint] != mint_address:
                    #print(f"returning {modules.config.mint_scan_address[mint]} {mint_address}")
                    #modules.config.mint_scan_address[mint] = mint_address
                    return mint_address,sig
        else:
            pass
            #print(tx)
    return mint_address,sig