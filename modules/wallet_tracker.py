import time
from datetime import datetime
import mysql.connector
from mysql.connector import errorcode
from mysql.connector import pooling
import pandas as pd
import modules.transfer as transfer
import modules.utils as utils
import json,random,requests
import modules.config
from modules.config import *
import modules.db as db

dblog=utils.get_logger()

def get_free_api(pool):
    for api in wallet_tracking_api:
        addresses=db.get_wallets_by_api(pool,api)
        if len(addresses) < max_wallets:
            return api
    return None
    

def add_helius_address(address):
    MYSQL_SERVER="pumpfun.mooo.com"
    MYSQL_PORT=3306
    MYSQL_USER="pump2"
    MYSQL_PASSWORD="pump2"
    MYSQL_DB="pump"
    MYSQL_POOL="mypool"
    MYSQL_POOL_SIZE="20"

    DATAGATHER=120

    pool = pooling.MySQLConnectionPool(
        pool_name=MYSQL_POOL, 
        user=MYSQL_USER,
        password=MYSQL_PASSWORD,
        host=MYSQL_SERVER,
        db=MYSQL_DB)
        

    api=get_free_api(pool)
    webhook_data=get_helius_webhookid(api)

    if webhook_data is not None:
        webhookID=webhook_data["webhookID"]
        addresses=webhook_data["accountAddresses"]
        if len(addresses)>max_wallets:
            print(f"address limit reached in webhook:{webhookID} api:{api}")
            return "Full"
        print(f"Got webhook :  webhook:{webhookID} api:{api} with addresses: {addresses}")
        if address not in addresses:
            addresses.append(address)
        else:
            print(f"address already exist in webhook:{webhookID} api:{api}")
            return None
    else:
        webhookID=create_webhook(api,address)
        #add address to db 
        print(f"Created new webhook:{webhookID} api:{api} with address: {address}")
        if webhookID:
            return {"api":api}
        else:
            return None

    response = requests.put(
        f"https://api.helius.xyz/v0/webhooks/{webhookID}?api-key={api}",
        headers={"Content-Type":"application/json"},
        json= {

                "webhookURL": "http://**************:7002/webhook",
                "accountAddresses": addresses ,
                "transactionTypes": [
                "Any"
                ],
                "webhookType": "enhanced"
            }

    )
    data = response.json()
    print(f"edited in webhook:{webhookID} api:{api}  response: {data}")
    if data:
        return {"api":api}
    else:
        return None

def get_helius_webhookid(api):

    response = requests.get(f"https://api.helius.xyz/v0/webhooks?api-key={api}")
    data = response.json()
    if len(data)>0:
        return data[0]
    else:
        return None
    
def create_webhook(api,address):
    print(f"Crate webhook with address:",address)
    response = requests.post(
        f"https://api.helius.xyz/v0/webhooks?api-key={api}",
        headers={"Content-Type":"application/json"},
        json={
            "webhookURL": "http://**************:7002/webhook",
            "transactionTypes": ["Any"],
            "accountAddresses": [address],
            "webhookType": "enhanced"
            }
    )
    data=response.json()
    if data:
        print(f"Webhook created {data["webhookID"]} api:{api}")
        return data["webhookID"]
    else:
        return None 


def delete_webhook(api,webhookID):
    print(f"Deleting webhook:",webhookID)
    response = requests.delete(
        f"https://api.helius.xyz/v0/webhooks/{webhookID}?api-key={api}",
        headers={"Content-Type":"application/json"}
    )
    print(response )