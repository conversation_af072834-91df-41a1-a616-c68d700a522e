import websocket
import json
import threading
import time
from modules.config import *
import modules.config
from modules.config import last_price
from modules.config import block_counter
from modules.config import redis_connection
import modules.decoders.pump as pump
import modules.redismq as redismq
import modules.utils
import logging

logging.basicConfig(level=logging.DEBUG, format='%(threadName)s: %(message)s')



def decode_pump_program_instruction(tx):
    txs=[]
    for i in tx:
        for ii in i["instructions"]:
            if ii["programId"] =="6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P":
                encoded_data=ii["data"]
                pump_decode=pump.decode(encoded_data)
                if isinstance(pump_decode, list) and pump_decode[0] == 'TradeEvent':
                    txs.append(pump_decode[1])
    return txs

def set_last_price(token,price):
    global last_price
    last_price[token]=price   

def get_last_price(token):
    global last_price
    if token in last_price:
        return last_price[token]
    else:
        return 0
    




def process_solana_transaction(block):
    global last_price
    global block_counter
    global pump_address
    global redismq
    global redis_connection
    slot = block["slot"]

    timestamp=block["block"]['blockTime']
    print(f"Slot: {slot}  Time: {timestamp}")

    for row in block["block"]["transactions"]:
        if row["meta"]["err"]:
            return

        signature=row["transaction"]["signatures"][0]
        #print(f"{ row['transaction']['message']['accountKeys'][0]['pubkey']}===== sig: {signature} ")
        owner=row['transaction']['message']['accountKeys'][0]['pubkey']


        dev=["AEDr4TBd6VSjMy1yb4YHst65wLBakr8dDhaAAktRaFkv",
            "AZt9KWsVpmcoH3Ka5nBMwyNhetJLW9Lj2C7V1dEnWAko",
            "BkBSrzFzwfkh1jk98zJTdw7Hrx2bNYeKnfumrD4NmPd8",
            "BqVKaTqaf8ePx1oDn2vJKhTAv6dyn2sfqX7N43L7Z35y",
            "EFoJLgXRTu9W7mgaAabkWhGDxgVdZWAk27ZWSefx7Gx2"]

        inner_instructions=row["meta"]["innerInstructions"]

        for log in row["meta"]["logMessages"]:
            if "Liquidity" in log:
                return

        try:
            pumptx=decode_pump_program_instruction(inner_instructions)
        except Exception as e:
            return
        #print("pump len:",len(pumptx))
        if len(pumptx)>0:
            print("PUMP")
            pump_address=modules.config.pump_address
            for p in pumptx:
                    mint=p["mint"]
                    tokenAmount=p["token_amount"]
                    solAmount=p["sol_amount"]
                    is_buy=p["is_buy"]
                    if is_buy:
                        trade="buy"
                    else:
                        trade="sell"
                    if solAmount == 0 or tokenAmount == 0:
                        return    
                    price=(solAmount/tokenAmount/1000)

                    mlp=get_last_price(pump_address) #last price
                    if mlp == 0:
                        mlp=price
                    if mlp > price:
                        high=mlp
                        low=price
                    else:
                        high=price
                        low=mlp

                    if low > high:
                        print(f"Error: low {low}  high {high}")
                    color="orange"
                    open_price=mlp
                    close_price=price

                    if slot in block_counter:
                        block_counter[slot]=block_counter[slot]+1
                    else:
                        block_counter[slot]=1
                    milliseconds=block_counter[slot]

                    ttime=modules.utils.get_tx_time(mint,int(timestamp),"append")
                    print(float(ttime))

                    data={"broker":"PUMP", "color":color,"open":open_price,"close":close_price,   "high":high,"low":low,"address":owner,"trade":trade, "tokenAmount":tokenAmount,"solAmount":solAmount,"price":price,"timestamp":float(ttime),"ttime":float(ttime),"milliseconds":milliseconds,"signature":signature,"slot":slot}
                    
                    print(mlp,price)   
                    set_last_price(pump_address,price)
                    redis_connection=modules.config.redis_connection
                    redismq.push_trade(redis_connection,data,pump_address,"rpush")




        else:
 
            pump_address=modules.config.pump_address
            def _check_chain_(transfers,pump_address): 
   
                final = transfers
                #print(f"Final len:{len(final)}")
                if len(final) == 2:
                    if final[0]["mint"] != pump_address and final[1]["mint"] != pump_address:
                        print("Error: no pump address.tx not related")
                        return
                    
                    if final[0]["mint"] != "So11111111111111111111111111111111111111112" and final[1]["mint"] != "So11111111111111111111111111111111111111112":
                        print("Error: no Raydium address.tx not related")
                        return

                                
                    if final[-1]["amount"] == 0 or final[0]["amount"] == 0:
                        return

                    if final[0]["mint"]==pump_address:
                        trade="sell"
                        price=final[-1]["amount"]/final[0]["amount"]
                        volume=final[0]["amount"]
                        tokenAmount=final[0]["amount"]
                        solAmount=final[-1]["amount"]
                    else:
                        trade="buy"
                        price=final[0]["amount"]/final[-1]["amount"]
                        volume=final[-1]["amount"]
                        tokenAmount=final[-1]["amount"]
                        solAmount=final[0]["amount"]


                    mlp=get_last_price(pump_address) #last price
                    if mlp == 0:
                        mlp=price
                    if mlp > price:
                        high=mlp
                        low=price
                    else:
                        high=price
                        low=mlp

                    if low > high:
                        print(f"Error: low {low}  high {high}")
                    color="orange"
                    open_price=mlp
                    close_price=price

                    if slot in block_counter:
                        block_counter[slot]=block_counter[slot]+1
                    else:
                        block_counter[slot]=1
                    milliseconds=block_counter[slot]

                    ttime=modules.utils.get_tx_time(mint,int(timestamp),"append")
                    print(float(ttime))

                    #millionseconds=int(timestamp)*1000  
                    unix_timestamp = int(time.time())
                    time_diff = unix_timestamp - int(timestamp)

                    print(f"{ row['transaction']['message']['accountKeys'][0]['pubkey']}== trade: {trade} ===price: {price} == sig: {signature} == timedif: {time_diff} ")
                    data={"broker":"RAYDIUM", "color":color,"open":open_price,"close":close_price,   "high":high,"low":low,"address":owner,"trade":trade, "tokenAmount":tokenAmount,"solAmount":solAmount,"price":price,"timestamp":float(ttime),"ttime":float(ttime),"milliseconds":milliseconds,"signature":signature,"slot":slot}
                    #print(json.dumps(data, indent=4))
                    #print(mlp,price)   
                    set_last_price(pump_address,price)
                    redis_connection=modules.config.redis_connection
                    redismq.push_trade(redis_connection,data,pump_address,"rpush")
            # END of check_chain



            transfers=[]
            token_owner={}
            instructions = row['transaction']['message']['instructions']
            account_keys = row['transaction']['message']['accountKeys']

            meta = row['meta']
            post_token_balances = meta['postTokenBalances']
            pre_token_balances = meta['preTokenBalances']


                

            def get_account_address_by_index(index):
                return account_keys[index]["pubkey"]
            
            def get_owner_by_token_account(tokenAccount):
                found=False
                for balance in meta["preTokenBalances"]:
                    if get_account_address_by_index(balance["accountIndex"]) == tokenAccount:
                        return balance["owner"],balance["mint"]
                if tokenAccount in token_owner:
                    return token_owner[tokenAccount]["owner"],token_owner[tokenAccount]["mint"]

                if found == False:
                    return tokenAccount,"unknown"
                
            def process_tx(instruction):
                if instruction["program"] == "system" and instruction["parsed"]["type"] == "transfer":
                    source = instruction["parsed"]["info"]["source"]
                    destination = instruction["parsed"]["info"]["destination"]
                    amount = instruction["parsed"]["info"]["lamports"]
                    #print(f"Instruction: (transfer) Source: {source} Destination: {destination} Amount: {amount}  lamports")


                if instruction["program"] == "spl-associated-token-account" and  instruction["parsed"]["type"] == "createIdempotent":
                    #print(f"Instruction: (createImpodent) Create associated token account owner:{instruction["parsed"]["info"]["source"]}  new splaccount:{instruction["parsed"]["info"]["account"]}  mint:{instruction["parsed"]["info"]["mint"]}")
                        token_owner[instruction["parsed"]["info"]["account"]]={"owner":instruction["parsed"]["info"]["source"],"mint":instruction["parsed"]["info"]["mint"]}
                
                if instruction["program"] == "spl-token" and instruction["parsed"]["type"] == "closeAccount":
                    pass
                    #print(f"Instruction: (closeAccount) owner:{instruction["parsed"]["info"]["owner"]}  closed account :{instruction["parsed"]["info"]["account"]}  ")
                        
                if instruction["program"] == "system" and instruction["parsed"]["type"] == "createAccount":
                    #print(f"Instruction: (createAccount) owner:{instruction["parsed"]["info"]["source"]}  new account :{instruction["parsed"]["info"]["newAccount"]}  sum:{instruction["parsed"]["info"]["lamports"]}")
                    pass
                    #token_owner[instruction["parsed"]["info"]["newAccount"]]={"owner":instruction["parsed"]["info"]["source"],"mint":"So11111111111111111111111111111111111111112"}     
                if instruction["program"] == "spl-token" and instruction["parsed"]["type"] == "initializeAccount3":
                    token_owner[instruction["parsed"]["info"]["account"]]={"owner":instruction["parsed"]["info"]["owner"],"mint":instruction["parsed"]["info"]["mint"]} 
                    #print(f"Instruction: (initializeAccount3)  Set token for account :{instruction["parsed"]["info"]["account"]}  mint:{instruction["parsed"]["info"]["mint"]}  owner:{instruction["parsed"]["info"]["owner"]}")
                    
                if instruction["program"] == "spl-token" and instruction["parsed"]["type"] == "transfer":
                    if "multisigAuthority" in instruction["parsed"]["info"]:
                        source_owner,_=get_owner_by_token_account(instruction["parsed"]["info"]["multisigAuthority"])
                    else:
                        source_owner,_=get_owner_by_token_account(instruction["parsed"]["info"]["authority"])
                    
                    destination_owner,destination_mint=get_owner_by_token_account(instruction["parsed"]["info"]["destination"]) 

                    #print(f"Instruction: (spl-transfer)  Source:{instruction["parsed"]["info"]["source"]}  Destination:{instruction["parsed"]["info"]["destination"]}  Amount:{instruction["parsed"]["info"]["amount"]} mint:{destination_mint}" )
                    ###print(f"Instruction: (spl-transfer)  Source:{source_owner}  Destination:{destination_owner}  Amount:{instruction["parsed"]["info"]["amount"]} mint:{destination_mint}" )

                    if destination_mint == pump_address:
                        amount=int(instruction["parsed"]["info"]["amount"])/1000000
                    elif destination_mint == "So11111111111111111111111111111111111111112":
                        amount=int(instruction["parsed"]["info"]["amount"])/**********
                    else:
                        amount=int(instruction["parsed"]["info"]["amount"])/1000000

                    # transfers.append({
                    #         'source': source_owner,
                    #         'destination': destination_owner,
                    #         'amount': amount,
                    #         'mint': destination_mint
                    #     })
                    return {
                            'source': source_owner,
                            'destination': destination_owner,
                            'amount': amount,
                            'mint': destination_mint}

                if instruction["program"] == "spl-token" and instruction["parsed"]["type"] == "transferChecked":

                    if "multisigAuthority" in instruction["parsed"]["info"]:
                        source_owner,_=get_owner_by_token_account(instruction["parsed"]["info"]["multisigAuthority"])
                    else:
                        source_owner,_=get_owner_by_token_account(instruction["parsed"]["info"]["authority"])
                    destination_owner,destination_mint=get_owner_by_token_account(instruction["parsed"]["info"]["destination"]) 
                    ###print(f"Instruction: (spl-transferChecked)  Source:{source_owner}  Destination:{destination_owner}  Amount:{instruction["parsed"]["info"]["tokenAmount"]["uiAmount"]} mint:{instruction["parsed"]["info"]["mint"]}" )

                    # transfers.append({
                    #     'source': source_owner,
                    #     'destination': destination_owner,
                    #     'amount': instruction["parsed"]["info"]["tokenAmount"]["uiAmount"],
                    #     'mint': instruction["parsed"]["info"]["mint"]
                    # })
                    return {
                            'source': source_owner,
                            'destination': destination_owner,
                            'amount': instruction["parsed"]["info"]["tokenAmount"]["uiAmount"],
                            'mint': instruction["parsed"]["info"]["mint"]}
                



#resolve new accounts


            for index,i in enumerate(instructions):
                if "program" in i and i["program"] == "spl-token" and ( i["parsed"]["type"] == "initializeAccount3" or i["parsed"]["type"] == "initializeAccount" ):
                        token_owner[i["parsed"]["info"]["account"]]={"owner":i["parsed"]["info"]["owner"],"mint":i["parsed"]["info"]["mint"]} 
                        #print(f"Instruction: (initializeAccount3)  Set token for account :{i["parsed"]["info"]["account"]}  mint:{i["parsed"]["info"]["mint"]}  owner:{i["parsed"]["info"]["owner"]}")

            for inner_instruction in meta["innerInstructions"]:
                for ii in inner_instruction["instructions"]:
                    if "program" in ii and ii["program"] == "spl-token" and ( ii["parsed"]["type"] == "initializeAccount3" or ii["parsed"]["type"] == "initializeAccount" ):
                        token_owner[ii["parsed"]["info"]["account"]]={"owner":ii["parsed"]["info"]["owner"],"mint":ii["parsed"]["info"]["mint"]} 
                        #print(f"Instruction: (initializeAccount3)  Set token for account :{ii["parsed"]["info"]["account"]}  mint:{ii["parsed"]["info"]["mint"]}  owner:{ii["parsed"]["info"]["owner"]}")


    # end resolve


            for index,instruction in enumerate(instructions):
    
                if instruction["programId"] == "675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8"  or instruction["programId"] == "LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo" :
                    transfers=[]
                    
                    for inner_instruction in meta["innerInstructions"]:
                        if inner_instruction["index"] == index:
                            for ii in inner_instruction["instructions"]:
                                if "program" in ii and ii["program"] == "spl-token" and (ii["parsed"]["type"] == "transfer" or ii["parsed"]["type"] == "transferChecked"):
                                    transfers.append(process_tx(ii))
                                if len(transfers) == 2:
                                    _check_chain_(transfers,pump_address)
                                    transfers=[]
                            transfers=[]                
                else:
                    transfers=[]
                    for inner_instruction in meta["innerInstructions"]:
                        if inner_instruction["index"] == index:    
                
                            transfers=[]
                            counter=0
                            for ii in inner_instruction["instructions"]:

                                if ii["programId"] == "675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8" or ii["programId"] == "LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo" :
                                    counter=1
                                    if len(transfers) > 0:
                                        #print(f"Send previouse ({len(transfers)})transfers:{transfers}")
                                        _check_chain_(transfers,pump_address)
                                        transfers=[]
                                else:
                                    if counter > 0 and  "program" in ii and ii["program"] == "spl-token" and (ii["parsed"]["type"] == "transfer" or ii["parsed"]["type"] == "transferChecked"):
                                        if counter < 3:
                                            counter=counter+1
                                            transfers.append(process_tx(ii))

                            if len(transfers) > 0:
                                _check_chain_(transfers,pump_address)
                            transfers=[]

     
            # for inner_instruction in meta["innerInstructions"]:
            #     #if inner_instruction["index"] == index:
            #     transfers=[]
            #     counter=0
            #     for ii in inner_instruction["instructions"]:

            #         if ii["programId"] == "675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8" or ii["programId"] == "LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo" :
            #             counter=1
            #             if len(transfers) > 0:
            #                 #print(f"Send previouse ({len(transfers)})transfers:{transfers}")
            #                 _check_chain_(transfers,pump_address)
            #                 transfers=[]
            #         else:
            #             if counter > 0 and  "program" in ii and ii["program"] == "spl-token" and (ii["parsed"]["type"] == "transfer" or ii["parsed"]["type"] == "transferChecked"):
            #                 if counter < 3:
            #                     counter=counter+1
            #                     transfers.append(process_tx(ii))

            #     if len(transfers) > 0:
            #         _check_chain_(transfers,pump_address)
            #     transfers=[]
                  

#TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA



def send_request(ws):
    pump_address=modules.config.pump_address
    token_address=modules.config.token_address
    request = {
        "jsonrpc": "2.0",
        "id": "1",
        "method": "blockSubscribe",
        "params": [
            {
            "mentionsAccountOrProgram": pump_address
            },
            {
            "commitment": "confirmed",
            "encoding": "jsonParsed",
            "showRewards": True,
            "transactionDetails": "full"
            ,"maxSupportedTransactionVersion": 0
            }
        ]
        }
    ws.send(json.dumps(request))

# Function to send a ping to the WebSocket server
def start_ping(ws):
    def ping():
        while True:
            if ws.sock and ws.sock.connected:
                ws.send('{"jsonrpc": "2.0", "method": "ping"}')
                #print('Ping sent')
            time.sleep(30)  # Ping every 30 seconds
    threading.Thread(target=ping).start()

# Define WebSocket event handlers
def on_open(ws):
    print('WebSocket is open')
    send_request(ws)  # Send a request once the WebSocket is open
    start_ping(ws)    # Start sending pings

def on_message(ws, message):
    if True:
        message_obj = json.loads(message)
        if "params" in message_obj:
            process_solana_transaction(message_obj["params"]["result"]["value"])
            # for row in message_obj["params"]["result"]["value"]["block"]["transactions"]:
            #     if row["meta"]["err"]==None:
            #         print(f"========================= {row["transaction"]["signatures"][0]}  ================= { row['transaction']['message']['accountKeys'][0]['pubkey']} =====================")
            #         process_solana_transaction(row)
         
                  

def on_error(ws, error):
    print('WebSocket error:', error)

def on_close(ws, close_status_code, close_msg):
    print('WebSocket is closed')

# Create a WebSocket connection


def ws_run():
  
    
    ws_url=token_update_ws_url # from config.py
    ws = websocket.WebSocketApp(ws_url,
                                on_open=on_open,
                                on_message=on_message,
                                on_error=on_error,
                                on_close=on_close)

    ws.run_forever()




