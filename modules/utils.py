#!/usr/bin/env python3

import logging
import pika,json
import requests
from modules.config import *
#from tenacity import retry, stop_after_attempt, wait_fixed
import modules.config
from discordwebhook import Discord


#@retry(stop=stop_after_attempt(3), wait=wait_fixed(2))
def fetch_token_price(token):
    try:
        url = f'https://swap-v2.solanatracker.io/rate?from=So11111111111111111111111111111111111111112&to={token}&amount=1&slippage=10'
        headers = {'authority': 'swap-v2.solanatracker.io'}
        response = requests.get(url, headers=headers)
        response.raise_for_status()  # Raises an exception for HTTP errors
        data = response.json()
        return data["currentPrice"]
    except Exception as e:
        logging.info(f"Failed to fetch token price for {token}: {e}")
        return None
    

def get_logger(logname="default", logfile=None):
    logger = logging.getLogger(logname)
    logger.setLevel(logging.INFO)

    # Check if logger already has handlers to avoid duplicate logs
    if not logger.hasHandlers():
        # Create console handler and set level to info
        ch = logging.StreamHandler()
        ch.setLevel(logging.INFO)

        # Create formatter and add it to the handler
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        ch.setFormatter(formatter)

        # Add the console handler to the logger
        logger.addHandler(ch)

        if logfile:
            # Create file handler and set level to info
            fh = logging.FileHandler(logfile)
            fh.setLevel(logging.INFO)

            # Add the same formatter to the file handler
            fh.setFormatter(formatter)

            # Add the file handler to the logger
            logger.addHandler(fh)

    # Suppress logging from other modules
    logging.getLogger('asyncio').setLevel(logging.WARNING)
    logging.getLogger('pika').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)
    logging.getLogger('solana').setLevel(logging.WARNING)
    logging.getLogger('solders').setLevel(logging.WARNING)

    return logger

def send_event(message,queue="events"):
    credentials = pika.PlainCredentials('pump', 'pump2pump') 
    parameters = pika.ConnectionParameters(RABBITMQ_SERVER, 5672, '/', credentials)
    connection = pika.BlockingConnection(parameters)
    channel = connection.channel()

    
    # Create a queue named 'task_queue'
    channel.queue_declare(queue=queue, durable=True)

    # Publish the message to the queue
    channel.basic_publish(
        exchange='',
        routing_key=queue,
        body=message,
        properties=pika.BasicProperties(
            delivery_mode=2,  # Make message persistent
        ))

    connection.close()

def send_pushover_message(message):
    for push in pushover:
        response = requests.post('https://api.pushover.net/1/messages.json', data={
            'token': push["pushover_token"],
            'user': push["pushover_api"],
            'message': message
        })



#block["token"]={"blocktime":4444444,"msec":1000}
def get_tx_time(mint,block_time,mode="append"):   
    block_time=float(block_time)
    if mint not in modules.config.block:
        modules.config.block[mint]={"blocktime":block_time,"msec":1050}
        return block_time+(modules.config.block[mint]["msec"]/1000000)
    
    if mode=="append":
        if block_time == modules.config.block[mint]["blocktime"]:
            modules.config.block[mint]["msec"]=modules.config.block[mint]["msec"]+1
            return block_time+(modules.config.block[mint]["msec"]/1000000)
        else:
            modules.config.block[mint]={"blocktime":block_time,"msec":1050}
            return block_time+(modules.config.block[mint]["msec"]/1000000)
    else:
        if block_time == modules.config.block[mint]["blocktime"]:
            modules.config.block[mint]["msec"]=modules.config.block[mint]["msec"]-1
            return block_time+(modules.config.block[mint]["msec"]/1000000)
        else:
            modules.config.block[mint]={"blocktime":block_time,"msec":1050}
            return block_time+(modules.config.block[mint]["msec"]/1000000)

def discord_webhook_signals(discord_webhook, message):
    discord = Discord(url=discord_webhook)
    print("sending")
    discord.post(content=message)

def discord_post(data):
    discord = Discord(url=discord_webhook)
    score = "N/A"
    name = data.token_name  
    contract_address = data.mint
    mcap = data.mcap
    mcap_change = "10%"
    liquidity = "$29.0K"
    liquidity_multiplier = "x5.4"
    volume = "$31K"
    age = "6d"
    one_hour_stats = "HLDR: {data.uniq_addresses} 🅑 {data.total_buys} 🅢 {data.total_sells}"
    top_holders = "18.6 ⋅ 3.1 ⋅ 3.1 ⋅ 2.7 ⋅ 2.6"
    top_holders_percentage = "40%"
    bonded = "yes/no"
    pump_comments = "(0/20/44) (dev/normal/new accounts)"
    dev_history = "10 tokens"
    ath_last5 = "20k, 100k, 400k"
    dev_holds = "10%"
    top10_holders = "15%"
    name_duplicates = "3 name duplicates"
    link_to_pump = "[link to pump]"
    image_reused = "image reused"
    link_to_lens = "[link to lens]"
    dex_paid = "yes/no"
    worthy_holders = "1, 2, 5, 5, 5, 5"
    twitter_ca_mentions = "1, 1, 2, 5, 10"
    twitter_ca_change = "10%"
    twitter_ticker_mentions = "10, 20, 44, 50"
    twitter_ticker_change = "20%"
    fresh_accounts_last5 = "10, 5, 30, 2"
    fresh_accounts_change = "10%"
    regular_accounts_last5 = "10, 100, 300"
    good_wallets_last5 = "10, 11, 11, 11 (win rate above 50%)"
    multi_accounts = "(1/10//15%), (3/70/33M/20%), (2/70) - 1 account transfers to 10 accounts total value 15%"
    token_transfers = "0%, 10%, 15%, 0%, 0%"
    volume_bot = "yes/no"
    ban_list = "[BAN]⋅[SHU]⋅[PHO]⋅[BLX]⋅[PVB]⋅[TTF]⋅[TW]"
    auto_report_conditions = (
        "Auto report when: not rugged still above 5k, no bundled in 30 sec "
        "(no multiple buys in first 30 seconds), no hidden transfers, very few new wallets or none at all, "
        "no copycats, no new account comments, some Twitter posts, no multi accounts"
    )

    # Construct the message using the variables
    message = f""" :green_circle: ** TOPCAT  `{name}` *** 
    > CA: `{contract_address}`
    > -:chart_with_upwards_trend: Mcap: `{mcap}`
    > -:droplet: Liq: `{liquidity}`
    > -:bar_chart: Vol: `{volume}`
    > -:hourglass_flowing_sand: Age: `{age}`
    > -:clock1: 1H: `{one_hour_stats}`
    > -:top: TopH: `{top_holders}` `[{top_holders_percentage}]`
    > -:link: Bonded: `{bonded}`
    > -:speech_balloon: Pump comments: `{pump_comments}`
    > -:chart_with_downwards_trend: Dev history: `{dev_history}`, ATH last 5 `{ath_last5}`
    > -:man_technologist: Dev holds: `{dev_holds}`
    > -:keycap_ten: Top10 holders: `{top10_holders}`
    > -:two_men_holding_hands: `{name_duplicates}` {link_to_pump}, `{image_reused}` {link_to_lens}
    > -:bank: Dex paid: `{dex_paid}`
    > -:money_mouth: Worthy Holders: `{worthy_holders}`
    > -:baby_chick: Twitter CA mentions: `{twitter_ca_mentions}` [ :arrow_up_small: `{twitter_ca_change}` ]
    > -:bird: Twitter ticker mentions: `{twitter_ticker_mentions}` [ :arrow_up_small: `{twitter_ticker_change}` ]
    > -:baby: Fresh accounts (last 5): `{fresh_accounts_last5}` [ :small_red_triangle_down: `{fresh_accounts_change}` ]
    > -:adult: Regular accounts (last 5): `{regular_accounts_last5}`
    > -:purse: Good wallets (last 5): `{good_wallets_last5}`
    > -:busts_in_silhouette: MultiAccounts: `{multi_accounts}`
    > -:coin: Token transfers: `{token_transfers}`
    > -:robot: Volume bot: `{volume_bot}`
    {ban_list}
    {auto_report_conditions}
    """

    # Send the message
    discord.post(content=message)

def notify_account(data):

    desc=shorten_tx(data["description"])
    swap=data["swap"]
    if data["account_name"]!="":
        subject = f"Account activity ({data['account_name']}) : {desc}  ({round(swap/1e9)})"
    else:
        subject = f"Account activity (unknown): {desc}  ({round(swap/1e9)})"  



    body = f"""
    <html>
    <body>
        <p>Account transaction:</p>
        <table border="1" cellpadding="5" cellspacing="0">

            <tr>
                <td><strong>Account name:</strong></td>
                <td>{data['account_name']}  ({data['owner']})</td>
            </tr>

            <tr>
                <td><strong>Description</strong></td>
                <td>{data['description']}</td>
            </tr>

            <tr>
                <td><strong>BullX</strong></td>
                <td><a href="https://bullx.io/terminal?chainId=**********&address={data['mint']}">Link to BullX Terminal</a></td>
            </tr>
            <tr>
                <td><strong>Photon</strong></td>
                <td><a href="https://photon-sol.tinyastro.io/en/lp/{data['mint']}">Link to Photon Terminal</a></td>
            </tr>

            <tr>
                <td><strong>Solscan TX info</strong></td>
                <td><a href="https://solscan.io/tx/{data['signature']}">Link to Solscan transaction info</a></td>
            </tr>
            <tr>
                <td><strong>Solscan Account info</strong></td>
                <td><a href="https://solscan.io/account/{data['owner']}">Link to Solscan Account info </a></td>
            </tr>
            <tr>
                <td><strong>Birdseye account info</strong></td>
                <td><a href="https://birdeye.so/profile/{data['owner']}/30D?chain=solana">Birds eye Profile info</a></td>
            </tr>
     

            
        </table>
    </body>
    </html>
    """

    
    send_email(subject, body, sender, recipients, mail_key)

def notify_token(mint_address, mint_name, owner_address, owner_balance, uniq_addresses, total_tx, scan_period,total_buys,total_sells,mcap,mint_count,mint_list):
    subject = f"New token discovered: {mint_name} ({mint_address})"
    mint_list_json=json.loads(mint_list)
    body = f"""
    <html>
    <body>
        <p>Here are the details of the discovered token:</p>
        <table border="1" cellpadding="5" cellspacing="0">
            <tr>
                <td><strong>BullX</strong></td>
                <td><a href="https://bullx.io/terminal?chainId=**********&address={mint_address}">Link to BullX Terminal</a></td>
            </tr>
            <tr>
                <td><strong>MINT solscan</strong></td>
                <td><a href="https://solscan.io/token/{mint_address}">Link to Solscan</a></td>
            </tr>
            <tr>
                <td><strong>OWNER solscan</strong></td>
                <td><a href="https://solscan.io/account/{owner_address}">Link to Solscan Account</a></td>
            </tr>
            <tr>
                <td><strong>Mint Address</strong></td>
                <td>{mint_address}</td>
            </tr>
            <tr>
                <td><strong>Name</strong></td>
                <td>{mint_name}</td>
            </tr>
            <tr>
                <td><strong>Owner Balance</strong></td>
                <td>{owner_balance}</td>
            </tr>
            <tr>
                <td><strong>Total Transactions</strong></td>
                <td>{total_tx}</td>
            </tr>
            <tr>
                <td><strong>Unique Addresses</strong></td>
                <td>{uniq_addresses}</td>
            </tr>
            <tr>
                <td><strong>Scan Period</strong></td>
                <td>{scan_period}</td>
            </tr>
            <tr>
                <td><strong>Total Buys</strong></td>
                <td>{total_buys}</td>
            </tr>
            <tr>
                <td><strong>Total Sells</strong></td>
                <td>{total_sells}</td>
            </tr>
            <tr>
                <td><strong>Last Price</strong></td>
                <td>${mcap}K</td>
            </tr>
            <tr>
                <td><strong>Owner amount of tokens created before</strong></td>
                <td>{mint_count}</td>
            </tr>
            <tr>
                <td><strong>Previous mints</strong></td>
                <td>  
                    <ul>
                        {"".join([f"<li>https://bullx.io/terminal?chainId=**********&address={mint}</li>" for mint in mint_list_json])}
                    </ul>
                </td>
            </tr>

            
        </table>
    </body>
    </html>
    """

    
    send_email(subject, body, sender, recipients, mail_key)


def send_email(subject, body, sender, recipients, password):
    import smtplib
    from email.mime.text import MIMEText
    msg = MIMEText(body,'html')
    msg['Subject'] = subject
    msg['From'] = sender
    msg['To'] = ', '.join(recipients)
    with smtplib.SMTP_SSL('smtp.gmail.com', 465) as smtp_server:
        smtp_server.login(sender, password)
        smtp_server.sendmail(sender, recipients, msg.as_string())
        print("Message sent!")


def shorten_tx(message):
    # Split the message into parts
    parts = message.split()

    # Shorten the first address (first part)
    sender_address = parts[0][:6]

    # Shorten the last address (last part)
    recipient_address = parts[-1][:6]

    # Join everything between the first and last parts (middle group)
    middle_part = " ".join(parts[1:-1])

    # Reconstruct the message with the shortened addresses and middle part
    updated_message = f"{sender_address} {middle_part} {recipient_address}"

    return updated_message


