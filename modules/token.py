import requests,random,json,time,re
import pandas as pd
import modules.db as db 
import modules.utils as utils
from modules.config import *
import redis,sys
import modules.redismq as redismq
from modules.transfer import get_token_info
import modules.decoders.pump as pump
import modules.decoders.system as system
from modules.config import last_price
from modules.config import block
#from modules.config import token_sigs
from modules.config import trade_colors
from modules.config import token_supply
from modules.config import solana_price
from modules.redismq import get_redis_latest_sig

from modules.utils import fetch_token_price

log=utils.get_logger()


def get_pump_swaps(mint):
    pass

def get_token_accounts(mint):
    randNum = random.randint(0, len(api_key) - 1)
    url = f"https://mainnet.helius-rpc.com/?api-key={api_key[randNum]}"
    all_owners = []
    cursor = None

    while True:
        params = {
            "limit": 1000,
            "mint": mint
        }

        if cursor is not None:
            params["cursor"] = cursor

        payload = {
            "jsonrpc": "2.0",
            "id": "helius-test",
            "method": "getTokenAccounts",
            "params": params
        }

        response = requests.post(url, headers={"Content-Type": "application/json"}, data=json.dumps(payload))

        if response.status_code != 200:
            print(f"Failed to fetch data: {response.status_code}")
            break

        data = response.json()
        print(data)
        if not data.get("result") or len(data["result"]["token_accounts"]) == 0:
            print("No more results")
            break

        # for account in data["result"]["token_accounts"]:
        #     all_owners.add(account["owner"])
        for account in data["result"]:
            all_owners.append(account)

        cursor = data["result"].get("cursor")
    return all_owners
    #with open("output.json", "w") as f:
    #    json.dump(list(all_owners), f, indent=2)

def get_token_report(token_id, retries=3):
    url = f'https://api.rugcheck.xyz/v1/tokens/{token_id}/report'
    headers = {
        'Authorization': f'Bearer {rugcheck_api_key}',
    }

    attempt = 0
    while attempt < retries:
        try:
            response = requests.get(url, headers=headers)
            response.raise_for_status()  # Raises an HTTPError if the status is 4xx, 5xx
            return response.json()  # Parse the response as JSON
        except requests.exceptions.HTTPError as http_err:
            print(f"HTTP error occurred: {http_err}")  # HTTP error
        except Exception as err:
            print(f"Other error occurred: {err}")  # Other error

        attempt += 1
        if attempt < retries:
            print(f"Retrying... ({attempt}/{retries})")
            time.sleep(2)  # Wait for 2 seconds before retrying

    print("Max retries exceeded. Exiting.")
    return None


def get_token_supply(token):
    url = 'https://solana-mainnet.api.syndica.io/api-key/4TMsFG1pLWMP4zpdF895C1kYrML8t6AXKQjFB3NzRh1xMjfcMAFPTwCmpRJWa1i72SKTFxjyxBMz7uAkfJoRfR5ZX4kQURk2wwc'
    headers = {
        'Content-Type': 'application/json'
    }
    payload = {
        "jsonrpc": "2.0",
        "id": 1,
        "method": "getTokenSupply",
        "params": [token]
    }

    response = requests.post(url, headers=headers, json=payload)

    if response.status_code == 200:
        response_json = response.json()
        print(response_json)
        if "result" in response_json and "value" in response_json["result"]:
            return float(response_json["result"]["value"]["uiAmount"])
        else:
            raise ValueError("Unexpected response structure")
    else:
        response.raise_for_status()


def decode_pump_program_instruction(tx):
    txs=[]
    for i in  tx["instructions"]:
        if i["programId"] == PUMP_PROGRAM:
            for ii in i["innerInstructions"]:
                if ii["programId"] =="6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P":
                    encoded_data=ii["data"]
                    pump_decode=pump.decode(encoded_data)
                    if isinstance(pump_decode, list) and pump_decode[0] == 'TradeEvent':
                        txs.append(pump_decode[1])
    return txs

def process_tokens_transfers(redis_connection,tokenTransfers,slot,blockTime,signature,trans=[]):
    first=1
    count=0 

    for row in tokenTransfers:        
        if row['fromUserAccount']==row['toUserAccount']:
            tokenTransfers.remove(row)

    for row in tokenTransfers:
        count+=1
        if first==1:
            init_row = row
            first=0
        
        if init_row['fromUserAccount']==row['toUserAccount'] and init_row['toUserAccount']==row['fromUserAccount']:
            #log.info(f"user: {init_row['fromUserAccount']} SWAPS  {init_row['tokenAmount']} {init_row['mint']} for {row['tokenAmount']} {row['mint']} with {init_row['toUserAccount']}")
            data={"type":"swap",
                    "fromUserAccount":init_row['fromUserAccount'],
                    "fromUserAccountMint":init_row['mint'],
                    "fromUserAccountAmount":init_row['tokenAmount'],
                    "toUserAccountMint":row['mint'],
                    "toUserAccountAmount":row['tokenAmount'],
                    "toUserAccount":row['toUserAccount'],
                    "signature":signature,
                    "slot":slot,
                    "blockTime":blockTime
                    }
            trans.append(data)
            first=1
            tokenTransfers.remove(init_row)
            tokenTransfers.remove(row)
            if len(tokenTransfers) > 0:
                process_tokens_transfers(redis_connection,tokenTransfers,slot,blockTime,signature,trans)
                
        if len(tokenTransfers)==count:
                #log.info(f"user: {init_row['fromUserAccount']} TRANSFERS  {init_row['tokenAmount']} {init_row['mint']} to {init_row['toUserAccount']}")
                data={"type":"transfer",
                        "fromUserAccount":init_row['fromUserAccount'],
                        "fromUserAccountMint":init_row['mint'],
                        "fromUserAccountAmount":init_row['tokenAmount'],
                        "toUserAccount":row['toUserAccount'],
                        "signature":signature,
                        "slot":slot,
                        "blockTime":blockTime
                        }
                trans.append(data)
    return trans

def fetch_all_transactions_to_sig(redis_connection,address=None,mode="lpush"):
    # get last signature  and status for token db.get_token_info(pool,token_address)
    beginning=True
    if mode == "lpush":
        last_signature=get_redis_latest_sig(redis_connection,address,mode)
    else: 
        token_sigs = get_redis_latest_sig(redis_connection,address,mode) 
        last_signature=None
    #last_signature="214knbAqh3inSwNYaiBvbrxuxNJ9EWhNgKLNcURCqAgWoXY6vuppQTVvUTavUdToNdqBPd6YJAcdBmtu94ShVigs"
    transactions = []
 
    while True:
        token_sigs = get_redis_latest_sig(redis_connection,address,mode) 
        print(f"token_sigs: {token_sigs}")

        randNum = random.randint(0, len(api_key) - 1)
        url = f"https://api.helius.xyz/v0/addresses/{address}/transactions?api-key={api_key[randNum]}"

        if last_signature:
            url_with_signature = f"{url}&before={last_signature}"
        else:
            url_with_signature = url
        response = requests.get(url_with_signature)


        if response.status_code != 200:
            print("Error code：", response.status_code)
            time.sleep(1)
            continue

      
        data = response.json()
        first_sig=data[0]["signature"]

    # process rpush 
        if mode=="rpush":
            
            tmpdata=[]
            if  token_sigs:
                for d in data:
                    if d["signature"] == token_sigs:
                        
                        if len(tmpdata) > 0:
                            tmpdata=list(reversed(tmpdata))
                            #token_sigs[address]=first_sig
                            process_transactions(redis_connection,address,tmpdata,mode) 
                            return
                        else:
                            return
                    else:

                        tmpdata.append(d)
                #token_sigs[address]=data[0]["signature"]
                #data=list(reversed(tmpdata))
                #process_transactions(redis_connection,address,tmpdata,mode) 
                log.info(f"Wiping out trades:{address} ")
                redis_connection.delete(f'trades:{address}')

                return
                ##print(f"looking for right sig: {token_sigs[address]}")
                #token_sigs[address]=data[0]["signature"]
                #data=list(reversed(data))
                #process_transactions(redis_connection,address,data,mode) 
                return
            else:
            
                #tmpdata.append(data[0])
                tmpdata=list(reversed(data))
                #token_sigs[address]=data[0]["signature"]
                process_transactions(redis_connection,address,tmpdata,mode) 
                return

            
       # end process rpush     

        #print(data)
        if "error" in data:
            if "exceeded limit for api" in data:
                print(data)
                time.sleep(3)
                continue   
            else:
                if len(transactions) > 0:
                    return  transactions,transactions[0]["signature"] 



        print(f"END len data: {len(data)}")
        if data and len(data) > 0:
            last_signature = data[-1]["signature"]
            #last_signature=
            process_transactions(redis_connection,address,data,mode)  
        
            

        else:
            last_signature=db.update_token_data(address,{"status":"completed","last_sig":last_signature})
            return "completed"
    
def set_last_price(token,price):
    global last_price
    last_price[token]=price   

def get_last_price(token):
    global last_price
    if token in last_price:
        return last_price[token]
    else:
        return 0

def process_transactions(redis_connection,address,txs,mode):
    global last_price
    #address=json.dumps(address)
    r_trans = list(reversed(txs))
    IS_PUMP=False
    dev=["AEDr4TBd6VSjMy1yb4YHst65wLBakr8dDhaAAktRaFkv",
        "AZt9KWsVpmcoH3Ka5nBMwyNhetJLW9Lj2C7V1dEnWAko",
        "BkBSrzFzwfkh1jk98zJTdw7Hrx2bNYeKnfumrD4NmPd8",
        "BqVKaTqaf8ePx1oDn2vJKhTAv6dyn2sfqX7N43L7Z35y",
        "EFoJLgXRTu9W7mgaAabkWhGDxgVdZWAk27ZWSefx7Gx2"]

    last_sig=""
    #======= Native block processing ======  
    
    ##    1720000222 
    c1=0
    for row in txs:

        if row["timestamp"]!=block["timestamp"]:
             block["timestamp"]=row["timestamp"]
             if mode=="lpush":
                block["timecounter"]=10000
             else:
                block["timecounter"]=10001

        
        #print(f"block: {block['timestamp']} timecounter: {block["timecounter"]}")
        if row["transactionError"] == None and not row["type"] == "BURN":
            print(f"==Process tx: {row['signature']}")
            fee_payer=row['feePayer']
            #log.info(f"==Process tx: {row['signature']}")
            #print(json.dumps(row,indent=4))
            fee=row["fee"] #fee in lamports
            pumptx=decode_pump_program_instruction(row)
            
            if len(pumptx)>0:
                IS_PUMP=True
                for p in pumptx:
                    mint=p["mint"]
                    tokenAmount=p["token_amount"]
                    solAmount=p["sol_amount"]
                    is_buy=p["is_buy"]
                    if is_buy:
                        trade="buy"
                    else:
                        trade="sell"
                    price=(solAmount/tokenAmount/1000)*token_supply[address]*solana_price
          


                    #print(f"pump price:{price}")
                    if mode=="lpush":
                        block["timecounter"]=block["timecounter"]-1
                    else:
                        block["timecounter"]=block["timecounter"]+1
                    timestamp=row["timestamp"]
                    milliseconds=block["timecounter"]

                    mlp=get_last_price(mint) #last price
                    if mlp == 0:
                        high=price
                        low=price
                        open_price=price
                        close_price=price
                        color="#882255"
                    elif mlp > price:
                        high=price
                        low=mlp
                        open_price=price
                        close_price=mlp
                        color="#26a69a"
                        if fee_payer in dev:
                            color="#882255"
                            
                    else:
                        high=mlp
                        low=price
                        open_price=price
                        close_price=mlp
                        color="#ef5350"
                        if fee_payer in dev:
                            color="#552288"
                        
                   
      
                    data={"broker":"PUMP", "color":color,"open":open_price,"close":close_price,   "high":high,"low":low,"address":fee_payer,"trade":trade, "tokenAmount":tokenAmount,"solAmount":solAmount,"price":price,"timestamp":int(timestamp),"milliseconds":milliseconds,"signature":row["signature"],"slot":row["slot"]} 
                    
                    set_last_price(mint,price)
                    #print(data)
                    redismq.push_trade(redis_connection,data,address,mode)
                    c1+=1
                    #continue
            
     

            if not IS_PUMP:
                print("Raydium")
                transactions=process_tokens_transfers(redis_connection,row["tokenTransfers"],row["slot"],row["timestamp"],row["signature"],[])
                if len(transactions) == 0:
                    spl=False
                    #check if created assocatte account
                    for i in row["instructions"]:
                        if i["programId"] == "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL":
                            print(f"Associated account created transfered: { abs(row["accountData"][0]["nativeBalanceChange"])  } ") 
                            spl=True
                            break
                    if not spl:
                        print(json.dumps(row,indent=4))
                        continue

            
                for tx in transactions:
                    
                    if tx["type"]=="swap":
                        if tx["fromUserAccountMint"] == address or tx["toUserAccountMint"] == address:
                            if tx["fromUserAccountMint"] == address:
                                trade="sell"
                                tokenAmount=tx["fromUserAccountAmount"]
                                if tx["toUserAccountMint"] in ["EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v","Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB"]:
                                    solAmount=tx["toUserAccountAmount"]/rate[tx["toUserAccountMint"]]   
                                elif tx["toUserAccountMint"] == "So11111111111111111111111111111111111111112":
                                    solAmount=tx["toUserAccountAmount"]
                                else:
                                    log.error(f"Unknown currency: {tx["toUserAccountMint"]}")
                                    continue
                                price=solAmount/tokenAmount
                            else:
                                tokenAmount=tx["toUserAccountAmount"]
                            
                                if tx["fromUserAccountMint"] in ["EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v","Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB"]:
                                    solAmount=tx["fromUserAccountAmount"] / rate[tx["fromUserAccountMint"]]
                                elif tx["fromUserAccountMint"] == "So11111111111111111111111111111111111111112":
                                    solAmount=tx["fromUserAccountAmount"]
                                else:
                                    log.error(f"Unknown currency: {tx["fromUserAccountMint"]}")
                                    continue
                                trade="buy"
                                price=solAmount/tokenAmount
                        
                            if mode=="lpush":
                                block["timecounter"]=block["timecounter"]-1
                            else:
                                block["timecounter"]=block["timecounter"]+1
                            timecounter=block["timecounter"]
                            timestamp=row["timestamp"]
                            milliseconds=timecounter
                            #print(f"pump raydium:{price}")
                            mcap=price*token_supply[address]*solana_price
                            price=mcap

                            mlp=get_last_price(address) #last price
                            if mlp == 0:
                                high=price
                                low=price
                                open_price=price
                                close_price=price
                                color=trade_colors["buy"]
                            elif mlp > price:
                                
                                high=price
                                low=mlp
                                open_price=price
                                close_price=mlp
                                color=trade_colors["sell"]
                                if fee_payer in dev:
                                    color=trade_colors["insider"]
                                    
                            else:
                                high=mlp
                                low=price
                                open_price=price
                                close_price=mlp
                                color=trade_colors["buy"]
                                if fee_payer in dev:
                                    color=trade_colors["insider"]


                            data={"broker":"RAYDIUM", 
                                  "color":color,
                                  "open":open_price,
                                  "close":close_price,   
                                  "high":high,
                                  "low":low,
                                  "address":fee_payer,
                                  "trade":trade, 
                                  "tokenAmount":tokenAmount,
                                  "solAmount":solAmount,
                                  "price":price,"timestamp":int(timestamp),
                                  "milliseconds":milliseconds,
                                  "signature":row["signature"],
                                  "slot":row["slot"]} 
                
                            
                            set_last_price(address,price)
                            
                            print(f"mlp: {mlp}  price: {price}")
                            redismq.push_trade(redis_connection,data,address,mode)
                     

 
