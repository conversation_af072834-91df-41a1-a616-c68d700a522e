import time
from datetime import datetime
import mysql.connector
from mysql.connector import errorcode
from mysql.connector import pooling
import pandas as pd
import modules.transfer as transfer
import modules.utils as utils
import json,random,sys
import modules.config
from modules.config import *

dblog=utils.get_logger()

#get total_tx	uniq_accounts	buy_count	sell_count	last_buy_time	last_sell_time	last_price

def insert_instagram_post(pool,code,username,full_name,profile_pic_url,caption,taken_at,image_url,video_url):
    conn = pool.get_connection()
    try:
        with conn.cursor(buffered=True) as cursor:
            # Check if the account exists
            query = """
            INSERT INTO instagram (code,username,full_name, profile_pic_url,caption, taken_at, image_urls,video_urls)
            VALUES (%s, %s, %s, %s,%s,%s,%s,%s)
            """
            cursor.execute(query, (code,username,full_name,profile_pic_url, caption, taken_at, image_url,video_url ))
            conn.commit()
            return True

    finally:
        conn.close()

def get_instagram_user_date(pool, username,taken_at):
    conn = pool.get_connection()
    try:
        with conn.cursor(buffered=True) as cursor:
            # Check if the account exists
            query = """
                    SELECT id FROM instagram WHERE username = %s and taken_at = %s;
            """
            cursor.execute(query, (username,taken_at,))
            rows = cursor.fetchone()
            if rows == None:
                return False
            return True

    finally:
        conn.close()

def get_wallets_by_api(pool, api):
    conn = pool.get_connection()
    try:
        with conn.cursor() as cursor:
            # Check if the account exists
            query = """
                    SELECT address FROM accounts WHERE helius_api = %s;
            """
            cursor.execute(query, (api,))
            rows = cursor.fetchall()
            data=[]
            if len(rows) > 0:
                for row in rows:
                    data.append(row[0])

            return data

    finally:
        conn.close()

def get_api_by_wallet(pool, address):
    conn = pool.get_connection()
    try:
        with conn.cursor() as cursor:
            # Check if the account exists
            query = """
                    SELECT helius_api FROM accounts WHERE address = %s limit 1;
            """
            cursor.execute(query, (address,))
            rows = cursor.fetchone()
            if rows:
                return rows[0]
            return None

    finally:
        conn.close()



def get_multi_account_swaps(pool, mint):
    conn = pool.get_connection()
    try:
        with conn.cursor() as cursor:
            # Check if the account exists
            query = """
                    SELECT 
                        mint,
                        account_name,
                        token_name,
                        SUM(CASE WHEN trade = 'buy' THEN 1 ELSE 0 END) AS buys,
                        SUM(CASE WHEN trade = 'sell' THEN 1 ELSE 0 END) AS sells,
                        SUM(CASE WHEN trade = 'buy' THEN token_amount ELSE 0 END) AS total_tokens_bought,
                        SUM(CASE WHEN trade = 'sell' THEN token_amount ELSE 0 END) AS total_tokens_sold,
                        CASE 
                            WHEN SUM(CASE WHEN trade = 'buy' THEN token_amount ELSE 0 END) = 0 AND SUM(CASE WHEN trade = 'sell' THEN token_amount ELSE 0 END) > 0 
                                THEN 100
                            WHEN SUM(CASE WHEN trade = 'buy' THEN token_amount ELSE 0 END) = SUM(CASE WHEN trade = 'sell' THEN token_amount ELSE 0 END)
                                THEN 100
                            WHEN SUM(CASE WHEN trade = 'buy' THEN token_amount ELSE 0 END) + SUM(CASE WHEN trade = 'sell' THEN token_amount ELSE 0 END) > 0
                                THEN (SUM(CASE WHEN trade = 'sell' THEN token_amount ELSE 0 END) / 
                                    (SUM(CASE WHEN trade = 'buy' THEN token_amount ELSE 0 END) + SUM(CASE WHEN trade = 'sell' THEN token_amount ELSE 0 END))) * 100
                            ELSE 0
                        END AS percentage_tokens_sold,
                        SUM(CASE WHEN trade = 'buy' THEN sol_amount ELSE 0 END) AS total_sol_bought,
                        SUM(CASE WHEN trade = 'sell' THEN sol_amount ELSE 0 END) AS total_sol_sold,
                        MIN(block_time) AS first_trade_timestamp,
                        CASE 
                            WHEN SUM(CASE WHEN trade = 'buy' THEN token_amount ELSE 0 END) = SUM(CASE WHEN trade = 'sell' THEN token_amount ELSE 0 END)
                                THEN MAX(CASE WHEN trade = 'sell' THEN block_time ELSE NULL END) - MIN(CASE WHEN trade = 'buy' THEN block_time ELSE NULL END)
                            ELSE UNIX_TIMESTAMP(NOW()) - MIN(CASE WHEN trade = 'buy' THEN block_time ELSE NULL END)
                        END AS seconds_in_token
                    FROM 
                        token_swaps
                    WHERE 
                        mint = %s
                    GROUP BY 
                        account_name, token_name;
            """
            cursor.execute(query, (mint,))
            rows = cursor.fetchall()
            data=[]
            if len(rows) > 0:
                for row in rows:
                    row_dict={}
                    row_dict['mint']=row[0]
                    row_dict['account_name']=row[1]
                    row_dict['token_name']=row[2]
                    row_dict['buys']=int(row[3])
                    row_dict['sells']=int(row[4])
                    row_dict['total_tokens_bought']=float(row[5])
                    row_dict['total_tokens_sold']=float(row[6])
                    row_dict['percentage_tokens_sold']=float(row[7])
                    row_dict['total_sol_bought']=float(row[8])
                    row_dict['total_sol_sold']=float(row[9])
                    row_dict['first_trade_timestamp']=row[10]
                    row_dict['seconds_in_token']=int(row[11])
                    data.append(row_dict)

            # if len(rows) > 0:
            #     return rows

            return data

    finally:
        conn.close()



def get_uniq_image(pool,mint):
    conn = pool.get_connection()
    try:
        with conn.cursor() as cursor:
            # Check if the account exists
            query = """
                SELECT COUNT(*) AS duplicate_count
                FROM token_discoveries2
                WHERE image_md5 = (SELECT image_md5 FROM token_discoveries2 WHERE mint = %s)
            """
            cursor.execute(query, (mint,))
            result = cursor.fetchone() 
            print(result)
            
            if result:
                return result[0]

    finally:
        conn.close()


def get_uniq_website(pool,mint):
    conn = pool.get_connection()
    try:
        with conn.cursor() as cursor:
            # Check if the account exists
            query = """
                                SELECT 
                        t.mint,
                        t.twitter, 
                        t.website, 
                        t.telegram 
                    FROM 
                        token_discoveries2 t
                    JOIN (
                        SELECT 
                            website
                        FROM 
                            token_discoveries2 
                        WHERE 
                            REPLACE(REPLACE(website, 'https://', ''), 'http://', '') REGEXP '^[^/]+/?$'
                            AND website NOT LIKE 'youtube%'
                            AND website NOT LIKE '%wikiped%'
                            AND website NOT LIKE 'https://x.com%'
                            AND website NOT LIKE 'https://t.me%'
                            AND website NOT LIKE 'https://twitter%'
                            AND website LIKE 'https://%' 
                        GROUP BY 
                            website 
                        HAVING 
                            COUNT(website) = 1 
                    ) AS filtered_websites ON t.website = filtered_websites.website
                    WHERE
                        t.mint = %s
                    ORDER BY 
                        t.website;
            """
            cursor.execute(query, (mint,))
            result = cursor.fetchone() 
            
            if result:
                return result[1]

    finally:
        conn.close()

def get_tx_stats_for_period(pool,mint):
    conn = pool.get_connection()
    try:
        with conn.cursor() as cursor:
            # Check if the account exists
            query = """
                SELECT
                    COUNT(ts.mint) AS total_tx,
                    COUNT(DISTINCT ts.account) AS uniq_accounts,
                    SUM(CASE WHEN ts.trade = 'buy' THEN 1 ELSE 0 END) AS buy_count,
                    SUM(CASE WHEN ts.trade = 'sell' THEN 1 ELSE 0 END) AS sell_count,
                    MAX(CASE WHEN ts.trade = 'buy' THEN ts.block_timestamp ELSE NULL END) AS last_buy_time,
                    MAX(CASE WHEN ts.trade = 'sell' THEN ts.block_timestamp ELSE NULL END) AS last_sell_time,
                    (SELECT ts2.price 
                    FROM `token_swaps` ts2
                    WHERE ts2.mint = ts.mint 
                    AND ts2.block_timestamp = (
                        SELECT MAX(ts3.block_timestamp)
                        FROM `token_swaps` ts3
                        WHERE ts3.mint = ts.mint
                    )
                    LIMIT 1) AS last_price,
                    (SELECT COUNT(*)
                    FROM `token_discoveries` td
                    WHERE td.owner = (
                        SELECT owner 
                        FROM `token_discoveries` td2 
                        WHERE td2.mint = ts.mint LIMIT 1)
                    ) AS minted_tokens_count,
                    (SELECT JSON_ARRAYAGG(td.mint)
                    FROM `token_discoveries` td
                    WHERE td.owner = (
                        SELECT owner 
                        FROM `token_discoveries` td2 
                        WHERE td2.mint = ts.mint LIMIT 1)
                    ) AS minted_tokens_list
                FROM
                    `token_swaps` ts
                WHERE
                    ts.mint = %s
            """
            cursor.execute(query, (mint,))
            result = cursor.fetchone() 
            
            if result:
                return result[1],result[0],result[2],result[3],result[6],result[7],result[8]

    finally:
        conn.close()


def get_uniq_total_tx(pool,mint):
    conn = pool.get_connection()
    try:
        with conn.cursor() as cursor:
            # Check if the account exists
            query = """
                    SELECT  count(mint) as total_tx,count(distinct(account)) as uniq_accounts
                    FROM `token_swaps`
                    where mint =%s;
            """
            cursor.execute(query, (mint,))
            result = cursor.fetchone() 
            
            if result:
                return result[1],result[0]

    finally:
        conn.close()

def get_mint_unique_holders(pool,mint,min_total_swaps=100,pct_unique_accounts=0.7):
    conn = pool.get_connection()
    try:
        with conn.cursor() as cursor:
            # Check if the account exists
            query = """
                    WITH swap_summary AS (
                        SELECT 
                            mint,
                            COUNT(*) AS total_swaps,
                            COUNT(DISTINCT account) AS unique_addresses
                        FROM 
                            token_swaps
                        WHERE 
                            mint = %s
                        GROUP BY 
                            mint
                    )
                    SELECT DISTINCT account
                    FROM token_swaps
                    WHERE 
                        mint = %s
                        AND EXISTS (
                            SELECT 1 
                            FROM swap_summary 
                            WHERE unique_addresses > %s * total_swaps
                            and total_swaps > %s
                        )
            """
            cursor.execute(query, (mint,mint,pct_unique_accounts,min_total_swaps,))
            result = cursor.fetchall() 
            
            if result:
                addresses=[]
                for row in result:
                    addresses.append(row[0])
                return addresses

    finally:
        conn.close()


def check_manual_disatance(pool,address):
    conn = pool.get_connection()
    try:
        with conn.cursor() as cursor:
            # Check if the account exists
            query = """
            SELECT distance FROM accounts WHERE address = %s
            """
            cursor.execute(query, (address,))
            result = cursor.fetchone()  # Fetch the first matching record
            
            if result:
                return result[0]

    finally:
        conn.close()


def add_token_swap(owner,mint,trade,tokenAmount,solAmount,price,timestamp,signature,slot,program,):
    pool=modules.config.pool
    conn = pool.get_connection()
    try:
        with conn.cursor() as cursor:
            
            # id	int Auto Increment	
            # mint	varchar(100)	
            # account	varchar(100)	
            # sol_amount	varchar(100)	
            # token_amount	varchar(45)	
            # trade	varchar(10)	
            # price	varchar(10)	
            # market_cup	varchar(10)	
            # program	varchar(20)	
            # block_time	int	
            # block_id	int	
            # signature	varchar(100)	
            insert_query = """
            INSERT IGNORE INTO token_swaps (account,mint,trade,token_amount,sol_amount,price,block_time,block_timestamp,signature,block_id,program)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s,%s)
            """

            cursor.execute(insert_query, (owner,mint,trade,tokenAmount/1e6,solAmount/1e9,price,timestamp,datetime.fromtimestamp(float(timestamp)/1000),signature,slot,program))
            conn.commit()
            #print(f"committing: {mint}")
            return True

    finally:
        conn.close()

def add_token_swap_name(owner,mint,trade,tokenAmount,solAmount,price,timestamp,signature,slot,program,account_name,token_name,token_url,token_description,token_symbol,token_created,twitter,website,telegram,mcap):
    pool=modules.config.pool
    pnl_array=[]
    if trade == "sell":
        print("trade is sell")
        avg_buy=get_avg_price(pool,owner,mint)
        if avg_buy == None:
            pnl_from_avg2=0
        else:
            print(f"avg_buy: {avg_buy}")
            #percentage_diff = ((price2 - price1) / price1) * 100
            pnl_from_avg2=((float(solAmount)/float(tokenAmount)) - float(avg_buy)) / float(avg_buy) * 100
            #print(f"pnl_from_avg2: {avg_buy}")
            #pnl_from_avg2=1.1
        
    else:
        pnl_from_avg2=0
        pnl = get_wallet_pnl(pool,owner) 
        
        for p in pnl:
            if p[5] is not None:
                if p[5]>0 and p[5]<50:
                    pnl_array.append(1)
                elif p[5]>=50 and p[5]<100:
                    pnl_array.append(2)
                elif p[5]>=100:
                    pnl_array.append(3)
                elif p[5]<0 and p[5]>-50:
                    pnl_array.append(-1)
                elif p[5]<=-50 and p[5]>-100:
                    pnl_array.append(-2)
                elif p[5]<=-100:
                    pnl_array.append(-3)
            else:
                pnl_array.append(0)

    # get pnd 
       
    conn = pool.get_connection()
    try:
        with conn.cursor() as cursor:
            
            # id	int Auto Increment	
            # mint	varchar(100)	
            # account	varchar(100)	
            # sol_amount	varchar(100)	
            # token_amount	varchar(45)	
            # trade	varchar(10)	
            # price	varchar(10)	
            # market_cup	varchar(10)	
            # program	varchar(20)	
            # block_time	int	
            # block_id	int	
            # signature	varchar(100)	
            insert_query = """
            INSERT IGNORE INTO token_swaps (account,mint,trade,token_amount,sol_amount,price,block_time,block_timestamp,signature,block_id,program,account_name,token_name,token_url,token_description,token_symbol,token_timestamp,twitter,website,telegram,mcap,pnl_from_avg2,pnl_array)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s)
            """

            cursor.execute(insert_query, (owner,mint,trade,tokenAmount,solAmount,price,timestamp,datetime.fromtimestamp(timestamp),signature,slot,program,account_name,token_name,token_url,token_description,token_symbol,token_created,twitter,website,telegram,mcap,pnl_from_avg2,json.dumps(pnl_array)))
            conn.commit()
            #print(f"committing: {mint}")
            return True

    finally:
        conn.close()



def update_token_metadata(pool, mint, token_name, token_symbol, img_url, score, freeze_auth, mint_authority, risks, mutable):
    conn = pool.get_connection()
    try:
        with conn.cursor() as cursor:
            query = """
            update token_discoveries set name = %s, symbol = %s, url = %s, score = %s, freeze_auth = %s, mint_auth = %s, risks = %s, mutable = %s where mint = %s
            """
            cursor.execute(query, (token_name, token_symbol, img_url, score, freeze_auth, mint_authority, risks, mutable, mint))
            conn.commit()
            return True
    except mysql.connector.Error as err:
        print(f"Error: {err}")
        conn.rollback()
    finally:
        conn.close()


def add_mint_to_db(pool,owner,mint,signature,bock_time, slot):
    conn = pool.get_connection()
    try:
        with conn.cursor() as cursor:
            
            insert_query = """
            INSERT INTO token_discoveries (mint,owner,first_sig,discovered_ts,update_ts,slot)
            VALUES (%s, %s, %s, %s,%s,%s)
            """
            cursor.execute(insert_query, (mint, owner, signature, datetime.fromtimestamp(bock_time),datetime.fromtimestamp(bock_time), slot))
            conn.commit()
            print(f"committing: {mint}")
            return True

    finally:
        conn.close()


def add_mint_to_db_full(pool,mint,symbol,name,description,image_uri,twitter,telegram,website,creator,created_timestamp,image_md5,image_local_uri,bonding_curve,associated_bonding_curve):
    conn = pool.get_connection()
    print(f"mint: {mint} symbol: {symbol} name: {name} description: {description} image_uri: {image_uri} twitter: {twitter} telegram: {telegram} website: {website} creator: {creator} created_timestamp: {created_timestamp} image_md5: {image_md5} image_local_uri: {image_local_uri} bonding_curve: {bonding_curve} associated_bonding_curve: {associated_bonding_curve}")
    try:
        with conn.cursor() as cursor:
            
            insert_query = """
            INSERT INTO token_discoveries2 (mint,symbol,name,description,image_uri,twitter,telegram,website,creator,created_timestamp,image_md5,image_local_uri,bonding_curve,associated_bonding_curve)
            VALUES (%s, %s, %s, %s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s)
            """
            cursor.execute(insert_query, (mint,symbol,name,description,image_uri,twitter,telegram,website,creator,created_timestamp,image_md5,image_local_uri,bonding_curve,associated_bonding_curve))
            conn.commit()
            print(f"committing: {mint}")
            return True
    except mysql.connector.Error as err:
        print(f"Error: {err}")
        conn.rollback()
    finally:
        conn.close()


def load_account_address_list_from_db(pool):
    conn = pool.get_connection()
    try:
        with conn.cursor() as cursor:
            # Check if the account exists
            query = """
            SELECT address,balance,last_sig FROM accounts where scan = 1 and scanned = 0 and master = %s
            """
            cursor.execute(query,(modules.config.master_address,))
            result = cursor.fetchall()    
            conn.commit()
            accounts=[]
            for row in result:
               addr={'address':row[0],'balance':row[1],'last_sig':row[2]}
               accounts.append(addr)
            #get tokens
            query = """
            SELECT address,symbol,name FROM tokens 
            """
            cursor.execute(query)
            result = cursor.fetchall()    
            conn.commit()
            tokens=[]
            for row in result:
               token={'address':row[0],'symbol':row[1],'name':row[2]}
               tokens.append(token)

            return accounts,tokens

    finally:
        conn.close()

def get_account_address_list():
    return "[ list from memory ]"

def add_token_to_tokens_list(pool,tokens,address,mint_assoc_address=""):
    # address, balance,lastsig
    ts=datetime.now() 
    def _add_to_tokens(tokens,address):
        token_info=transfer.get_token_info(address)
        uri=token_info["files"][0]["uri"]
        token_info=token_info["metadata"]
        if not token_info:
            return False
        dblog.info(token_info)
        addr={'address':address,'symbol':token_info['symbol'],'name':token_info['name'],'url':uri}
        tokens.append(addr) 
        #add to db
        add_token(pool, address,token_info['symbol'],token_info['name'],uri)
        add_account(pool, address, 0, 'token', mint_assoc_address, ts, ts, ts, '','new',"mint",0)


        return tokens,token_info['symbol']
    if tokens == None:
        tokens=[]
        return _add_to_tokens(tokens,address)
    for t in tokens:
        if t['address'] == address:
            return tokens,t['symbol']
  
    return _add_to_tokens(tokens,address)

def add_address_to_account_list(accounts,address,balance=None,last_sig=None):
    # address, balance,lastsig
    def _add_to_accounts(accounts,address,balance=None,last_sig=None):
        addr={'address':address,'balance':balance,'last_sig':last_sig}
        accounts.append(addr) 
        return accounts,"new"
    if accounts == None:
        accounts=[]
        return _add_to_accounts(accounts,address,balance,last_sig)
    for acc in accounts:
        if acc['address'] == address:
            if balance == None and last_sig == None:
                print(f"Already exist: {acc['address']}")
                return accounts,"exist"
            else:
                accounts[acc]['balance']=balance
                accounts[acc]['last_sig']=last_sig
                return accounts
  
    return _add_to_accounts(accounts,address,balance,last_sig)

def add_token(pool, address,symbol,name,url):

    conn = pool.get_connection()
    try:
        with conn.cursor() as cursor:
            # Check if the account exists
            query = """
            SELECT address FROM tokens WHERE address = %s
            """
            cursor.execute(query, (address,))
            result = cursor.fetchone()  # Fetch the first matching record
            
            if result:
                return "Token already exists."
            
            # Insert the account if it does not exist
            insert_query = """
            INSERT INTO tokens (address, symbol,name,url)
            VALUES (%s, %s, %s, %s)
            """
            cursor.execute(insert_query, (address, symbol,name,url))
            conn.commit()
            return True

    finally:
        conn.close()

def get_wallet_pnl(pool,address):

    conn = pool.get_connection()
    try:
        with conn.cursor() as cursor:
            # Check if the account exists
            query = """
                    WITH token_buys AS (
                        SELECT
                            mint,
                            account,
                            SUM(token_amount) AS total_tokens_bought,
                            SUM(sol_amount) AS total_sol_spent
                        FROM
                            token_swaps
                        WHERE
                            trade = 'buy'
                        GROUP BY mint, account
                    ),
                    token_sells AS (
                        SELECT
                            mint,
                            account,
                            sol_amount,
                            token_amount,
                            block_time,
                            token_name, -- Include the token name
                            ROW_NUMBER() OVER (PARTITION BY mint, account ORDER BY block_time) AS rn -- Assign row numbers for uniqueness
                        FROM
                            token_swaps
                        WHERE
                            trade = 'sell'
                    ),
                    remaining_tokens AS (
                        SELECT
                            tb.mint,
                            tb.account,
                            tb.total_tokens_bought - COALESCE(SUM(ts.token_amount), 0) AS tokens_remaining
                        FROM
                            token_buys tb
                        LEFT JOIN
                            token_sells ts ON tb.mint = ts.mint AND tb.account = ts.account
                        GROUP BY tb.mint, tb.account, tb.total_tokens_bought
                    )
                    SELECT
                        ts.mint,
                        ts.account,
                        ts.token_name, -- Include token name in output
                        ts.block_time, -- Include block_time for ordering
                        CASE 
                            WHEN remaining_tokens.tokens_remaining = 0 -- Only calculate PnL when all tokens are sold
                            THEN 
                                (ts.sol_amount - (tb.total_sol_spent * (ts.token_amount / tb.total_tokens_bought)))
                            ELSE NULL 
                        END AS pnl, -- Profit and Loss calculation
                        CASE 
                            WHEN remaining_tokens.tokens_remaining = 0 -- Only calculate PnL when all tokens are sold
                            THEN 
                                ((ts.sol_amount - (tb.total_sol_spent * (ts.token_amount / tb.total_tokens_bought))) / tb.total_sol_spent) * 100
                            ELSE NULL 
                        END AS pnl_pct -- PnL percentage calculation
                    FROM
                        token_sells ts
                    LEFT JOIN
                        token_buys tb ON ts.mint = tb.mint AND ts.account = tb.account
                    LEFT JOIN
                        remaining_tokens ON ts.mint = remaining_tokens.mint AND ts.account = remaining_tokens.account
                    WHERE
                        ts.account = %s 
                        AND ts.rn = 1 -- Keep only the first sell transaction for each mint and account
                    GROUP BY
                        ts.mint, ts.account, ts.token_name, ts.block_time, ts.sol_amount, ts.token_amount, tb.total_sol_spent, tb.total_tokens_bought, remaining_tokens.tokens_remaining
                    ORDER BY
                        ts.block_time DESC 
                    limit 10

            """

            cursor.execute(query,(address,))
            result = cursor.fetchall()
            return result

    finally:
        conn.close()


def get_unique_address_pairs(pool):

    conn = pool.get_connection()
    try:
        with conn.cursor() as cursor:
            # Check if the account exists
            query = """
            SELECT  source,  destination, SUM(amount) AS amount FROM trans GROUP BY source, destination;
            """
            cursor.execute(query)
            result = cursor.fetchall()
            return result

    finally:
        conn.close()
    
def add_account(pool, address, balance, account_type, owner, discovery_account_ts, first_tx_ts, last_tx_ts, last_sig,status,role="regular",scan=1,distance=0,mint_path=0):

    conn = pool.get_connection()
    try:
        with conn.cursor() as cursor:
            # Check if the account exists
            query = """
            SELECT address FROM accounts WHERE address = %s
            """
            cursor.execute(query, (address,))
            result = cursor.fetchone()  # Fetch the first matching record
            
            if result:
                return "Account already exists."
            
            # Insert the account if it does not exist
            insert_query = """
            INSERT INTO accounts (master,address, balance, account_type, owner, disovery_account_ts, first_tx_ts, last_tx_ts, last_sig,status,role,scan,scanned,distance,mint_path)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s,%s,%s,%s,%s,%s,%s,%s)
            """
            cursor.execute(insert_query, (modules.config.master_address, address, balance, account_type, owner, discovery_account_ts, first_tx_ts, last_tx_ts, last_sig,status,role,scan,0,distance,mint_path))
            conn.commit()
            return "Account added successfully."

    finally:
        conn.close()

def extract_address_from_tx(neo_client,pool,accounts,df,last_sig,address):
    '''
    Extract addresses from transactions
    '''
    scan={}
    scan["regular"]=1
    scan["mint"]=0

    for _, row in df.iterrows():
        if row['source'] != address:
            accounts,status=add_address_to_account_list(accounts,row['source'],balance=None,last_sig=None)

            if status == "new":
                
                #print(f"Add source to db when it is source: {row['source']}")
                add_account(pool, row['source'], ***************, row["src_type"], "system", datetime.now(), datetime.now(), datetime.now(), "",status,row["src_type"],scan[row["src_type"]],row["distance"],row["mint_path"])

                #address1 = neo_client.add_unique_node("Address", {"name": address, "balance": 0, "total_tx": 20, "role": "user", "group": "B", "age": 30, "type": "wallet"})
                #address2 = neo_client.add_unique_node("Address", {"name": row["source"], "balance": 0, "total_tx": 20, "role": "user", "group": "B", "age": 30, "type": "wallet"})
                #print(f"edgelabel: {address}-{row["source"]}")
                #relationship = neo_client.add_unique_relationship("Address", {"name": address}, "Address", {"name": row["source"]}, "TRANSFERRED", {"amount": 0,"amount_send": 0, "amount_received": 0})
    


                # add neo 
        if row['destination'] != address:
            accounts,status=add_address_to_account_list(accounts,row['destination'],balance=None,last_sig=None)
            if status == "new":
                #print(f"Add dest to db when its destination: {row['destination']}")
                add_account(pool, row['destination'], ***************,row["dest_type"], "system", datetime.now(), datetime.now(), datetime.now(), "",status,row["dest_type"],scan[row["dest_type"]],row["distance"],row["mint_path"])
              
                #address1 = neo_client.add_unique_node("Address", {"name": row["source"], "balance": 0, "total_tx": 20, "role": "user", "group": "B", "age": 30, "type": "wallet"})
                #address2 = neo_client.add_unique_node("Address", {"name": row["destination"], "balance": 0, "total_tx": 20, "role": "user", "group": "B", "age": 30, "type": "wallet"})
                #print(f"edgelabel: {row["source"]}-{row["destination"]}")
                #relationship = neo_client.add_unique_relationship("Address", {"name": row["source"]}, "Address", {"name": row["destination"]}, "TRANSFERRED", {"amount": 0,"amount_send": 0, "amount_received": 0})
    

            
    return accounts
     
def get_trader_accounts(pool,address):
    conn = pool.get_connection()
    try:
        with conn.cursor() as cursor:
            # Check if the account exists
            query = """
                    select address from accounts where address = %s
            """
            cursor.execute(query, (address,))
            result = cursor.fetchone() 
            return result
    finally:
        conn.close()

def get_account_name(pool,address):
    conn = pool.get_connection()
    try:
        with conn.cursor() as cursor:
            # Check if the account exists
            query = """
                    select address from accounts where address = %s
            """
            cursor.execute(query, (address,))
            result = cursor.fetchone() 
            return result
    finally:
        conn.close()


def get_avg_price(pool,address,mint):
    conn = pool.get_connection()
    try:
        with conn.cursor() as cursor:
            # Check if the account exists
            query = """
                    SELECT 
                        sum(sol_amount)/sum(token_amount) as price
                    FROM 
                        token_swaps
                    where mint = %s  and account = %s and trade = 'buy' 

            """
            cursor.execute(query, (mint,address))
            result = cursor.fetchone() 
            if len(result) > 0:
                return result[0]
            else:
                return 0
    finally:
        conn.close()



def get_all_accounts_by_type(pool,scan_type,status=".*"):
    conn = pool.get_connection()
    try:
        with conn.cursor() as cursor:
            # Check if the account exists
            query = """
            SELECT address,balance,last_sig FROM accounts WHERE scan = 1 and address not like '' and role = %s and status REGEXP %s order by scan_prior asc
            """
            cursor.execute(query, (scan_type,status))
            result = cursor.fetchall()  # Fetch the first matching record
            conn.commit()
            accounts=[]
            current_balances=[]
            addr_sigs=[]
            for row in result:
                addr_sig={"address":row[0],"last_sig":row[2]}
                accounts.append(row[0])
                current_balances.append(row[1])
                addr_sigs.append(addr_sig)
            return accounts,current_balances,addr_sigs
    except mysql.connector.Error as err:
        print(f"Error: {err}")
        conn.rollback()
            
    finally:
        conn.close()

def get_all_accounts_for_update(pool,status=".*"):
    conn = pool.get_connection()
    try:
        with conn.cursor() as cursor:
            # Check if the account exists
            query = """
            SELECT address,balance,last_sig FROM accounts WHERE scan = 1 and address not like '' and status REGEXP %s AND NOT ( status like 'exist' and  balance = 0 and role != 'master'   ) order by distance asc
            """
            cursor.execute(query, (status,))
            result = cursor.fetchall()  # Fetch the first matching record
            conn.commit()
            accounts=[]
            current_balances=[]
            addr_sigs=[]
            #print (result)
            for row in result:
                addr_sig={"address":row[0],"last_sig":row[2]}
                accounts.append(row[0])
                current_balances.append(row[1])
                addr_sigs.append(addr_sig)
            return accounts,current_balances,addr_sigs
    except mysql.connector.Error as err:
        print(f"Error: {err}")
        conn.rollback()
            
    finally:
        conn.close()

def get_all_accounts_update(pool,status=".*"):
    conn = pool.get_connection()
    try:
        with conn.cursor() as cursor:
            # Check if the account exists
            #
            # query = """
            # SELECT address,balance,last_sig FROM accounts WHERE scan = 1 and address not like '' and status REGEXP %s AND NOT ((distance > 1 AND balance = 0) or (master = 'GUqaKLm1JpA3adxn6kYUDeBbMne3Mz5VxVP6QNKH2UNo' AND distance > 0 AND balance = 0) )  order by distance asc
            # """
            #query = """
            #SELECT address,balance,last_sig FROM accounts WHERE scan = 1 and address not like '' and status REGEXP %s AND NOT (distance > 1 AND balance = 0)   order by distance asc
            #"""
            query = """
            SELECT address,balance,last_sig FROM accounts WHERE scan = 1 and address not like '' and status REGEXP %s     order by distance asc
            """

            cursor.execute(query,(status,))
            #cursor.execute(query, (status,))
            result = cursor.fetchall()  # Fetch the first matching record
            conn.commit()
            accounts=[]
            current_balances=[]
            addr_sigs=[]
            #print (result)
            for row in result:
                addr_sig={"address":row[0],"last_sig":row[2]}
                accounts.append(row[0])
                current_balances.append(row[1])
                addr_sigs.append(addr_sig)
            return accounts,current_balances,addr_sigs
    except mysql.connector.Error as err:
        print(f"Error: {err}")
        conn.rollback()
            
    finally:
        conn.close()

def get_all_accounts(pool,status=".*"):
    conn = pool.get_connection()
    try:
        with conn.cursor() as cursor:
            # Check if the account exists
            # query = """
            # SELECT address,balance,last_sig FROM accounts WHERE scan = 1 and address not like '' and status REGEXP %s order by distance asc
            # """
            query = """
            SELECT address, balance, last_sig 
            FROM accounts 
            WHERE scan = 1 
            AND master = %s
            AND ((scanned = 0 AND distance < %s) OR status REGEXP %s) 
            ORDER BY distance ASC
            """

            cursor.execute(query,(modules.config.master_address,max_hops,status))
            #cursor.execute(query, (status,))
            result = cursor.fetchall()  # Fetch the first matching record

            conn.commit()
            accounts=[]
            current_balances=[]
            addr_sigs=[]
            #print (result)
            for row in result:
                addr_sig={"address":row[0],"last_sig":row[2]}
                accounts.append(row[0])
                current_balances.append(row[1])
                addr_sigs.append(addr_sig)
            return accounts,current_balances,addr_sigs
    except mysql.connector.Error as err:
        print(f"Error: {err}")
        conn.rollback()
            
    finally:
        conn.close()


def get_account_last_sig(account):
    #retrieve account last signature 
    pass

def get_account_balance(address,start=None,end=None):
    #get balance in sol 
    pass

def get_account_turnaround(address,start=None,end=None):
    # count in+out amounts to get turnaround for specific period default 24h
    pass


def get_account_tx_count(address,start=None,end=None):
    #retrieve amount of transactions 
    pass

def get_account_token_balance(address,token,start=None,end=None):
    #return specific token balance for account
    pass

def get_account_all_token_balance(address):
    #retur list of alltokens acocunt has and its balance
    pass

def get_account_age(address):
    pass



def get_account_last_sig(address):
    #return sig,time
    pass

def check_all_account_balances():
    #get all account balances int oarray 
    #get all new balances and compare
    # update account balance if difernece
    pass

def update_token_data(pool,address,data):
    col,value=data.items()

    conn = pool.get_connection()
    try:
        with conn.cursor() as c:
            for col, value in data.items():
                query = f"""
                update tokens set {col} = %s where address = %s 
                """
                c.execute(query,(value,address))
            conn.commit()
            return True
    finally:
        dblog.debut(f"update: token: {address}  last_sig: {data}")
        conn.close()


def update_account_last_sig(pool,account,last_sig):
    account=str(account)
    conn = pool.get_connection()
    try:
        with conn.cursor() as c:
            # Check if the account exists
            #dblog.info(f"type acc: {type(account)}  sig:{type(last_sig)}")
            #dblog.info(f"acc:{account} sig:{last_sig}")
            query = """
            update accounts set last_sig = %s,status='exist',scanned = 1  where address = %s 
            """
            c.execute(query,(last_sig,account))
            conn.commit()
            return True
    finally:
        dblog.info(f"update: account: {account}  last_sig: {last_sig}")
        conn.close()

def update_account_balance(pool,account,balance):
    conn = pool.get_connection()
    try:
        with conn.cursor() as cursor:
            # Check if the account exists
            query = """
            update accounts set balance = %s,status="update"   where address = %s 
            """
            cursor.execute(query,(balance,account))
            conn.commit()

            insert_query = """
            INSERT INTO account_balance (address, balance,ts)
            VALUES (%s, %s, %s)
            """
            cursor.execute(insert_query, (account, balance,datetime.now() ))
            conn.commit()

    finally:
        conn.close()

def retire_account(pool, addr):
    conn = pool.get_connection()
    try:
        with conn.cursor() as cursor:
            insert_query = """
            update accounts set status = "retired" where address = %s
            """
            cursor.execute(insert_query, (addr,))  # Wrap addr in a tuple
            print("committing")
            conn.commit()
    finally:
        conn.close()      

def update_account_balances(pool,balances):
    conn = pool.get_connection()
    try:
        with conn.cursor() as cursor:
            ts=datetime.now() 
            for index, row in balances.iterrows():
                print(f"update account: {row['account']}  new balance: {round(row['balance']/1e9,2)}")

                query = """
                update accounts set balance = %s,status="update"   where address = %s 
                """
                cursor.execute(query,(row['balance'],row['account']))


                insert_query = """
                INSERT INTO account_balance (address, balance,ts)
                VALUES (%s, %s, %s)
                """
                cursor.execute(insert_query, (row['account'], row['balance'],ts ))
            print("committing")
            conn.commit()

    finally:
        conn.close()       
def add_single_tx_to_db(pool,df,minsol):
    #print("procesing txs")
    conn = pool.get_connection()
    try:
        with conn.cursor() as cursor:

            for _, tx in df.iterrows():
                if tx["coin"] == "sol" and float(tx["amount"]) < minsol:
                    continue
                elif tx["type"] == "BURN":
                    continue
                else:
                    ts=datetime.fromtimestamp(tx["timestamp"])   
                    q=f"INSERT INTO trans (tx_type, currency_name, slot, source, destination, amount, mint_address,mint_name, sig,transaction_date) VALUES ('{tx['type']}', '{tx['coin']}', {tx['slot']}, '{tx['source']}', '{tx['destination']}', '{tx['amount']}','{tx['mint']}','{tx['mint_name']}','{tx['signature']}','{ts}');"
                    cursor.execute(q)
            conn.commit()
            
            return "txs added successfully."

    finally:
        conn.close()

def add_tx_to_db(pool,df,minsol):
    print("procesing txs")
    conn = pool.get_connection()
    try:
        with conn.cursor() as cursor:

            for _, tx in df.iterrows():
                if tx["coin"] == "sol" and float(tx["amount"]) < minsol:
                    continue
                elif tx["type"] == "BURN":
                    continue
                else:
                    ts=datetime.fromtimestamp(tx["timestamp"])   
                    q=f"INSERT INTO trans (tx_type, currency_name, slot, source, destination, amount, mint_address,mint_name, sig,transaction_date) VALUES ('{tx["type"]}', '{tx["coin"]}', {tx["slot"]}, '{tx["source"]}', '{tx["destination"]}', '{tx["amount"]}','{tx["mint"]}','{tx["mint_name"]}','{tx["signature"]}','{ts}');"
                    cursor.execute(q)
            conn.commit()
            
            return "txs added successfully."

    finally:
        conn.close()

def update_account_balances_many(pool, balances,type="events"):
    conn = pool.get_connection()
    try:
        with conn.cursor() as cursor:
            ts = datetime.now()
            update_data = []
            insert_data = []
            scanned=1
            for index, row in balances.iterrows():
                print(f"update account: {row['account']}  new balance: {round(row['balance']/1e9,2)}")
                if row['balance'] > 0:
                    scanned=0
                ui_balance=round(row['balance']/1e9,2)
                update_data.append((row['balance'],ui_balance,scanned,ts, row['account']))
                insert_data.append((row['account'], row['balance'], ts))
                #dblog.info(f"db: send notification: {row['account']}  new balance: {row['balance']}")
                #utils.send_event(json.dumps({"type":"balance" ,"account": row["account"], "balance": row["balance"]}),type)
            # Batch update queries
            update_query = """
            UPDATE accounts SET balance = %s, balance_ui = %s,scanned = %s,last_balance_update = %s,  status="update" WHERE address = %s
            """
            cursor.executemany(update_query, update_data)

            insert_query = """
            INSERT INTO account_balance (address, balance, ts)
            VALUES (%s, %s, %s)
            """
            cursor.executemany(insert_query, insert_data)

            conn.commit()

    except mysql.connector.Error as err:
        print(f"Error: {err}")
        conn.rollback()
    finally:
        conn.close()

def add_account_follow(pool,mint,account):
    conn = pool.get_connection()
    try:
        with conn.cursor() as cursor:
            insert_query = """
                INSERT INTO accounts (master, address, balance, status, scan,scanned, distance,enabled)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            """
            cursor.execute(insert_query, (mint,account,*********,'new',1,0,0,4))
            conn.commit()
    finally:
        conn.close()

def delete_account(pool,account):
    conn = pool.get_connection()
    try:
        with conn.cursor() as cursor:
        
            query = """
            delete from accounts where address = %s; 
            """
            cursor.execute(query, (account,))

            print("committing")
            conn.commit()
            conn.close()
    finally:
        conn.close()


def delete_all_tables(pool):
    conn = pool.get_connection()
    try:
        with conn.cursor() as cursor:
            query = """
            delete from accounts where scan=1 and role !='master' ;
            """
            cursor.execute(query)

            query = """
            delete from accounts where role='mint'; 
            """
            cursor.execute(query)

            query = """
            delete from accounts where role='token';
            """
            cursor.execute(query)

            query = """
            delete from tokens; 
            """
            cursor.execute(query)

            print("committing")

            query = """
            delete from account_balance;
            """
            cursor.execute(query)

            query = """
            delete from trans; 
            """
            cursor.execute(query)

            print("committing")
            conn.commit()
    finally:
        conn.close()

def get_follow_zero_accounts(pool):
    conn = pool.get_connection()
    try:
        with conn.cursor() as cursor:
            # Check if the account exists
            query = """
                    SELECT master,address FROM accounts WHERE balance = 0 and enabled = 4;
            """
            cursor.execute(query)
            rows = cursor.fetchall()
            data=[]
            if len(rows) > 0:
                for row in rows:
                    data.append([row[0],row[1]])

            return data

    finally:
        conn.close()

def get_account_one(pool,address):

    cursor = None
    conn = None
    conn = pool.get_connection()
    try:
        with conn.cursor() as cursor:
            # Check if the account exists
            query = """
                    select * from accounts where address = %s
            """
            cursor.execute(query, (address,))
            result = cursor.fetchone() 
            return result
    finally:
        conn.close()


def get_follow_mint(pool,mint):

    cursor = None
    conn = None
    conn = pool.get_connection()
    try:
        with conn.cursor(buffered=True) as cursor:
            # Check if the account exists
            query = """
                    select * from follow_mints where mint = %s
            """
            cursor.execute(query, (mint,))
            result = cursor.fetchone() 
            return result
    finally:
        cursor.close()
        conn.close()

def add_follow_mint(pool,mint):
    cursor = None
    conn = None
    conn = pool.get_connection()
    try:
        with conn.cursor() as cursor:
            insert_query = """
                INSERT INTO follow_mints (mint)
                VALUES (%s)
            """
            cursor.execute(insert_query, (mint,))
            conn.commit()
       
    finally:
        conn.close()

def update_follow_account(pool,address):
    cursor = None
    conn = None
    conn = pool.get_connection()
    try:
        with conn.cursor() as cursor:
            insert_query = """
                update accounts set status = 'exist' where address = %s
            """
            cursor.execute(insert_query, (address,))
            conn.commit()
    finally:
        conn.close()