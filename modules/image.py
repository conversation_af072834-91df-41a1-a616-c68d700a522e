import requests
import os
import hashlib
import filetype
from PIL import Image
from io import BytesIO

def save_image(image_url):
    folder_path = "images"
    image_name = image_url.split('/')[-1]  # Get image name from URL
    
    # Create the folder if it doesn't exist
    os.makedirs(folder_path, exist_ok=True)

    # Download the image
    response = requests.get(image_url)
    if response.status_code == 200:
        # Calculate the MD5 hash of the image content
        md5_hash = hashlib.md5(response.content).hexdigest()

        # Detect file type and add the appropriate extension
        kind = filetype.guess(BytesIO(response.content))
        if kind:
            image_extension = kind.extension
            image_name = f"{image_name}.{image_extension}" if '.' not in image_name else image_name
        else:
            print("Unknown file type. Could not determine extension.")
            return None

        # Open the image for cropping and resizing
        image = Image.open(BytesIO(response.content))

        # Crop the image to a square (centered)
        width, height = image.size
        min_dimension = min(width, height)
        left = (width - min_dimension) / 2
        top = (height - min_dimension) / 2
        right = (width + min_dimension) / 2
        bottom = (height + min_dimension) / 2
        image = image.crop((left, top, right, bottom))

        # Resize the cropped image to 60x60 pixels
        image.thumbnail((60, 60))

        # Save the final thumbnail to disk
        thumbnail_path = os.path.join(folder_path, image_name)
        image.save(thumbnail_path)
        return {"path": thumbnail_path, "md5": md5_hash}
    else:
        print("Failed to download image")
        return None
