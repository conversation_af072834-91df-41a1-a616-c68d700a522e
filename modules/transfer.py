import requests,random,json,time,re
import pandas as pd
import modules.db as db 
import modules.utils as utils
from modules.config import *
import time,json
import modules.config

log=utils.get_logger()
log_neo=utils.get_logger()

#omniatechio


#exchange = pd.read_csv("exchange.csv")

def shift_account(address):
    # if account.balance == 0 
    # get last transaction/ getget account where money send
    # remove previous account 
    fetch_last_account(address)
    pass

def fetch_last_account(pool,mint,address):
    url = f"https://api.helius.xyz/v0/addresses/{address}/transactions?api-key={api_key[0]}"
    response = requests.get(url)
    data = response.json()
    for row in data:
        for tx in row['nativeTransfers']: 
            dest=tx['toUserAccount']
            if db.get_account_one(pool,dest):
                db.delete_account(pool,address)
            else:
                if dest not in SKIP_ACCOUNTS:
                    db.add_account_follow(pool,mint,dest)
                    db.delete_account(pool,address)
                else:
                    db.delete_account(pool,address)
            return
            
def send_to_discord_follow(message):
    DISCORD_WEBHOOK = "https://discord.com/api/webhooks/1332630555233353811/lkxjRvYag5LP6EQvQIpHKe6uMF8F1vhWHzO0B78FB9BqUUIM4t-ie77oe4JI21BifsYX"
    data = {
        "content": message
    }
    response = requests.post(DISCORD_WEBHOOK, json=data)
    if response.status_code == 204:
        print("Message sent to Discord successfully.")
    else:
        print(f"Failed to send message to Discord. Status code: {response.status_code}")

def get_mint_name(address):
        token_info=get_token_info(address)
        token_info=token_info["metadata"]
        return token_info["symbol"]

# run when balance update !!
def fetch_follow_trades(pool,address):
    url = f"https://api.helius.xyz/v0/addresses/{address}/transactions?api-key={api_key[0]}"
    response = requests.get(url)
    data = response.json()
    #print(json.dumps(data, indent=4))
    for row in data:
        for tx in row['tokenTransfers']: 
            if not db.get_follow_mint(pool,tx['mint']):

                
                mint_name=""
                try:
                    mint=get_mint_name(tx['mint'])
                except:
                    mint=None
                if mint:
                    mint_name=mint
                else:
                    mint_name=address

                #TODO: send to discord
                message = (
                    f"[A](https://kroocoin.xyz/analyzer?token_address={tx['mint']}&time_window=555555) mint:{mint_name} [{tx['mint']}](https://neo.bullx.io/terminal?chainId=**********&address={tx['mint']})   by address:{address}"
                )

                 
                #TODO: send rabbitmq    
                tradeData = {
                        "tokenAddress": {tx['mint']},
                        "isBuy": True,
                        "amount": 0.001,
                        "slippage": 0.80,
                        "priorityLevel": "medium"
                    }
                utils.send_event(json.dumps(tradeData),"snipes")
                db.add_follow_mint(pool,tx['mint'])
                send_to_discord_follow(message)
        db.update_follow_account(pool,address)



def add_key_value(d, key, value):
    row={'key':key,'value':value}
    d.append(row)
    return d

# Function to find a key by its value
def find_key_by_value(d, value):
    for row in d:
        if row['value'] == value:
            return row['key']
    return None
    
def fetch_1transactions(address="4NVoofLVJqExqFCLGEaw2hfNT7pDRd1Rzbas1XR8f2YY"):
    randNum = random.randint(0, len(api_key) - 1)
    url = f"https://api.helius.xyz/v0/addresses/{address}/transactions?api-key={api_key[randNum]}"
    response = requests.get(url)
    data = response.json()
    return  data   

def fetch_jupiter_price(token_name= "SOL"):
    url = "https://price.jup.ag/v4/price"
    params = {"ids": token_name}
    price = 0

    response = requests.get(url, params=params)

    if response.status_code == 200:
        data = response.json()
        try:
            price = data["data"][token_name]["price"]
            
        except:
            #print(f"{token_name} Failed to fetch price:", data)
            pass
            
    else:
        #print(f"{token_name} Failed to fetch price:", response.status_code)
        pass
    
    return price

def get_multiple_accounts2(accounts):
    try:
        print(len(accounts))
        randNum = random.randint(0, len(api_key) - 1)
        url = "https://endpoints.omniatech.io/v1/sol/mainnet/b6efe195db51499ca3719ad3eb838aa6"
        #url = f"https://mainnet.helius-rpc.com/?api-key={api_key[randNum]}"
        headers = {'Content-Type': 'application/json'}
        data = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "getMultipleAccounts",
            "params": [accounts]
            
        }
        response = requests.post(url, headers=headers, json=data)
        print(response.json())
        sys.exit()
        balances=[]
        data=response.json()['result']['value']
        for row in data:
            if row == None:
                balances.append(0)
            else:
                balances.append(row['lamports'])
        return balances
    except Exception as e:
        print(e)
        return 0
    
def get_multiple_accounts(accounts):
    if True:
        #print(f"get_multiple_accounts: {len(accounts)}" )
        randNum = random.randint(0, len(api_key) - 1)
        #print({api_key[randNum]})
        url = f"https://mainnet.helius-rpc.com/?api-key={api_key[randNum]}"
        headers = {'Content-Type': 'application/json'}
        data = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "getMultipleAccounts",
            "params": [accounts]
            
        }




        response = requests.post(url, headers=headers, json=data)
        if response.status_code != 200:
            print(f"Got status code {response.status_code}, returning empty list.")
            return []
        
        try:
            resp_dict = response.json()
        except json.JSONDecodeError:
            print("Server did not return valid JSON, returning empty list.")
            return []
        if "result" not in resp_dict:
            print("No 'result' in response, returning empty list.")
            return []

        if "value" not in resp_dict["result"]:
            print("No 'value' in 'result', returning empty list.")
            return []
    
        balances=[]
        if "result" in response.json():
            data=response.json()['result']['value']
        else:
            print("No result")
            print(response.json())
            return []
        for row in data:
            if row == None:
                balances.append(0)
            else:
                balances.append(row['lamports'])
        return balances
    #except Exception as e:
    #    print(e)
    #    return 0
    
def getBalance(address):
    try:
        randNum = random.randint(0, len(api_key) - 1)
        url = f"https://mainnet.helius-rpc.com/?api-key={api_key[randNum]}"
        headers = {'Content-Type': 'application/json'}
        data = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "getBalance",
            "params": [
                address ]

            
        }
        response = requests.post(url, headers=headers, json=data)
        
        return response.json()['result']['value'] / 10e8
    
    except:
        print(response.json())
        return 0
    
def get_token_info(asset_id):

    randNum = random.randint(0, len(api_key) - 1)
    url = f"https://mainnet.helius-rpc.com/?api-key={api_key[randNum]}"
    headers = {"Content-Type": "application/json"}
    payload = {
        "jsonrpc": "2.0",
        "id": "M4tCEjM9B7k8kNMVBtAfktmRJShMjVrdPWZDZboXfQG",
        "method": "getAsset",
        "params": {
            "id": asset_id
        }
    }


    response = requests.post(url, headers=headers, data=json.dumps(payload))
    response_data = response.json()



    if "result" in response_data and "content" in response_data['result'] and "metadata" in response_data['result']['content']:

        return response_data['result']['content']
    else:
        raise ValueError("Token info not found in the response")


def scan_wallet(address,last_sig=None):
    print("process:",address)
    rpc_url ="https://endpoints.omniatech.io/v1/sol/mainnet/bf8f530c3d624f1889e1d0c720901fb1"
 
    if last_sig:
        request = {
            "jsonrpc": "2.0",
            "id": "1",
            "method": "getSignaturesForAddress",
            "params": [address, {"limit":500,"before":last_sig,"commitment":"confirmed"}]
        }
    else:   
        request = {
                "jsonrpc": "2.0",
                "id": "1",
                "method": "getSignaturesForAddress",
                "params": [address, {"limit":500,"commitment":"confirmed"}]
            }
    
    randNum = random.randint(0, len(omnia_keys) - 1)
    url = f"https://endpoints.omniatech.io/v1/sol/mainnet/{omnia_keys[randNum]}"
    url="https://light-skilled-violet.solana-mainnet.quiknode.pro/fe06024365496464f231a10a777b0a61c3b65f58"
    #randNum = random.randint(0, len(api_key) - 1)
    #url=f"https://mainnet.helius-rpc.com/?api-key={api_key[randNum]}"
    #url="https://go.getblock.io/c8d421e01d66409f88bdcde68057de0d"

    request = requests.post(url, json=request)

    if "result" in request.json():
        data = request.json()["result"]
    else:
        data = []

    if len(data) == 0:
        print("no sigs")
        return [],last_sig
    print("data len:",len(data))
    good_sigs = []
    for sig in data:
        if sig["err"] == None:
            good_sigs.append(sig['signature']) 
            unix_timestamp = int(time.time())
            time_diff = int(unix_timestamp)- int(sig["blockTime"]) 
    if len(good_sigs) == 0:
        print("No good sigs")
        return
    print("good sigs:",len(good_sigs))
    if good_sigs:
        batch_transactions = []
        counter=1
        for sig in good_sigs:
            batch_transactions.append({"jsonrpc": "2.0", "id": counter, "method": "getTransaction", "params": [sig, {"maxSupportedTransactionVersion":0,"encoding": "jsonParsed","commitment":"confirmed"}]})
            counter+=1
        batch_request_json = json.dumps(batch_transactions)


        randNum = random.randint(0, len(omnia_keys) - 1)
        rpc_url = f"https://endpoints.omniatech.io/v1/sol/mainnet/{omnia_keys[randNum]}"

        response = requests.post(url, headers={"Content-Type": "application/json"}, data=batch_request_json)
        
        batch_data=json.loads(response.text)
        print("transactions:",len(batch_data))
        acc_updates=[]
        import os
        os.mkdir(f"tmp/{address}")

        for row in batch_data:
            with open(f"tmp/{address}/{row["result"]["transaction"]["signatures"][0]}.json", "a") as file:
                file.write(json.dumps(row["result"]))
            if "result" in row:
                slot=row["result"]["slot"]
                blockTime=row["result"]["blockTime"]
                if "transaction" in row["result"]:                    
                    sig=row["result"]["transaction"]["signatures"][0]
                    owner = row["result"]["transaction"]["message"]["accountKeys"][0]
                    for instruction in row["result"]["transaction"]["message"]["instructions"]:
                        if "parsed" in instruction:
                            if "type" in instruction["parsed"]:
                                if instruction["parsed"]["type"] == "transfer" and "info" in instruction["parsed"] and "source" in instruction["parsed"]["info"] and "destination" in instruction["parsed"]["info"] and "lamports" in instruction["parsed"]["info"]:                                            
                                            source = instruction["parsed"]["info"]["source"]
                                            destination = instruction["parsed"]["info"]["destination"]
                                            amount = instruction["parsed"]["info"]["lamports"]
                                            if source == address and amount > ***********:
                                                acc_updates.append({"destination":destination,"amount":amount,"sig":sig,"slot":slot,"blockTime":blockTime,"source":owner})
        print("acc_updates:",len(acc_updates))
        return acc_updates,data[0]["signature"]
        


def follow_wallet(address):
    print("process:",address)
    rpc_url ="https://endpoints.omniatech.io/v1/sol/mainnet/bf8f530c3d624f1889e1d0c720901fb1"
    request = {
            "jsonrpc": "2.0",
            "id": "1",
            "method": "getSignaturesForAddress",
            "params": [address, {"limit":5,"commitment":"confirmed"}]
        }
    
    randNum = random.randint(0, len(omnia_keys) - 1)
    url = f"https://endpoints.omniatech.io/v1/sol/mainnet/{omnia_keys[randNum]}"
    url="https://light-skilled-violet.solana-mainnet.quiknode.pro/fe06024365496464f231a10a777b0a61c3b65f58"



    request = requests.post(url, json=request)
    if "result" in request.json():
        data = request.json()["result"]
    else:
        data = []

    if len(data) == 0:
        #print("No more transactions:last signature",lsig)
        return

    good_sigs = []
    for sig in data:
        if sig["err"] == None:
            good_sigs.append(sig['signature']) 
            unix_timestamp = int(time.time())
            time_diff = int(unix_timestamp)- int(sig["blockTime"]) 
    if len(good_sigs) == 0:
        print("No good sigs")
        return
    if good_sigs:
        batch_transactions = []
        counter=1
        for sig in good_sigs:
            batch_transactions.append({"jsonrpc": "2.0", "id": counter, "method": "getTransaction", "params": [sig, {"maxSupportedTransactionVersion":0,"encoding": "jsonParsed","commitment":"confirmed"}]})
            counter+=1
        batch_request_json = json.dumps(batch_transactions)


        randNum = random.randint(0, len(omnia_keys) - 1)
        rpc_url = f"https://endpoints.omniatech.io/v1/sol/mainnet/{omnia_keys[randNum]}"

        response = requests.post(url, headers={"Content-Type": "application/json"}, data=batch_request_json)

        batch_data=json.loads(response.text)

        for row in batch_data:
            if "result" in row:
                if "transaction" in row["result"]:
                    owner = row["result"]["transaction"]["message"]["accountKeys"][0]
                    for instruction in row["result"]["transaction"]["message"]["instructions"]:
                        if "parsed" in instruction:
                            if "type" in instruction["parsed"]:
                                if instruction["parsed"]["type"] == "transfer":                                            
                                            source = instruction["parsed"]["info"]["source"]
                                            destination = instruction["parsed"]["info"]["destination"]
                                            amount = instruction["parsed"]["info"]["lamports"]
                                            #print(f"source:{source} dest:{destination} amount:{amount}")
                                            if source == address and amount > ************:
                                                follow_wallet(destination)
       
    #             "instructions": [
    #                 {
    #                     "parsed": {
    #                         "info": {
    #                             "destination": "********************************************",
    #                             "lamports": 613362952116,
    #                             "source": "uE7fF2C2mQb8CgnWjdZHkapvwQr8JmMkyXhFDhoFrhc"
    #                         },
    #                         "type": "transfer"

                

       


def fetch_all_transactions_to_sig(address,sig=None):
    print("fetch_all_transactions_to_sig: start")
    last_signature = None 
    transactions = []
    start_signature = None
  
    log.info(f"fetch_all_transactions_to_sig2: {address}")
    while True:
        randNum = random.randint(0, len(api_key) - 1)
        url = f"https://api.helius.xyz/v0/addresses/{address}/transactions?api-key={api_key[randNum]}"

        if last_signature:
            url_with_signature = f"{url}&before={last_signature}"
        else:
            url_with_signature = url
        print("start fetch")
        response = requests.get(url_with_signature)
        print("end fetch")
        #log.info(response)

        if response.status_code != 200:
            print("Error code：", response.status_code)
            time.sleep(3)
            continue

      
        data = response.json()
        log.info(f"data len: {len(data)}")

        if "error" in data:
            if "exceeded limit for api" in data:
                print(data)
                time.sleep(3)
                continue   
            else:
                if len(transactions) > 0:
                    return  transactions,transactions[0]["signature"] 
        # set first signature 
        if data and len(data) > 0:
            if not start_signature:
                print("start sig:",data[0]["signature"])
                start_signature = data[0]["signature"]

        for row in data:
            # signature reached
            if row['signature']==sig:
                # if we have some tx collected send them plus first signature in list 
                return transactions,start_signature
            #less then two days
            if row['timestamp'] < time.time()-tx_max_age:
                return transactions,start_signature
            else:
                if row['feePayer']!=address:
                    continue
                else:
                    transactions.append(row)

        
        
        if len(data) > 0:
            last_signature = data[-1]["signature"]

        elif len(transactions)>max_sig:
            return transactions,start_signature
        
        else:
            print("End of check")
            print(f"Transaction count:{len(transactions)}")
            break
        
        if len(transactions)>max_sig:
            return transactions,start_signature
        print(f"Processed {len(transactions)}")

    if len(transactions)> 0:       
        return  transactions,start_signature
    else:
        return [],"None"



def fetch_all_transactions(address,max_tx=100):
    last_signature = None 
    transactions = []
 
    while True:
        randNum = random.randint(0, len(api_key) - 1)
        url = f"https://api.helius.xyz/v0/addresses/{address}/transactions?api-key={api_key[randNum]}"

        if last_signature:
            url_with_signature = f"{url}&before={last_signature}"
        else:
            url_with_signature = url
        response = requests.get(url_with_signature)


        if response.status_code != 200:
            print("Error code：", response.status_code)
            time.sleep(1)
            continue

      
        data = response.json()
        
        if "error" in data:
            if "exceeded limit for api" in data:
                print(data)
                time.sleep(3)
                continue   
            else:
                return  transactions  
        
        transactions = transactions + data
        
        if data and len(data) > 0:
            print("Transaction count：", len(transactions))
            last_signature = data[-1]["signature"]
            print(f"transaction count:{len(transactions)}")
          
        elif len(transactions)>max_tx:
            break
          
        else:
            print("End of check")
            print(f"transaction count:{len(transactions)}")
            break
        
        if len(transactions)>max_tx:
          break
          
    return  transactions  

def parse_transactions(transactions, address, threshold, jup_check=True):
    df = pd.DataFrame(transactions)
    df.to_json('data1.json', orient='records')
    import sys
    sys.exit()
    df["UTC"] = pd.to_datetime(df["timestamp"], unit="s", utc=True)
    df = df.query("type=='TRANSFER'& not description.str.contains('multiple') & not description.str.contains('0 SOL')")

    def parse_description(description):
        matches = re.findall(r'(\w+) transferred ([\d.]+) (\w+) to (\w+)\.', description)
        if matches:
            sender_address, amount, token_name, receiver_address = matches[0]
            return sender_address, amount, token_name, receiver_address
        else:
            return None, None, None, None
        
    df["sender"], df["amount"], df["token_name"], df["receiver"] = zip(*df["description"].apply(parse_description))
    df = df[["sender","amount","token_name","receiver","UTC","signature"]]
    


    if jup_check == True:
        df["amount"] = df["amount"].astype(float).round(2)
        df = df.query("token_name.str.len() < 10") # 排除 token_name 是地址的

        # 取得 token price by Jupiter Api
        token_list = df["token_name"].unique()
        token_price = {}
        for token in token_list:
            price = fetch_jupiter_price(token)
            token_price[token] = price
            
        df["USD"] = df.apply(lambda row: row["amount"] * token_price.get(row["token_name"], 0), axis=1)
        df["USD"] = df["USD"].astype(float).round(0)
        df = df.query(f"USD > {threshold}")

        sendTX = df.query(f"sender=='{address}'")
        receiveTX = df.query(f"receiver=='{address}'")

        sendTX_group = sendTX.groupby(["receiver", "token_name"]).agg(total_amount=("amount", "sum"), usd=("USD", "sum"), tx_count=("amount", "count")).sort_values("usd",ascending=False)
        receiveTX_group = receiveTX.groupby(["sender", "token_name"]).agg(total_amount=("amount", "sum"), usd=("USD", "sum"), tx_count=("amount", "count")).sort_values("usd",ascending=False)
    
    elif jup_check == False:
        sendTX = df.query(f"sender=='{address}'")
        receiveTX = df.query(f"receiver=='{address}'")

        sendTX_group = sendTX.groupby(["receiver"]).count()
        receiveTX_group = receiveTX.groupby(["sender"]).count()
    
    return sendTX_group, receiveTX_group

def recent_tx_count(address="AYhux5gJzCoeoc1PoJ1VxwPDe22RwcvpHviLDD1oCGvW"): 
    try:
        data = fetch_1transactions(address)
        current_timestamp = time.time()
        one_hour_ago_timestamp = current_timestamp - 7200 # 最近2hr 
        recent_timestamps = [transaction['timestamp'] for transaction in data if transaction['timestamp'] >= one_hour_ago_timestamp]
        return data, len(recent_timestamps)
    except:
        print("error recent_tx_count: "+address)
        return data, 0
    

def exchange_deposit_address_check(ddd, address):
    sss, rrr = parse_transactions(ddd, address, 10, jup_check=False)
    idx = sss.index.get_level_values("receiver").isin(exchange["address"])
    contains_exchange = sss.index.get_level_values("receiver")[idx]
    if contains_exchange.any():
        exchange_name = exchange.loc[exchange['address'] == contains_exchange[0], 'exchange'].values[0]
    else:
        exchange_name = False

    return exchange_name


def find_associated_wallet(sendTX_group, receiveTX_group):
    send_usd = sendTX_group.groupby("receiver").agg(send=("usd", "sum"), sendTX=("tx_count", "sum")).sort_values(["send"],ascending=False)
    receive_usd = receiveTX_group.groupby("sender").agg(receive=("usd", "sum"), receiveTX=("tx_count", "sum")).sort_values(["receive"],ascending=False)
    total_interact = pd.concat([send_usd, receive_usd], axis=1)
    total_interact.columns = ["sendUSD", "sendTX","receiveUSD","receiveTX"]
    total_interact = total_interact.fillna(0)
    total_interact.insert(0, "totalUSD", total_interact["sendUSD"] + total_interact["receiveUSD"])
    total_interact.insert(1, "totalTX", total_interact["sendTX"] + total_interact["receiveTX"])
    total_interact = total_interact.sort_values("totalUSD", ascending=False)


    # 判斷是否為 cex 發錢，標記為 cex
    address_list = total_interact.index
    total_interact.insert(0, "mark", "")
    total_interact["lastTx"] = ""

    for i in range(len(exchange)):
        try:
            eidx = address_list.get_loc(exchange["address"].loc[i])
            total_interact["mark"].iloc[eidx] = exchange["exchange"].loc[i]
        except:
            pass

    # 查每個地址的 tx, sol balance
    total_interact["SOL bal."] = 0
    count = 0
    for aaa in total_interact.index:
        if  total_interact["mark"].loc[aaa] == "":  # 尚未標記成 cex 則繼續
            print(aaa)
            total_interact.at[aaa, "SOL bal."] = getBalance(aaa)
            ddd, txs = recent_tx_count(aaa)

            # 2hr Txs > 50, 可能是某合約地址/cex/bot
            if txs > 50: 
                total_interact["mark"].loc[aaa] = "🤖"

            # 判斷是否為打錢去 cex
            if total_interact["receiveTX"].loc[aaa] == 0 and total_interact["sendTX"].loc[aaa] > 1:
                exchangeTF = exchange_deposit_address_check(ddd, aaa)
                print(exchangeTF)
                if exchangeTF != False:
                    total_interact["mark"].loc[aaa] = "its "+exchangeTF 

            # 計算最後交易日
            try:
                lastTx = pd.to_datetime(ddd[0]["timestamp"], unit='s').strftime('%Y-%m-%d')        
                total_interact["lastTx"].loc[aaa] = lastTx
            except:
                total_interact["lastTx"].loc[aaa] = ""
              
            count += 1
            if count > 7:
                pass
                #break

    total_interact["SOL bal."] = total_interact["SOL bal."].round(4)
    
    # 高機率是小號
    total_interact.loc[(total_interact['sendTX'] >= 3) & (total_interact['receiveTX'] >= 3), 'mark'] = "🔗"

    return total_interact

def process_transactions_follow(pool,txs,address,last_sig):
    import datetime
    #{"destination":destination,"amount":amount,"sig":sig,"slot":slot,"blockTime":blockTime,"source":owner}
    for tx in txs:
        destination=tx["destination"]
        amount=tx["amount"]
        sig=tx["sig"]
        slot=tx["slot"]
        owner=tx["source"]
        blockTime=tx["blockTime"]
        log.info(f"Process acc: {destination}")
        db.add_account(pool, destination, 0, "regular", "system", datetime.datetime.fromtimestamp(blockTime), datetime.datetime.fromtimestamp(blockTime), datetime.datetime.fromtimestamp(blockTime), None,"new",role="follow",scan=1,distance=None)
    
    pass

def process_transactions(pool,neo_client,transactions,tokens,address,accounts,last_sig=None):
    #address=json.dumps(address)
    master_address=modules.config.master_address
    df = pd.DataFrame(transactions)
    #df = pd.read_json('data.json', orient='records' )
    df = df.sort_values(by='timestamp')
    df["UTC"] = pd.to_datetime(df["timestamp"], unit="s", utc=True)
    mints = []
    
  #======= Native block processing ======  
    for index, row in df.iterrows():
        fee_payer=row['feePayer']
        #log.info(f"Process tx: {row['signature']}")
        
        if row['nativeTransfers']:   
            #log_neo.info("nativeTransfer")
            #log_neo.info(row['nativeTransfers'])
            if row['type'] == "UNKNOWN":
                row_type="TRANSFER"
                #log.info("UNKNOWN transactioin set to TRANSFER")
            else:
                row_type=row['type']
            for tx in row['nativeTransfers']:

                # if fee_payer == tx['fromUserAccount']: 
                #     dest=tx['toUserAccount']
                # else:
                #     dest=tx['fromUserAccount']    
                dest=tx['toUserAccount']
                # skip skip_accounts
                if dest in SKIP_ACCOUNTS:
                    continue
                    
                native_transfer_record = {
                    'type': row_type,
                    'coin': 'sol',
                    'slot': row['slot'],
                    'timestamp': row['timestamp'],
                    'source': tx['fromUserAccount'],
                    'destination': tx['toUserAccount'],
                    'amount': tx['amount'],
                    #'new_address':dest,
                    'new_address':tx['toUserAccount'],
                    'mint': '',
                    'mint_name': '',
                    'mint_assoc_address': '',
                    'UTC': row['UTC'],
                    'signature': row['signature'],
                    'src_type':"regular",
                    'dest_type':"regular"
                }
                transfers_data = []
                transfers_data.append(native_transfer_record)
                df_single = pd.DataFrame(transfers_data)
                df_single['amount']=df_single['amount'].astype(float)
                if address != master_address:
                    distance=neo_client.count_hops(master_address,address)
                else:
                    distance=0


                
                df_single['distance']=distance

                # if distance == 1 and dest != "Fy5MCbuZo4AUxpPmA6k3v2vzWKYHNhNaBbUChK1HYzvS":
                #     continue 
                # else:
                #     print("Skipping:",dest)

                # if distance == 2:
                #     if tx['amount'] == mint_amount or tx['amount'] == mint_amount2:
                #         df_single['mint_path']=1
                #     else:
                #         continue
                # if distance > 2 :
                #     df_single['mint_path']=1
                # if distance < 2:
                #     df_single['mint_path']=0
                df_single['mint_path']=0


                if float(tx['amount']) > minsol:
                #if True:
                    #add account 
                    

                    if db.get_account_name(pool,dest):
                        #TODO: ad graph link to existing node !!!
                        relationship = neo_client.add_unique_relationship("Address", {"name": tx['fromUserAccount']}, "Address", {"name": tx['toUserAccount']}, "TRANSFERRED", {"amount": 0,"amount_send": 0, "amount_received": 0})
                        continue
                        

                    log.info(f"Add Native account to database:{dest} ")
                    db.extract_address_from_tx(neo_client,pool,accounts,df_single,row['signature'],address)

                    #log.info("Add transaction to database")
                    db.add_single_tx_to_db(pool,df_single,minsol)
                     #log.info(f"update address signature: {address}")
                    #db.update_account_last_sig(pool,address,row["signature"])
                    #db.update_account_last_sig(pool,address,last_sig)
                    # update distance 
                    



                    src_type="regular"
                    dest_type="regular"
                    # if fee_payer != address:


                    #     address0 = neo_client.add_unique_node("Address", {"name": address, "balance": 0, "total_tx": 20, "role": "user", "group": "B", "age": 30, "type": "wallet"})    
                    #     address1 = neo_client.add_unique_node("Address", {"name": fee_payer, "balance": 0, "total_tx": 20, "role": src_type, "group": "B", "age": 30, "type": "wallet"})
                    #     address2 = neo_client.add_unique_node("Address", {"name": dest, "balance": 0, "total_tx": 20, "role": dest_type, "group": "B", "age": 30, "type": "wallet"})
                    #     relationship = neo_client.add_unique_relationship("Address", {"name": address}, "Address", {"name": fee_payer}, "TRANSFERRED", {"amount": 0,"amount_send": 0, "amount_received": 0})
                    #     relationship = neo_client.add_unique_relationship("Address", {"name": fee_payer}, "Address", {"name": dest}, "TRANSFERRED", {"amount": 0,"amount_send": 0, "amount_received": 0})
 
                    # else:
        
                    #     address1 = neo_client.add_unique_node("Address", {"name": address, "balance": 0, "total_tx": 20, "role": src_type, "group": "B", "age": 30, "type": "wallet"})
                    #     address2 = neo_client.add_unique_node("Address", {"name": dest, "balance": 0, "total_tx": 20, "role": dest_type, "group": "B", "age": 30, "type": "wallet"})
                    #     relationship = neo_client.add_unique_relationship("Address", {"name": address}, "Address", {"name": dest}, "TRANSFERRED", {"amount": 0,"amount_send": 0, "amount_received": 0})

                    address1 = neo_client.add_unique_node("Address", {"name": tx['fromUserAccount'], "balance": 0, "total_tx": 20, "role": src_type, "group": "B", "age": 30, "type": "wallet"})
                    address2 = neo_client.add_unique_node("Address", {"name": tx['toUserAccount'], "balance": 0, "total_tx": 20, "role": dest_type, "group": "B", "age": 30, "type": "wallet"})
                    relationship = neo_client.add_unique_relationship("Address", {"name": tx['fromUserAccount']}, "Address", {"name": tx['toUserAccount']}, "TRANSFERRED", {"amount": 0,"amount_send": 0, "amount_received": 0})
                # else:
                #    #log.info(" update last sig")
                #    #db.update_account_last_sig(pool,address,row['signature'])
                #    db.update_account_last_sig(pool,address,last_sig)
                #    #log.info(f"Skip native due less then minsol: {minsol} account:{dest}")
#========= TOKEN block processing 

                #add to addresses and tx an graph HERE
        if row['tokenTransfers']:
            #log_neo.info("tokenTransfer")
            #log_neo.info(row['tokenTransfers'])
            if row['type'] == "UNKNOWN":
                row_type="TRANSFER"
            else:
                row_type=row['type']
            minted=False
            for m in row["accountData"]:
                if m["account"] == METAPLEX_ACCOUNTS:
                    minted=True
                    break
            if not minted:
                continue

            # Get real mint it is mentioned in one of transaction bloks
            real_mint=""
            for tx in row['tokenTransfers']:
                if tx['mint'] != "So11111111111111111111111111111111111111112":
                    real_mint=tx['mint']
                    break
            if real_mint == "":
                 #log_neo.error("MINT address NOT found!!")
                 continue
            # continue

            #log_neo.info(f"Real mint: {real_mint}")

            for tx in row['tokenTransfers']:
                src=""
                dst=""
                #log.info(row['tokenTransfers'])
                if fee_payer == tx["fromUserAccount"] and tx["mint"] != "So11111111111111111111111111111111111111112":
                    #log_neo.info("Selling Tokens: sending tokens")
                    src=fee_payer 
                    dest=real_mint
                    tx_type="token"
                    src_type="regular"
                    dest_type="mint"
                    #token transaction 

                elif  fee_payer == tx["toUserAccount"] and tx["mint"] == "So11111111111111111111111111111111111111112":
                    #log_neo.info("selling tokens : getting sols")
                    src=real_mint
                    dest=fee_payer
                    tx_type="sol"
                    src_type="mint"
                    dest_type="regular"
                    #sol transaction

                elif fee_payer == tx["toUserAccount"] and tx["mint"] != "So11111111111111111111111111111111111111112":
                    #log_neo.info("Bying Tokens: reci g tokens")
                    src=real_mint
                    dest=fee_payer
                    tx_type="token"
                    src_type="mint"
                    dest_type="regular"
                    #token transaction 
                elif fee_payer == tx["fromUserAccount"] and tx['mint'] == "So11111111111111111111111111111111111111112" :
                    #log_neo.info("Bying Tokens: sending sols")
                    src=fee_payer
                    dest=real_mint
                    tx_type="sol"
                    src_type="regular"
                    dest_type="mint"
                if src == "" or dest == "":
                    #log_neo.error("Source or destination not found")
                    continue

                try:
                    tokens,mint_name=db.add_token_to_tokens_list(pool,tokens,tx['mint'])    
                except:
                    continue
                if tx['mint'] == "So11111111111111111111111111111111111111112":
                    amount= float(tx['tokenAmount'])***********
                    mint_name="WSOL"
                else:
                    amount=tx['tokenAmount']
                
                message=f"{mint_name} [{real_mint}](https://neo.bullx.io/terminal?chainId=**********&address={real_mint})"
                utils.discord_webhook_signals(modules.config.discord_webhook_scanner, message)
                utils.send_pushover_message(f"{mint_name}")


                token_transfer_record = {
                    'type': row_type,
                    'coin': 'token',
                    'slot': row['slot'],
                    'timestamp': row['timestamp'],
                    'source': src,
                    'destination': dest,
                    'amount': amount,
                    'new_address': fee_payer,
                    'mint': tx['mint'],
                    'mint_assoc_address':"",
                    'mint_name': mint_name,
                    'UTC': row['UTC'],
                    'signature': row['signature'],
                    'src_type':src_type,
                    'dest_type':dest_type
                }
                #log.info(token_transfer_record)
                transfers_data = []
                transfers_data.append(token_transfer_record)
                df_single = pd.DataFrame(transfers_data)
                df_single['amount']=df_single['amount'].astype(float)
                if float(amount) > minsol:
                    log.info(f"Add Native account to database(fee_payer):{fee_payer} ")
                    # Processed source and destinatjioin of transaction add to database if any is unseen
                    
                    if address != master_address:
                        distance=neo_client.count_hops(master_address,address)
                    else:
                        distance=0
                    df_single['distance']=distance
                    db.extract_address_from_tx(neo_client,pool,accounts,df_single,row['signature'],address)

                    #log.info(f"Add transaction to db: {fee_payer}")
                    db.add_single_tx_to_db(pool,df_single,minsol)
                    #log.info(f"update address signature: {address}")
                    #db.update_account_last_sig(pool,address,row["signature"])
                    #db.update_account_last_sig(pool,address,last_sig)
                    log_neo.info(f"add token graph nodes: {address}   {dest}")
                  
                  
                    if fee_payer != address:
                        #log_neo.info(row['signature'])
                        #log_neo.info(f"======TOKEN:3 add native graph nodes======")
                        #log_neo.info(f"TOKEN:3.1 node: {address}")
                        #log_neo.info(f"TOKEN:3.2 node: {src}")
                        #log_neo.info(f"TOKEN:3.3 node: {dest}")

                        address0 = neo_client.add_unique_node("Address", {"name": address, "balance": 0, "total_tx": 20, "role": "user", "group": "B", "age": 30, "type": "wallet"})    
                          
                        address1 = neo_client.add_unique_node("Address", {"name": src, "balance": 0, "total_tx": 20, "role": src_type, "group": "B", "age": 30, "type": "wallet"})
                        address2 = neo_client.add_unique_node("Address", {"name": dest, "balance": 0, "total_tx": 20, "role": dest_type, "group": "B", "age": 30, "type": "wallet"})

                        address1 = neo_client.add_unique_node("Address", {"name": src, "balance": 0, "total_tx": 20, "role": src_type, "group": "B", "age": 30, "type": "wallet"})
                        address2 = neo_client.add_unique_node("Address", {"name": dest, "balance": 0, "total_tx": 20, "role": dest_type, "group": "B", "age": 30, "type": "wallet"})

                        if src_type == "mint": 
                            #log_neo.info(f"TOKEN:3.1 edgelabel addr-fee: {address}-{src}")
                            relationship = neo_client.add_unique_relationship("Address", {"name": address}, "Address", {"name": dest}, "TRANSFERRED", {"amount": 0,"amount_send": 0, "amount_received": 0})
                            #log_neo.info(f"TOKEN:3.2 edgelabel: fee-dest: {src}-{dest}")
                            relationship = neo_client.add_unique_relationship("Address", {"name": src}, "Address", {"name": dest}, "TRANSFERRED", {"amount": 0,"amount_send": 0, "amount_received": 0})
                        else:
                            #log_neo.info(f"TOKEN:3.1 edgelabel addr-fee: {address}-{src}")
                            relationship = neo_client.add_unique_relationship("Address", {"name": address}, "Address", {"name": src}, "TRANSFERRED", {"amount": 0,"amount_send": 0, "amount_received": 0})
                            #log_neo.info(f"TOKEN:3.2 edgelabel: fee-dest: {src}-{dest}")
                            relationship = neo_client.add_unique_relationship("Address", {"name": src}, "Address", {"name": dest}, "TRANSFERRED", {"amount": 0,"amount_send": 0, "amount_received": 0})
                     

                    else:
                        #log_neo.info(f"TOKEN:2.1 node: main:{address}")
                        #log_neo.info(f"TOKEN:2.1 node: fee: {src}")
                        #log_neo.info(f"TOKEN:2.3 node: dest: {dest}")                  
                        address1 = neo_client.add_unique_node("Address", {"name": address, "balance": 0, "total_tx": 20, "role": src_type, "group": "B", "age": 30, "type": "wallet"})
                        address2 = neo_client.add_unique_node("Address", {"name": dest, "balance": 0, "total_tx": 20, "role": dest_type, "group": "B", "age": 30, "type": "wallet"})
                        #log_neo.info(f"TOKEN:2.1 edgelabel: addr-dest: {address}-{dest}")
                        relationship = neo_client.add_unique_relationship("Address", {"name": address}, "Address", {"name": dest}, "TRANSFERRED", {"amount": 0,"amount_send": 0, "amount_received": 0})
                        
                #else:
                #    log.info(f"Skip token due less then minsol: {minsol} account:{dest}")     


                #add to addresses and tx an graph HERE
    db.update_account_last_sig(pool,address,last_sig)
    return
    df1 = pd.DataFrame(transfers_data)
    df1['amount']=df1['amount'].astype(float)



    return df1,tokens

    
def get_account_balances(accounts,current_balances,min_diff=*********):
    balances=get_multiple_accounts(accounts)

    data=zip(accounts,balances)
    if len(accounts) != len(balances) and len(balances) != len(current_balances):
        print("The length of accounts and balances lists must be the same.")
        #raise ValueError("The length of accounts and balances lists must be the same.")
    data = {
        'account': accounts,
        'current_balances': current_balances,
        'balance': balances
    }
    df = pd.DataFrame(data)
    df['current_balances']=df['current_balances']
    df['balance']=df['balance'].apply(lambda x: x)
    df=df.query(f"abs(current_balances - balance) > {min_diff}")
    return df

def get_token_address(token):
    try:

        import sys
        from solana.rpc.api import Client
        from solana.rpc.types import TokenAccountOpts
        from solders.pubkey import Pubkey
        solana_client = Client("https://go.getblock.io/c8d421e01d66409f88bdcde68057de0d")
        opts = TokenAccountOpts(program_id=Pubkey.from_string('TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA'))

        print(solana_client.get_token_accounts_by_owner(Pubkey.from_string("8bFh1ogHE1gtcKD549NQEvRhmw3DHPFfg1xjyvE3pump"), TokenAccountOpts(program_id=Pubkey.from_string('TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA'))))
        sys.exit()


        randNum = random.randint(0, len(api_key) - 1)
        url = f"https://mainnet.helius-rpc.com/?api-key={api_key[randNum]}"
        url ="https://go.getblock.io/c8d421e01d66409f88bdcde68057de0d"
        #url ="https://endpoints.omniatech.io/v1/sol/mainnet/f9253d2f9bb449b0b5563ef577582afd"
        url = "https://solana-mainnet.api.syndica.io/api-key/2ae4a9kSMefy6NqAwvDRM28uH2U8tFAYhJHNfZnWo3BAHgr86neXanNXK5GV8GM7FSPQkaRCpSECFANx3tidTM26xGrwUkX2nvD"
        url = "https://little-sleek-mansion.solana-mainnet.quiknode.pro/4b69dc2b49ac07ae787d9c65b8a33ccee7a904d2"
        headers = {'Content-Type': 'application/json'}
        data = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "getAccountInfo",
            "params": [token]
            
        }
        response = requests.post(url, headers=headers, json=data)
        owner=response.json()['result']['value']['owner']
        print(owner)
        data = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "getTokenAccountsByOwner",
            "params": [owner,{"mint":token},{"encoding": "jsonParsed"}]
            
        }
        result=response = requests.post(url, headers=headers, json=data)
        print(response.json())
    except Exception as e:
        print(e)
        return 0