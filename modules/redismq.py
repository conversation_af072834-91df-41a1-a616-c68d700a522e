import redis
import random
import json
import logging
import requests

# Configure logging
logging.basicConfig(level=logging.INFO)

def push_trade(redis_connection, data, chart_id, mode="lpush"):
    try:
        
        load = {
            'trade_id': random.randint(1, 1000000),
            'price': data['price'],
            'high': data['high'],
            'low': data['low'],
            'open': data['open'],
            'close': data['close'],
            'color': data['color'],
            'trade': data['trade'],
            'volume': data['solAmount'] ,
            'timestamp': data['timestamp'],
            'milliseconds': data['milliseconds'],
            'address': data['address'],
            'signature': data['signature']
        }

        data_json = json.dumps(load)
        
        # Publish data to the Redis channel
        
        
        # Push data to the Redis list
        if mode == "lpush":
            #logging.info("Using LPUSH")
            redis_connection.publish(chart_id, data_json) 
            redis_connection.lpush(f'trades:{chart_id}', data_json)
            
        else:
            redis_connection.publish(chart_id, data_json) 
            #logging.info(f"Using RPUSH :{chart_id}")
            #redis_connection.rpush(f'trades:{chart_id}', data_json)
            redis_connection.rpush(f'trades:{chart_id}', data_json)
            #requests.post(f'http://pump1:6790/rpush/{chart_id}', data = {'data': data_json})
        
        #logging.info(f"Data pushed: {data_json}")
        
    except Exception as e:
        logging.error(f"Error pushing data: {e}")


def get_redis_latest_sig(redis_connection, chart_id,modes="rpush"):
    try:
        # Get the latest trade data from the Redis list
        if modes == "rpush":
            latest_trade = redis_connection.lrange(f'trades:{chart_id}',-1,-1)
        else:
            latest_trade = redis_connection.lrange(f'trades:{chart_id}',0,0)

        return json.loads(latest_trade[0])["signature"]
    except Exception as e:
        return None
