#!/usr/bin/env python
import redis
import asyncio
import websockets
import json
import logging

logging.basicConfig(level=logging.INFO)

# Connect to Redis
r = redis.Redis(host='localhost', port=6379, db=0,password="pump2pump")

async def subscribe_to_redis(websocket, path):
    try:
        # Parse chart_id from the path
        chart_id = path.strip('/')
        logging.info(f"Client subscribed to chart: {chart_id}")

        pubsub = r.pubsub()
        pubsub.subscribe(chart_id)

        # Send the initial dataset in chunks
        initial_data = r.lrange(f'trades:{chart_id}', 0, -1)
        chunk_size = 100  # Adjust the chunk size as needed
        for i in range(0, len(initial_data), chunk_size):
            chunk = initial_data[i:i+chunk_size]
            print(json.dumps([json.loads(trade) for trade in chunk]))
            await websocket.send(json.dumps([json.loads(trade) for trade in chunk]))

        # Handle incoming messages from Redis and send them to WebSocket clients
        while True:
            message = pubsub.get_message()
            if message and message['type'] == 'message':
                trade_data = message['data']
                trade_data_json = json.loads(trade_data)
                trade_data_json['timestamp'] = float(trade_data_json['timestamp'])
                await websocket.send(json.dumps(trade_data_json))
            await asyncio.sleep(0.01)  # Prevents busy waiting
    except websockets.ConnectionClosed as e:
        logging.error(f"Connection closed: {e}")
    except Exception as e:
        logging.error(f"Error: {e}")

async def main():
    async with websockets.serve(subscribe_to_redis, "0.0.0.0", 6789):
        await asyncio.Future()  # run forever

if __name__ == "__main__":
    asyncio.run(main())
