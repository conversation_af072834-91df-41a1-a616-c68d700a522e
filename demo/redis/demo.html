<!DOCTYPE html>
<html>
<head>
    <title>TradingView Chart with Live Data</title>
    <style>
        html, body {
            height: 100%;
            margin: 0;
            display: flex;
            flex-direction: column;
        }
        #chart {
            flex-grow: 1;
        }
        #tooltip {
            position: absolute;
            display: none;
            padding: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            border-radius: 5px;
            pointer-events: none;
            z-index: 1000;
        }
    </style>
    <script src="https://unpkg.com/lightweight-charts@4.2.0/dist/lightweight-charts.standalone.production.js"></script>
</head>
<body>
    <div id="chart"></div>
    <div id="tooltip"></div> 
    <script>
        var mint = "HMZ7PzuwErxEkcyTEUXawmnSwtqDgLAXqQzQMST1pump";

        function getSolanaPriceInUSD() {
            const mintAddress = 'So11111111111111111111111111111111111111112';

            return fetch('https://api.primeapis.com/price', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ mintAddress: mintAddress }),
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();  // Parse the JSON from the response
            })
            .then(data => {
                // Assuming the API returns a JSON object with the USD value under `price.usd`
                return data.USD;  // Extract the USD price
            })
            .catch(error => {
                console.error('Error fetching price data:', error);
                return null;  // Return null if there's an error
            });
        }

        document.addEventListener('DOMContentLoaded', function () {
    // Fetch the Solana price before initializing the chart


     // Tooltip log



    getSolanaPriceInUSD().then(solprice => {
        if (solprice !== null) {
            console.log('Solana price:', solprice);
            // Price fetched successfully, proceed to initialize the chart

            const container = document.getElementById('chart');

            const toolTipWidth = 80;
            const toolTipHeight = 80;
            const toolTipMargin = 15;

            // Create and style the tooltip html element
            const toolTip = document.createElement('div');
            toolTip.style = `width: 96px; height: 80px; position: absolute; display: none; padding: 8px; box-sizing: border-box; font-size: 12px; text-align: left; z-index: 1000; top: 12px; left: 12px; pointer-events: none; border: 1px solid; border-radius: 2px;font-family: -apple-system, BlinkMacSystemFont, 'Trebuchet MS', Roboto, Ubuntu, sans-serif; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale;`;
            toolTip.style.background = 'white';
            toolTip.style.textColor = 'red' ;
            //toolTip.style.color = 'red';
            //toolTip.style.borderColor = '#2962FF';
            container.appendChild(toolTip);



            const chart = LightweightCharts.createChart(document.getElementById('chart'), {
                width: window.innerWidth,
                height: window.innerHeight,
                backgroundColor: '#000000',
                layout: {
                    background: {
                        color: '#000000'
                    },
                    textColor: '#444',
                },
                grid: {
                    vertLines: {
                        color: '#222',
                    },
                    horzLines: {
                        color: '#222',
                    },
                },
                rightPriceScale: {
                    borderColor: '#555',
                },
                timeScale: {
                    borderColor: '#555',
                    timeVisible: true,
                    secondsVisible: true,
                },
                priceFormat: {
                    formatter: price => {
                        if (price >= 1000000) {
                            return (price / 1000000).toFixed(1) + 'M';
                        } else if (price >= 1000) {
                            return (price / 1000).toFixed(1) + 'K';
                        }
                        return price.toFixed(2);
                    }
                },
            });

           


            const candlestickSeries = chart.addCandlestickSeries({
                upColor: '#26a69a',
                downColor: '#ef5350',
                borderVisible: false,
                wickUpColor: '#26a69a',
                wickDownColor: '#ef5350',
                priceFormat: {
                    type: 'volume',
                    formatter: price => {
                        if (price >= 1000000) {
                            return (price / 1000000).toFixed(1) + 'M';
                        } else if (price >= 1000) {
                            return (price / 1000).toFixed(1) + 'K';
                        }
                        return price.toFixed(2);
                    }
                }
            });

            const volumeSeries = chart.addHistogramSeries({
                color: '#26a69a',
                priceFormat: {
                    type: 'volume',
                    formatter: price => {
                        if (price >= 1000000) {
                            return (price / 1000000).toFixed(1) + 'M';
                        } else if (price >= 1000) {
                            return (price / 1000).toFixed(1) + 'K';
                        }
                        return price.toFixed(2);
                    }
                },
                priceScaleId: '',
                scaleMargins: {
                    top: 0.9,
                    bottom: 0,
                },
            });


          



            volumeSeries.priceScale().applyOptions({
                scaleMargins: {
                    top: 0.9,
                    bottom: 0,
                },
            });


            // chart.subscribeCrosshairMove(param => {
            //     if (
            //         param.point === undefined ||
            //         !param.time ||
            //         param.point.x < 0 ||
            //         param.point.x > container.clientWidth ||
            //         param.point.y < 0 ||
            //         param.point.y > container.clientHeight
            //     ) {
            //         toolTip.style.display = 'none';
            //     } else {
            //         // time will be in the same format that we supplied to setData.
            //         // thus it will be YYYY-MM-DD
            //         const dateStr = param.time;
            //         toolTip.style.display = 'block';
               
            //         const data = param.seriesData.get(volumeSeries);
            //         const price = data.value !== undefined ? data.value : data.close;
            //         toolTip.innerHTML = `<div style="color: ${'#ccc'}">Apple Inc.</div><div style="font-size: 24px; margin: 4px 0px; color: ${'black'}">
            //             ${Math.round(100 * price) / 100}
            //             </div><div style="color: ${'black'}">
            //             ${dateStr}
            //             </div>`;


            //             // toolTip.innerHTML = `<div style="color: ${'#ccc'}">Apple Inc.</div><div style="font-size: 24px; margin: 4px 0px; color: ${'black'}">
            //             // ${Math.round(100 * price) / 100}
            //             // </div><div style="color: ${'black'}">
            //             // ${dateStr}
            //             // </div>`;
            //         const coordinate = series.priceToCoordinate(price);
            //         let shiftedCoordinate = param.point.x - 50;
            //         if (coordinate === null) {
            //             return;
            //         }
            //         shiftedCoordinate = Math.max(
            //             0,
            //             Math.min(container.clientWidth - toolTipWidth, shiftedCoordinate)
            //         );
            //         const coordinateY =
            //             coordinate - toolTipHeight - toolTipMargin > 0
            //                 ? coordinate - toolTipHeight - toolTipMargin
            //                 : Math.max(
            //                     0,
            //                     Math.min(
            //                         container.clientHeight - toolTipHeight - toolTipMargin,
            //                         coordinate + toolTipMargin
            //                     )
            //                 );
            //         toolTip.style.left = shiftedCoordinate + 'px';
            //         toolTip.style.top = coordinateY + 'px';
            //     }
            // });



            const volumeData = [];
            const candleStickData = [];

            volumeSeries.setData(volumeData);
            candlestickSeries.setData(candleStickData);

            const socket = new WebSocket('ws://pump1:6789/' + mint);
            const solPrice = parseFloat(solprice);
            const tokenSupply = 1000000000;
            var msec = 100;
            var tmp_timestamp = 0;

            socket.onmessage = function (event) {
                try {
                    const data = JSON.parse(event.data);

                    if (Array.isArray(data)) {
                        data.forEach(tradeData => {
                            
                            processTradeData(tradeData, candlestickSeries, volumeSeries, solPrice, tokenSupply);
                        });
                    } else {
                        
                        processTradeData(data, candlestickSeries, volumeSeries, solPrice, tokenSupply);
                    }
                } catch (error) {
                    console.error('Error processing message:', error);
                }
            };

            socket.onopen = function () {
                console.log('Connected to WebSocket server');
            };

            socket.onclose = function (event) {
                console.log('Disconnected from WebSocket server', event);
                setTimeout(() => {
                    location.reload();
                }, 1000);
            };

            socket.onerror = function (error) {
                console.error('WebSocket error:', error);
            };

            window.addEventListener('resize', function () {
                chart.resize(window.innerWidth, window.innerHeight);
            });

            function processTradeData(tradeData, candlestickSeries, volumeSeries, solPrice, tokenSupply) {
                console.log(tradeData);
                //var msec = 100;
                //var tmp_timestamp = tradeData.timestamp;
                //const time = tmp_timestamp + msec / 1000;

                if (tradeData.timestamp > tmp_timestamp) {
                    tmp_timestamp = tradeData.timestamp;
                    msec = 100;
                } else {
                    msec += 100;
                }
                const time = tmp_timestamp + msec / 1000
                

                const price = tradeData.price * tokenSupply * solPrice;
                const volume = tradeData.volume * solPrice;
                const high = tradeData.high * tokenSupply * solPrice;
                const low = tradeData.low * tokenSupply * solPrice;
                const open = tradeData.open * tokenSupply * solPrice;
                const close = tradeData.close * tokenSupply * solPrice;

                //const color = tradeData.trade === "buy" ? '#26a69a' : '#ef5350';
                const color = tradeData.open < tradeData.close ? '#26a69a' : '#ef5350';  // Same for trade data

                const volumeUpdate = { time: time, value: volume ,color: color};
                const candlestickUpdate = { color: color, time: time, open: open, high: high, low: low, close: close };
                console.log('Volume:', volume, 'Price:', price, 'High:', high, 'Low:', low, 'Open:', open, 'Close:', close);

                volumeSeries.update(volumeUpdate);
                candlestickSeries.update(candlestickUpdate);
            }
        } else {
            console.error('Failed to fetch Solana price.');
        }
    });
});
    </script>
</body>
</html>
