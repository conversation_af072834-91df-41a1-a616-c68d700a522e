import redis
import time
import json
import random

# Connect to Redis
r = redis.Redis(host='localhost', port=6379, db=0)

def generate_trade():
    """Simulate a trade data entry"""
    return {
        'trade_id': random.randint(1, 1000000),
        'price': round(random.uniform(1.0, 1000.0), 2),
        'volume': round(random.uniform(1.0, 1000.0), 2),
        'timestamp': time.time()
    }

if __name__ == "__main__":
    chart_id = 'aaapump'  # Example chart ID, change as needed
    while True:
        trade_data = generate_trade()
        trade_data_json = json.dumps(trade_data)
        r.publish(chart_id, trade_data_json)
        r.rpush(f'trades:{chart_id}', trade_data_json)
        time.sleep(1)
