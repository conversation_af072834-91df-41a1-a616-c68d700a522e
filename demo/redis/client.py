#!/usr/bin/env python
import redis
import asyncio
import websockets
import json
import logging
import os
import time
logging.basicConfig(level=logging.INFO)

# Connect to Redis
r = redis.Redis(host='pump1', port=6379, db=0,password="pump2pump")

# Connect to the WebSocket server
# get token from cli args
import argparse
parser = argparse.ArgumentParser(description='Fetch token price')
parser.add_argument('--token', type=str, help='Token address')
args = parser.parse_args()


chart_id = args.token

logging.info(f"Client subscribed to chart: {chart_id}")

pubsub = r.pubsub()
pubsub.subscribe(chart_id)

# Send the initial dataset in chunks
initial_data = r.lrange(f'trades:{chart_id}', 0, -1)
chunk_size = 100  # Adjust the chunk size as needed
for i in range(0, len(initial_data), chunk_size):
    chunk = initial_data[i:i+chunk_size]
    json.dumps([json.loads(trade) for trade in chunk])
    print(json.dumps([json.loads(trade) for trade in chunk]))
# Handle incoming messages from Redis and send them to WebSocket clients
while True:
    message = pubsub.get_message()
    if message and message['type'] == 'message':
        trade_data = message['data']
        trade_data_json = json.loads(trade_data)
        trade_data_json['timestamp'] = float(trade_data_json['timestamp'])
        json.dumps(trade_data_json)
    time.sleep(0.01)  # Prevents busy waiting

