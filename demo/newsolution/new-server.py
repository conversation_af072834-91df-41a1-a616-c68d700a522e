#!/usr/bin/env python
import asyncio
import websockets
import json
import logging
from collections import defaultdict, deque
from threading import Lock
from aiohttp import web

logging.basicConfig(level=logging.INFO)

# Thread-safe in-memory data store with notification for multiple queues
class InMemoryDataStore:
    def __init__(self):
        self.queues = defaultdict(deque)
        self.locks = defaultdict(Lock)
        self.new_data_queues = defaultdict(asyncio.Queue)

    def lpush(self, queue_id, value):
        with self.locks[queue_id]:
            self.queues[queue_id].appendleft(value)
        # LPUSH data is not put into new_data_queue

    def rpush(self, queue_id, value):
        with self.locks[queue_id]:
            self.queues[queue_id].append(value)
        asyncio.create_task(self.new_data_queues[queue_id].put(value))

    def get_all(self, queue_id):
        with self.locks[queue_id]:
            return list(self.queues[queue_id])

# Initialize the data store
data_store = InMemoryDataStore()

# WebSocket handler
async def subscribe_to_data(websocket, path):
    try:
        queue_id = path.strip('/')
        logging.info(f"Client connected to queue: {queue_id}")

        # Send the initial dataset in chunks
        initial_data = data_store.get_all(queue_id)
        chunk_size = 100  # Adjust the chunk size as needed
        for i in range(0, len(initial_data), chunk_size):
            chunk = initial_data[i:i+chunk_size]
            await websocket.send(json.dumps(chunk))

        # Handle real-time updates for RPUSH only
        while True:
            new_trade = await data_store.new_data_queues[queue_id].get()
            await websocket.send(json.dumps(new_trade))

    except websockets.ConnectionClosed as e:
        logging.error(f"Connection closed: {e}")
    except Exception as e:
        logging.error(f"Error: {e}")

# HTTP handler for LPUSH
async def handle_lpush(request):
    try:
        queue_id = request.match_info['queue_id']
        data = await request.json()
        data_store.lpush(queue_id, data)
        logging.info(f"Data LPUSHed to queue {queue_id}: {data}")
        return web.json_response({"status": "success"})
    except Exception as e:
        logging.error(f"Error in LPUSH handler: {e}")
        return web.json_response({"status": "error", "message": str(e)})

# HTTP handler for RPUSH
async def handle_rpush(request):
    try:
        queue_id = request.match_info['queue_id']
        data = await request.json()
        data_store.rpush(queue_id, data)
        logging.info(f"Data RPUSHed to queue {queue_id}: {data}")
        return web.json_response({"status": "success"})
    except Exception as e:
        logging.error(f"Error in RPUSH handler: {e}")
        return web.json_response({"status": "error", "message": str(e)})

# Main WebSocket server loop
async def websocket_main():
    async with websockets.serve(subscribe_to_data, "0.0.0.0", 6789):
        await asyncio.Future()  # run forever

# Main HTTP server loop
async def http_main():
    app = web.Application()
    app.router.add_post('/lpush/{queue_id}', handle_lpush)
    app.router.add_post('/rpush/{queue_id}', handle_rpush)
    runner = web.AppRunner(app)
    await runner.setup()
    site = web.TCPSite(runner, '0.0.0.0', 6790)
    await site.start()
    await asyncio.Future()  # run forever

# Run both servers concurrently
async def main():
    await asyncio.gather(websocket_main(), http_main())

if __name__ == "__main__":
    asyncio.run(main())
