#!/usr/bin/env python

import json

def process_solana_transaction(transaction_json):
    
    transaction = transaction_json['transaction']
    instructions = transaction['message']['instructions']
    account_keys = transaction['message']['accountKeys']

    meta = transaction_json['meta']
    post_token_balances = meta['postTokenBalances']
    pre_token_balances = meta['preTokenBalances']

    def get_account_address_by_index(index):
        return account_keys[index]["pubkey"]
    
    def get_owner_by_token_account(tokenAccount):
         for balance in meta["preTokenBalances"]:
            if get_account_address_by_index(balance["accountIndex"]) == tokenAccount:
                return balance["owner"],balance["mint"]

    def process_tx(instruction):
            if instruction["program"] == "system" and instruction["parsed"]["type"] == "transfer":
                source = instruction["parsed"]["info"]["source"]
                destination = instruction["parsed"]["info"]["destination"]
                amount = instruction["parsed"]["info"]["lamports"]
                print(f"Instruction: (transfer) Source: {source} Destination: {destination} Amount: {amount}  lamports")


            if instruction["program"] == "spl-associated-token-account" and  instruction["parsed"]["type"] == "createIdempotent":
                print(f"Instruction: (createImpodent) Create associated token account owner:{instruction["parsed"]["info"]["source"]}  new splaccount:{instruction["parsed"]["info"]["account"]}  mint:{instruction["parsed"]["info"]["mint"]}")
            #
            if instruction["program"] == "spl-token" and instruction["parsed"]["type"] == "closeAccount":
                print(f"Instruction: (closeAccount) owner:{instruction["parsed"]["info"]["owner"]}  closed account :{instruction["parsed"]["info"]["account"]}  ")
                       
            if instruction["program"] == "system" and instruction["parsed"]["type"] == "createAccount":
                print(f"Instruction: (createAccount) owner:{instruction["parsed"]["info"]["owner"]}  new account :{instruction["parsed"]["info"]["newAccount"]}  sum:{instruction["parsed"]["info"]["lamports"]}")
                       
            if instruction["program"] == "spl-token" and instruction["parsed"]["type"] == "initializeAccount3":
                print(f"Instruction: (initializeAccount3)  Set token for account :{instruction["parsed"]["info"]["account"]}  mint:{instruction["parsed"]["info"]["mint"]}  owner:{instruction["parsed"]["info"]["owner"]}")
                 
            if instruction["program"] == "spl-token" and instruction["parsed"]["type"] == "transfer":
                
                source_owner,source_mint=get_owner_by_token_account(instruction["parsed"]["info"]["source"])
                destination_owner,destination_mint=get_owner_by_token_account(instruction["parsed"]["info"]["destination"]) 
    
                #print(f"Instruction: (spl-transfer)  Source:{instruction["parsed"]["info"]["source"]}  Destination:{instruction["parsed"]["info"]["destination"]}  Amount:{instruction["parsed"]["info"]["amount"]} mint:{destination_mint}" )
                print(f"Instruction: (spl-transfer)  Source:{source_owner}  Destination:{destination_owner}  Amount:{instruction["parsed"]["info"]["amount"]} mint:{destination_mint}" )

    for index,instruction in enumerate(instructions):
        if "program" in instruction:
            # Check if the instruction is for the token program
            process_tx(instruction)
            for inner_instruction in meta["innerInstructions"]:
                if inner_instruction["index"] == index:
                        for ii in inner_instruction["instructions"]:
                            if "program" in ii:
                                process_tx(ii)
        else: 
            for inner_instruction in meta["innerInstructions"]:
                if inner_instruction["index"] == index:
                        for ii in inner_instruction["instructions"]:
                            if "program" in ii:
                                process_tx(ii)

    



def main():
    file_path = 'tx/4ar6hMTH52kzuJgvASzNh3k2wrzSZQgobDPzoF5F1stMCAKFz6xvKaZQW2B5yyk2sQ5rngaenWt8u9NbTV1HG32p.json'  # Specify your file path here

    # Read the transaction file
    with open(file_path, 'r') as file:
        transaction_json = json.load(file)

    # Process the transaction
    process_solana_transaction(transaction_json)

if __name__ == "__main__":
    main()
