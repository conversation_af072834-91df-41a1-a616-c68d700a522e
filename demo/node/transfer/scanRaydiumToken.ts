import { 
    getAccountAuto3,
    updateAddressName,
    getEnabledAccounts,
    disableAccount,
    AddressApp,
    updateAccountStatus,
    updateAccountSignature ,
    addMint,
    getMint
} from "./src/utils/db"; 
import { processSwapTransaction } from "./getTransactionSwap";
import { Connection, PublicKey , VersionedTransactionResponse} from '@solana/web3.js';
import dotenv from 'dotenv';
import { 
    PRIVATE_KEY,
    RPC_URL, 
    REDIS_URL,
    ignoreAccounts
} from './src/config'; // Corrected import path
import { addAppAccountToDBWithName } from './src/utils/db';
import { Redis } from 'ioredis';
import { addRedis, getRedis,isMemberOfSet } from './redis';
import { logger } from "./logger";
import { get } from "http";

import { Command } from 'commander';

const program = new Command();

dotenv.config();
const WEBHOOK = "https://discord.com/api/webhooks/1339217907380523040/tdXh3hfzR-AJNgGNYupg31BKirR1KoDV8mj937I7DAk1OqqWlNpinLl2O0TfawVVyG9K";

type Task = 'getToken' | 'getDestination' | 'getMasters';
let task: Task ="getToken";
let redis: Redis;
type Account = { address: string; app: string };
const bootstrapAccount="6jPE8dK2fUUTdvJdogo4SWnvtAHtLvE2PGxKYH8B7dCR";
const lowSol=0.01;
const lowSolLamports = lowSol * 1e9;    
const followSol=100;
const followSolLamports = followSol * 1e9;

async function getTransactions(accountAddress: string, last_sig?: string | undefined) {   
    const connection = new Connection(RPC_URL);
    const limit = 1000;
    let before = undefined; 
    before = "4Ex2qKEmsgSMqWckoLoJt4gs6H7DFBshF8rpZZJzH8E9qGEwv9ftNtczRdXoc4ovzJKoZNG5nmcTasDTGqogjsSH"; 
    const publicKey = new PublicKey(accountAddress);
    const signatures = await connection.getSignaturesForAddress(publicKey, { before, limit: limit });

    const firstSig = signatures[0]?.signature;
    const sigs = [];
    for (const signature of signatures) {
        if (signature.err === null) {
            if (signature.signature === last_sig) {
                break;
            }
            sigs.push(signature.signature);
        }
    }

    const chunkSize = 20;
    const tradeSummary = { buy: 0, sell: 0 };

    for (let i = 0; i < sigs.length; i += chunkSize) {
        const chunk = sigs.slice(i, i + chunkSize);
        const results = await Promise.all(chunk.map(async sig => {
            const result = await processSwapTransaction(redis,sig, accountAddress);
            return result ? JSON.parse(result) : null; // Assuming the result is a JSON string
        }));

        for (const result of results) {
            if (result) {
                if (result.trade === 'buy') {
                    tradeSummary.buy += result.amountOfSolana;
                } else if (result.trade === 'sell') {
                    tradeSummary.sell += result.amountOfSolana;
                }
            }
        }
    }

    console.log('Cumulative Buys:', tradeSummary.buy);
    console.log('Cumulative Sells:', tradeSummary.sell);
    console.log('Cumulative Diff:', tradeSummary.buy - tradeSummary.sell);

    return;
}

async function fetchMultipleTransactions(signatures: string[]) {
    const connection = new Connection(RPC_URL);
    const multipleTransactions = await connection.getTransactions(signatures, {
        maxSupportedTransactionVersion: 0,
    });
    return multipleTransactions;
}

async function processTransaction(transaction: VersionedTransactionResponse, accountAddress: string) {
    const signature = transaction.transaction.signatures[0];

    // RULE:1  GET MINT ADDRESS  result: send to rabbit/db 
    // ACCOUNT will accumulate 800sol+profit

    const accountKeys = transaction.transaction.message.staticAccountKeys.map(key => key.toBase58());
    const keys = accountKeys.map(key => key);
   
    const accuntKeyIndex = accountKeys.indexOf(accountAddress); 
    //RULE 0; follow  
    if (transaction.meta?.postBalances && transaction.meta?.postBalances.length > 0 && transaction.meta?.preBalances[accuntKeyIndex] > followSolLamports) {
        const preBalances = transaction.meta.preBalances;
        const postBalances = transaction.meta.postBalances;
        const signature = transaction.transaction.signatures[0];
        let maxGain = 0;
        let maxGainIndex = -1;

        for (let i = 0; i < preBalances.length; i++) {
            const gain = postBalances[i] - preBalances[i];
            if (gain > maxGain) {
                maxGain = gain;
                maxGainIndex = i;
            }
        }

        if (maxGainIndex !== -1) {
            const destinationAccount = accountKeys[maxGainIndex];
            if (maxGain > 100 * 1e9) {
                logger.debug(`Add REGULAR account: ${destinationAccount}`);
                //ignore if 
                if (ignoreAccounts.includes(destinationAccount)) {
                    logger.debug('Ignore account:', destinationAccount);
                    return;
                }
                await addAppAccountToDBWithName(destinationAccount, postBalances[maxGainIndex], 'auto3', signature, 'regular');
            }
        }
    } 

    // rule 1: get mint 
    if (transaction?.meta?.postTokenBalances) {
        const accountKeys = transaction.transaction.message.staticAccountKeys.map(key => key.toBase58());
        const keys = accountKeys.map(key => key);
        const signer = keys[0];
        for (const postTokenBalance of transaction?.meta?.postTokenBalances) {
            if (postTokenBalance.uiTokenAmount.uiAmount && postTokenBalance.uiTokenAmount.uiAmount > ********* && postTokenBalance.owner != signer) {
                console.log('Transaction:', signature);
                console.log('mint:', postTokenBalance.mint);
                console.log('owner:', postTokenBalance.owner);
                console.log('uiAmount:', postTokenBalance.uiTokenAmount.uiAmount);
                const mint = await getMint(postTokenBalance.mint);

                if (!mint) {    
                    await addMint(signer, postTokenBalance.mint, "auto3");
                    const timestamp = new Date().toISOString();
                }
            }
        }   
    }

    // RULE: 2 GET BUNDLER ACCOUNTS . result: send to redis
    if (transaction && transaction?.meta?.postTokenBalances?.length == 0 && transaction?.meta?.preTokenBalances?.length == 0 && transaction?.meta?.postBalances) {
        const accountKeys = transaction.transaction.message.staticAccountKeys.map(key => key.toBase58());
        const keys = accountKeys.map(key => key);
        const signer = keys[0];
        const postBalances = transaction?.meta?.postBalances;
        const balanceCounts = postBalances.reduce<{ [key: number]: number }>((acc, balance) => {
            if (balance > ********** && balance < ***********) {
                acc[balance] = (acc[balance] || 0) + 1;
            }
            return acc;
        }, {});

        const accountsWithBalance = Object.keys(balanceCounts).filter((balance: string) => balanceCounts[Number(balance)] > 1);
        const accounts = accountsWithBalance.map(balance => {
            const indexes = [];
            let idx = postBalances.indexOf(Number(balance));
            while (idx != -1) {
                indexes.push(idx);
                idx = postBalances.indexOf(Number(balance), idx + 1);
            }
            return indexes.map(index => keys[index]);
        });

        if (accounts.length > 0) {
            const redisData: string[] = await getRedis(redis, "auto3a");
            console.log('redisData:', redisData.length);
         
            let redisSet = new Set(redisData);
            for (const account of accounts[0]) {
                redisSet.add(account);
                //addAppAccountToDB(account,'auto3');
            }
            await addRedis(redis, "auto3a", Array.from(redisSet));  
        }
    }
}

program
  .command('scan')
  .description('Scan token')
  .option('-t, --token <tokenCA>', 'Token contract address')
  .action(async (options) => {
    console.log("Scan token...", options.token);
    if (options.token) {
        redis = new Redis({host: REDIS_URL, port: 6379, password: "pump2pump"});
        await getTransactions(options.token, undefined);
        console.log("closing redis...");
        await redis.quit();
        process.exit(0); // Ensure the process exits
    }
  });

program.parse(process.argv);
