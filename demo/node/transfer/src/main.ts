// filepath: /Users/<USER>/repo/pump/demo/node/transfer/src/main.ts
import { connect } from 'amqplib';
import { buyToken, sellToken } from './trading.js';
import dotenv from 'dotenv';

dotenv.config();


/*
todo: get signals from blockchain: helisu webhook or balance changes. trigger send to rabbitmq 
trade logic: 
- read data from rabbitmq. queue data: {'tokenAddress',isBuy,amount,slippage,priorityLevel}
- perform trade
*/

interface TradeData {
  tokenAddress: string;
  isBuy: boolean;
  amount: number;
  slippage: number;
  priorityLevel: string;
}

let tokenBalance = 0; // balance of token after buying used for selling. todo: implement in sellToken function

async function consumeMessages() {
  try {
    const connection = await connect({
      protocol: 'amqp',
      hostname: process.env.RABBITMQ_HOSTNAME,
      port: process.env.RABBITMQ_PORT ? parseInt(process.env.RABBITMQ_PORT, 10) : undefined,
      username: process.env.RABBITMQ_USERNAME,
      password: process.env.RABBITMQ_PASSWORD,
      vhost: '/'
    });

    const channel = await connection.createChannel();
    const queue = String(process.env.RABBITMQ_QUEUE);

    await channel.assertQueue(queue, { durable: true });

    console.log(`Waiting for messages in queue: ${queue}`);

    channel.consume(queue, async (msg) => {
      if (msg !== null) {
        const tradeData: TradeData = JSON.parse(msg.content.toString());

        if (tradeData.isBuy) {
          console.log(`Buying token: ${tradeData.tokenAddress}`);
          //await buyToken(tradeData.tokenAddress, tradeData.amount, tradeData.slippage, tradeData.priorityLevel);

        } else {
          console.log(`Selling token: ${tradeData.tokenAddress}`);
          //await sellToken(tradeData.tokenAddress, tradeData.amount, tradeData.slippage, tradeData.priorityLevel);

        }

        channel.ack(msg);
      }
    });
  } catch (error) {
    console.error('Error consuming messages:', error);
  }
}

consumeMessages();