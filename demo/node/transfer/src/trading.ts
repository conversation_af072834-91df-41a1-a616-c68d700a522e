import { Connection, Keypair, Transaction, PublicKey } from "@solana/web3.js";
import { getBuyInstruction, getSellInstruction } from "./utils/trade.js";
import { PRIVATE_KEY, RPC_URL } from "./config.js";
import { getKeyPairFromPrivateKey } from "./utils/helper.js";
import { createAssociatedTokenAccountInstruction, getAssociatedTokenAddress } from '@solana/spl-token';
import { bs58 } from "@coral-xyz/anchor/dist/cjs/utils/bytes/index.js";

const connection = new Connection(RPC_URL, "confirmed");

export async function buyToken(tokenAddress: string, amount: number, slippage: number, priorityLevel: string) {
  const privateKey = PRIVATE_KEY;
  const signer = Keypair.fromSecretKey(Buffer.from(privateKey, "base64"));
  const transaction = new Transaction();
  const buyInstructionResult = await getBuyInstruction(connection, privateKey, tokenAddress, amount, slippage, transaction);

  if (!buyInstructionResult) {
    throw new Error("Failed to get buy instruction");
  }

  const { instruction, tokenAmount } = buyInstructionResult;
  transaction.add(instruction);

  // Add priority fee if needed
  // ...

  transaction.feePayer = signer.publicKey;
  transaction.recentBlockhash = (await connection.getLatestBlockhash()).blockhash;
  transaction.sign(signer);

  const signature = await connection.sendTransaction(transaction, [signer]);
  await connection.confirmTransaction(signature, "processed");

  return { signature, tokenAmount };
}

export async function getTokenBalance(tokenAddress: string,privateKey: string) {
  const signer = Keypair.fromSecretKey(bs58.decode(privateKey));
  const tokenAccountAddress = await getAssociatedTokenAddress(new PublicKey(tokenAddress), signer.publicKey, false);
  const tokenAccountBalance = await connection.getTokenAccountBalance(tokenAccountAddress);

  if (!tokenAccountBalance) {
    throw new Error("Token account not found");
  }

  const tokenBalance = tokenAccountBalance.value.amount;
  return tokenBalance;
}


export async function sellToken(tokenAddress: string, percentage: number, slippage: number, priorityLevel: string) {
  const privateKey = PRIVATE_KEY;
  const signer = Keypair.fromSecretKey(Buffer.from(privateKey, "base64"));
  const tokenAccountAddress = await getAssociatedTokenAddress(new PublicKey(tokenAddress), signer.publicKey, false);
  const tokenAccountBalance = await connection.getTokenAccountBalance(tokenAccountAddress);

  if (!tokenAccountBalance) {
    throw new Error("Token account not found");
  }

  const tokenBalance = parseInt(tokenAccountBalance.value.amount) * (percentage / 100);
  const transaction = new Transaction();
  const sellInstruction = await getSellInstruction(connection, privateKey, tokenAddress, tokenBalance, slippage);

  if (!sellInstruction) {
    throw new Error("Failed to get sell instruction");
  }

  transaction.add(sellInstruction);

  // Add priority fee if needed
  // ...

  transaction.feePayer = signer.publicKey;
  transaction.recentBlockhash = (await connection.getLatestBlockhash()).blockhash;
  transaction.sign(signer);

  const signature = await connection.sendTransaction(transaction, [signer]);
  await connection.confirmTransaction(signature, "processed");

  return signature;
}
