import {
  Connection,
  SystemProgram,
  Transaction,
  sendAndConfirmTransaction,
  Keypair,
  ComputeBudgetProgram,
  PublicKey,
  SendTransactionError,
} from "@solana/web3.js";
import dotenv from "dotenv";
import bs58 from "bs58";
import fs from "fs";
import { Command } from "commander";
import getTokenData from "./utils/coindata.js"; // Assuming getTokenData is in test.ts
import { createAssociatedTokenAccountInstruction, getAssociatedTokenAddress } from '@solana/spl-token';
import { getBuyInstruction, getSellInstruction } from "./utils/trade.js"; // Import the buy and sell functions

const LAMPORTS_PER_SOL = Number(1_000_000_000);
export const GLOBAL = new PublicKey('4wTV1YmiEkRvAtNtsSGPtUrqRYQMe5SKy2uB4Jjaxnjf');
export const FEE_RECIPIENT = new PublicKey('CebN5WGQ4jvEPvsVU4EoHEpgzq1VV7AbicfhtW4xC9iM');
export const TOKEN_PROGRAM_ID = new PublicKey('TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA');
export const ASSOC_TOKEN_ACC_PROG = new PublicKey('ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL');
export const RENT = new PublicKey('SysvarRent111111111111111111111111111111111');
export const PUMP_FUN_PROGRAM = new PublicKey('6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P');
export const PUMP_FUN_ACCOUNT = new PublicKey('Ce6TQqeHC9p8KetsN6JsjHK7UTZk7nasjjnr7XxXp9F1');
export const SYSTEM_PROGRAM_ID = SystemProgram.programId;

dotenv.config();

const verifyRetryPause = 500;
const numOfRetries = 30;

const HeliusURL = "https://mainnet.helius-rpc.com/?api-key=cf3aa81f-7796-401b-a170-5567272f5f65";
const alchemyURL = "https://solana-mainnet.g.alchemy.com/v2/********************************";

const connection = new Connection(HeliusURL);
const connection2 = new Connection(HeliusURL);

const program = new Command();

export async function createAccount() {
  const space = 0; // any extra space in the account
  const rentLamports = await connection.getMinimumBalanceForRentExemption(space);
  console.log("Minimum balance for rent exception:", rentLamports);

  const privateKey = process.env.PRIVATE_KEY ? bs58.decode(process.env.PRIVATE_KEY) : new Uint8Array();
  const signer = Keypair.fromSecretKey(privateKey);

  const balance = await connection.getBalance(signer.publicKey);
  console.log("Signer balance:", balance / LAMPORTS_PER_SOL, "SOL");

  if (balance < rentLamports) {
    throw new Error("Insufficient funds in the signer account.");
  }

  const newAccountKeypair = Keypair.generate();
  console.log("New account address:", newAccountKeypair.publicKey.toBase58());

  const newAccountPrivateKey = bs58.encode(newAccountKeypair.secretKey);
  fs.writeFileSync(`account-${newAccountKeypair.publicKey.toBase58()}.txt`, newAccountPrivateKey);

  const { blockhash } = await connection.getRecentBlockhash();

  const transaction = new Transaction().add(
    SystemProgram.createAccount({
      fromPubkey: signer.publicKey,
      newAccountPubkey: newAccountKeypair.publicKey,
      lamports: rentLamports,
      space: space,
      programId: SystemProgram.programId,
    })
  );

  transaction.feePayer = signer.publicKey;
  transaction.recentBlockhash = blockhash;

  transaction.sign(signer, newAccountKeypair);

  try {
    const signature = await sendAndConfirmTransaction(connection, transaction, [signer, newAccountKeypair], { preflightCommitment: "processed" });
    console.log("Signature:", signature);
  } catch (error) {
    if (error instanceof SendTransactionError) {
      console.error("Failed to send transaction:", error.message);
      console.error("Logs:", error.logs);
    } else {
      console.error("Failed to send transaction:", error);
    }
  }
}

export async function closeAccount(accountName: string) {
  const privateKey = process.env.PRIVATE_KEY ? bs58.decode(process.env.PRIVATE_KEY) : new Uint8Array();
  const signer = Keypair.fromSecretKey(privateKey);

  const accountPublicKey = new PublicKey(accountName);

  const accountPrivateKey = bs58.decode(fs.readFileSync(`account-${accountName}.txt`, 'utf8'));
  const accountKeypair = Keypair.fromSecretKey(accountPrivateKey);

  const transaction = new Transaction().add(
    SystemProgram.transfer({
      fromPubkey: accountPublicKey,
      toPubkey: signer.publicKey,
      lamports: await connection.getBalance(accountPublicKey),
    })
  );

  transaction.feePayer = signer.publicKey;
  const { blockhash } = await connection.getRecentBlockhash();
  transaction.recentBlockhash = blockhash;

  transaction.sign(signer, accountKeypair);

  try {
    const signature = await sendAndConfirmTransaction(connection, transaction, [signer, accountKeypair], { preflightCommitment: "processed" });
    console.log("Account closed. Signature:", signature);
  } catch (error) {
    if (error instanceof SendTransactionError) {
      console.error("Failed to send transaction:", error.message);
      console.error("Logs:", error.logs);
    } else {
      console.error("Failed to send transaction:", error);
    }
  }
}

export async function transferSOL(toAccount: string, amount: string, priorityLevel: PriorityLevel) {
  const fromKeypair = process.env.PRIVATE_KEY ? Keypair.fromSecretKey(bs58.decode(process.env.PRIVATE_KEY)) : Keypair.generate();
  const toPubkey = new PublicKey(toAccount);

  const lamports = parseFloat(amount) * LAMPORTS_PER_SOL;

  const transaction = new Transaction();
  const transferIx = SystemProgram.transfer({
    fromPubkey: fromKeypair.publicKey,
    toPubkey,
    lamports,
  });
  transaction.add(transferIx);

  let feeEstimate: PriorityFeeEstimateResult = { priorityFeeEstimate: 0 };
  if (priorityLevel !== "NONE") {
    feeEstimate = await getPriorityFeeEstimate(priorityLevel, transaction, fromKeypair);
    const computePriceIx = ComputeBudgetProgram.setComputeUnitPrice({
      microLamports: feeEstimate.priorityFeeEstimate,
    });
    transaction.add(computePriceIx);
  }

  transaction.feePayer = fromKeypair.publicKey;
  transaction.recentBlockhash = (await connection.getLatestBlockhash()).blockhash;
  transaction.sign(fromKeypair);

  try {
    const txid = await sendAndConfirmTransaction(connection, transaction, [fromKeypair], { commitment: "processed", preflightCommitment: "processed" });
    console.log(`Transaction sent successfully with signature ${txid}`);
  } catch (error) {
    if (error instanceof SendTransactionError) {
      console.error("Failed to send transaction:", error.message);
      console.error("Logs:", error.logs);
    } else {
      console.error("Failed to send transaction:", error);
    }
  }
}

export async function buyToken(tokenCA: string, amount: string, slippage: string, priorityLevel: PriorityLevel) {
  const privateKey = process.env.PRIVATE_KEY ? bs58.decode(process.env.PRIVATE_KEY) : new Uint8Array();
  const signer = Keypair.fromSecretKey(privateKey);

  const transaction = new Transaction();
  const buyInstructionResult = await getBuyInstruction(connection2, bs58.encode(privateKey), tokenCA, parseFloat(amount), parseFloat(slippage), transaction);

  if (!buyInstructionResult) {
    console.error('Failed to get buy instruction');
    return;
  }

  const { instruction, tokenAmount } = buyInstructionResult;

  transaction.add(instruction);

  let feeEstimate: PriorityFeeEstimateResult = { priorityFeeEstimate: 0 };
  if (priorityLevel !== "NONE") {
    feeEstimate = await getPriorityFeeEstimate(priorityLevel, transaction, signer);
    const computePriceIx = ComputeBudgetProgram.setComputeUnitPrice({
      microLamports: feeEstimate.priorityFeeEstimate,
    });
    transaction.add(computePriceIx);
  }

  transaction.feePayer = signer.publicKey;
  transaction.recentBlockhash = (await connection.getLatestBlockhash()).blockhash;
  transaction.sign(signer);

  try {
    const txid = await sendAndConfirmTransaction(connection, transaction, [signer], { skipPreflight: true,  commitment: "processed", preflightCommitment: "processed" });
    console.log(`Transaction sent successfully with signature ${txid}`);
    return txid;
  } catch (error) {
    if (error instanceof SendTransactionError) {
      console.error("Failed to send transaction:", error.message);
      console.error("Logs:", error.logs);
      return "";
    } else {
      console.error("Failed to send transaction:", error);
      return "";
    }
  }
}

export async function getTokenBalance(tokenCA: string) {
  const privateKey = process.env.PRIVATE_KEY ? bs58.decode(process.env.PRIVATE_KEY) : new Uint8Array();
  const signer = Keypair.fromSecretKey(privateKey);
  const tokenAccountAddress = await getAssociatedTokenAddress(new PublicKey(tokenCA), signer.publicKey, false);
  let tokenAccountBalance;
  try {
    tokenAccountBalance = await connection2.getTokenAccountBalance(tokenAccountAddress);
  } catch (error) {
    //console.error('Failed to get token account balance:', error);
    return null;
  }

  const tokenBalance = parseInt(tokenAccountBalance.value.amount);
  console.log("Token balance",tokenBalance," mint:",tokenCA);
  return tokenBalance;
}

export async function sellTokens(tokenCA: string, amount: string, slippage: string, priorityLevel: PriorityLevel) {
  const privateKey = process.env.PRIVATE_KEY ? bs58.decode(process.env.PRIVATE_KEY) : new Uint8Array();
  const signer = Keypair.fromSecretKey(privateKey);

  const tokenBalance = parseFloat(amount);

  const transaction = new Transaction();
  const sellInstruction = await getSellInstruction(connection, bs58.encode(privateKey), tokenCA, tokenBalance, parseFloat(slippage));

  if (!sellInstruction) {
    console.error('Failed to get sell instruction');
    return;
  }

  transaction.add(sellInstruction);

  let feeEstimate: PriorityFeeEstimateResult = { priorityFeeEstimate: 0 };
  if (priorityLevel !== "NONE") {
    feeEstimate = await getPriorityFeeEstimate(priorityLevel, transaction, signer);
    const computePriceIx = ComputeBudgetProgram.setComputeUnitPrice({
      microLamports: feeEstimate.priorityFeeEstimate,
    });
    transaction.add(computePriceIx);
  }

  transaction.feePayer = signer.publicKey;
  transaction.recentBlockhash = (await connection.getLatestBlockhash()).blockhash;
  transaction.sign(signer);

  try {
    const txid = await sendAndConfirmTransaction(connection, transaction, [signer], {skipPreflight: true, commitment: "processed", preflightCommitment: "processed" });
    console.log(`Transaction sent successfully with signature ${txid}`);
    console.log(`Verifying transaction: ${txid}`);
    let retries = numOfRetries;
    while (retries > 0) {
      const verified = await verifyTransaction(txid);
      if (verified) {
        console.log(`Sold ${amount} tokens`);
        break;
      } else {
        console.log(`Verification failed, retrying... (${5 - retries + 1}/5)`);
        retries--;
        await new Promise(resolve => setTimeout(resolve, verifyRetryPause));
      }
    }
  } catch (error) {
    if (error instanceof SendTransactionError) {
      console.error("Failed to send transaction:", error.message);
      console.error("Logs:", error.logs);
    } else {
      console.error("Failed to send transaction:", error);
    }
  }
}

export async function sellTokenPercentage(tokenCA: string, percentage: string, slippage: string, priorityLevel: PriorityLevel) {
  const privateKey = process.env.PRIVATE_KEY ? bs58.decode(process.env.PRIVATE_KEY) : new Uint8Array();
  const signer = Keypair.fromSecretKey(privateKey);

  const tokenAccountAddress = await getAssociatedTokenAddress(new PublicKey(tokenCA), signer.publicKey, false);
  const tokenAccountBalance = await connection.getTokenAccountBalance(tokenAccountAddress);

  if (!tokenAccountBalance) {
    console.error('Token account not found');
    return;
  }

  const tokenBalance = parseInt(tokenAccountBalance.value.amount) * (parseFloat(percentage) / 100);
  if (tokenBalance === 0) {
    console.error('Token balance is 0');
    return;
  }
  console.log("selling", tokenBalance, "tokens");
  const transaction = new Transaction();
  const sellInstruction = await getSellInstruction(connection, bs58.encode(privateKey), tokenCA, tokenBalance, parseFloat(slippage));

  if (!sellInstruction) {
    console.error('Failed to get sell instruction');
    return;
  }

  transaction.add(sellInstruction);

  let feeEstimate: PriorityFeeEstimateResult = { priorityFeeEstimate: 0 };
  if (priorityLevel !== "NONE") {
    feeEstimate = await getPriorityFeeEstimate(priorityLevel, transaction, signer);
    const computePriceIx = ComputeBudgetProgram.setComputeUnitPrice({
      microLamports: feeEstimate.priorityFeeEstimate,
    });
    transaction.add(computePriceIx);
  }

  transaction.feePayer = signer.publicKey;
  transaction.recentBlockhash = (await connection.getLatestBlockhash()).blockhash;
  transaction.sign(signer);

  try {
    const txid = await sendAndConfirmTransaction(connection, transaction, [signer], {skipPreflight: true, commitment: "processed", preflightCommitment: "processed" });
    console.log(`Transaction sent successfully with signature ${txid}`);
    return txid;
  } catch (error) {
    if (error instanceof SendTransactionError) {
      console.error("Failed to send transaction:", error.message);
      console.error("Logs:", error.logs);
    } else {
      console.error("Failed to send transaction:", error);
    }
  }
}

export async function verifyTransaction(signature: string) {
  let transaction;
  try {
    transaction = await connection.getTransaction(signature, { maxSupportedTransactionVersion: 0 });
  } catch (error) {
    return false;
  }
  if (!transaction) {
    console.error('Transaction not found');
    return false;
  }
  if (!transaction.meta?.err) {
    return true;
  }
  return false;
}

program
  .command("create")
  .description("Create a new account")
  .action(createAccount);

program
  .command("close <accountName>")
  .description("Close an account")
  .action(closeAccount);

program
  .command("transfer <toAccount> <amount>")
  .description("Transfer SOL to another account")
  .option("--priorityLevel <level>", "Priority level for the transaction", "NONE")
  .action((toAccount, amount, options) => {
    transferSOL(toAccount, amount, options.priorityLevel as PriorityLevel);
  });

program
  .command("buy <tokenCA> <amount> <slippage>")
  .description("Buy token on pump.fun")
  .option("--priorityLevel <level>", "Priority level for the transaction", "NONE")
  .action((tokenCA, amount, slippage, options) => {
    buyToken(tokenCA, amount, slippage, options.priorityLevel as PriorityLevel);
  });

program
  .command("getBalance <tokenCA>")
  .description("Get Token balance")
  .action(getTokenBalance);

program
  .command("verifyTransaction <signature>")
  .description("Verify a transaction")
  .action(verifyTransaction);

program
  .command("sellTokens <tokenCA> <amount> <slippage>")
  .description("Sell token on pump.fun")
  .option("--priorityLevel <level>", "Priority level for the transaction", "NONE")
  .action((tokenCA, amount, slippage, options) => {
    sellTokens(tokenCA, amount, slippage, options.priorityLevel as PriorityLevel);
  });

program
  .command("sell <tokenCA> <percentage> <slippage>")
  .description("Sell token on pump.fun")
  .option("--priorityLevel <level>", "Priority level for the transaction", "NONE")
  .action((tokenCA, percentage, slippage, options) => {
    sellTokenPercentage(tokenCA, percentage, slippage, options.priorityLevel as PriorityLevel);
  });

program.parse(process.argv);

type PriorityLevel = "Min" | "Low" | "Medium" | "High" | "VeryHigh" | "UnsafeMax" | "NONE";

interface PriorityFeeEstimateResult {
  priorityFeeEstimate: number;
}

async function getPriorityFeeEstimate(priorityLevel: PriorityLevel, transaction: Transaction, fromKeypair: Keypair): Promise<PriorityFeeEstimateResult> {
  transaction.feePayer = fromKeypair.publicKey;
  transaction.recentBlockhash = (await connection.getLatestBlockhash()).blockhash;

  const response = await fetch(HeliusURL, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({
      jsonrpc: "2.0",
      id: "1",
      method: "getPriorityFeeEstimate",
      params: [
        {
          transaction: bs58.encode(transaction.serialize({ requireAllSignatures: false })),
          options: { priorityLevel: priorityLevel },
        },
      ],
    }),
  });
  const data = await response.json();
  console.log("Fee in function for", priorityLevel, ":", data.result.priorityFeeEstimate);
  return data.result;
}

export async function getKeyPairFromPrivateKey(key: string) {
  return Keypair.fromSecretKey(new Uint8Array(bs58.decode(key)));
}

export function bufferFromUInt64(value: number | string) {
  let buffer = Buffer.alloc(8);
  buffer.writeBigUInt64LE(BigInt(value));
  return buffer;
}