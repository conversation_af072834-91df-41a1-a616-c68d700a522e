import { connect } from 'amqplib';
import dotenv from 'dotenv';

dotenv.config();

interface TradeData {
  tokenAddress: string;
  isBuy: boolean;
  amount: number;
  slippage: number;
  priorityLevel: string;
}

let buyDefaults: TradeData = {
  tokenAddress: "testbuytoken",
  isBuy: true,
  amount: 0,
  slippage: 0.50,
  priorityLevel: "medium"
};

let sellDefaults: TradeData = {
  tokenAddress: "testselltoken",
  isBuy: false,
  amount: 100,
  slippage: 0.50,
  priorityLevel: "medium"
};

async function postToQueue(tradeData: TradeData) {
  try {
    const connection = await connect({
      protocol: 'amqp',
      hostname: process.env.RABBITMQ_HOSTNAME,
      port: process.env.RABBITMQ_PORT ? parseInt(process.env.RABBITMQ_PORT, 10) : undefined,
      username: process.env.RABBITMQ_USERNAME,
      password: process.env.RABBITMQ_PASSWORD,
      vhost: '/'
    });

    const channel = await connection.createChannel();
    const queue = String(process.env.RABBITMQ_QUEUE);

    await channel.assertQueue(queue, { durable: true });

    channel.sendToQueue(queue, Buffer.from(JSON.stringify(tradeData)), { persistent: true });
    console.log(`Posted to queue: ${queue}`, tradeData);

    await channel.close();
    await connection.close();
  } catch (error) {
    console.error('Error posting to queue:', error);
  }
}

async function checkSignals() {
  try {
    /*
    signal logic:
    - read data from database. table: buy_signals. columns: tokenAddress,amount,slippage,priorityLevel
    - trigger send to rabbitmq
    */
    console.log('Checking signals...');
    await postToQueue(buyDefaults);
    //check for buys from pumpfun.  compare with database data. if no data sell on mcatp 20% profit


    // const buySignalsResult = await queryDatabase("SELECT * FROM buy_signals WHERE condition = ?", ["positive"]);
    // const buySignals = buySignalsResult as any[];
    // for (const signal of buySignals) {
    //   const tradeData: TradeData = {
    //     tokenAddress: signal.tokenAddress,
    //     isBuy: true,
    //     amount: signal.amount,
    //     slippage: signal.slippage,
    //     priorityLevel: signal.priorityLevel
    //   };
    //   await postToQueue(tradeData);
    // }

  } catch (error) {
    console.error('Error checking signals:', error);
  }
}

setInterval(checkSignals, 1000); // Check every 1 minute
