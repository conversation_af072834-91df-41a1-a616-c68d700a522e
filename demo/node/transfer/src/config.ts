import dotenv from "dotenv";

dotenv.config();
export const MASTER_ACCOUNT = process.env.MASTER_ACCOUNT || "Gh9J24j688AfsoiZQJiUGYtxb8RwhcZPJsQtiAJtLFAQ";

export const discord_alerting = [
  {
    "account": "9H7GmRENevv3ikBfMxnCRm8hc1Dyrh3jPP7pNfbugm2H",
    "name": "9H7G",
    "minSol": 1000*1e9,
    "maxSol": 99999*1e9,   // 0 for no max
    "direction": "out", // in,out,both
    "discort": "https://discord.com/api/webhooks/1338978131305959434/2651AndXUZ1tTURsOQrAWDE-1PWkVt33rZrskUws_a9yQI0mJgnlOouiLmnpqrr-SoLe"
},
{
  "account": "DhLPHfDofyck4MQ55hAwv18YmSpemtEpDkg9V1RRGX74",
  "minSol": 1000*1e9,
  "name": "Dh<PERSON>",
  "maxSol": 99999*1e9,   // 0 for no max
  "direction": "out", // in,out,both
  "discort": "https://discord.com/api/webhooks/1338978243532816486/tO4fo76RIwrV8FCWa-A_x7fml-ZuEB9FlhEvCWkgd2-JNgY-go6ESINHwrAMJnpshkg0"
},
{
  "account": "5V2weYbsBpz2iG87oSnZbjyAC1BtcnWqyLodw4i8hob1",
  "minSol": 1000*1e9,
  "name": "5V2w",
  "maxSol": 99999*1e9,   // 0 for no max
  "direction": "out", // in,out,both
  "discort": "https://discord.com/api/webhooks/1339364294378389554/T3S62xrrVstRPW10d7qd25mQioUGGby4Qw5hrh0BK2PPT5VaHu_h1D0d5GniECY95ejt"
},
{
  "account": "6Lo9uik1YsHZqoQRMFv1BystuZwBnhmp4uHiKWRQvwpc",
  "minSol": 19*1e9,
  "name": "6Lo9",
  "maxSol": 99999*1e9,   // 0 for no max
  "direction": "out", // in,out,both
  "discort": "https://discord.com/api/webhooks/1339641779833798666/C5vT1saFX3Wo2PueWiNvRI80ODiitc8zvQVsvWkitUZGo_sO7CC3YGlgz5ceQz4iP0p3"
},
{
  "account": "Dcmc4bmp2PbtBwm8oMbenV9otGyx6HN7y7uYrcQorkaQ",
  "minSol": 1000*1e9,
  "name": "Dcmc",
  "maxSol": 99999*1e9,   // 0 for no max
  "direction": "out", // in,out,both
  "discort": "https://discord.com/api/webhooks/1339643020600934531/TrcT7LV3MEbfJlb6Cf9zCVkSYDM6UM1aPWyF2yKRVpR5zRipOTWmH_hzRKD2oFaRAjbM"
},
{
  "account": "8jsDQTsdWony38q77VdVJ2Z6JXTK3h2FFnSvuDDvV9pY",
  "minSol": 0,
  "name": "LIKE",
  "maxSol": 99999*1e9,   // 0 for no max
  "direction": "out", // in,out,both
  "discort": "https://discord.com/api/webhooks/1339648433019162688/vHSc6TubCYfmOyXdC1ojsbhDajYTdNE2i0Iom6MZRDSDxt5YXwH85vTbAYTrNDq24GC1"
},
{
  "account": "97RDdBRsJYW34DVhYotyEyT6J5j4oYWdNScSqUJxmcjG",
  "minSol": 1000*1e9,
  "name": "97RD",
  "maxSol": 99999*1e9,   // 0 for no max
  "direction": "out", // in,out,both
  "discort": "https://discord.com/api/webhooks/1339676460037705772/K1NU8lKWRB9gzYenHdm44Ai-EJGv9FJu2hwFwesRj2CjnaNysmCIqXfhVXfSvTKaWgfz"
},

{
  "account": "CtrGj1kyDYtZyRUdhMXWJQ2ecADx7BUQGEYegTKau9Zx",
  "minSol": 1000*1e9,
  "name": "CtrG",
  "maxSol": 99999*1e9,   // 0 for no max
  "direction": "out", // in,out,both
  "discort": "https://discord.com/api/webhooks/1339856552634617887/Vo8fz8ABPFPs_p0WmbaI9wtyhxea-KSXa2Fb1KCTKWyobPVOlyOm1N7069p1ujM2n2Re"
},
{
  "account": "XmY5MVLZT7UTEBF9xrru189KcQ9ByWGDYfc2cYJYWat",
  "minSol": 1000*1e9,
  "name": "XmY5",
  "maxSol": 99999*1e9,   // 0 for no max
  "direction": "out", // in,out,both
  "discort": "https://discord.com/api/webhooks/1340062974718644288/Bfb9IN81I-PUp1OkOZYBagpdMKeZBTZK_q9GOkggtRD_EYV-606onZV0f5HKS6kslTe_"
},

{
  "account": "CuRtuxmvMzB7yBV7BhUg1FuCDLM9YZgakmD2VtQMqTN8",
  "minSol": 1000*1e9,
  "name": "CuR",
  "maxSol": 99999*1e9,   // 0 for no max
  "direction": "out", // in,out,both
  "discort": "https://discord.com/api/webhooks/1340992985931714590/5vOvQ4aLePvSpC1Xm5hJQnyqGW6hTo9Iqq6Ss3CyHcOcEzAnLcbmxEk-_F6dbOXSEl4Q"
},
{
  "account": "Fuhqfu67TZBbxV83SWqMTquRmK7GbK3nstXsNKXPnTC9",
  "minSol": 1000*1e9,
  "name": "Fuhq",
  "maxSol": 99999*1e9,   // 0 for no max
  "direction": "out", // in,out,both
  "discort": "https://discord.com/api/webhooks/1340993384927465513/PMp68Q9fUTiS_WdELwUwsnjnYHqs8_NqB6eKYTVTcECIC-xjOWPt2o12Zrf7drD16Mth"
},
{
  "account": "Fd4B8c1QGH34ppjDtc78ZPyybEwXqRrvhudCBv3gVHJw",
  "minSol": 1000*1e9,
  "name": "Fd4B",
  "maxSol": 99999*1e9,   // 0 for no max
  "direction": "out", // in,out,both
  "discort": "https://discord.com/api/webhooks/1340994136823562260/YVag0mhzoa-dvKnABNgjKuttbN_Fd4VfS0DWST2WSIYbbYnAj1vRKqSQFe9iX4sTWJNG"
},
{
  "account": "6c5H5P61s5yH9ss5ENqPrf94JSsm5SKyrr8pmv3JtG9i",
  "minSol": 1000*1e9,
  "name": "6c5",
  "maxSol": 99999*1e9,   // 0 for no max
  "direction": "out", // in,out,both
  "discort": "https://discord.com/api/webhooks/1340994255036092456/3W45e3dPpyQY1kzaz20QAAnBFnUZrzrumfHKITzTdbKOKrhAKbC88d9X7VcxVSbSDe8b"
},
{
  "account": "39QS2CNMfKnjKxpQFJ88V5PkM4octvDrc7WMdd4YqBpF",
  "minSol": 1000*1e9,
  "name": "39QS",
  "maxSol": 99999*1e9,   // 0 for no max
  "direction": "out", // in,out,both
  "discort": "https://discord.com/api/webhooks/1340994376456867851/HnTiUd6qM3uDbZIJR0RtyxD2fNZlKD33L3rDuu7RWBNXZQDYqHgO1DiIhjjQnM6E8a6e"
},
{
  "account": "BWja6uQhncWx2JciDRV9rEJJPGioNZJyYSr65NhJCUsa",
  "minSol": 0.1*1e9,
  "name": "BWja",
  "maxSol": 2.5*1e9,   // 0 for no max
  "direction": "out", // in,out,both
  "discort": "https://discord.com/api/webhooks/1342196164107305011/PJDSexxbTB5u5Xank983dS8rplTczY-huomrXawu983XJwr4kPSSDPE65Qn64jqw9oc6"
},
{
  "account": "7Pu2aY65jDu1idV5DP4iNDxb7VhA1ZsLdoPCgPbv1puN",
  "minSol": 20*1e9,
  "name": "7Pu2",
  "maxSol": 5000*1e9,   // 0 for no max
  "direction": "out", // in,out,both
  "discort": "https://discord.com/api/webhooks/1347278577762172938/i-Jlf74On49711KhEhj6owLD5LDNio9D4h_fMryb8A6np220kVRRrp4ta7fwnNPL1fRP"
},


];


// export const appAccounts  = {
//   "Gh9J24j688AfsoiZQJiUGYtxb8RwhcZPJsQtiAJtLFAQ": "auto1",
//   "ChnVoEmTK54uWCfgLx2iZ8EUN6GSv4q6vyHCECdC2ik7": "auto2",
// }


export const  APP = process.env.APP || "auto1";
export const PRIVATE_KEY = process.env.PRIVATE_KEY || "";
export const WSS_URL = process.env.WSS_URL || "";
export const MYSQL_CONFIG = {
  host: process.env.MYSQL_HOST || ".xyz",
  user: process.env.MYSQL_USER || "",
  password: process.env.MYSQL_PASSWORD || "",
  database: process.env.MYSQL_DATABASE || "",
};

export const RPC_URL='https://solana-mainnet.g.alchemy.com/v2/********************************';

const SELL_CONDITIONS = {
  noSellTime: 120,  //seconds no sell. only if above minOwnerProfit met
  maxBuyBid: 0.49,  //Sell if above and meets other conditions after noSellTime 
  maxProfit: 70,   //Maximum profit level, sell if above 
  minOwnerProfit: 1.7, // within 2 mins
  fastMultiBuys: 0.9 // buys withint 1-3 seconds. check all buys for past 3 sec 
}

// TODO: collect data for fastMultibys 
 const WEBHOOK_SERVER='http://kroocoin.xyz:7007/webhook';

export const REDIS_URL="kroocoin.xyz";

export const ignoreAccounts = [
  "MWinVXj3HscKfwcdrJetvSgNFmEYPqC1NDxDd7vZqPd",
  "7SEFNefGeEuDLNzSzdQJfCC7oey1k8GcXQP8K78BMCaZ",
  "DPzd4nhKeMzwDqMKJdMkeDSswHHCVezKpcDC7tWhwPfF",
  "5YY4uFaKyGBZ6o7MTHDkCkv5wxnzUjmXAJczdR1CdMHk",

]