import {
  Keypair,
  PublicKey,
  SystemProgram,
  Transaction,
  TransactionInstruction,
  Connection,
} from "@solana/web3.js";
import bs58 from "bs58";
import { createAssociatedTokenAccountInstruction, getAssociatedTokenAddress } from '@solana/spl-token';
import getTokenData from "./coindata.js"; // Assuming getTokenData is in test.ts
import { GLOBAL, FEE_RECIPIENT, TOKEN_PROGRAM_ID, RENT, PUMP_FUN_ACCOUNT, PUMP_FUN_PROGRAM, SYSTEM_PROGRAM_ID,ASSOC_TOKEN_ACC_PROG } from "../index.js";
const LAMPORTS_PER_SOL = Number(1_000_000_000);

export async function getBuyInstruction(connection: Connection, privateKey: string, tokenAddress: string, amount: number, slippage: number = 0.25, txBuilder: Transaction) {
  const coinData = await getTokenData(tokenAddress);
  if (!coinData) {
    console.error('Failed to retrieve coin data...');
    return;
  }

  const walletPrivateKey = Keypair.fromSecretKey(bs58.decode(privateKey));
  const owner = walletPrivateKey.publicKey;
  const token = new PublicKey(tokenAddress);

  const tokenAccountAddress = await getAssociatedTokenAddress(token, owner, false);

  const tokenAccountInfo = await connection.getAccountInfo(tokenAccountAddress);

  let tokenAccount: PublicKey;
  if (!tokenAccountInfo) {
    txBuilder.add(
      createAssociatedTokenAccountInstruction(walletPrivateKey.publicKey, tokenAccountAddress, walletPrivateKey.publicKey, token)
    );
    tokenAccount = tokenAccountAddress;
  } else {
    tokenAccount = tokenAccountAddress;
  }

  const solInLamports = amount * LAMPORTS_PER_SOL;
  const tokenOut = Math.floor((solInLamports * coinData['virtual_token_reserves']) / coinData['virtual_sol_reserves']);

  const amountWithSlippage = amount * (1 + slippage);
  const maxSolCost = Math.floor(amountWithSlippage * LAMPORTS_PER_SOL);
  const ASSOCIATED_USER = tokenAccount;
  const USER = owner;
  const BONDING_CURVE = new PublicKey(coinData['bonding_curve']);
  const ASSOCIATED_BONDING_CURVE = new PublicKey(coinData['associated_bonding_curve']);

  const keys = [
    { pubkey: GLOBAL, isSigner: false, isWritable: false },
    { pubkey: FEE_RECIPIENT, isSigner: false, isWritable: true },
    { pubkey: token, isSigner: false, isWritable: false },
    { pubkey: BONDING_CURVE, isSigner: false, isWritable: true },
    { pubkey: ASSOCIATED_BONDING_CURVE, isSigner: false, isWritable: true },
    { pubkey: ASSOCIATED_USER, isSigner: false, isWritable: true },
    { pubkey: USER, isSigner: false, isWritable: true },
    { pubkey: SystemProgram.programId, isSigner: false, isWritable: false },
    { pubkey: TOKEN_PROGRAM_ID, isSigner: false, isWritable: false },
    { pubkey: RENT, isSigner: false, isWritable: false },
    { pubkey: PUMP_FUN_ACCOUNT, isSigner: false, isWritable: false },
    { pubkey: PUMP_FUN_PROGRAM, isSigner: false, isWritable: false }
  ];
  const data = Buffer.concat([bufferFromUInt64('16927863322537952870'), bufferFromUInt64(tokenOut), bufferFromUInt64(maxSolCost)]);

  const instruction = new TransactionInstruction({
    keys: keys,
    programId: PUMP_FUN_PROGRAM,
    data: data
  });

  return { instruction: instruction, tokenAmount: tokenOut };
}

export async function getSellInstruction(connection: Connection, privateKey: string, tokenAddress: string, tokenBalance: number, slippage: number = 0.25) {
  const coinData = await getTokenData(tokenAddress);
  if (!coinData) {
    console.error('Failed to retrieve coin data...');
    return;
  }

  const payer = Keypair.fromSecretKey(bs58.decode(privateKey));
  const owner = payer.publicKey;
  const mint = new PublicKey(tokenAddress);
  const txBuilder = new Transaction();

  const tokenAccountAddress = await getAssociatedTokenAddress(mint, owner, false);

  const tokenAccountInfo = await connection.getAccountInfo(tokenAccountAddress);

  let tokenAccount: PublicKey;
  if (!tokenAccountInfo) {
    txBuilder.add(createAssociatedTokenAccountInstruction(payer.publicKey, tokenAccountAddress, payer.publicKey, mint));
    tokenAccount = tokenAccountAddress;
  } else {
    tokenAccount = tokenAccountAddress;
  }

  const minSolOutput = Math.floor((tokenBalance * (1 - slippage) * coinData['virtual_sol_reserves']) / coinData['virtual_token_reserves']);

  const keys = [
    { pubkey: GLOBAL, isSigner: false, isWritable: false },
    { pubkey: FEE_RECIPIENT, isSigner: false, isWritable: true },
    { pubkey: mint, isSigner: false, isWritable: false },
    { pubkey: new PublicKey(coinData['bonding_curve']), isSigner: false, isWritable: true },
    { pubkey: new PublicKey(coinData['associated_bonding_curve']), isSigner: false, isWritable: true },
    { pubkey: tokenAccount, isSigner: false, isWritable: true },
    { pubkey: owner, isSigner: false, isWritable: true },
    { pubkey: SYSTEM_PROGRAM_ID, isSigner: false, isWritable: false },
    { pubkey: ASSOC_TOKEN_ACC_PROG, isSigner: false, isWritable: false },
    { pubkey: TOKEN_PROGRAM_ID, isSigner: false, isWritable: false },
    { pubkey: PUMP_FUN_ACCOUNT, isSigner: false, isWritable: false },
    { pubkey: PUMP_FUN_PROGRAM, isSigner: false, isWritable: false }
  ];



  const data = Buffer.concat([bufferFromUInt64('12502976635542562355'), bufferFromUInt64(tokenBalance), bufferFromUInt64(minSolOutput)]);

  const instruction = new TransactionInstruction({
    keys: keys,
    programId: PUMP_FUN_PROGRAM,
    data: data
  });

  return instruction;
}

export function bufferFromUInt64(value: number | string) {
  let buffer = Buffer.alloc(8);
  buffer.writeBigUInt64LE(BigInt(value));
  return buffer;
}
