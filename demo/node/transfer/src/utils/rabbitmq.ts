
import amqp from 'amqplib/callback_api';
import dotenv from 'dotenv';


dotenv.config();

const RABBITMQ_SERVER = process.env.RABBITMQ_HOSTNAME || '';
const RABBITMQ_QUEUE = process.env.RABBITMQ_QUEUE || 'snipes';

export async function sendRabbitMessage(message: string, queue: string = RABBITMQ_QUEUE) {
  amqp.connect({
    protocol: 'amqp',
    hostname: RABBITMQ_SERVER,
    port: parseInt(process.env.RABBITMQ_PORT || '5672'),
    username: process.env.RABBITMQ_USERNAME,
    password: process.env.RABBITMQ_PASSWORD,
  }, (error0, connection) => {
    if (error0) {
      throw error0;
    }
    connection.createChannel((error1, channel) => {
      if (error1) {
        throw error1;
      }
      channel.assertQueue(queue, {
        durable: true
      });

      channel.sendToQueue(queue, Buffer.from(message), {
        persistent: true
      });
      console.log(" [x] Sent %s", message);
    });
  });
}