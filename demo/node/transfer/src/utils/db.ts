import mysql from "mysql2/promise";
import { MYSQL_CONFIG } from "../config";


export async function getSolPrice() {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  const [results]: any = await connection.execute('SELECT sol_price FROM configs  LIMIT 1');
  await connection.end();
  return results[0].sol_price;
}


export async function getRuggerAccounts() {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  const [results]: any = await connection.execute('SELECT address FROM accounts WHERE app = "auto1"  ');
  await connection.end();
  return results.map((row: any) => row.address);
}

export async function getAllRuggerAccounts() {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  const [results]: any = await connection.execute('SELECT address, app FROM accounts WHERE app LIKE "auto%"');
  await connection.end();
  return results.map((row: any) => ({ address: row.address, app: row.app }));
}
export type AddressApp = { address: string; app: string,balance: number, last_sig: string };

export async function getEnabledAccounts(app: string) {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  const [results]: any = await connection.execute('SELECT address, app,balance, last_sig FROM accounts WHERE app = ? and enabled = 1 and status = "update" ', [app]);
  await connection.end();
  return results.map((row: AddressApp) => ({ address: row.address, app: row.app, balance: row.balance, last_sig: row.last_sig }));
}

export async function disableAccount(address: string) {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  await connection.execute('UPDATE accounts SET enabled = 0 WHERE address = ?', [address]);
  await connection.end();
}


export async function getMintStatus(mint: string) {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  const [results]: any = await connection.execute('SELECT trade_status FROM rugger_mints WHERE mint= ? LIMIT 1', [mint]);
  await connection.end();
  return results[0].trade_status;
}

export async function setBuyCouner(mint: string,buy_counter: number) {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  await connection.execute('UPDATE rugger_mints SET buy_counter = ? WHERE mint = ?', [buy_counter, mint]);
  await connection.end();
}

export async function updateAccountStatus(address: string,status: string) {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  await connection.execute('UPDATE accounts SET status = ? WHERE address = ?', [status, address]);
  await connection.end();
}

export async function updateAccountSignature(address: string,last_sig: string) {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  await connection.execute('UPDATE accounts SET last_sig = ? WHERE address = ?', [last_sig,address]);
  await connection.end();
}


// set treade status
export async function setMintStatus(mint: string, status: string) {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  await connection.execute('UPDATE rugger_mints SET trade_status = ? WHERE mint = ?', [status, mint]);
  await connection.end();
}

export async function updatMintTradeDataBuy(mint: string, status: string, buy_sol_amount: number, buy_token_amount: number,tx : string,buy_mc : number) {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  await connection.execute('UPDATE rugger_mints SET trade_status = ?,buy_sol_amount = ?, buy_token_amount = ?,buy_tx = ?,buy_mc = ?  WHERE mint = ?', [status,buy_sol_amount,buy_token_amount, tx,buy_mc, mint]);
  await connection.end();
}

export async function updatMintTradeDataSell(mint: string, status: string, buy_token_amount: number, sell_token_amount: number,tx : string,sell_mc : number) {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  await connection.execute('UPDATE rugger_mints SET trade_status = ?,buy_sol_amount = ?, buy_token_amount = ?,sell_tx = ?, sell_mc = ?  WHERE mint = ?', [status,buy_token_amount,sell_token_amount,tx,sell_mc, mint]);
  await connection.end();
}

export async function updatMintBuyTokens(mint: string, buy_token_amount: number) {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  await connection.execute('UPDATE rugger_mints SET buy_token_amount = ?  WHERE mint = ?', [buy_token_amount, mint]);
  await connection.end();
}
export async function updateMaxMC(mint: string, max_mc: number) {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  await connection.execute('UPDATE rugger_mints SET max_mc = ?  WHERE mint = ?', [max_mc, mint]);
  await connection.end();
}

export async function updateLastSoldTime(mint: string) {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  await connection.execute('UPDATE rugger_mints SET last_sold_time = ?  WHERE mint = ?', [new Date(), mint]);
  await connection.end();
}


export async function updatMintBuyTx(mint: string, buy_tx: string) {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  await connection.execute('UPDATE rugger_mints SET buy_tx = ?  WHERE mint = ?', [buy_tx, mint]);
  await connection.end();
}


export async function getAccountAuto2() {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  const [results]: any = await connection.execute('SELECT address FROM accounts WHERE app = "auto2" order by disovery_account_ts desc limit 1  ');
  await connection.end();
  return results[0].address;
}

export async function getAccountAuto3() {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  const [results]: any = await connection.execute('SELECT address FROM accounts WHERE app = "auto3" order by disovery_account_ts desc limit 1  ');
  await connection.end();
  return results[0].address;
}

export async function getTimeSinceLastSold(mint:string) {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  const [results]: any = await connection.execute('SELECT last_sold_time FROM rugger_mints WHERE mint = ? limit 1 ', [mint]);
  await connection.end();
  const last_sold_time=results[0].last_sold_time;
  const now = new Date();
  const lastSoldTime = new Date(last_sold_time);
  const timeSinceLastSold = Math.floor((now.getTime() - lastSoldTime.getTime()) / 1000);
  return timeSinceLastSold;
}


export async function addMint(owner: string , mint: string, appType: string) {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  await connection.execute('INSERT INTO rugger_mints (rugger, mint, buy_sol_amount, buy_token_amount, sell_sol_amount, sell_token_amount, buy_tx, sell_tx, buy_mc, sell_mc, created_at, app) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)', [owner, mint, 0, 0, 0, 0, 0, 0, 0, 0, new Date(), appType]);
  await connection.end();
}

export async function getMint(mint: string) {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  const [results]: any = await connection.execute('SELECT mint FROM rugger_mints where mint = ? limit 1',[mint]);
  await connection.end();
  if (results.length === 0) {
    return null;
  }
  return results[0].mint;
}


export async function addAppAccountToDB(address: string, sol_amount:number, app: string,sig: string) {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  await connection.execute('INSERT INTO accounts (master, address, balance, status, scan, scanned, distance, app, disovery_account_ts, last_sig) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE disovery_account_ts = VALUES(disovery_account_ts)', [address, address, 99999, "new", 1, 0, 10, app, new Date(), sig]);
 
  await connection.end();
}



export async function addAppAccountToDBWithName(address: string, sol_amount:number, app: string,sig: string,name: string) {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  await connection.execute('INSERT INTO accounts (master, address, balance, status, scan, scanned, distance, app, disovery_account_ts, last_sig,name,enabled) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?,?,?) ON DUPLICATE KEY UPDATE disovery_account_ts = VALUES(disovery_account_ts)', [address, address, 99999, "new", 1, 0, 10, app, new Date(), sig,name,1]);
 
  await connection.end();
}

export async function updateAddressName(address: string, name: string) {
  const connection = await mysql.createConnection(MYSQL_CONFIG);
  await connection.execute('UPDATE accounts SET name = ?  WHERE address = ?', [name, address]);
  await connection.end();
}

export async function getMintTradeData(mint: string) {
  const connection = await mysql.createConnection(MYSQL_CONFIG);

  const query = `
    SELECT 
        t.mint,
        rm.trade_status,
        rm.buy_token_amount,
        SUM(CASE WHEN t.trade = 'buy' THEN t.sol_amount/1e9 ELSE 0 END) AS total_buys,
        SUM(CASE WHEN t.trade = 'sell' THEN t.sol_amount/1e9 ELSE 0 END) AS total_sells,
        (SELECT market_cup 
         FROM token_tx 
         WHERE token_tx.mint = t.mint 
         ORDER BY block_time DESC 
         LIMIT 1) AS last_market_cup,
        MAX(t.block_time) - MIN(t.block_time) AS time_diff_seconds,
        COUNT(*) AS count,
        SUM(CASE WHEN t.trade = 'buy' THEN t.sol_amount/1e9 ELSE 0 END) -  
        SUM(CASE WHEN t.trade = 'sell' THEN t.sol_amount/1e9 ELSE 0 END) AS cumulative_diff,
        rm.buy_tx,
        rm.sell_tx,
        rm.app,
        MAX(t.market_cup) AS max_market_cup,
        rm.buy_counter
    FROM 
        token_tx t
    JOIN 
        rugger_mints rm ON t.mint = rm.mint
    WHERE  
        t.signer != 'Gh9J24j688AfsoiZQJiUGYtxb8RwhcZPJsQtiAJtLFAQ'
        AND t.mint = ?
    GROUP BY 
        t.mint, rm.trade_status 
    LIMIT 1
  `;

  try {
    const [rows]: [any[], any] = await connection.execute(query, [mint]);

    await connection.end();

    // If no data is found, return null
    if (rows.length === 0) {
      return null;
    }

    // Convert result row into an object
    return rows[0];
  } catch (error) {
    console.error("Database query failed:", error);
    await connection.end();
    throw error; // Rethrow error for better error handling
  }
}
  



export async function getMintTradeDataMonitor(mint: string) {
  const connection = await mysql.createConnection(MYSQL_CONFIG);

  const query = `
        SELECT 
          account,
          sol_amount,
          trade,
          market_cup,
          block_time,
          -- Cumulative sum of buys
          SUM(CASE WHEN trade = 'buy' THEN sol_amount ELSE 0 END) 
              OVER (ORDER BY block_time ASC, account ASC ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW) AS buy_sum,
          -- Cumulative sum of sells
          SUM(CASE WHEN trade = 'sell' THEN sol_amount ELSE 0 END) 
              OVER (ORDER BY block_time ASC, account ASC ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW) AS sell_sum,
          -- Difference between buy_sum and sell_sum
          SUM(CASE 
                  WHEN trade = 'buy' THEN sol_amount 
                  WHEN trade = 'sell' THEN -sol_amount 
                  ELSE 0 
              END) 
              OVER (ORDER BY block_time ASC, account ASC ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW) AS trade_diff
      FROM 
          token_tx t
      WHERE  
          t.signer != 'Gh9J24j688AfsoiZQJiUGYtxb8RwhcZPJsQtiAJtLFAQ'
          AND t.mint = ?
      ORDER BY block_id desc, account ASC;

  `;

  try {
    const [rows]: [any[], any] = await connection.execute(query, [mint]);

    await connection.end();

    // If no data is found, return null
    if (rows.length === 0) {
      return null;
    }

    // Convert result row into an object
    return rows;
  } catch (error) {
    console.error("Database query failed:", error);
    await connection.end();
    throw error; // Rethrow error for better error handling
  }
}
  