import axios from 'axios';

/**
 * Sends a message to a Discord channel using a webhook URL.
 * @param {string} WEBHOOK_URL - The Discord webhook URL.
 * @param {string} message - The message to send.
 */
export async function sendToDiscord(WEBHOOK_URL: string, message: string): Promise<void> {
    try {
        const payload = {
            content: message
        };

        const response = await axios.post(WEBHOOK_URL, payload, {
            headers: {
                'Content-Type': 'application/json'
            }
        });

        if (response.status === 204) {
            console.log('Message sent successfully');
        } else {
            console.error('Failed to send message', response.status, response.statusText);
        }
    } catch (error) {
        console.error('Error sending message to Discord', error);
    }
}