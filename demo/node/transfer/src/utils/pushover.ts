import axios from 'axios';

// {
//   pushover_token: "arbs7ccq98rqoz78pagtcqyc523hro",
//   pushover_api: "utmxhixkgtchrdahx3tfj6sxgfkbw5"
// },
const pushover = [

  {
    pushover_token: "af5c3jtswdoan29x92sycyjqz76rbq",
    pushover_api: "ug7adwprvzzmzo133c678cz85vpc9m"
  }
];

export async function sendPushoverMessage(message: string): Promise<void> {
  for (const push of pushover) {
    try {
      const response = await axios.post('https://api.pushover.net/1/messages.json', {
        token: push.pushover_token,
        user: push.pushover_api,
        message: message
      });
      console.log('Pushover response:', response.data);
    } catch (error) {
      console.error('Error sending Pushover message:', error);
    }
  }
}

