import Fastify from 'fastify';
import mysql from 'mysql2/promise';
import path from 'path';
import { fileURLToPath } from 'url';

const app = Fastify();


// MySQL database credentials
const MYSQL_SERVER = 'kroocoin.xyz';
const MYSQL_PORT = 3306;
const MYSQL_USER = 'pump2';
const MYSQL_PASSWORD = 'pump2';
const MYSQL_DB = 'pump';

// Create a MySQL connection pool
const pool = mysql.createPool({
  host: MYSQL_SERVER,
  port: MYSQL_PORT,
  user: MYSQL_USER,
  password: MYSQL_PASSWORD,
  database: MYSQL_DB,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
});

app.register(require('@fastify/static'), {
  root: path.join(__dirname, 'public'),
  prefix: '/public/',
});

app.get('/', async (request, reply) => {
  reply.type('text/html').send(`
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Web Panel</title>
      <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
      <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap-dark.min.css" rel="stylesheet">
      <style>
        body {
          background-color: #343a40;
          color: #ffffff;
        }
        table {
          width: 100%;
        }
        th, td {
          text-align: center;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <h1 class="my-4">Web Panel</h1>
        <div id="content"></div>
      </div>
      <script>
        async function fetchData() {
          const response = await fetch('/data');
          const data = await response.json();
          document.getElementById('content').innerHTML = data.html;
        }
        setInterval(fetchData, 1000);
        fetchData();
      </script>
    </body>
    </html>
  `);
});

app.get('/data', async (request, reply) => {
  const connection = await pool.getConnection();
  try {
    const [ruggerMints] = await connection.query<any[]>('SELECT * FROM rugger_mints ORDER BY id DESC LIMIT 10');
    const [tokenTx] = await connection.query<any[]>('SELECT * FROM token_tx ORDER BY id DESC LIMIT 10');
    const [accounts] = await connection.query<any[]>('SELECT * FROM accounts ORDER BY id DESC LIMIT 10');

    const ruggerMintsTable = generateTable('Latest Created Tokens', ruggerMints);
    const tokenTxTable = generateTable('Latest Transactions', tokenTx);
    const accountsTable = generateTable('Latest Accounts', accounts);

    const html = `
      ${ruggerMintsTable}
      ${tokenTxTable}
      ${accountsTable}
    `;

    reply.send({ html });
  } catch (error) {
    console.error('Error fetching data:', error);
    reply.status(500).send('Internal Server Error');
  } finally {
    connection.release();
  }
});

function generateTable(title: string, data: any[]): string {
  if (data.length === 0) return `<h2>${title}</h2><p>No data available.</p>`;

  const headers = Object.keys(data[0]);
  const rows = data.map(row => `
    <tr>
      ${headers.map(header => `<td>${row[header]}</td>`).join('')}
    </tr>
  `).join('');

  return `
    <h2>${title}</h2>
    <table class="table table-dark table-striped table-bordered">
      <thead>
        <tr>
          ${headers.map(header => `<th>${header}</th>`).join('')}
        </tr>
      </thead>
      <tbody>
        ${rows}
      </tbody>
    </table>
  `;
}

const start = async () => {
  try {
    await app.listen({ port: 3000, host: '0.0.0.0' });
    console.log('Server is running on http://0.0.0.0:3000');
  } catch (err) {
    app.log.error(err);
    process.exit(1);
  }
};

start();
