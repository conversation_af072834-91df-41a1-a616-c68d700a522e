import { getAccountAuto2 } from "./src/utils/db"; 
import { Connection, PublicKey } from '@solana/web3.js';
import dotenv from 'dotenv';
import { PRIVATE_KEY,RPC_URL, REDIS_URL } from './src/config'; // Corrected import path
import { addAppAccountToDB } from './src/utils/db';
import e from "express";
import { getRedisClient, closeRedisConnection, addSetRedis } from './redis';
import { get } from "http";

dotenv.config();



async function getTransactions(accountAddress: string) {
    const connection = new Connection(RPC_URL);
    const limit = 100;
    const publicKey = new PublicKey(accountAddress);
    const before =  undefined;
    const signatures = await connection.getSignaturesForAddress(publicKey, { before, limit: limit });
    const promises = [];
    for (const signature of signatures) {
        const transaction = await connection.getTransaction(signature.signature, {
            maxSupportedTransactionVersion: 0,
          });
          if (transaction) {
            // Skip transactions that are in an error state
            if (transaction.meta && transaction.meta.err) {
              continue;
            }
            if ( transaction?.meta?.postTokenBalances  ){
                getTransaction(signature.signature);
            }
        }
    }
}

async function getTransaction(signature: string) {
    const connection = new Connection(RPC_URL);
 
        const transaction = await connection.getTransaction(signature, {
            maxSupportedTransactionVersion: 0,
          });
          const transaction0 = await connection.getParsedTransaction(signature, {
            maxSupportedTransactionVersion: 0,
          });
     
          //console.log('Transaction:',transaction);
/// RULE:1  get token address with ********** amount and owner is not signer

        if (transaction?.meta?.postTokenBalances) {
            const accountKeys = transaction.transaction.message.staticAccountKeys.map(key => key.toBase58());
            const keys = accountKeys.map(key => key);
            const signer = keys[0];
            for ( const postTokenBalance of transaction?.meta?.postTokenBalances) {


                if (postTokenBalance.uiTokenAmount.uiAmount == ********** && postTokenBalance.owner != signer) {
                    console.log('Transaction:',signature);
                    console.log('mint:',postTokenBalance.mint);
                    console.log('owner:',postTokenBalance.owner);
                    console.log('uiAmount:',postTokenBalance.uiTokenAmount.uiAmount);
                }
            }   
        }

// RULE: 2 get manu account sending aout over 1 sol 
        if (transaction && transaction?.meta?.postTokenBalances?.length == 0 && transaction?.meta?.preTokenBalances?.length == 0 && transaction?.meta?.postBalances) {

                const accountKeys = transaction.transaction.message.staticAccountKeys.map(key => key.toBase58());
                const keys = accountKeys.map(key => key);
                const signer = keys[0];
                const postBalances =transaction?.meta?.postBalances;
                //console.log('Transaction:',postBalances);
                //console.log('Transaction:',keys);
                const balanceCounts = postBalances.reduce<{ [key: number]: number }>((acc, balance) => {
                    if (balance > ********** && balance < ***********) {
                        acc[balance] = (acc[balance] || 0) + 1;
                    }
                    return acc;
                }, {});

                const accountsWithBalance = Object.keys(balanceCounts).filter((balance: string) => balanceCounts[Number(balance)] > 1);
                const accounts = accountsWithBalance.map(balance => {
                    const indexes = [];
                    let idx = postBalances.indexOf(Number(balance));
                    while (idx != -1) {
                        indexes.push(idx);
                        idx = postBalances.indexOf(Number(balance), idx + 1);
                    }
                    return indexes.map(index => keys[index]);
                    
                });
                //const redisData: string [] = await getRedis(redis,"auto3");
                //let redisSet = new Set(redisData);
                if ( accounts.length > 0) {
                    const redis = getRedisClient();
                    try {
                        for (const account of accounts[0]) {
                            await addSetRedis(redis, "auto3b", account);
                            console.log('Added account to Redis:', account);
                        }
                    } catch (error) {
                        console.error('Error adding accounts to Redis:', error);
                    }
                }
                



        }
                
        
    
}



async function getAccountBalance(account: string) {
    const connection = new Connection(RPC_URL);
    const publicKey = new PublicKey(account);
    const accountInfo = await connection.getAccountInfo(publicKey);
    try {

        if (accountInfo) {
            const balance = accountInfo.lamports;
            return balance;
        } else {
            return 0;
        }
    }  
    catch (error) {
        return 0;
    }
}

 
(async function main() {
    try {
    
      // Use accountAddress from process.argv
      const cmd = process.argv[2] ;
      const address = process.argv[3] ;
  
     if ( cmd == 'tx') {
        await getTransaction(address);
     } else if ( cmd == 'address') {
        await getTransactions(address);
     }else {
        console.log('Invalid command');
     }
  
    } catch (error) {
      console.error('Error starting monitor:', error);
    } finally {
        await closeRedisConnection();
        }
    
  })();