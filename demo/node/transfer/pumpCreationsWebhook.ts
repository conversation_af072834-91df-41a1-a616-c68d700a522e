import Fastify from 'fastify';
import mysql from 'mysql2/promise';
import amqp from 'amqplib/callback_api';
import dotenv from 'dotenv';
import { MASTER_ACCOUNT, APP, discord_alerting } from "./src/config.js";
import fs from 'fs';
import path from 'path';
import { sendToDiscord } from './src/utils/discord.js';
import { sendPushoverMessage } from './src/utils/pushover.js';
import { getRuggerAccounts, getAllRuggerAccounts } from './src/utils/db.js';
import { Redis } from 'ioredis';
import {  getRedis } from './redis';
import { 
  REDIS_URL,
} from './src/config.js';

let redis: Redis;

const WEBHOOK = "https://discord.com/api/webhooks/1339217907380523040/tdXh3hfzR-AJNgGNYupg31BKirR1KoDV8mj937I7DAk1OqqWlNpinLl2O0TfawVVyG9K";

redis = new Redis({host: REDIS_URL, port: 6379, password: "pump2pump"});

dotenv.config();

const fastify = Fastify({ logger: false });

const pool = mysql.createPool({
  host: process.env.MYSQL_HOST,
  user: process.env.MYSQL_USER,
  password: process.env.MYSQL_PASSWORD,
  database: process.env.MYSQL_DATABASE,
});

const RABBITMQ_SERVER = process.env.RABBITMQ_HOSTNAME || '';
const RABBITMQ_QUEUE = process.env.RABBITMQ_QUEUE || 'snipes';

function sendMessage(message: string, queue: string = RABBITMQ_QUEUE) {
  amqp.connect({
    protocol: 'amqp',
    hostname: RABBITMQ_SERVER,
    port: parseInt(process.env.RABBITMQ_PORT || '5672'),
    username: process.env.RABBITMQ_USERNAME,
    password: process.env.RABBITMQ_PASSWORD,
  }, (error0, connection) => {
    if (error0) {
      throw error0;
    }
    connection.createChannel((error1, channel) => {
      if (error1) {
        throw error1;
      }
      channel.assertQueue(queue, {
        durable: true
      });

      channel.sendToQueue(queue, Buffer.from(message), {
        persistent: true
      });
      console.log(" [x] Sent %s", message);
    });
  });
}

fastify.post('/webhook', async (request, reply) => {
  const data = request.body as any;

  const description = data[0].description;

  const sig = data[0].signature;
  const type = data[0].type;
  const timestamp = data[0].timestamp;
  const slot = data[0].slot;
  const owner = data[0].feePayer;

  try {
    //const accounts = await getAllRuggerAccounts();
    //const accounts = await getRedis(redis, "auto1");
    //const accountList: string[] = accounts.map((account: { address: string }) => account.address);
    const accountList = await getRedis(redis, "auto1");
    if (accountList.includes(owner)) {
      //const appType = accounts.find((account: { address: string, app: string }) => account.address === owner)?.app || 'app9999';
      const appType = "auto1";
      const currentDate = new Date().toISOString();
      console.log(`[${currentDate}] New transaction: ${sig} ${type} ${timestamp} ${slot} ${owner} ${appType}`);
      for (const token of data[0].tokenTransfers) {
        if (token.mint !== "So11111111111111111111111111111111111111112") {
          let mint = token.mint;
          const currentTime = new Date().toLocaleTimeString('en-US', { hour12: false });
          console.log(`${currentTime} mint: ${mint}  }`);

          await pool.query('INSERT INTO rugger_mints (rugger, mint, buy_sol_amount, buy_token_amount, sell_sol_amount, sell_token_amount, buy_tx, sell_tx, buy_mc, sell_mc, created_at, app) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)', [owner, mint, 0, 0, 0, 0, 0, 0, 0, 0, new Date(), appType]);

          sendMessage(JSON.stringify({ "mint": token.mint, "timestamp": timestamp, "app": appType }), 'snipes');
          sendToDiscord(WEBHOOK, `New token: ${mint} ${data[0].signature} ${currentDate}`);

          break;
        }
      }
    }
  } catch (error) {
    console.error('Failed to retrieve Rugger accounts:', error);
  }

  return reply.send({ status: "success" });
});

fastify.listen({ port: 7008, host: '0.0.0.0' }, (err, address) => {
  if (err) {
    fastify.log.error(err);
    process.exit(1);
  }
  fastify.log.info(`Server is running on ${address}`);
});