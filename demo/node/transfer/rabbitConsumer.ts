import { spawn } from 'child_process';
import path from 'path';
import amqp from 'amqplib/callback_api';
import dotenv from 'dotenv';
import { setMintStatus, getMintStatus } from './src/utils/db.js';

dotenv.config();

const RABBITMQ_SERVER = process.env.RABBITMQ_HOSTNAME || '';
const RABBITMQ_QUEUE = process.env.RABBITMQ_QUEUE || 'snipes';
const RABBITMQ_PORT = parseInt(process.env.RABBITMQ_PORT || '5672');
const RABBITMQ_USERNAME = process.env.RABBITMQ_USERNAME || '';
const RABBITMQ_PASSWORD = process.env.RABBITMQ_PASSWORD || '';
const txScript = path.join(__dirname, 'test', 'tx-process.js');

const seenMessages = new Set<string>();

amqp.connect({
  protocol: 'amqp',
  hostname: RABBITMQ_SERVER,
  port: RABBITMQ_PORT,
  username: RABBITMQ_USERNAME,
  password: RABBITMQ_PASSWORD,
}, (error0, connection) => {
  if (error0) {
    throw error0;
  }
  connection.createChannel((error1, channel) => {
    if (error1) {
      throw error1;
    }
    channel.assertQueue(RABBITMQ_QUEUE, {
      durable: true
    });

    console.log(` [*] Waiting for messages in ${RABBITMQ_QUEUE}. To exit press CTRL+C`);

    channel.consume(RABBITMQ_QUEUE, async (msg) => {
      if (msg !== null) {
        const content = msg.content.toString();
        const message = JSON.parse(content);
        const accountAddress = message.mint;
        const app = message.app;
        
        const timestamp = message.timestamp;
        //current timestamp 
        const now = new Date().getTime();
       
        // skip if time difference more than 60 seconds
        if (now - timestamp * 1000 > 60000) {
          console.log(` [x] Received ${content} but skipped due to time difference: ${now - timestamp * 1000}`);
          channel.ack(msg);
          return;
        }

        if (seenMessages.has(content)) {
          console.log(`Duplicate message: ${content} - skipping`);
          channel.ack(msg);
          return;
        }
        seenMessages.add(content);

        console.log(` [x] Received ${content}`);

        // Check if the mint is already being processed
        const mintStatus = await getMintStatus(accountAddress);
        if (mintStatus === 'active') {
          console.log(`Mint ${accountAddress} already processed`);
          channel.ack(msg);
          return;
        } else {
          await setMintStatus(accountAddress, 'active');
          
        }

        // Spawn a new process for each message
        console.log(`Spawning process for ${accountAddress}`);
        const monitorProcess = spawn('node', [txScript, accountAddress,app], {
          stdio: ['pipe', 'pipe', 'pipe']
        });

        // Acknowledge right away so we can process in parallel
        channel.ack(msg);
        
        monitorProcess.stdout?.on('data', (data) => {
          console.log(`Monitor stdout: ${data}`);
        });

        monitorProcess.stderr?.on('data', (data) => {
          console.error(`Monitor stderr: ${data}`);
        });

        monitorProcess.on('close', async (code) => {
          console.log(`Monitor process exited with code ${code}`);

          //await setMintStatus(accountAddress, 'inactive');
        });
      }
    }, {
      noAck: false
    });
  });
});
