import solanaWeb3 from "@solana/web3.js";
import dotenv from "dotenv";
import bs58 from "bs58";
import fs from "fs";
import { Command } from "commander";

dotenv.config();

const {
  pipe,
  Connection,
  Keypair,
  Transaction,
  sendAndConfirmTransaction,
  SystemProgram,
  LAMPORTS_PER_SOL,
} = solanaWeb3;

const connection = new Connection("https://mainnet.helius-rpc.com/?api-key=cf3aa81f-7796-401b-a170-5567272f5f65", "confirmed");

const program = new Command();

program
  .command("create")
  .description("Create a new account")
  .action(async () => {
    const space = 0; // any extra space in the account
    const rentLamports = await connection.getMinimumBalanceForRentExemption(space);
    console.log("Minimum balance for rent exception:", rentLamports);

    // Load the signer from the private key in the .env file
    const privateKey = bs58.decode(process.env.PRIVATE_KEY);
    const signer = Keypair.fromSecretKey(privateKey);

    // Check the balance of the signer
    const balance = await connection.getBalance(signer.publicKey);
    console.log("Signer balance:", balance / LAMPORTS_PER_SOL, "SOL");

    if (balance < rentLamports) {
      throw new Error("Insufficient funds in the signer account.");
    }

    // generate a new keypair and address to create
    const newAccountKeypair = Keypair.generate();
    console.log("New account address:", newAccountKeypair.publicKey.toBase58());

    // Save the private key of the newly created account to a file
    const newAccountPrivateKey = bs58.encode(newAccountKeypair.secretKey);
    fs.writeFileSync(`account-${newAccountKeypair.publicKey.toBase58()}.txt`, newAccountPrivateKey);

    const { blockhash } = await connection.getRecentBlockhash();

    const transaction = new Transaction().add(
      SystemProgram.createAccount({
        fromPubkey: signer.publicKey,
        newAccountPubkey: newAccountKeypair.publicKey,
        lamports: rentLamports,
        space: space,
        programId: SystemProgram.programId,
      })
    );

    transaction.feePayer = signer.publicKey;
    transaction.recentBlockhash = blockhash;

    // Sign the transaction with the signers
    transaction.sign(signer, newAccountKeypair);

    const signature = await sendAndConfirmTransaction(connection, transaction, [signer, newAccountKeypair]);

    console.log("Signature:", signature);
  });

program
  .command("close <accountName>")
  .description("Close an account")
  .action(async (accountName) => {
    const privateKey = bs58.decode(process.env.PRIVATE_KEY);
    const signer = Keypair.fromSecretKey(privateKey);

    const accountPublicKey = new solanaWeb3.PublicKey(accountName);

    const transaction = new Transaction().add(
      SystemProgram.transfer({
        fromPubkey: accountPublicKey,
        toPubkey: signer.publicKey,
        lamports: await connection.getBalance(accountPublicKey),
      })
    );

    transaction.feePayer = signer.publicKey;
    const { blockhash } = await connection.getRecentBlockhash();
    transaction.recentBlockhash = blockhash;

    // Sign the transaction with the signer
    transaction.sign(signer);

    const signature = await sendAndConfirmTransaction(connection, transaction, [signer]);

    console.log("Account closed. Signature:", signature);
  });

program.parse(process.argv);