import { Connection, PublicKey, ParsedTransactionWithMeta } from '@solana/web3.js';
import * as bs58 from 'bs58';
import { TokenInfo, TokenListProvider } from '@solana/spl-token-registry';
import { RPC_URL } from './src/config'; // Corrected import path
import { isMemberOfSet } from './redis';
import { REDIS_URL } from './src/config';
import Redis from "ioredis";
// Raydium program ID

let redis : Redis;

const RAYDIUM_SWAP_PROGRAM_ID = '675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8';

interface Token {
  symbol: string;
  decimals: number;
  address: string;
}

interface SwapInfo {
  fromToken: Token;
  toToken: Token;
  fromAmount: number;
  toAmount: number;
}

class SolanaSwapParser {
  private connection: Connection;
  private tokenMap: Map<string, TokenInfo> = new Map();

  constructor(rpcEndpoint: string) {
    this.connection = new Connection(rpcEndpoint, 'confirmed');
  }

  // Get token info by address
  private getTokenInfo(address: string): Token {
    const tokenInfo = this.tokenMap.get(address);
    
    if (tokenInfo) {
      return {
        symbol: tokenInfo.symbol,
        decimals: tokenInfo.decimals,
        address
      };
    }
    
    // Default for unknown tokens
    return {
      symbol: address.slice(0, 4) + '...' + address.slice(-4),
      decimals: 9,
      address
    };
  }

  // Parse transaction
  async parseTransaction(redis:Redis,txSignature: string,tokenCA: string): Promise<string | null> {
    try {
      // Fetch transaction data
      const txInfo = await this.connection.getParsedTransaction(txSignature, {
        maxSupportedTransactionVersion: 0,
      });
      
      if (!txInfo) {
        return 'Transaction not found';
      }
      
      // Check if it's a Raydium swap
      if (!this.isRaydiumSwap(txInfo)) {
        return null;
      }
      
      // Extract swap information
      const swapInfo = await this.extractSwapInfo(txInfo);
      if (!swapInfo) {
        return null;
      }
      
      // Format output
    const fromAmount = swapInfo.fromAmount / Math.pow(10, swapInfo.fromToken.decimals);
    const toAmount = swapInfo.toAmount / Math.pow(10, swapInfo.toToken.decimals);
    
    const solToken="So11111111111111111111111111111111111111112";
    const ownerAddress = txInfo.transaction.message.accountKeys[0].pubkey.toBase58();
    
    const isMember = await isMemberOfSet(redis,"auto3b", ownerAddress);

    const tradeType = swapInfo.toToken.address === tokenCA ? 'sell' : 'buy';
    if (![solToken, tokenCA].includes(swapInfo.toToken.address) || ![solToken, tokenCA].includes(swapInfo.fromToken.address)) {
      console.log(`Invalid token swap: ${swapInfo.fromToken.address} -> ${swapInfo.toToken.address}   tx: ${txSignature}`);
      return null;
    }
    const nonSolToken = tradeType === 'buy' ? swapInfo.fromToken : swapInfo.toToken;
    const solAmount = tradeType === 'buy' ? toAmount : fromAmount;
    const tokenAmount = tradeType === 'buy' ? fromAmount*1000 : toAmount*1000;
    if (!isMember) {
        console.log(`Trade: ${tradeType} ${tokenAmount}  for ${solAmount}  by ${ownerAddress}  tx ${txSignature} ismember: ${isMember} `);
        return JSON.stringify({
        trade: tradeType,
        from: nonSolToken.address,
        amountOfToken: tokenAmount,
        amountOfSolana: solAmount,
        owner: ownerAddress
        });
    }else
    {
      return null;
    }

    } catch (error) {
      if (error instanceof Error) {
        return null;
      } else {
        return 'An unknown error occurred';
      }
    }
  }

  // Check if transaction is a Raydium swap
  private isRaydiumSwap(txInfo: ParsedTransactionWithMeta): boolean {
    const accountKeys = txInfo.transaction.message.accountKeys;
    return accountKeys.some(id => id.pubkey.toBase58() === RAYDIUM_SWAP_PROGRAM_ID);
  }

  // Extract swap details from transaction
  private async extractSwapInfo(txInfo: ParsedTransactionWithMeta): Promise<SwapInfo | null> {
    try {
      // Look for token balance changes to determine swap details
      if (!txInfo.meta) {
        return null;
      }
      const preTokenBalances = txInfo.meta.preTokenBalances || [];
      const postTokenBalances = txInfo.meta.postTokenBalances || [];
      
      let fromToken: Token | null = null;
      let toToken: Token | null = null;
      let fromAmount = 0;
      let toAmount = 0;
      
      // Find token with decreased balance (source token)
      for (const pre of preTokenBalances) {
        const post = postTokenBalances.find(p => 
          p.mint === pre.mint && p.owner === pre.owner
        );
        
        if (post) {
          const preAmount = Number(pre.uiTokenAmount.amount);
          const postAmount = Number(post.uiTokenAmount.amount);
          const diff = preAmount - postAmount;
          
          if (diff > 0) {
            fromToken = this.getTokenInfo(pre.mint);
            fromAmount = diff;
            break;
          }
        }
      }
      
      // Find token with increased balance (destination token)
      for (const post of postTokenBalances) {
        const pre = preTokenBalances.find(p => 
          p.mint === post.mint && p.owner === post.owner
        );
        
        const preAmount = pre ? Number(pre.uiTokenAmount.amount) : 0;
        const postAmount = Number(post.uiTokenAmount.amount);
        const diff = postAmount - preAmount;
        
        if (diff > 0 && (!fromToken || post.mint !== fromToken.address)) {
          toToken = this.getTokenInfo(post.mint);
          toAmount = diff;
          break;
        }
      }
      
      // Handle SOL swaps (check for SOL balance changes)
      if (!fromToken || !toToken) {
        const preSOL = txInfo.meta.preBalances;
        const postSOL = txInfo.meta.postBalances;
        
        // Compare SOL balances to detect SOL swaps
        for (let i = 0; i < preSOL.length; i++) {
          const diff = preSOL[i] - postSOL[i];
          
          // If SOL decreased significantly (accounting for fees)
          if (diff > 10000) {
            fromToken = {
              symbol: 'SOL',
              decimals: 9,
              address: 'SOL'
            };
            fromAmount = diff;
          }
          // If SOL increased
          else if (postSOL[i] - preSOL[i] > 0) {
            toToken = {
              symbol: 'SOL',
              decimals: 9,
              address: 'SOL'
            };
            toAmount = postSOL[i] - preSOL[i];
          }
        }
      }
      
      if (fromToken && toToken) {
        return {
          fromToken,
          toToken,
          fromAmount,
          toAmount
        };
      }
      
      return null;
    } catch (error) {
      console.error('Error extracting swap info:', error);
      return null;
    }
  }
}

export async function processSwapTransaction(redis: Redis, tx: string,tokenCA: string) {
    
    const rpcEndpoint = RPC_URL
    const parser = new SolanaSwapParser(rpcEndpoint);
    
    const result = await parser.parseTransaction(redis,tx,tokenCA);
    
    return result;
}

// Example usage
async function main() {
  if (process.argv.length < 3) {
    console.log('Usage: ts-node index.ts <transaction-signature>');
    process.exit(1);
  }
  
  const txSignature = process.argv[2];
  const rpcEndpoint = process.env.SOLANA_RPC_ENDPOINT || 'https://api.mainnet-beta.solana.com';
  
  const parser = new SolanaSwapParser(rpcEndpoint);
  const redis = new Redis({host: REDIS_URL, port: 6379, password: "pump2pump"});
  const result = await parser.parseTransaction(redis,txSignature,"");
  redis.disconnect();
  console.log(result);
}

// Run the application
main().catch(console.error);