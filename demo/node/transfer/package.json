{"type": "commonjs", "dependencies": {"@coral-xyz/anchor": "^0.30.1", "@fastify/static": "^8.1.0", "@solana/spl-token": "^0.4.11", "@solana/spl-token-registry": "^0.2.4574", "@solana/web3.js": "^1.98.0", "@types/node": "^22.12.0", "amqplib": "^0.10.5", "axios": "^1.7.9", "base-64": "^1.0.0", "bs58": "^6.0.0", "dotenv": "^16.4.7", "express": "^4.21.2", "fastify": "^5.2.1", "https-proxy-agent": "^7.0.6", "ioredis": "^5.5.0", "mysql2": "^3.12.0", "mysql2-promise": "^0.1.4", "node-fetch": "^3.3.2", "socks-proxy-agent": "^8.0.5", "typescript": "^5.7.3", "why-is-node-running": "^3.2.2", "winston": "^3.17.0", "ws": "^8.18.0"}, "scripts": {"build": "tsc", "start": "node dist/index.js"}, "devDependencies": {"@types/amqplib": "^0.10.6", "@types/express": "^5.0.0", "ts-node": "^10.9.2"}}