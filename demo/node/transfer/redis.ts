import Redis from 'ioredis';
import { REDIS_URL } from './src/config';

let redisClient: Redis | null = null;

export function getRedisClient(): Redis {
  if (!redisClient || !redisClient.status || redisClient.status !== 'ready') {
    redisClient = new Redis({
      host: REDIS_URL,
      port: 6379,
      password: "pump2pump",
      retryStrategy(times) {
        const delay = Math.min(times * 50, 2000);
        return delay;
      },
      maxRetriesPerRequest: 3,
      enableAutoPipelining: true,
      autoResubscribe: true
    });

    redisClient.on('error', (err) => {
      console.error('Redis Client Error:', err);
    });

    redisClient.on('connect', () => {
      console.log('Redis Client Connected');
    });
  }
  return redisClient;
}

export async function closeRedisConnection() {
  if (redisClient) {
    await redisClient.quit();
    redisClient = null;
  }
}

export async function addSetRedis(redis: Redis, key: string, value: string): Promise<void> {
  try {
    const client = getRedisClient();
    await client.sadd(key, value);
  } catch (error) {
    console.error('Error adding to Redis set:', error);
    throw error;
  }
}

export async function getSetRedis(redis: Redis, key: string): Promise<string[]> {
  try {
    const client = getRedisClient();
    return await client.smembers(key);
  } catch (error) {
    console.error('Error getting Redis set:', error);
    return [];
  }
}

export async function addRedis<T>(redis: Redis, key: string, data: T[]): Promise<void> {
  await redis.set(key, JSON.stringify(data));
  console.log(`Data stored under key: ${key}`);
}

export async function addRedisString(redis: Redis, key: string, data: string): Promise<void> {
  await redis.set(key, data);
  console.log(`Data stored under key: ${key}`);
}

// Function to retrieve accounts from Redis
export async function getRedis<T>(redis: Redis, key: string): Promise<T[]> {
  const data = await redis.get(key);
  return data ? JSON.parse(data) : [];
}

export async function getRedisString(redis: Redis, key: string): Promise<string> {
  const data = await redis.get(key);
  return data ? data : "";
}

export async function isMemberOfSet(redis: Redis, key: string, value: string): Promise<boolean> {
  try {
    const client = getRedisClient();
    const result = await client.sismember(key, value);
    return result === 1;
  } catch (error) {
    console.error('Error checking Redis set membership:', error);
    return false;
  }
}

export async function checkRedisKey(redis: Redis, key: string): Promise<boolean> {
  try {
    const client = getRedisClient();
    const result = await client.exists(key);
    return result === 1;
  } catch (error) {
    console.error('Error checking if key exists in Redis:', error);
    return false;
  }
}