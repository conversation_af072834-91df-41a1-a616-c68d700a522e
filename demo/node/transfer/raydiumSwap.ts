import { 
    Connection, 
    Keypair, 
    VersionedTransaction,
    TransactionSignature,
    PublicKey,
    SystemProgram,
    BlockheightBasedTransactionConfirmationStrategy
  } from '@solana/web3.js';
  import { getAssociatedTokenAddress } from '@solana/spl-token';
  import bs58 from 'bs58';
  import axios from 'axios';
  import dotenv from 'dotenv';
  import { Command } from 'commander';
import { RPC_URL } from './src/config';

  const program = new Command();
  dotenv.config();
  
  const PRIVATE_KEY = process.env.PRIVATE_KEY || "";
  const QN_RPC = "https://capable-wispy-emerald.solana-mainnet.quiknode.pro/e14cad62ecf815fc184007d5d058dd7365cb9254/";
  
  interface QuoteResponse {
    data: {
      inputMint: string;
      outputMint: string;
      amount: number;
      slippageBps: number;
      otherAmountThreshold?: string;
      swapMode?: string;
      routes?: any[];
    }
    swapTransaction: string;
  }
  
  interface SwapResponse {
    swapTransaction: string;
  }
  
  // Create connection to Solana
  const connection = new Connection(RPC_URL);
  
  // Create keypair from private key
  const keypair = Keypair.fromSecretKey(bs58.decode(PRIVATE_KEY));
  
  async function swap(inputMint: string, outputMint: string,amount: number,slippage: number): Promise<void> {
    try {
      console.log("Swapping SOL to USDC...");
      console.log(`Wallet: ${keypair.publicKey.toString()}`);
  
      // Step 1: Get quote for swapping SOL to USDC
      const quoteResponse = await axios.get<QuoteResponse>('https://quote-api.jup.ag/v6/quote', {
        params: {
          inputMint: inputMint, // SOL mint address
          outputMint: outputMint, // USDC mint address
          amount: amount, // 0.01 SOL in lamports
          slippageBps: slippage // 0.5% slippage
        }
      });
      console.log("Quote received:", quoteResponse.data);
  
      // Step 2: Get swap transaction with our priority fee
      const swapResponse = await axios.post<SwapResponse>('https://quote-api.jup.ag/v6/swap', {
        quoteResponse: quoteResponse.data,
        userPublicKey: keypair.publicKey.toString(),
        wrapAndUnwrapSol: true,
        dynamicComputeUnitLimit: true, // Adding priority fee
        //prioritizationFeeLamports: "auto",
        prioritizationFeeLamports: {
          autoMultiplier: 2,
        },
      }, {
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      // Step 3: Deserialize the transaction
      const swapTransactionBuf = Buffer.from(swapResponse.data.swapTransaction, 'base64');
      const transaction = VersionedTransaction.deserialize(swapTransactionBuf);
      
      // Step 4: Sign the transaction
      transaction.sign([keypair]);
      
      // Step 5: Get the latest blockhash for confirmation
      const bhInfo = await connection.getLatestBlockhashAndContext('processed'); // Using processed instead of finalized
      
      // Step 6: Simulate the transaction before sending
    //   console.log("Simulating transaction...");
    //   const simulation = await connection.simulateTransaction(transaction, { commitment: 'processed' });
    //   if (simulation.value.err) {
    //     throw new Error(`Simulation failed: ${JSON.stringify(simulation.value.err)}`);
    //   }
    //   console.log("Simulation successful");
      
      // Step 7: Send the transaction
      console.log("Sending transaction...");
      const signature: TransactionSignature = await connection.sendTransaction(transaction, { 
        skipPreflight: true, 
        preflightCommitment: 'processed' 
      });
      console.log(`Transaction sent with signature: ${signature}`);
      console.log(`View transaction: https://solscan.io/tx/${signature}`);
  
      // Step 8: Just check transaction status without waiting for finalization
      console.log("Transaction sent! Not waiting for full confirmation.");
      console.log("Checking initial status...");
      
      // We'll check the status once but not wait for full confirmation
 
        try {
          const status = await connection.getSignatureStatus(signature, { searchTransactionHistory: true });
          console.log("Transaction status:", status.value?.confirmationStatus || "unknown");
          if (status.value?.err) {
            console.error("Transaction error:", status.value.err);
          } else {
            console.log("Transaction appears to be successful!");
          }
        } catch (err) {
          console.log("Error checking status:", err);
        }

      
      console.log("You can continue with other operations while the transaction settles...");
      
    } catch (error) {
      console.error('Error during swap:', error);
    }
  }
  
  // Execute the swap
  export async function getTokenBalance(tokenCA: string) {
    const privateKey = process.env.PRIVATE_KEY ? bs58.decode(process.env.PRIVATE_KEY) : new Uint8Array();
    const signer = Keypair.fromSecretKey(privateKey);
    const tokenAccountAddress = await getAssociatedTokenAddress(new PublicKey(tokenCA), signer.publicKey, false);
    let tokenAccountBalance;
    try {
      tokenAccountBalance = await connection.getTokenAccountBalance(tokenAccountAddress);
    } catch (error) {
      //console.error('Failed to get token account balance:', error);
      return null;
    }
  
    const tokenBalance = parseInt(tokenAccountBalance.value.amount);
    console.log("Token balance",tokenBalance," mint:",tokenCA);
    return tokenBalance;
  }

  
  const inputMint ='So11111111111111111111111111111111111111112';
  //const outputMint ='EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v';
  const outputMint ='8H4ZNMucw8o28gvufMBojsxKRhBKSgnkQ1uiv9ZhqeDg' ;
  
  const solAmount=0.01;
  let amount = solAmount * **********;  // 0.01 SOL
  const slippage = 10000;

program
  .name('raydium-swap')
  .description('CLI for Raydium swap operations')
  .version('0.8.0');

program
  .command('buy')
  .description('Buy token')
  .option('-t, --token <tokenCA>', 'Token contract address')
  .action((options) => {
    console.log("Buying token...", options.token);
    if (options.token) {
      swap(inputMint, options.token, amount, slippage);
    }
  });

program
  .command('sell')
  .description('Sell token')
  .option('-t, --token <tokenCA>', 'Token contract address')
  .action(async (options) => {
    console.log("Selling token...", options.token);
    if (options.token) {
      const tokenBalance = await getTokenBalance(options.token);
      if (tokenBalance && tokenBalance > 0) {
        await swap(options.token, inputMint, tokenBalance, slippage);
      }
    }
  });

program.parse(process.argv);