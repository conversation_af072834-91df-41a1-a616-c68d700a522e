# Auto Trading

## Autotrader : auto1

Logic:

1. Scan account Gh9J24j688AfsoiZQJiUGYtxb8RwhcZPJsQtiAJtLFAQ
   1. Scan only outgoint -5 solana . One of these will make token
      "getTradesWebhook.ts"
2. Setup webhook to scan all pump.fun creations. Register if owner is our account
   "pumpCreationsWebhook.ts"
3. Setup token scanner scan all changes for token and post in reddit and databse
   "rabbitConsumers.ts"
4. setup buy/sell bot get all trades and decides when to buy and sell
   "tradeBuySell.ts"
   1. buy logic: by between 27k-37k  if cumuliative less then 1.2 betwween 1 and 2 min.
   2. sell when cumulative above 2 or sigle buy above 1 and mc above 50

## Automation : auto2

====Follower =======
Follower:  auto2 = address

1 Check latest auto 2 balance ( get auto2 last ):
2  if 0
3 list all txt . See at top where send out .get address
4 get balance if > 1   auto2= address    set app=auto2 in database as new account

======== get token =====
x - Default scan
x - Update scanner . Check app id  . Set app id for rugger_mionts when created
==== rabbit mq =====
x - Update support for app=id send to trades
=== buy/sell=======

N/A - Implement app id
N/A - Write formula for each



# BOT manager

1. enable/disable
2. set trasholds. rule engine ???
   1. buy
      1. when to buy time ingerva
   2. sells
      1. time intervals amount to sell
3. display profits statistics
4. rebuy options
5. account managemet ?? creation interval distributions etc

# token analyzer
1. put token address
2. calculates enries 
3. find patterm  
