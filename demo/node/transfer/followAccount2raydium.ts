import { 
    getAccountAuto3,
    updateAddressName,
    getEnabledAccounts,
    disableAccount,
    AddressApp,
    updateAccountStatus,
    updateAccountSignature ,
    addMint,
    getMint
} from "./src/utils/db"; 

import { Connection, PublicKey , VersionedTransactionResponse} from '@solana/web3.js';
import dotenv from 'dotenv';
import { 
    PRIVATE_KEY,
    RPC_URL, 
    REDIS_URL,
    ignoreAccounts
} from './src/config';
import { addAppAccountToDBWithName } from './src/utils/db';
import { Redis } from 'ioredis';
import { addSetRedis, getRedis } from './redis';
import { logger } from "./logger";
import { get } from "http";
import { sendToDiscord } from './src/utils/discord';
import { sendRabbitMessage } from './src/utils/rabbitmq';



dotenv.config();
const WEBHOOK = "https://discord.com/api/webhooks/1339217907380523040/tdXh3hfzR-AJNgGNYupg31BKirR1KoDV8mj937I7DAk1OqqWlNpinLl2O0TfawVVyG9K";

type Task = 'getToken' | 'getDestination' | 'getMasters';
let task: Task ="getToken";
let redis: Redis;
type Account = { address: string; app: string };
const bootstrapAccount="6jPE8dK2fUUTdvJdogo4SWnvtAHtLvE2PGxKYH8B7dCR";
const lowSol=0.01;
const lowSolLamports = lowSol * 1e9;    
const followSol=100;
const followSolLamports = followSol * 1e9;


// async function getDestinationAccount(accountAddress: string) {
//     logger.debug(`Get destination account for: ${accountAddress}`);
//     const connection = new Connection(RPC_URL);
//     const limit = 100;
//     const publicKey = new PublicKey(accountAddress);
//     const before =  undefined;
//     const signatures = await connection.getSignaturesForAddress(publicKey, { before, limit: limit });
//     const promises = [];
//     for (const signature of signatures) {
//         const transaction = await connection.getTransaction(signature.signature, {
//             maxSupportedTransactionVersion: 0,
//           });
//           if (transaction) {
//             // Skip transactions that are in an error state
//             if (transaction.meta && transaction.meta.err) {
//               continue;
//             }
//             const accountKeys = transaction.transaction.message.staticAccountKeys.map(key => key.toBase58());
//             const keys = accountKeys.map(key => key);
//             const accuntKeyIndex = accountKeys.indexOf(accountAddress); 
            
//                 if ( transaction.meta?.postBalances &&  transaction.meta?.postBalances.length >0 && transaction.meta?.postBalances[accuntKeyIndex] < lowSolLamports && transaction.meta?.preBalances[accuntKeyIndex] > followSolLamports){

//                     const preBalances = transaction.meta.preBalances;
//                     const postBalances = transaction.meta.postBalances;

//                     let maxGain = 0;
//                     let maxGainIndex = -1;

//                     for (let i = 0; i < preBalances.length; i++) {
//                         const gain = postBalances[i] - preBalances[i];
//                         if (gain > maxGain) {
//                             maxGain = gain;
//                             maxGainIndex = i;
//                         }
//                     }

//                     if (maxGainIndex !== -1) {
//                         const destinationAccount = accountKeys[maxGainIndex];
//                         if (maxGain > 700 * 1e9 && maxGain < 900 * 1e9) {
//                             logger.debug(`Add  MANAGER account: ${accountAddress}`);
//                             await addAppAccountToDBWithName(destinationAccount, postBalances[maxGainIndex], 'auto3', signature.signature, 'manager');
//                             return ;
//                         } else if (maxGain > 350 * 1e9 && maxGain < 700 * 1e9) {
//                             logger.debug(`Add  BUNDLER account: ${accountAddress}`);
//                             await addAppAccountToDBWithName(destinationAccount, postBalances[maxGainIndex], 'auto3', signature.signature, 'bundler');
//                             return ;
//                         } else {
//                             logger.debug(`Add  REGULAR account: ${accountAddress}`);
//                             await addAppAccountToDBWithName(destinationAccount, postBalances[maxGainIndex], 'auto3', signature.signature, 'regular');
//                         }
//                         logger.debug(`Disable EMPTY account: ${accountAddress}`);
//                         await disableAccount(accountAddress);
                        
                
//                     }
                
//                 } 
            
//             // if (task == "getToken") {
//             //     if (transaction?.meta?.postTokenBalances) {
//             //         const accountKeys = transaction.transaction.message.staticAccountKeys.map(key => key.toBase58());
//             //         const keys = accountKeys.map(key => key);
//             //         const signer = keys[0];
//             //         for ( const postTokenBalance of transaction?.meta?.postTokenBalances) {
        
        
//             //             if (postTokenBalance.uiTokenAmount.uiAmount == ********** && postTokenBalance.owner != signer) {
//             //                 logger.debug('Transaction:',signature);
//             //                 logger.debug('mint:',postTokenBalance.mint);
//             //                 logger.debug('owner:',postTokenBalance.owner);
//             //                 logger.debug('uiAmount:',postTokenBalance.uiTokenAmount.uiAmount);
//             //             }
//             //         }   
//             //     }
//             // }


//           }

//     }

//     return null;
    
// }

// async function getTokenAccounts(accountAddress: string) {
//     const connection = new Connection(RPC_URL);
//     const publicKey = new PublicKey(accountAddress);
//     const tokenAccounts = await connection.getParsedTokenAccountsByOwner(publicKey, {
//         programId: new PublicKey("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA")
//     });

//     const tokens = tokenAccounts.value.map(tokenAccount => {
//         const accountInfo = tokenAccount.account.data.parsed.info;
//         return {
//             mint: accountInfo.mint,
//             tokenAmount: accountInfo.tokenAmount.uiAmount
//         };
//     });

//     return tokens;
// }

// async function getAccountBalance(account: string) {
//     const connection = new Connection(RPC_URL);
//     const publicKey = new PublicKey(account);
//     const accountInfo = await connection.getAccountInfo(publicKey);
//     try {

//         if (accountInfo) {
//             const balance = accountInfo.lamports;
//             return balance;
//         } else {
//             return 0;
//         }
//     }  
//     catch (error) {
//         return 0;
//     }
// }

async function getTransactions(redis:Redis, accountAddress: string,last_sig?: string | undefined) {   
    const connection = new Connection(RPC_URL);
    const limit = 1000;
    const    before=undefined;
    const publicKey = new PublicKey(accountAddress);
    const signatures = await connection.getSignaturesForAddress(publicKey, { before, limit: limit });
    const promises = [];

    const firstSig = signatures[0].signature;
    const sigs = [];
    for (const signature of signatures) {
        if (signature.err === null) {
            
            if (signature.signature === last_sig) {
                break;
            }
            sigs.push(signature.signature);
        }
    }

  

   
    const txs = await connection.getTransactions(sigs,{
        maxSupportedTransactionVersion: 0,
      });

    for ( const transaction of txs) {
        if (transaction){
            await processTransaction(redis,transaction,accountAddress);
        }
    }
   
    await updateAccountSignature(accountAddress,firstSig); 

    await updateAccountStatus(accountAddress,"exists");
}


async function processTransaction(redis:Redis, transaction: VersionedTransactionResponse,accountAddress: string) {
        const signature = transaction.transaction.signatures[0];

        // RULE:1  GET MINT ADDRESS  result: send to rabbit/db 
        // ACCOUNT will accumulate 800sol+profit

        const accountKeys = transaction.transaction.message.staticAccountKeys.map(key => key.toBase58());
        const keys = accountKeys.map(key => key);
       
        const accuntKeyIndex = accountKeys.indexOf(accountAddress); 
         //RULE 0; follow  
         // 
        if ( transaction.meta?.postBalances &&  transaction.meta?.postBalances.length >0  && transaction.meta?.preBalances[accuntKeyIndex] > followSolLamports){

                const preBalances = transaction.meta.preBalances;
                const postBalances = transaction.meta.postBalances;
                const signature = transaction.transaction.signatures[0];
                let maxGain = 0;
                let maxGainIndex = -1;

                for (let i = 0; i < preBalances.length; i++) {
                    const gain = postBalances[i] - preBalances[i];
                    if (gain > maxGain) {
                        maxGain = gain;
                        maxGainIndex = i;
                    }
                }
                
                if (maxGainIndex !== -1) {
                    const destinationAccount = accountKeys[maxGainIndex];
                    if (maxGain > 85 * 1e9 ) {
                        logger.debug(`Add  REGULAR account: ${destinationAccount}`);
                        //ignore if 
                        if (ignoreAccounts.includes(destinationAccount)) {
                            logger.debug('Ignore account:',destinationAccount);
                            return;
                        }
                        await addAppAccountToDBWithName(destinationAccount, postBalances[maxGainIndex], 'auto3', signature, 'regular');
                    }
                    //logger.debug(`Disable EMPTY account: ${accountAddress}`);
                    //await disableAccount(accountAddress);
                    
            
                }
            
            } 

        // Check for outgoing SOL transfer greater than 1 SOL
        if (transaction.meta?.preBalances && transaction.meta?.postBalances) {
            const preBalances = transaction.meta.preBalances;
            const postBalances = transaction.meta.postBalances;
            const accountIndex = accountKeys.indexOf(accountAddress);

            if (accountIndex !== -1) {
                const outgoingTransfer = preBalances[accountIndex] - postBalances[accountIndex];
                if (outgoingTransfer > 1 * 1e9 && (!transaction.meta?.postTokenBalances || transaction.meta?.postTokenBalances.length === 0)) {
                    console.log(`Outgoing SOL transfer greater than 1 SOL detected for ${accountAddress}: ${outgoingTransfer / 1e9} SOL`);
                    const outgoingAccountIndex = postBalances.findIndex((balance, index) => index !== accountIndex && balance > preBalances[index]);
                    if (outgoingAccountIndex !== -1) {
                        const outgoingAccount = accountKeys[outgoingAccountIndex];
                        console.log(`Outgoing address: ${outgoingAccount}`);
                        addSetRedis(redis, 'auto3b', outgoingAccount);
                    }
                    //TODO: add to redis set
                }
            }
        }

        // rule 1: get mint 
        if (transaction?.meta?.postTokenBalances) {
            const accountKeys = transaction.transaction.message.staticAccountKeys.map(key => key.toBase58());
            const keys = accountKeys.map(key => key);
            const signer = keys[0];
            for ( const postTokenBalance of transaction?.meta?.postTokenBalances) {


                if (postTokenBalance.uiTokenAmount.uiAmount && postTokenBalance.uiTokenAmount.uiAmount > ********* && postTokenBalance.owner != signer) {
                    console.log('Transaction:',signature);
                    console.log('mint:',postTokenBalance.mint);
                    console.log('owner:',postTokenBalance.owner);
                    console.log('uiAmount:',postTokenBalance.uiTokenAmount.uiAmount);
                    const mint= await getMint(postTokenBalance.mint);

                    if (!mint) {    
                       await addMint(signer,postTokenBalance.mint,"auto3");
                       const timestamp = new Date().toISOString();
                       //send to 
                      // sendRabbitMessage(JSON.stringify({ "mint": postTokenBalance.mint, "timestamp": timestamp, "app": "auto3" }), 'snipes');
                       await sendToDiscord(WEBHOOK, `New token auto3: [${postTokenBalance.mint}](https://neo.bullx.io/terminal?chainId=**********&address=${postTokenBalance.mint})`);
                       
                    }
                }
            }   
        }

        // RULE: 2 GET BUNDLER ACCOUNTS . result: send to redis
        // TODO: rewrite add to redis all which sends oout more than 1sol 

        // if (transaction && transaction?.meta?.postTokenBalances?.length == 0 && transaction?.meta?.preTokenBalances?.length == 0 && transaction?.meta?.postBalances) {

        //         const accountKeys = transaction.transaction.message.staticAccountKeys.map(key => key.toBase58());
        //         const keys = accountKeys.map(key => key);
        //         const signer = keys[0];
        //         const postBalances =transaction?.meta?.postBalances;
        //         const balanceCounts = postBalances.reduce<{ [key: number]: number }>((acc, balance) => {
        //             if (balance > ********** && balance < **********0) {
        //                 acc[balance] = (acc[balance] || 0) + 1;
        //             }
        //             return acc;
        //         }, {});

        //         const accountsWithBalance = Object.keys(balanceCounts).filter((balance: string) => balanceCounts[Number(balance)] > 1);
        //         const accounts = accountsWithBalance.map(balance => {
        //             const indexes = [];
        //             let idx = postBalances.indexOf(Number(balance));
        //             while (idx != -1) {
        //                 indexes.push(idx);
        //                 idx = postBalances.indexOf(Number(balance), idx + 1);
        //             }
        //             return indexes.map(index => keys[index]);
                    
        //         });

        //         if ( accounts.length > 0) {
        //             const redisData: string [] = await getRedis(redis,"auto3a");
        //             console.log('redisData:',redisData.length);
                 
        //             let redisSet = new Set(redisData);
        //             for (const account of accounts[0]) {
        //                 redisSet.add(account);
        //                 //addAppAccountToDB(account,'auto3');
        //             }
        //             await addRedis(redis, "auto3a", Array.from(redisSet));  
        //         }

     
        // }
               
    
}



/**
 * Main entry point
 */
(async () => {

     redis = new Redis({host: REDIS_URL, port: 6379, password: "pump2pump"});

    let isProcessing = false;

   // setInterval(async () => {
        if (isProcessing) {
           
            return;
        }

        isProcessing = true;
        try {
            // await getAccountsToFollow(); const accounts = await connection.getMultipleAccountsInfo([new PublicKey("AWaJ4oXHWXaNnFJirydYdXdD3Yjmkgh6rYhmwAyB5d8o"), new PublicKey("yT2bBMrE4AVG3BM8LHjDSr1yketzabmfp3rYimS36C9")]);

            //await followAccount();
           const accountsApp: AddressApp[] = await getEnabledAccounts("auto3");
 
            for ( const account of accountsApp) {
                if (ignoreAccounts.includes(account.address)) {
                    logger.debug('Ignore account:',account.address);
                    continue;
                }
                logger.debug(` ================================== Processing account: ${account.address} Balance: ${account.balance/1e9} ==================================`); 
                await getTransactions(redis,account.address,account.last_sig);

            }
        logger.debug('Processing completed');
           
        } catch (error) {
            console.error('Error occurred:', error);
        } finally {
            isProcessing = false;
        }
  //  }, 1000);



})();