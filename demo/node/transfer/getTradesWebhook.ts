import Fastify from 'fastify';
import mysql from 'mysql2/promise';
import amqp from 'amqplib/callback_api';
import dotenv from 'dotenv';
import { MASTER_ACCOUNT,APP,discord_alerting } from "./src/config.js";
import fs from 'fs';
import path from 'path';
import { sendToDiscord } from './src/utils/discord.js';
import { sendPushoverMessage } from './src/utils/pushover.js';
import { Redis } from 'ioredis';
import { addRedis, getRedis } from './redis';
import { 
  REDIS_URL,
} from './src/config.js';

let redis: Redis;

redis = new Redis({host: REDIS_URL, port: 6379, password: "pump2pump"});

dotenv.config();

const fastify = Fastify({ logger: false });

const pool = mysql.createPool({
  host: process.env.MYSQL_HOST,
  user: process.env.MYSQL_USER,
  password: process.env.MYSQL_PASSWORD,
  database: process.env.MYSQL_DATABASE,
});

const RABBITMQ_SERVER = process.env.RABBITMQ_HOSTNAME || '';
const RABBITMQ_QUEUE = process.env.RABBITMQ_QUEUE || 'snipes';

function sendMessage(message: string, queue: string = RABBITMQ_QUEUE) {
  amqp.connect({
    protocol: 'amqp',
    hostname: RABBITMQ_SERVER,
    port: parseInt(process.env.RABBITMQ_PORT || '5672'),
    username: process.env.RABBITMQ_USERNAME,
    password: process.env.RABBITMQ_PASSWORD,
  }, (error0, connection) => {
    if (error0) {
      throw error0;
    }
    connection.createChannel((error1, channel) => {
      if (error1) {
        throw error1;
      }
      channel.assertQueue(queue, {
        durable: true
      });

      channel.sendToQueue(queue, Buffer.from(message), {
        persistent: true
      });
      console.log(" [x] Sent %s", message);
    });
  });
}

fastify.post('/webhook', async (request, reply) => {
  const data = request.body as any;

  const description = data[0].description;

  //Save data to file txt/<signature>.json
  
  //const filePath = path.join(__dirname, 'tx', `${data[0].signature}.json`);
  //fs.writeFileSync(filePath, JSON.stringify(data, null, 2));

  const sig = data[0].signature;
  const type = data[0].type;
  const timestamp = data[0].timestamp;
  const slot = data[0].slot;
  const owner = data[0].feePayer;
  let mint = "";
  // set 

   /// RUNNER processing
   // if woner in  discord_alerting[0].account, discord_alerting[1].account

  for (let account = 0; account < discord_alerting.length; account++) {
    for (const transfer of data[0].nativeTransfers) {
      if (transfer.amount > discord_alerting[account].minSol && transfer.amount < discord_alerting[account].maxSol && transfer.fromUserAccount == discord_alerting[account].account) {
        // round number to 2 decimal places
        const amountInSol = (transfer.amount / 1e9).toFixed(2);
        const fromAccount = transfer.fromUserAccount.substring(0, 4);
        const toAccount = transfer.toUserAccount.substring(0, 4);
        sendPushoverMessage(`${discord_alerting[account].name}->${toAccount} ${amountInSol} SOL`);
        sendToDiscord(discord_alerting[account].discort, `Transfer of ${amountInSol} SOL to ${transfer.toUserAccount}`);
      }
    }
  }
    



    //// STAIRS processing
  if ( owner == MASTER_ACCOUNT ) {
    let counter=0
    for (const transfer of data[0].nativeTransfers) {

          if (transfer.amount == **********) {
            //write data to file folder tx
            if (counter == 0) { 
              console.log("--------------------------tx: ",data[0].signature);
              counter=1
            }
            const currentTime = new Date().toLocaleTimeString('en-US', { hour12: false });
            console.log(`${currentTime} ${transfer.toUserAccount} 5 Sol transfeer`);
            
            await pool.query(`
            INSERT INTO accounts (master, address, balance, status, scan, scanned, distance, app, disovery_account_ts, last_sig) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ON DUPLICATE KEY UPDATE disovery_account_ts = VALUES(disovery_account_ts)
            `, [MASTER_ACCOUNT, transfer.toUserAccount, 99999, "new", 1, 0, 10, APP, new Date(), sig]);

                    const redisData: string [] = await getRedis(redis,"auto1");                 
                    let redisSet = new Set(redisData);
                        redisSet.add(transfer.toUserAccount);
                    await addRedis(redis, "auto1", Array.from(redisSet));  


        }
        
       
      }



    for (const token of data[0].tokenTransfers) {

      
        if (token.fromTokenAccount && token.mint !== "So11111111111111111111111111111111111111112") {
          mint = token.mint;

          // Check if the mint already exists in the database
          const [rows]: [any[], any] = await pool.query('SELECT * FROM rugger_mints WHERE mint = ?', [mint]);
          if (rows.length === 0) {
            // Insert the new mint into the database with created_at timestamp
            // send mint and current timestamp to rabbitmq
            //await pool.query('INSERT INTO rugger_mints (rugger, mint, created_at) VALUES (?, ?, ?)', [owner, mint, new Date()]);
            //sendMessage(JSON.stringify({"mint":token.mint, "timestamp":timestamp}));
            
          }
        }
      

    }

  }
  
  


  return reply.send({ status: "success" });
});

fastify.listen({ port: 7007, host: '0.0.0.0' }, (err, address) => {
  if (err) {
    fastify.log.error(err);
    process.exit(1);
  }
  fastify.log.info(`Server is running on ${address}`);
});