import {
  Connection,
  SystemProgram,
  Transaction,
  sendAndConfirmTransaction,
  Keypair,
  ComputeBudgetProgram,
  PublicKey,
} from "@solana/web3.js";
import dotenv from "dotenv";
import bs58 from "bs58";
import fs from "fs";
import { Command } from "commander";
const LAMPORTS_PER_SOL = Number(1_000_000_000);

dotenv.config();

const HeliusURL = "https://mainnet.helius-rpc.com/?api-key=cf3aa81f-7796-401b-a170-5567272f5f65";
const connection = new Connection(HeliusURL);

const program = new Command();

program
  .command("create")
  .description("Create a new account")
  .action(async () => {
    const space = 0; // any extra space in the account
    const rentLamports = await connection.getMinimumBalanceForRentExemption(space);
    console.log("Minimum balance for rent exception:", rentLamports);

    // Load the signer from the private key in the .env file
    const privateKey = process.env.PRIVATE_KEY ? bs58.decode(process.env.PRIVATE_KEY) : new Uint8Array();
    const signer = Keypair.fromSecretKey(privateKey);

    // Check the balance of the signer
    const balance = await connection.getBalance(signer.publicKey);
    console.log("Signer balance:", balance / LAMPORTS_PER_SOL, "SOL");

    if (balance < rentLamports) {
      throw new Error("Insufficient funds in the signer account.");
    }

    // generate a new keypair and address to create
    const newAccountKeypair = Keypair.generate();
    console.log("New account address:", newAccountKeypair.publicKey.toBase58());

    // Save the private key of the newly created account to a file
    const newAccountPrivateKey = bs58.encode(newAccountKeypair.secretKey);
    fs.writeFileSync(`account-${newAccountKeypair.publicKey.toBase58()}.txt`, newAccountPrivateKey);

    const { blockhash } = await connection.getRecentBlockhash();

    const transaction = new Transaction().add(
      SystemProgram.createAccount({
        fromPubkey: signer.publicKey,
        newAccountPubkey: newAccountKeypair.publicKey,
        lamports: rentLamports,
        space: space,
        programId: SystemProgram.programId,
      })
    );

    transaction.feePayer = signer.publicKey;
    transaction.recentBlockhash = blockhash;

    // Sign the transaction with the signers
    transaction.sign(signer, newAccountKeypair);

    const signature = await sendAndConfirmTransaction(connection, transaction, [signer, newAccountKeypair]);

    console.log("Signature:", signature);
  });

program
  .command("close <accountName>")
  .description("Close an account")
  .action(async (accountName) => {
    const privateKey = process.env.PRIVATE_KEY ? bs58.decode(process.env.PRIVATE_KEY) : new Uint8Array();
    const signer = Keypair.fromSecretKey(privateKey);

    const accountPublicKey = new PublicKey(accountName);

    // Load the private key of the account to be closed from the file
    const accountPrivateKey = bs58.decode(fs.readFileSync(`account-${accountName}.txt`, 'utf8'));
    const accountKeypair = Keypair.fromSecretKey(accountPrivateKey);

    const transaction = new Transaction().add(
      SystemProgram.transfer({
        fromPubkey: accountPublicKey,
        toPubkey: signer.publicKey,
        lamports: await connection.getBalance(accountPublicKey),
      })
    );

    transaction.feePayer = signer.publicKey;
    const { blockhash } = await connection.getRecentBlockhash();
    transaction.recentBlockhash = blockhash;

    // Sign the transaction with the signer and the account to be closed
    transaction.sign(signer, accountKeypair);

    const signature = await sendAndConfirmTransaction(connection, transaction, [signer, accountKeypair]);

    console.log("Account closed. Signature:", signature);
  });

program
  .command("transfer <toAccount> <amount>")
  .description("Transfer SOL to another account")
  .option("--priorityLevel <level>", "Priority level for the transaction", "NONE")
  .action(async (toAccount, amount, options) => {
    const fromKeypair = process.env.PRIVATE_KEY ? Keypair.fromSecretKey(bs58.decode(process.env.PRIVATE_KEY)) : Keypair.generate();
    const toPubkey = new PublicKey(toAccount);
    const priorityLevel = options.priorityLevel as PriorityLevel;

    const lamports = parseFloat(amount) * LAMPORTS_PER_SOL;

    const transaction = new Transaction();
    const transferIx = SystemProgram.transfer({
      fromPubkey: fromKeypair.publicKey,
      toPubkey,
      lamports,
    });
    transaction.add(transferIx);

    let feeEstimate: PriorityFeeEstimateResult = { priorityFeeEstimate: 0 };
    if (priorityLevel !== "NONE") {
      feeEstimate = await getPriorityFeeEstimate(priorityLevel, transaction, fromKeypair);
      const computePriceIx = ComputeBudgetProgram.setComputeUnitPrice({
        microLamports: feeEstimate.priorityFeeEstimate,
      });
      transaction.add(computePriceIx);
    }

    // Set the fee payer and recentBlockhash before signing and sending the transaction
    transaction.feePayer = fromKeypair.publicKey;
    transaction.recentBlockhash = (await connection.getLatestBlockhash()).blockhash;
    transaction.sign(fromKeypair);

    try {
      const txid = await sendAndConfirmTransaction(connection, transaction, [fromKeypair], { commitment: "processed" });
      console.log(`Transaction sent successfully with signature ${txid}`);
    } catch (e) {
      console.error(`Failed to send transaction: ${e}`);
    }
  });

program.parse(process.argv);

type PriorityLevel = "Min" | "Low" | "Medium" | "High" | "VeryHigh" | "UnsafeMax" | "NONE";

interface PriorityFeeEstimateResult {
  priorityFeeEstimate: number;
}

async function getPriorityFeeEstimate(priorityLevel: PriorityLevel, transaction: Transaction, fromKeypair: Keypair): Promise<PriorityFeeEstimateResult> {
  // Set the fee payer and recentBlockhash before serializing the transaction
  transaction.feePayer = fromKeypair.publicKey;
  transaction.recentBlockhash = (await connection.getLatestBlockhash()).blockhash;

  const response = await fetch(HeliusURL, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({
      jsonrpc: "2.0",
      id: "1",
      method: "getPriorityFeeEstimate",
      params: [
        {
          transaction: bs58.encode(transaction.serialize({ requireAllSignatures: false })), // Pass the serialized transaction in Base58
          options: { priorityLevel: priorityLevel },
        },
      ],
    }),
  });
  const data = await response.json();
  console.log(
    "Fee in function for",
    priorityLevel,
    " :",
    data.result.priorityFeeEstimate
  );
  return data.result;
}