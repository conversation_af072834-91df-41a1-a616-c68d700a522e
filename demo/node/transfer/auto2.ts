
import dotenv from 'dotenv';
import { updatMintTradeDataBuy,updatMintTradeDataSell ,updatMintBuyTokens,updatMintBuyTx} from './src/utils/db.js';
import { sellTokenPercentage,buyToken,getTokenBalance } from './src/index.js';


/// Add buy logic. if triggeted see if after 10 seconds not rugged buy again . 

// FIX  1. check mcap by dev , 2. check time from begining 


export async function tradeBuySellAuto2(buySol:number, mint: string, isBuy: boolean, amount: number, mcap: number, tradeData: any) {


        if (tradeData.trade_status === 'active' ) {
          /**          ===== BUY CONDITION =====
            *            My when condition met
            *            update database set "bought" and save by_tx
            * 
            * Notes: max 2 sol at 4 min 
            */

          
                let buy=false;

                if (mcap > 22000 && mcap < 42000) {
                  if (tradeData.time_diff_seconds > 60 && tradeData.time_diff_seconds <= 120 && tradeData.cumulative_diff < 2) {
                          buy=true;
                  } 
              }
              
              if (buy){ 
                await updatMintTradeDataBuy(mint, 'bought', buySol, 0,'', mcap);
              
                buyToken(mint, `${buySol}`,"0.5", 'Low').then(async (tx) => {
                  console.log(`Bought: ${mint} - ${tx}`);
                  if (tx) {
                    await updatMintBuyTx(mint, tx);
                  }
    
                });
            }




        } else if (tradeData.trade_status === 'bought' || tradeData.buy_token_amount > 0 ) {

          /**       ===== SELL CONDITION =====
           *    See if condition met SELL!!  twice.. so its for sure
           *    update database set "sold" and save sell_tx
           *  || mcap > 60000 || mcap < 22000
           */
           let sell = false;
           if (tradeData.time_diff_seconds > 6200 || tradeData.cumulative_diff > 2.5  ){

              sell=true;
            
           } else if (mcap > 52000 || mcap < 22000){

              sell=true;
           }

             
                if (sell){
              
                      // SELL once 
                      try {
                      await sellTokenPercentage(mint, "100", "0.5", 'Low').then(async (tx) => {
                        console.log(`Sold: ${mint} - ${tx}`);
                        updatMintTradeDataSell(mint, 'sold', buySol, 0, '' , mcap);

                      });
                      } catch (error) {
                      console.error(`Failed to sell tokens for mint: ${mint} - ${(error as Error).message}`);
                      }



                      // Sell twice - it will fails if already sold
                      try {
                      setTimeout(async () => {
                        try {
                          await sellTokenPercentage(mint, "100", "0.5", 'Low').then(async (tx) => {
                            console.log(`Sold: ${mint} - ${tx}`);
                            updatMintTradeDataSell(mint, 'sold', buySol, 0, '', mcap);
                          });
                        } catch (error) {
                          console.error(`Failed to sell tokens for mint: ${mint} - ${(error as Error).message}`);
                        }
                      }, 5000);
                    } catch (error) {
                      console.error(`Failed to sell tokens for mint: ${mint} - ${(error as Error).message}`);
                      }
      





            }
            if ( tradeData.buy_token_amount == 0) {
              getTokenBalance(mint).then(async (balance) => {
                  if (balance) {  updatMintBuyTokens(mint,  balance); } 
              });
          }



        }

}