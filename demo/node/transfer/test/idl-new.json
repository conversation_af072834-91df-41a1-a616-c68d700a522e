{"address": "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P", "metadata": {"name": "pump", "version": "0.1.0", "spec": "0.1.0"}, "instructions": [{"name": "initialize", "docs": ["Creates the global state."], "discriminator": [175, 175, 109, 31, 13, 152, 155, 237], "accounts": [{"name": "global", "writable": true}, {"name": "user", "writable": true, "signer": true}, {"name": "system_program"}], "args": []}, {"name": "set_params", "docs": ["Sets the global state parameters."], "discriminator": [27, 234, 178, 52, 147, 2, 187, 141], "accounts": [{"name": "global", "writable": true}, {"name": "user", "writable": true, "signer": true}, {"name": "system_program"}, {"name": "event_authority"}, {"name": "program"}], "args": [{"name": "fee_recipient", "type": "pubkey"}, {"name": "initial_virtual_token_reserves", "type": "u64"}, {"name": "initial_virtual_sol_reserves", "type": "u64"}, {"name": "initial_real_token_reserves", "type": "u64"}, {"name": "token_total_supply", "type": "u64"}, {"name": "fee_basis_points", "type": "u64"}]}, {"name": "create", "docs": ["Creates a new coin and bonding curve."], "discriminator": [24, 30, 200, 40, 5, 28, 7, 119], "accounts": [{"name": "mint", "writable": true, "signer": true}, {"name": "mint_authority"}, {"name": "bonding_curve", "writable": true}, {"name": "associated_bonding_curve", "writable": true}, {"name": "global"}, {"name": "mpl_token_metadata"}, {"name": "metadata", "writable": true}, {"name": "user", "writable": true, "signer": true}, {"name": "system_program"}, {"name": "token_program"}, {"name": "associated_token_program"}, {"name": "rent"}, {"name": "event_authority"}, {"name": "program"}], "args": [{"name": "name", "type": "string"}, {"name": "symbol", "type": "string"}, {"name": "uri", "type": "string"}]}, {"name": "buy", "docs": ["Buys tokens from a bonding curve."], "discriminator": [102, 6, 61, 18, 1, 218, 235, 234], "accounts": [{"name": "global"}, {"name": "fee_recipient", "writable": true}, {"name": "mint"}, {"name": "bonding_curve", "writable": true}, {"name": "associated_bonding_curve", "writable": true}, {"name": "associated_user", "writable": true}, {"name": "user", "writable": true, "signer": true}, {"name": "system_program"}, {"name": "token_program"}, {"name": "rent"}, {"name": "event_authority"}, {"name": "program"}], "args": [{"name": "amount", "type": "u64"}, {"name": "max_sol_cost", "type": "u64"}]}, {"name": "sell", "docs": ["Sells tokens into a bonding curve."], "discriminator": [51, 230, 133, 164, 1, 127, 131, 173], "accounts": [{"name": "global"}, {"name": "fee_recipient", "writable": true}, {"name": "mint"}, {"name": "bonding_curve", "writable": true}, {"name": "associated_bonding_curve", "writable": true}, {"name": "associated_user", "writable": true}, {"name": "user", "writable": true, "signer": true}, {"name": "system_program"}, {"name": "associated_token_program"}, {"name": "token_program"}, {"name": "event_authority"}, {"name": "program"}], "args": [{"name": "amount", "type": "u64"}, {"name": "min_sol_output", "type": "u64"}]}, {"name": "withdraw", "docs": ["Allows the admin to withdraw liquidity for a migration once the bonding curve completes"], "discriminator": [183, 18, 70, 156, 148, 109, 161, 34], "accounts": [{"name": "global"}, {"name": "mint"}, {"name": "bonding_curve", "writable": true}, {"name": "associated_bonding_curve", "writable": true}, {"name": "associated_user", "writable": true}, {"name": "user", "writable": true, "signer": true}, {"name": "system_program"}, {"name": "token_program"}, {"name": "rent"}, {"name": "event_authority"}, {"name": "program"}], "args": []}], "accounts": [{"name": "Global", "discriminator": [167, 232, 232, 177, 200, 108, 114, 127]}, {"name": "BondingCurve", "discriminator": [23, 183, 248, 55, 96, 216, 172, 96]}], "events": [{"name": "CreateEvent", "discriminator": [27, 114, 169, 77, 222, 235, 99, 118]}, {"name": "TradeEvent", "discriminator": [189, 219, 127, 211, 78, 230, 97, 238]}, {"name": "CompleteEvent", "discriminator": [95, 114, 97, 156, 212, 46, 152, 8]}, {"name": "SetParamsEvent", "discriminator": [223, 195, 159, 246, 62, 48, 143, 131]}], "errors": [{"code": 6000, "name": "NotAuthorized", "msg": "The given account is not authorized to execute this instruction."}, {"code": 6001, "name": "AlreadyInitialized", "msg": "The program is already initialized."}, {"code": 6002, "name": "TooMuchSolRequired", "msg": "slippage: Too much SOL required to buy the given amount of tokens."}, {"code": 6003, "name": "TooLittleSolReceived", "msg": "slippage: Too little SOL received to sell the given amount of tokens."}, {"code": 6004, "name": "MintDoesNotMatchBondingCurve", "msg": "The mint does not match the bonding curve."}, {"code": 6005, "name": "BondingCurveComplete", "msg": "The bonding curve has completed and liquidity migrated to raydium."}, {"code": 6006, "name": "BondingCurveNotComplete", "msg": "The bonding curve has not completed."}, {"code": 6007, "name": "NotInitialized", "msg": "The program is not initialized."}], "types": [{"name": "Global", "type": {"kind": "struct", "fields": [{"name": "initialized", "type": "bool"}, {"name": "authority", "type": "pubkey"}, {"name": "fee_recipient", "type": "pubkey"}, {"name": "initial_virtual_token_reserves", "type": "u64"}, {"name": "initial_virtual_sol_reserves", "type": "u64"}, {"name": "initial_real_token_reserves", "type": "u64"}, {"name": "token_total_supply", "type": "u64"}, {"name": "fee_basis_points", "type": "u64"}]}}, {"name": "BondingCurve", "type": {"kind": "struct", "fields": [{"name": "virtual_token_reserves", "type": "u64"}, {"name": "virtual_sol_reserves", "type": "u64"}, {"name": "real_token_reserves", "type": "u64"}, {"name": "real_sol_reserves", "type": "u64"}, {"name": "token_total_supply", "type": "u64"}, {"name": "complete", "type": "bool"}]}}, {"name": "CreateEvent", "type": {"kind": "struct", "fields": [{"name": "name", "type": "string"}, {"name": "symbol", "type": "string"}, {"name": "uri", "type": "string"}, {"name": "mint", "type": "pubkey"}, {"name": "bonding_curve", "type": "pubkey"}, {"name": "user", "type": "pubkey"}]}}, {"name": "TradeEvent", "type": {"kind": "struct", "fields": [{"name": "mint", "type": "pubkey"}, {"name": "sol_amount", "type": "u64"}, {"name": "token_amount", "type": "u64"}, {"name": "is_buy", "type": "bool"}, {"name": "user", "type": "pubkey"}, {"name": "timestamp", "type": "i64"}, {"name": "virtual_sol_reserves", "type": "u64"}, {"name": "virtual_token_reserves", "type": "u64"}]}}, {"name": "CompleteEvent", "type": {"kind": "struct", "fields": [{"name": "user", "type": "pubkey"}, {"name": "mint", "type": "pubkey"}, {"name": "bonding_curve", "type": "pubkey"}, {"name": "timestamp", "type": "i64"}]}}, {"name": "SetParamsEvent", "type": {"kind": "struct", "fields": [{"name": "fee_recipient", "type": "pubkey"}, {"name": "initial_virtual_token_reserves", "type": "u64"}, {"name": "initial_virtual_sol_reserves", "type": "u64"}, {"name": "initial_real_token_reserves", "type": "u64"}, {"name": "token_total_supply", "type": "u64"}, {"name": "fee_basis_points", "type": "u64"}]}}]}