import { Connection, PublicKey, ParsedInstruction, MessageCompiledInstruction, CompiledInstruction, ConfirmedSignatureInfo } from '@solana/web3.js';
import bs58 from 'bs58';
import { BorshCoder } from '@coral-xyz/anchor';
import mysql, { RowDataPacket } from 'mysql2/promise';
import axios from 'axios';
import { publicKey } from '@coral-xyz/anchor/dist/cjs/utils';
import { getSolPrice,setMintStatus } from '../src/utils/db.js';
const idl = require("./idl-new.json");
import async_hooks from 'async_hooks';
import crypto from 'crypto';
import { sendRabbitMessage } from '../src/utils/rabbitmq.js';
import { getRuggerAccounts,getAllRuggerAccounts} from '../src/utils/db.js';

type TradeEvent = {
  mint: string;
  solAmount: number;
  tokenAmount: number;
  isBuy: boolean;
  user: string;
  timestamp: number;
  virtualSolReserves: number;
  virtualTokenReserves: number;
  realSolReserves: number;
  realTokenReserves: number;
  signature?: string;
  signer?: string;
  block_id?: number;
  app?: string;
};

let app = 'auto9999';
let skipTrades = false;
let skipCreate = true;
let limit = 1000;
let masterSignedAccounts: Set<string> = new Set();
let totalBuys = 0;
let totalSells = 0;
let lastBlockIdInIteration = 0;
let lastBlockId = 0;
let allSignaturesReversed: ConfirmedSignatureInfo[] = [];

// MySQL database credentials
const MYSQL_SERVER = 'kroocoin.xyz';
const MYSQL_PORT = 3306;
const MYSQL_USER = 'pump2';
const MYSQL_PASSWORD = 'pump2';
const MYSQL_DB = 'pump';
const solFallBackPrice=167;
let solPrice = solFallBackPrice;
 
const rpcUrl: string = 'https://solana-mainnet.g.alchemy.com/v2/********************************';

let accountAddress: string = '';
const PUMP_PROGRAM_ID = '6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P';
const EVENT_AUTHORITY = 'Ce6TQqeHC9p8KetsN6JsjHK7UTZk7nasjjnr7XxXp9F1';  // use to get the event data
const MASTER_ACCOUNT = 'Gh9J24j688AfsoiZQJiUGYtxb8RwhcZPJsQtiAJtLFAQ' ;


const connection = new Connection(rpcUrl, 'confirmed');


const pool = mysql.createPool({
  host: MYSQL_SERVER,
  port: MYSQL_PORT,
  user: MYSQL_USER,
  password: MYSQL_PASSWORD,
  database: MYSQL_DB,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
});

interface LastProcessedSignatureRow extends RowDataPacket {
  last_sig: string;
}

async function getLastProcessedSignature(accountAddress: string): Promise<string | null> {
  const connection = await pool.getConnection();
  try {
    const [rows]: [LastProcessedSignatureRow[], any] = await connection.query('SELECT last_sig FROM rugger_mints WHERE mint = ?', [accountAddress]);
    if (rows.length > 0) {
      return rows[0].last_sig;
    }
    return null;
  } catch (error) {
    console.error('Error fetching last processed signature:', error);
    return null;
  } finally {
    connection.release();
  }
}
async function saveLastProcessedSignature(accountAddress: string, signature: string): Promise<void> {
  const connection = await pool.getConnection();
  try {
    //console.log('Saving last processed signature:', signature);
    await connection.query('update rugger_mints set last_sig = ? where mint = ?', [signature,accountAddress]);
  } catch (error) {
    console.error('Error saving last processed signature:', error);
  } finally {
    connection.release();
  }
}


async function getSolPriceFromPump(): Promise<number> {
  try {
    const response = await axios.get('https://frontend-api-v3.pump.fun/sol-price');
    return response.data.solPrice;
  } catch (error) {
    //console.error('Error fetching SOL price:', error);
    return 200; // Default to 1 if there's an error
  }
}



async function getTransactions(app: string, accountAddress: string,ruggerAccounts: string[],lastProcessedSignature: string | null = null) {
  let swapData: TradeEvent[] = [];
  try {
    const publicKey = new PublicKey(accountAddress);
    /////////let lastProcessedSignature = await getLastProcessedSignature(accountAddress);
    //console.log('Last processed signature:', lastProcessedSignature);
    const before =  undefined;

    const signatures = await connection.getSignaturesForAddress(publicKey, { before, limit: limit });
   


    let tempSignatures: ConfirmedSignatureInfo[] = [];
    for (const signatureInfo of signatures) {
      tempSignatures.push(signatureInfo);
      if (signatureInfo.signature === lastProcessedSignature) {
        break;
      }
    }

    if (signatures.length > 0) {
      //lastProcessedSignature = swapData[0].signature ?? null;
      lastProcessedSignature = signatures[0].signature ?? null;
      if (lastProcessedSignature) {
        await saveLastProcessedSignature(accountAddress,lastProcessedSignature);
      }
    }else {
      console.log('No new signatures found');
      return;
    }



    const allSignatures=tempSignatures.reverse();

    let maxThreads =1;
    const promises = [];
    for (const signatureInfo of allSignatures) {
      await processTransaction(app,ruggerAccounts,signatureInfo.signature, swapData);
    }
    return lastProcessedSignature;
 


  } catch (err) {
    console.error('Error fetching transactions:', err);
  }
}


async function processTransaction(app: string, ruggerAccounts: string [],signature: string, swapData: TradeEvent[], newSwapData: TradeEvent[] = [], retries: number = 3) {
  try {
    if (!signature) {
      console.error('Invalid signature:', signature);
      return;
    }

    // Get transaction details with maxSupportedTransactionVersion parameter
    const transaction = await connection.getTransaction(signature, {
      maxSupportedTransactionVersion: 0,
    });
   
    if (transaction) {
      // Skip transactions that are in an error state
      if (transaction.meta && transaction.meta.err) {
        return;
      }
 
      // Get transaction slot id
      
      const block_id = transaction.slot; 

      try {
        const accountKeys = transaction.transaction.message.staticAccountKeys.map(key => key.toBase58());
       
        let signer = accountKeys[0] || '';
        // check if signer is in rugger accounts
        if (ruggerAccounts.includes(signer)) {
          signer = MASTER_ACCOUNT;
        }
        

        const innerInstructions = transaction.meta?.innerInstructions || [];
        for (const innerInstruction of innerInstructions) {
          for (const instruction of innerInstruction.instructions) {
            let innerAccountKeys;
            try {
              innerAccountKeys = instruction.accounts.map(index => accountKeys[index]);
            } catch (err) {
              console.error(`Error processing inner instruction for transaction ${signature}:`, err);
              continue;
            }
         
           // if (innerAccountKeys.length == 1 && innerAccountKeys[0] != EVENT_AUTHORITY) {
           //   continue;
           // }

            const coder = new BorshCoder(idl as any);
            const data = instruction.data;
            let args = coder.events.decode(Buffer.from(bs58.decode(data)).slice(8).toString('base64'));
            if (args) {
              if (args.name === "TradeEvent") {
                if (skipTrades) {
                  continue;
                }
                const data1 = decodeTradeEventValues(bs58.decode(data));

                if (data1) {
                  if (signer != MASTER_ACCOUNT) { 
                    let price = data1.solAmount / data1.tokenAmount;
                   
                    let marketCap = price * 1e6 * solPrice;



                    if (data1.isBuy) {
                      totalBuys += data1.solAmount;
                    } else {
                      totalSells += data1.solAmount;
                    }
                    // SELL HERE
                    const shortMint = data1.mint.slice(0, 4) ;
                    const currentTime = new Date().toLocaleTimeString('en-US', { hour12: false });
                    

                    const totalBuysFormatted = (totalBuys / 1e9).toFixed(3);
                    const totalSellsFormatted = (totalSells / 1e9).toFixed(3);
                    const netBuysSellsFormatted = ((totalBuys - totalSells) / 1e9).toFixed(3);
                    const solAmountFormatted = (data1.solAmount / 1e9).toFixed(3);
                    
                    let tmpTrade :TradeEvent[] = [{ ...data1, signature, signer, block_id,app }];
                    await saveTransactionsToDatabase(tmpTrade, solPrice);
                    //calculate uniq md5 hash  from combination of  mint, user, isBuy, amount, signature
                    const hash = crypto.createHash('md5').update(data1.mint + data1.user + data1.isBuy + data1.solAmount + signature).digest("hex");


                    sendRabbitMessage(JSON.stringify({"mint":data1.mint, "isBuy":data1.isBuy ,"amount":solAmountFormatted, "timestamp":data1.timestamp,"account":data1.user,"mcap":marketCap,"hash":hash,"app":app }),'trade');

                    console.log(`${shortMint} ${currentTime} ${totalBuysFormatted} ${totalSellsFormatted} ${netBuysSellsFormatted} ${data1.isBuy} ${solAmountFormatted} ${marketCap} ${data1.user}`);
           
                    
                    if (marketCap < 20000 && marketCap > 0 ) {
                      await setMintStatus(data1.mint, "inactive");
                      console.log("========= stting inactive.",marketCap);
                      
                      await sendRabbitMessage(JSON.stringify({"mint":data1.mint, "isBuy":data1.isBuy ,"amount":solAmountFormatted, "timestamp":data1.timestamp,"account":data1.user,"mcap":marketCap,"hash":hash,"app":app }),'trade');
                      console.log(`${shortMint} ${currentTime} ${totalBuysFormatted} ${totalSellsFormatted} ${netBuysSellsFormatted} ${data1.isBuy} ${solAmountFormatted} ${marketCap} ${data1.user}`);
           
                      
                       console.log("Market cap dropped below 20000. Closing WebSocket and forcibly exiting.");
                       process.exit(1); // Forcefully exit the spawned process
                       return;
                     }




                    //process.stdout.write(`\r${currentTime}  ${(totalBuys / 1e9).toFixed(3)} ${(totalSells / 1e9).toFixed(3)} ${((totalBuys - totalSells) / 1e9).toFixed(3)} ${data1.isBuy} ${(data1.solAmount / 1e9).toFixed(3)} ${marketCap} ${data1.user}`);
                  }else{
                    masterSignedAccounts.add(data1.user);
                    data1.signer = MASTER_ACCOUNT;
                    let tmpTrade :TradeEvent[] = [{ ...data1, signature, signer, block_id,app }];
                    //saveTransactionsToDatabase(tmpTrade, solPrice);

                  }




                }

                // if (data1 && data1.mint) {
                //   if (signer === MASTER_ACCOUNT) {
                //     masterSignedAccounts.add(data1.user);
                //     data1.signer = MASTER_ACCOUNT;
                //   }
                //   let tmpTrade :TradeEvent[] = [{ ...data1, signature, signer, block_id }];
                //   saveTransactionsToDatabase(tmpTrade, solPrice);
           
                // }
              } 
            }
          }
        }
      } catch (err) {
        console.error(`Error processing inner instructions for transaction ${signature}:`, err);
        if (retries > 0) {
          await new Promise(resolve => setTimeout(resolve, 100));
          await processTransaction(app,ruggerAccounts,signature, swapData, newSwapData, retries - 1);
        }
        return;
      }
    } else {
      if (retries > 0) {
        await processTransaction(app,ruggerAccounts,signature, swapData, newSwapData, retries - 1);
      }
    }
  } catch (err) {
    console.error(`Error processing transaction ${signature}:`, err);
    if (retries > 0) {
      await processTransaction(app,ruggerAccounts,signature, swapData, newSwapData, retries - 1);
    }
  }
}

async function saveTransactionsToDatabase(trades: TradeEvent[], solPrice: number) {
  const connection = await pool.getConnection();
  try {
    const query = `
      INSERT INTO token_tx (mint, account, sol_amount, token_amount, trade, price, market_cup, block_time, block_id, signature, signer,app)
      VALUES ?
    `;
    // before inserting check if there is no duplicates remove them
    const uniqueTrades = trades.filter((trade, index, self) => 
      index === self.findIndex((t) => (
      t.user === trade.user && t.signature === trade.signature && t.isBuy === trade.isBuy
      ))
    );

    const values = uniqueTrades.map(trade => {
      let price = trade.solAmount / trade.tokenAmount;
      let marketCap = price * 1e6 * solPrice;

      // Ensure price and marketCap are valid numbers
      if (!isFinite(price)) {
        price = 0;
      }
      if (!isFinite(marketCap)) {
        marketCap = 0;
      }

      const tradeType = trade.isBuy ? 'buy' : 'sell';

      return [
        trade.mint,
        trade.user,
        trade.solAmount.toString(),
        trade.tokenAmount.toString(),
        tradeType,
        price,
        marketCap,
        trade.timestamp,
        trade.block_id, // Use block_id instead of timestamp
        trade.signature,
        trade.signer,
        trade.app
      ];
    });

    await connection.query(query, [values]);
    //console.log('Transactions saved to database:', values.length);
  } catch (err) {
    
    //console.error('Error saving transactions to database:', err,trades);
  } finally {
    connection.release();
  }
}


function decodeTradeEventValues(data: Uint8Array): TradeEvent | null {
  const buffer = Buffer.from(data).slice(8); // Remove first 8 bytes for the event CPI

  const mint = bs58.encode(buffer.slice(8, 40));
  const solAmount = buffer.readBigUInt64LE(40);
  const tokenAmount = buffer.readBigUInt64LE(48);
  const isBuy = Boolean(buffer[56]);
  const user = bs58.encode(buffer.slice(57, 89));
  const timestamp = buffer.readBigInt64LE(89);
  const virtualSolReserves = buffer.readBigUInt64LE(97);
  const virtualTokenReserves = buffer.readBigUInt64LE(105);
  const realSolReserves = buffer.readBigUInt64LE(113);
  const realTokenReserves = buffer.readBigUInt64LE(121);

  return {
    mint,
    solAmount: Number(solAmount),
    tokenAmount: Number(tokenAmount),
    isBuy,
    user,
    timestamp: Number(timestamp),
    virtualSolReserves: Number(virtualSolReserves),
    virtualTokenReserves: Number(virtualTokenReserves),
    realSolReserves: Number(realSolReserves),
    realTokenReserves: Number(realTokenReserves),
  };
}


async function monitorAccounts(account: string,appLocal: string) {
  limit=100;
  let lastProcessedSignature = null;
  app=appLocal;
  while (true) {
      const ruggerAccountsFull = await getAllRuggerAccounts();
      const ruggerAccounts: string[] = ruggerAccountsFull.map((account: { address: string }) => account.address);
      

      lastProcessedSignature=await getTransactions(app,account,ruggerAccounts,lastProcessedSignature);
    
    limit=100;
    await new Promise(resolve => setTimeout(resolve, 1000)); // 1-second pause
    
  }
}

// Main entry point to start monitoring
(async function main() {
  try {
    //solPrice = await getSolPrice();


    const getPumpPrice= await getSolPriceFromPump();
    if (getPumpPrice > 0) {
      solPrice = getPumpPrice;
    }else{
      solPrice = solFallBackPrice;
    }

    console.log('===================== SOL price:', solPrice,'=====================');

    //console.log('SOL price:', solPrice);
    
    const account = process.argv[2];
    const app = process.argv[3];
    if (!account) {
      console.error('Please provide an account address as a command-line argument.');
      process.exit(1);
    }
    await monitorAccounts(account,app);
    console.log('Finished monitoring accounts');
  } catch (error) {
    console.error('Error starting monitoring process:', error);
  } finally {
    // Close the connection pool when done
    try {
      await pool.end();
      console.log('MySQL connection pool closed.');
    } catch (err) {
      console.error('Error closing MySQL pool:', err);
    }
    process.exit(0);
  }
})();




