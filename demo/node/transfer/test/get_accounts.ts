import { Connection, PublicKey } from '@solana/web3.js';
import { getTokenBalance } from '../src/trading.js';
import dotenv from 'dotenv';
import { PRIVATE_KEY } from '../src/config.js';
dotenv.config();


async function getPrice() {
    const connection = new Connection('https://solana-mainnet.g.alchemy.com/v2/********************************');
    const publicKey = new PublicKey('4zMMC9srt5Ri5X14GAgXhaHii3GnPAEERYPJgZJDncDU');
    const accountInfo = await connection.getAccountInfo(publicKey);
    
    if (accountInfo) {
        const balance = accountInfo.lamports;
        console.log(balance);
    } else {
        console.log('Account not found');
    }
}

async function main() {
    await getPrice();
    
    console.log(await getTokenBalance('8n9FpnwuBbgMaDpgHY1sXx7m76VWN3EhzKrC64ihpump', PRIVATE_KEY));

}

main();