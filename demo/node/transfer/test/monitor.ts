import { Connection, PublicKey, ParsedInstruction, MessageCompiledInstruction, CompiledInstruction, ConfirmedSignatureInfo } from '@solana/web3.js';
import bs58 from 'bs58';
import { BorshCoder } from '@coral-xyz/anchor';
import mysql, { RowDataPacket } from 'mysql2/promise';
import axios from 'axios';
import WebSocket from 'ws';
const idl = require("./idl-new.json");

type TradeEvent = {
  mint: string;
  solAmount: number;
  tokenAmount: number;
  isBuy: boolean;
  user: string;
  timestamp: number;
  virtualSolReserves: number;
  virtualTokenReserves: number;
  realSolReserves: number;
  realTokenReserves: number;
  signature?: string;
  signer?: string;
  block_id?: number;
};
const solFallBackPrice=200;


let swapData: TradeEvent[] = [];
let masterSignedAccounts: Set<string> = new Set();
let totalBuys = 0;
let totalSells = 0;
let startTime=0;
let timeDiff=0;
let lastBlockIdInIteration = 0;
let lastBlockId = 0;
let allSignaturesReversed: ConfirmedSignatureInfo[] = [];

// MySQL database credentials
const MYSQL_SERVER = 'kroocoin.xyz';
const MYSQL_PORT = 3306;
const MYSQL_USER = 'pump2';
const MYSQL_PASSWORD = 'pump2';
const MYSQL_DB = 'pump';

 
const rpcUrl: string = 'https://solana-mainnet.g.alchemy.com/v2/********************************';
//const rpcUrl: string = 'https://rpc.ankr.com/solana/3a2ca3f89ce8d11cbaa43746a9e34a4f6ed020d7a1ef37a62065e8323b1f32ac' ;

const wsUrl: string = 'wss://mainnet.helius-rpc.com/?api-key=7925fe36-55e9-4fb6-88df-5505a68b23e3';
let accountAddress: string = '';
const PUMP_PROGRAM_ID = '6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P';
const EVENT_AUTHORITY = 'Ce6TQqeHC9p8KetsN6JsjHK7UTZk7nasjjnr7XxXp9F1';  // use to get the event data
const MASTER_ACCOUNT = 'Gh9J24j688AfsoiZQJiUGYtxb8RwhcZPJsQtiAJtLFAQ' ;


const connection = new Connection(rpcUrl, 'confirmed');


const pool = mysql.createPool({
  host: MYSQL_SERVER,
  port: MYSQL_PORT,
  user: MYSQL_USER,
  password: MYSQL_PASSWORD,
  database: MYSQL_DB,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
});

async function getLastProcessedSignature(): Promise<string | null> {
  const connection = await pool.getConnection();
  try {
    const [rows]: [RowDataPacket[], any] = await connection.query('SELECT last_sig FROM mint_sig WHERE mint = ?', [accountAddress]);
    if (rows.length > 0) {
      return rows[0].last_sig;
    }
    return null;
  } catch (error) {
    console.error('Error fetching last processed signature:', error);
    return null;
  } finally {
    connection.release();
  }
}

async function saveLastProcessedSignature(signature: string): Promise<void> {
  const connection = await pool.getConnection();
  try {
    await connection.query('INSERT INTO mint_sig (mint, last_sig) VALUES (?, ?) ON DUPLICATE KEY UPDATE last_sig = VALUES(last_sig)', [accountAddress, signature]);
  } catch (error) {
    console.error('Error saving last processed signature:', error);
  } finally {
    connection.release();
  }
}

async function getSolPrice(): Promise<number> {
  try {
    const response = await axios.get('https://frontend-api-v3.pump.fun/sol-price');
    return response.data.solPrice;
  } catch (error) {
    //console.error('Error fetching SOL price:', error);
    return 200; // Default to 1 if there's an error
  }
}

async function getTransactions(solPrice: number) {
  try {
    const publicKey = new PublicKey(accountAddress);
    let lastProcessedSignature = await getLastProcessedSignature();

    //console.log('Last processed signature:', lastProcessedSignature);
    lastProcessedSignature = null;
    let before: string | undefined = undefined;
    const maxThreads = 10;

    // Clear swapData before fetching new transactions
    swapData = [];
    
    let allSignatures: ConfirmedSignatureInfo[] = [];
    while (true) {
      const signatures = await connection.getSignaturesForAddress(publicKey, { before, limit: 1000 });
      // last sitnature 


      console.log('Signatures (before):', before);
      if (signatures.length === 0) { 
        lastBlockId = lastBlockIdInIteration;
        break; }

      lastBlockIdInIteration = signatures[signatures.length - 1].slot;
      console.log("Last block id in iteration:", lastBlockIdInIteration);

      allSignatures.push(...signatures);

      before = signatures[signatures.length - 1].signature;
    }
    
    // revers all signatures
    //allSignatures=allSignatures.reverse();
    let tempSignatures: ConfirmedSignatureInfo[] = [];
    for (const signatureInfo of allSignatures) {
      tempSignatures.push(signatureInfo);
      if (signatureInfo.signature === lastProcessedSignature) {
        break;
      }
    }

    allSignatures=tempSignatures.reverse();

    const promises = [];
    for (const signatureInfo of allSignatures) {
      //console.log("Processing signature:", signatureInfo.signature);
      if (signatureInfo.signature === lastProcessedSignature) {
        console.log("Exiting loop");
        break;
      }
      if (promises.length >= maxThreads) {
        await Promise.all(promises);
        promises.length = 0;
      }
      promises.push(processTransaction(signatureInfo.signature, solPrice, ws));
    }

    await Promise.all(promises);

    // Update lastProcessedSignature after processing all signatures
    if (swapData.length > 0) {
      lastProcessedSignature = swapData[0].signature ?? null;
      if (lastProcessedSignature) {
        await saveLastProcessedSignature(lastProcessedSignature);
      }
    }
    console.log("Saving rows:",swapData.length);
    // Batch insert all processed transactions into the database
    // if ( swapData.length > 0 ) {
    //   await saveTransactionsToDatabase(swapData, solPrice);
    // }

  } catch (err) {
    console.error('Error fetching transactions:', err);
  }
}

let ws: WebSocket;
let shouldReconnect = true;

async function processTransaction(signature: string, solPrice: number, ws: WebSocket, newSwapData: TradeEvent[] = [], retries: number = 3) {
  try {
    if (!signature) {
      console.error('Invalid signature:', signature);
      return;
    }

    // Get transaction details with maxSupportedTransactionVersion parameter
    const transaction = await connection.getTransaction(signature, {
      maxSupportedTransactionVersion: 0,
    });

    if (transaction) {
      // Skip transactions that are in an error state
      if (transaction.meta && transaction.meta.err) {
        return;
      }
     // k4Pz2u95eCPMopVMQy9GgYoH1KgtxJSUrPmvVpteXgmcKp73AxGrXiGVVrjy3yJUZrFMh2bJ8fpmF2t5zt3oHwN
      // Get transaction slot id
      const block_id = transaction.slot; 

      try {
        const accountKeys = transaction.transaction.message.staticAccountKeys.map(key => key.toBase58());

        let signer = accountKeys[0] || '';
        if (block_id == lastBlockId) signer = MASTER_ACCOUNT;

        if (masterSignedAccounts.has(signer)) {
          signer = MASTER_ACCOUNT;
        }

        const innerInstructions = transaction.meta?.innerInstructions || [];
        for (const innerInstruction of innerInstructions) {
          for (const instruction of innerInstruction.instructions) {
            let innerAccountKeys;
            try {
              innerAccountKeys = instruction.accounts.map(index => accountKeys[index]);
            } catch (err) {
              console.error(`Error processing inner instruction for transaction ${signature}:`, err);
              continue;
            }
         
            //if (innerAccountKeys.length == 1 && innerAccountKeys[0] != EVENT_AUTHORITY) {
            //  continue;
           // }
            
            const coder = new BorshCoder(idl as any);
            const data = instruction.data;

            let args = coder.events.decode(Buffer.from(bs58.decode(data)).slice(8).toString('base64'));

            if (args) {
              if (args.name === "TradeEvent") {
                const data1 = decodeTradeEventValues(bs58.decode(data));

                if (data1) {
                  if (signer != MASTER_ACCOUNT) { 
                    let price = data1.solAmount / data1.tokenAmount;
                    let marketCap = price * 1e6 * solPrice;

                    if (marketCap < 6000 ) {
                      console.log("Market cap dropped below 20000. Closing WebSocket and forcibly exiting.");
                      shouldReconnect = false;
                      if (ws) ws.close(); // Ensure WebSocket is closed
                      process.exit(1); // Forcefully exit the spawned process
                      return;
                    }

                    if (data1.isBuy) {
                      totalBuys += data1.solAmount;
                    } else {
                      totalSells += data1.solAmount;
                    }
                    // SELL HERE
                    const date = new Date(data1.timestamp * 1000);
                    const hours = date.getUTCHours().toString().padStart(2, '0');
                    const minutes = date.getUTCMinutes().toString().padStart(2, '0');
                    const seconds = date.getUTCSeconds().toString().padStart(2, '0');
                    const formattedTime = `${hours}:${minutes}:${seconds}`;
                    if (transaction.blockTime)
                      { 
                         timeDiff = transaction.blockTime-startTime;
                      }
                    console.log( timeDiff,  formattedTime, (totalBuys / 1e9).toFixed(3), (totalSells / 1e9).toFixed(3), ((totalBuys - totalSells) / 1e9).toFixed(3), data1.isBuy, (data1.solAmount / 1e9).toFixed(3), marketCap, data1.user);
                  }
                }

                if (data1 && data1.mint) {
                  if (signer === MASTER_ACCOUNT) {
                    masterSignedAccounts.add(data1.user);
                    data1.signer = MASTER_ACCOUNT;
                  }

                  swapData.push({ ...data1, signature, signer, block_id });
                  newSwapData.push(data1);
                }
              } else if (args.name === "CreateEvent") {
                //console.log("CreateEvent:", args);
                startTime = transaction.blockTime ?? 0;
              }
            }
          }
        }
      } catch (err) {
        console.error(`Error processing inner instructions for transaction ${signature}:`, err);
        if (retries > 0) {
          await new Promise(resolve => setTimeout(resolve, 100));
          await processTransaction(signature, solPrice, ws, newSwapData, retries - 1);
        }
        return;
      }
    } else {
      if (retries > 0) {
        await processTransaction(signature, solPrice, ws, newSwapData, retries - 1);
      }
    }
  } catch (err) {
    console.error(`Error processing transaction ${signature}:`, err);
    if (retries > 0) {
      await processTransaction(signature, solPrice, ws, newSwapData, retries - 1);
    }
  }
}

async function saveTransactionsToDatabase(trades: TradeEvent[], solPrice: number) {
  const connection = await pool.getConnection();
  try {
    const query = `
      INSERT INTO token_tx (mint, account, sol_amount, token_amount, trade, price, market_cup, block_time, block_id, signature, signer)
      VALUES ?
      ON DUPLICATE KEY UPDATE
        sol_amount = VALUES(sol_amount),
        token_amount = VALUES(token_amount),
        trade = VALUES(trade),
        price = VALUES(price),
        market_cup = VALUES(market_cup),
        block_time = VALUES(block_time),
        block_id = VALUES(block_id),
        signer = VALUES(signer)
    `;
    const values = trades.map(trade => {
      let price = trade.solAmount / trade.tokenAmount;
      let marketCap = price * 1e6 * solPrice;

      // Ensure price and marketCap are valid numbers
      if (!isFinite(price)) {
        price = 0;
      }
      if (!isFinite(marketCap)) {
        marketCap = 0;
      }

      const tradeType = trade.isBuy ? 'buy' : 'sell';

      return [
        accountAddress,
        trade.user,
        trade.solAmount.toString(),
        trade.tokenAmount.toString(),
        tradeType,
        price,
        marketCap,
        trade.timestamp,
        trade.block_id, // Use block_id instead of timestamp
        trade.signature,
        trade.signer
      ];
    });

    await connection.query(query, [values]);
    //console.log('Transactions saved to database:', values.length);
  } catch (err) {
    console.error('Error saving transactions to database:', err,trades);
  } finally {
    connection.release();
  }
}

function extractSwapDetails(instructions: (MessageCompiledInstruction | CompiledInstruction)[], accountKeys: PublicKey[]): any[] {
  const swaps: any[] = [];
  const coder = new BorshCoder(idl as any);

  for (const instruction of instructions) {
    if (accountKeys[instruction.programIdIndex].toBase58() === PUMP_PROGRAM_ID) {
      const data = instruction.data;
      let buffer = Buffer.from(data);
      let args = coder.instruction.decode(buffer);

      if (args) {
        swaps.push(args);
      }
    }
  }
  return swaps;
}



function decodeTradeEventValues(data: Uint8Array): TradeEvent | null {
  const buffer = Buffer.from(data).slice(8); // Remove first 8 bytes for the event CPI

  const mint = bs58.encode(buffer.slice(8, 40));
  const solAmount = buffer.readBigUInt64LE(40);
  const tokenAmount = buffer.readBigUInt64LE(48);
  const isBuy = Boolean(buffer[56]);
  const user = bs58.encode(buffer.slice(57, 89));
  const timestamp = buffer.readBigInt64LE(89);
  const virtualSolReserves = buffer.readBigUInt64LE(97);
  const virtualTokenReserves = buffer.readBigUInt64LE(105);
  const realSolReserves = buffer.readBigUInt64LE(113);
  const realTokenReserves = buffer.readBigUInt64LE(121);

  return {
    mint,
    solAmount: Number(solAmount),
    tokenAmount: Number(tokenAmount),
    isBuy,
    user,
    timestamp: Number(timestamp),
    virtualSolReserves: Number(virtualSolReserves),
    virtualTokenReserves: Number(virtualTokenReserves),
    realSolReserves: Number(realSolReserves),
    realTokenReserves: Number(realTokenReserves),
  };
}

 

async function subscribeToAccountChanges(solPrice: number) {
  return new Promise<void>((resolve, reject) => {
    ws = new WebSocket(wsUrl);
    ws.onopen = () => {
      console.log('WebSocket connection established.');
      ws.send(JSON.stringify({
        jsonrpc: "2.0",
        id: 1,
        method: "logsSubscribe",
        params: [
          {
            mentions: [accountAddress]
          },
          {
            commitment: "confirmed"
          }
        ]
      }));
    };

    ws.onmessage = (event) => {
      const data = JSON.parse(event.data.toString());
      if (data.method === "logsNotification") {
        const signature = data.params.result.value.signature;
        if (signature) {
          swapData = [];
          processTransaction(signature, solPrice, ws).then(() => {
            saveLastProcessedSignature(signature);
            if (swapData.length > 0) {
              saveTransactionsToDatabase(swapData, solPrice).catch(err => {
                console.error('Error saving processed data to database:', err);
              });
            }
          }).catch(err => {
            console.error('Error processing transaction from WebSocket:', err);
          });
        } else {
          console.error('Invalid signature received:', data.params.result);
        }
      }
    };

    ws.onerror = (error) => {
      console.error('WebSocket error:', error);
      reject(error);
    };

    ws.onclose = () => {
      console.log('WebSocket connection closed.');
      if (shouldReconnect) {
        console.log('Reconnecting...');
        setTimeout(() => subscribeToAccountChanges(solPrice), 1000);
      } else {
        resolve();
      }
    };
  });
}

async function monitorAccount(accountAddress: string, ws: WebSocket) {
  const solPrice = await getSolPrice();
  await getTransactions(solPrice);

  ws.onmessage = (event) => {
    const data = JSON.parse(event.data.toString());
    if (data.method === "logsNotification") {
      const signature = data.params.result.value.signature;
      if (signature) {
        swapData = [];
        processTransaction(signature, solPrice, ws).then(() => {
          saveLastProcessedSignature(signature);
          if (swapData.length > 0) {
            saveTransactionsToDatabase(swapData, solPrice).catch(err => {
              console.error('Error saving processed data to database:', err);
            });
          }
        }).catch(err => {
          console.error('Error processing transaction from WebSocket:', err);
        });
      } else {
        console.error('Invalid signature received:', data.params.result);
      }
    }
  };

  ws.onclose = () => {
    console.log('WebSocket connection closed.');
  };

  console.log('monitorAccount finished');
}

// Function to close all resources
async function closeResources() {
  shouldReconnect = false;
  if (ws) {
    ws.close();
  }
  await pool.end();
}

// Main entry point to start monitoring
(async function main() {
  try {
    // Use accountAddress from process.argv
    const addrFromArgs = process.argv[2] || accountAddress;
    accountAddress = process.argv[2]  ;
    console.log('Starting monitor for:', accountAddress);

    // Open WebSocket and do all monitoring inside here
    const solPrice = await getSolPrice();
    await getTransactions(solPrice);
    //disale websocket
   // await subscribeToAccountChanges(solPrice);

  } catch (error) {
    console.error('Error starting monitor:', error);
    process.exit(1);
  }
})();

// Export the monitorAccount function and closeResources function
export { monitorAccount, closeResources };


