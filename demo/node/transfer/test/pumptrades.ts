import axios from 'axios';
import { promises as fs } from 'fs';
import path from 'path';
import { SocksProxyAgent } from 'socks-proxy-agent';
import { HttpsProxyAgent } from 'https-proxy-agent';
import { fileURLToPath } from 'url';

//const proxyAgent = new SocksProxyAgent('socks5://a81ad8141f53fcbd72ac__cr.ca:<EMAIL>:824');
const proxyAgent = new HttpsProxyAgent('http://a81ad8141f53fcbd72ac__cr.mv:<EMAIL>:823');
axios.defaults.httpsAgent = proxyAgent;

// Get the directory name of the current module
const __filename = path.join(process.cwd(), 'pumptrades.ts');
const __dirname = path.dirname(__filename);

const OFFSET_FILE = path.join(__dirname, 'offset.txt');
const LIMIT = 100;

const BASE_URL =
  'https://frontend-api.pump.fun/trades/all/J21ZZZb2rLy7YPgfvFNeL8EXDZaFQpKpj8B1tACDpump';

/** Define the structure of a trade object */
interface Trade {
  signature: string;
  mint: string;
  sol_amount: number;
  token_amount: number;
  is_buy: boolean;
  user: string;
  timestamp: number;
  tx_index: number;
  username: string | null;
  profile_image: string | null;
  slot: number;
}

/** Reads the last offset from file (or returns 0 if not found) */
async function getLastOffset(): Promise<number> {
  try {
    const data = await fs.readFile(OFFSET_FILE, 'utf8');
    const offset = parseInt(data, 10);
    return isNaN(offset) ? 0 : offset;
  } catch (error) {
    // File doesn't exist – start from offset 0.
    return 0;
  }
}

/** Persists the current offset to file */
async function saveOffset(offset: number): Promise<void> {
  await fs.writeFile(OFFSET_FILE, offset.toString(), 'utf8');
}

/** Fetches a batch of trades given the offset */
async function fetchTrades(offset: number): Promise<Trade[]> {
  const url = `${BASE_URL}?limit=${LIMIT}&offset=${offset}&minimumSize=0`;
  const response = await axios.get(url);
  return response.data as Trade[];
}

/** Main execution function */
async function main() {
  // Retrieve the last processed offset or start at 0.
  let offset = await getLastOffset();
  console.log(`Starting from offset: ${offset}`);

  const signatures: string[] = [];

  while (true) {
    console.log(`Fetching trades at offset: ${offset}`);
    let trades: Trade[];
    try {
      trades = await fetchTrades(offset);
    } catch (error) {
      console.error('Error fetching data:', error);
      break;
    }

    if (trades.length === 0) {
      console.log('No new trades available. Waiting for new trades...');
      // Wait for 5 seconds before trying again
      await new Promise(resolve => setTimeout(resolve, 0));
      continue;
    }

    // Extract the signature from each trade.
    trades.forEach((trade) => signatures.push(trade.signature));

    // Update the offset. (Using the number of items returned ensures that if the
    // last batch is less than LIMIT, the offset is accurate.)
    offset += trades.length;
    await saveOffset(offset);
    console.log(`Updated offset to ${offset}`);

    // Wait for 100 milliseconds before the next iteration.
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  // When complete, print only the signatures.
  console.log('Signatures:');
  signatures.forEach((sig) => console.log(sig));
}

main().catch((err) => {
  console.error('Unhandled error:', err);
});
