
import dotenv from 'dotenv';
import { setBuyCouner,setMintStatus,updatMintTradeDataBuy,updatMintTradeDataSell ,updatMintBuyTokens,updatMintBuyTx,updateMaxMC,updateLastSoldTime,getTimeSinceLastSold} from './src/utils/db.js';
import { sellTokenPercentage,buyToken,getTokenBalance } from './src/index.js';


/// Add buy logic. if triggeted see if after 10 seconds not rugged buy again . 
const rebuyAfterSeconds = 10;

export async function tradeBuySellAuto1(buySol:number, mint: string, isBuy: boolean, amount: number, mcap: number, tradeData: any) {


        if (tradeData.trade_status === 'active' ) {
          /**          ===== BUY CONDITION =====
            *            My when condition met
            *            update database set "bought" and save by_tx
            * 
            * Notes: max 2 sol at 4 min 
            * no more than 2 buys max time 20 from start time_diff_seconds
            */
               let buy_counter=0;
               if(tradeData.buy_counter){ buy_counter=tradeData.buy_counter; }
          
                let buy=false;
                if (tradeData.cumulative_diff < 1.4 && mcap < 35000   ){
                  buy=true; 
                }
                // if (mcap > 27000 && mcap < 40000 && tradeData.time_diff_seconds < 20 * 60 && buy_counter < 1) {
                //   if (tradeData.time_diff_seconds > 60 && tradeData.time_diff_seconds <= 120 && tradeData.cumulative_diff < 1.2) {
                //           buy=true;
                //   } else if (tradeData.time_diff_seconds > 120 && tradeData.cumulative_diff < 1.6) {
                //           buy=true;
                //   }
                //  }
             // buy=false;
              if (buy){ 
                await updatMintTradeDataBuy(mint, 'bought', buySol, 0,'', mcap);
              
                buyToken(mint, `${buySol}`,"0.5", 'Low').then(async (tx) => {
                  console.log(`Bought: ${mint} - ${tx}`);
                  if (tx) {
                    await updatMintBuyTx(mint, tx);
                    setBuyCouner(mint, buy_counter+1);
                  }
    
                });
            }




        } else if (tradeData.trade_status === 'bought' || tradeData.buy_token_amount > 0 ) {

          /**       ===== SELL CONDITION =====
           *    See if condition met SELL!!  twice.. so its for sure
           *    update database set "sold" and save sell_tx
           *  || mcap > 60000 || mcap < 22000
           */
           let sell = false;
           if (tradeData.cumulative_diff > 1.7 || (isBuy && amount > 0.90) || ( tradeData.cumulative_diff > 1.5 && tradeData.time_diff_seconds < 60 ) ){
              sell=true;
           } else if (mcap > 40000 || mcap < 10000){
              sell=true;
           }

             
                if (sell){
                      console.log(`======= SELL: ${mint} - ${mcap} - ${tradeData.cumulative_diff} - ${tradeData.time_diff_seconds}`);
                      
                      // SELL once 
                      await updatMintTradeDataSell(mint, 'sold', buySol, 0, '' , mcap);

                      try {
                      //sellTokenPercentage(mint, "100", "1", 'Low');
                      await sellTokenPercentage(mint, "100", "1", 'Low').then(async (tx) => {
                        console.log(`Sold: ${mint} - ${tx}`);
                        await updatMintTradeDataSell(mint, 'sold', buySol, 0, '' , mcap);
                        await updateLastSoldTime(mint);

                      });
                      } catch (error) {
                        await updatMintTradeDataBuy(mint, 'bought', buySol, 0,'', mcap);

                      console.error(`Failed to sell tokens for mint: ${mint} - ${(error as Error).message}`);
                      }



                      // Sell twice - it will fails if already sold
                      try {
                      setTimeout(async () => {
                        try {

                          getTokenBalance(mint).then(async (balance) => {
                            if (balance && balance > 0) {  
                              
                                  await sellTokenPercentage(mint, "100", "1", 'Low').then(async (tx) => {
                                    console.log(`Sold: ${mint} - ${tx}`);
                                    await updatMintTradeDataSell(mint, 'sold', buySol, 0, '', mcap);
                                    await updateLastSoldTime(mint);
                                  });
                                
                             } 
                          });

                          // get balance


                        } catch (error) {
                          console.error(`Failed to sell tokens for mint: ${mint} - ${(error as Error).message}`);
                        }
                      }, 6000);
                    } catch (error) {
                      console.error(`Failed to sell tokens for mint: ${mint} - ${(error as Error).message}`);
                      }
      





            }
            if ( tradeData.buy_token_amount == 0) {
              getTokenBalance(mint).then(async (balance) => {
                  if (balance) {  updatMintBuyTokens(mint,  balance); } 
              });
          }


        } else if (tradeData.trade_status === 'sold' ) {

            // if not rugged 10 after I sell set to active
            // getTimeSinceLastSold(mint).then(async (last_sold_time) => {
            //   if (last_sold_time && last_sold_time > rebuyAfterSeconds  ) {
            //     //await setMintStatus(mint, 'active');

            //    console.log(`RESET TO ACTIVE: ${last_sold_time} - ${mint} - ${mcap}`);
            //   }
            // });


        } else if (tradeData.trade_status === 'inactive' ) {

          getTokenBalance(mint).then(async (balance) => {
            if (balance && balance > 0) {  
              
                  await sellTokenPercentage(mint, "100", "0.5", 'Low').then(async (tx) => {
                    console.log(`Sold: ${mint} - ${tx}`);
                    await updatMintTradeDataSell(mint, 'sold', buySol, 0, '', mcap);
                    await updateLastSoldTime(mint);
                  });
                
             } 
          });

        }

}