#!/usr/bin/env python3
import websocket
import json
import threading
import time

# Function to send a request to the WebSocket server
def send_request(ws):
    request = {
        "jsonrpc": "2.0",
        "id": 420,
        "method": "transactionSubscribe",
        "params": [
            {
                "accountInclude": ["BMocPvavt5CLXvJpQohTRbg17WhPMfkFxibHEQpSpump"]
            },
            {
                "commitment": "processed",
                "encoding": "jsonParsed",
                "transactionDetails": "full",
                "showRewards": True,
                "maxSupportedTransactionVersion": 0
            }
        ]
    }
    ws.send(json.dumps(request))

# Function to send a ping to the WebSocket server
def start_ping(ws):
    def ping():
        while True:
            if ws.sock and ws.sock.connected:
                ws.send('{"jsonrpc": "2.0", "method": "ping"}')
                print('Ping sent')
            time.sleep(30)  # Ping every 30 seconds
    threading.Thread(target=ping).start()

# Define WebSocket event handlers
def on_open(ws):
    print('WebSocket is open')
    send_request(ws)  # Send a request once the WebSocket is open
    start_ping(ws)    # Start sending pings

def on_message(ws, message):
    try:
        message_obj = json.loads(message)
        print('Received:', message_obj)
    except json.JSONDecodeError as e:
        print('Failed to parse JSON:', e)

def on_error(ws, error):
    print('WebSocket error:', error)

def on_close(ws, close_status_code, close_msg):
    print('WebSocket is closed')

# Create a WebSocket connection


ws_url = 'wss://atlas-mainnet.helius-rpc.com/?api-key=12728bae-550f-4f59-a42c-94b89d9e86b8'
ws = websocket.WebSocketApp(ws_url,
                            on_open=on_open,
                            on_message=on_message,
                            on_error=on_error,
                            on_close=on_close)

# Run the WebSocket in a separate thread
ws_thread = threading.Thread(target=ws.run_forever)
ws_thread.start()
