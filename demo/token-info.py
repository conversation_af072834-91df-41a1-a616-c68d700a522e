#!/usr/bin/env python3
import requests,time,logging,os


def fetch_token_price(token):
    try:
        url = f'https://swap-v2.solanatracker.io/rate?from=So11111111111111111111111111111111111111112&to={token}&amount=1&slippage=10'
        headers = {'authority': 'swap-v2.solanatracker.io'}
        response = requests.get(url, headers=headers)
        response.raise_for_status()  # Raises an exception for HTTP errors
        data = response.json()
        return data["currentPrice"]
    except Exception as e:
        logging.info(f"Failed to fetch token price for {token}: {e}")
        return None


def get_token_info(token_address):
    url = f"https://public-api.birdeye.so/defi/price?address={token_address}"
    headers = {"X-API-KEY": "957cd536c6344fd3a1d3bc2e37674d12"}
    response = requests.get(url, headers=headers)
    return response.json()

if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser(description='Fetch token price')
    parser.add_argument('--token', type=str, help='Token address')
    args = parser.parse_args()

    while True:
        #print(get_token_info("BMocPvavt5CLXvJpQohTRbg17WhPMfkFxibHEQpSpump"))
        print(fetch_token_price(args.token))


