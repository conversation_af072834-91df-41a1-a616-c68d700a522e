#!/usr/bin/env python3

import json

def get_ui_amount(token_balance):
    """
    Helper function to safely get the uiAmount from the token balance.
    """
    ui_amount = token_balance['uiTokenAmount']['uiAmount']
    return float(ui_amount) if ui_amount is not None else 0.0

def get_swap_pairs(data):
    # Extract relevant information from the transaction data
    pre_token_balances = data['meta']['preTokenBalances']
    post_token_balances = data['meta']['postTokenBalances']

    swaps = []

    # Iterate through the token balances to find swaps
    for pre, post in zip(pre_token_balances, post_token_balances):
        pre_amount = get_ui_amount(pre)
        post_amount = get_ui_amount(post)

        if pre_amount != post_amount:
            swaps.append({
                "mint": pre['mint'],
                "pre_amount": pre_amount,
                "post_amount": post_amount,
                "change": post_amount - pre_amount,
                "owner": pre['owner'],
                "account_index": pre['accountIndex']
            })

    return swaps

def get_native_balance_change(data, fee_payer_index):
    pre_balances = data['meta']['preBalances']
    post_balances = data['meta']['postBalances']

    pre_balance = pre_balances[fee_payer_index]
    post_balance = post_balances[fee_payer_index]

    return (pre_balance - post_balance) / 1e9  # Convert lamports to SOL

def main():
    # Read the JSON data from the file
    with open('data.json', 'r') as f:
        data = json.load(f)

    fee_payer_index = 0
    fee_payer = data['transaction']['accountKeys'][fee_payer_index]['pubkey']
    swaps = get_swap_pairs(data)
    native_balance_change = get_native_balance_change(data, fee_payer_index)

    #print(f"Fee Payer: {fee_payer}")
    print(f"Native Balance Change: {native_balance_change:.9f} SOL")
    #print("Detected Swaps involving Fee Payer:")
    for swap in swaps:
        account_pubkey = data['transaction']['accountKeys'][swap['account_index']]['pubkey']
        if swap['owner'] == fee_payer:
            if swap['change'] < 0:
                print(f"Swapped out {abs(swap['change'])} of {swap['mint']} from account {account_pubkey} (owner: {swap['owner']})")
            else:
                print(f"Received {swap['change']} of {swap['mint']} to account {account_pubkey} (owner: {swap['owner']})")

if __name__ == "__main__":
    main()
