#!/usr/bin/env python3
import websocket
import json
import threading
import time

JUPITER_FEE = [
    "45ruCyfdRkWpRNGEqWzjCiXRHkZs8WXCLQ67Pnpye7Hp"
]

SYSTEM_FEE_ACCOUNT="HWEoBxYs7ssKuudEjzjmpfJVX7Dvi7wescFsVx2L5yoY"
PUMP_FEE_ACCOUNT="CebN5WGQ4jvEPvsVU4EoHEpgzq1VV7AbicfhtW4xC9iM"
JITO_FEE = [
    "96gYZGLnJYVFmbjzopPSU6QiEV5fGqZNyN9nmNhvrZU5",
    "HFqU5x63VTqvQss8hp11i4wVV8bD44PvwucfZ2bU7gRe",
    "Cw8CFyM9FkoMi7K7Crf6HNQqf4uEMzpKw6QNghXLvLkY",
    "ADaUMid9yfUytqMBgopwjb2DTLSokTSzL1zt6iGPaS49",
    "DfXygSm4jCyNCybVYYK6DwvWqjKee8pbDmJGcLWNDXjh",
    "ADuUkR4vqLUMWXxW9gh6D6L8pMSawimctcNZ5pGwDcEt",
    "DttWaMuVvTiduZRnguLF7jNxTgiMBZ1hyAumKUiL2KRL",
    "3AVi9Tg9Uo68tJfuvoKvqKNWKkC5wPdSSdeBnizKZ6jT"
]
RAYDIUM = "5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1"
METEOR = "2MFoS3MPtvyQ4Wh4M9pdfPjz6UhVoNbFbGJAskCPCj3h"

AUTHORITIES=[RAYDIUM,METEOR]
FEE_ACCOUNTS=[SYSTEM_FEE_ACCOUNT,PUMP_FEE_ACCOUNT]+JITO_FEE+JUPITER_FEE


pump_address="Hc5oUkbMAmLHRmyxzxNkkVhsqSAn5A9LEjcCqZH7pump"
# Function to send a request to the WebSocket server




def process_solana_transaction(row):

    def _check_chain_(transfers,pump_address):
        chain = []
        last={}
        first=True
        for transfer in transfers:
            if first:
                chain.append(transfer)
                last=transfer
            else:
                if last["destination"] == transfer["source"]:
                    chain.append(transfer)
                    last=transfer
                # else:
                #     print(f"Chain length: {len(chain)}")
                #     print(chain)
                #     chain=[]
                #     chain.append(transfer)
                #     last=transfer

        print(f"Transfer len:{len(transfers)}/{len(chain)}")
        final=[]
        for row in chain:
            if row["mint"] == pump_address or row["mint"] == "So11111111111111111111111111111111111111112":
                if  row['source'] in [RAYDIUM,METEOR] or row["destination"] in [RAYDIUM,METEOR]:  
                    #print(f"Source: {row['source']} Destination: {row['destination']} Amount: {row['amount']} Mint: {row['mint']}")
                    final.append(row)
        def print_entry(row):
            print(f"Source: {row['source']} Destination: {row['destination']} Amount: {row['amount']} Mint: {row['mint']}")

        if len(final) > 1:
            # Print first and last entry 
            print_entry(final[0])
            print_entry(final[-1])


    # END of check_chain



    transfers=[]
    token_owner={}
    instructions = row['transaction']['message']['instructions']
    account_keys = row['transaction']['message']['accountKeys']

    meta = row['meta']
    post_token_balances = meta['postTokenBalances']
    pre_token_balances = meta['preTokenBalances']


        

    def get_account_address_by_index(index):
        return account_keys[index]["pubkey"]
    
    def get_owner_by_token_account(tokenAccount):
        found=False
        for balance in meta["preTokenBalances"]:
            if get_account_address_by_index(balance["accountIndex"]) == tokenAccount:
                return balance["owner"],balance["mint"]
        if tokenAccount in token_owner:
            return token_owner[tokenAccount]["owner"],token_owner[tokenAccount]["mint"]

        if found == False:
            return tokenAccount,"None or SOL"
        
    def process_tx(instruction):
            if instruction["program"] == "system" and instruction["parsed"]["type"] == "transfer":
                source = instruction["parsed"]["info"]["source"]
                destination = instruction["parsed"]["info"]["destination"]
                amount = instruction["parsed"]["info"]["lamports"]
                #print(f"Instruction: (transfer) Source: {source} Destination: {destination} Amount: {amount}  lamports")


            if instruction["program"] == "spl-associated-token-account" and  instruction["parsed"]["type"] == "createIdempotent":
                #print(f"Instruction: (createImpodent) Create associated token account owner:{instruction["parsed"]["info"]["source"]}  new splaccount:{instruction["parsed"]["info"]["account"]}  mint:{instruction["parsed"]["info"]["mint"]}")
                token_owner[instruction["parsed"]["info"]["account"]]={"owner":instruction["parsed"]["info"]["source"],"mint":instruction["parsed"]["info"]["mint"]}
            
            if instruction["program"] == "spl-token" and instruction["parsed"]["type"] == "closeAccount":
                pass
                #print(f"Instruction: (closeAccount) owner:{instruction["parsed"]["info"]["owner"]}  closed account :{instruction["parsed"]["info"]["account"]}  ")
                       
            if instruction["program"] == "system" and instruction["parsed"]["type"] == "createAccount":
                #print(f"Instruction: (createAccount) owner:{instruction["parsed"]["info"]["source"]}  new account :{instruction["parsed"]["info"]["newAccount"]}  sum:{instruction["parsed"]["info"]["lamports"]}")
                pass
                #token_owner[instruction["parsed"]["info"]["newAccount"]]={"owner":instruction["parsed"]["info"]["source"],"mint":"So11111111111111111111111111111111111111112"}     
            if instruction["program"] == "spl-token" and instruction["parsed"]["type"] == "initializeAccount3":
                token_owner[instruction["parsed"]["info"]["account"]]={"owner":instruction["parsed"]["info"]["owner"],"mint":instruction["parsed"]["info"]["mint"]} 
                #print(f"Instruction: (initializeAccount3)  Set token for account :{instruction["parsed"]["info"]["account"]}  mint:{instruction["parsed"]["info"]["mint"]}  owner:{instruction["parsed"]["info"]["owner"]}")
                 
            if instruction["program"] == "spl-token" and instruction["parsed"]["type"] == "transfer":
                if "multisigAuthority" in instruction["parsed"]["info"]:
                    source_owner,_=get_owner_by_token_account(instruction["parsed"]["info"]["multisigAuthority"])
                else:
                    source_owner,_=get_owner_by_token_account(instruction["parsed"]["info"]["authority"])
                
                destination_owner,destination_mint=get_owner_by_token_account(instruction["parsed"]["info"]["destination"]) 

                #print(f"Instruction: (spl-transfer)  Source:{instruction["parsed"]["info"]["source"]}  Destination:{instruction["parsed"]["info"]["destination"]}  Amount:{instruction["parsed"]["info"]["amount"]} mint:{destination_mint}" )
                ###print(f"Instruction: (spl-transfer)  Source:{source_owner}  Destination:{destination_owner}  Amount:{instruction["parsed"]["info"]["amount"]} mint:{destination_mint}" )

                if destination_mint == pump_address:
                    amount=int(instruction["parsed"]["info"]["amount"])/1000000
                elif destination_mint == "So11111111111111111111111111111111111111112":
                    amount=int(instruction["parsed"]["info"]["amount"])/**********
                else:
                    amount=int(instruction["parsed"]["info"]["amount"])

                transfers.append({
                    'source': source_owner,
                    'destination': destination_owner,
                    'amount': amount,
                    'mint': destination_mint
                })


            if instruction["program"] == "spl-token" and instruction["parsed"]["type"] == "transferChecked":

                if "multisigAuthority" in instruction["parsed"]["info"]:
                    source_owner,_=get_owner_by_token_account(instruction["parsed"]["info"]["multisigAuthority"])
                else:
                    source_owner,_=get_owner_by_token_account(instruction["parsed"]["info"]["authority"])
                destination_owner,destination_mint=get_owner_by_token_account(instruction["parsed"]["info"]["destination"]) 
                ###print(f"Instruction: (spl-transferChecked)  Source:{source_owner}  Destination:{destination_owner}  Amount:{instruction["parsed"]["info"]["tokenAmount"]["uiAmount"]} mint:{instruction["parsed"]["info"]["mint"]}" )

                transfers.append({
                    'source': source_owner,
                    'destination': destination_owner,
                    'amount': instruction["parsed"]["info"]["tokenAmount"]["uiAmount"],
                    'mint': instruction["parsed"]["info"]["mint"]
                })

    for index,instruction in enumerate(instructions):
        if "program" in instruction:
            # Check if the instruction is for the token program
            process_tx(instruction)
            for inner_instruction in meta["innerInstructions"]:
                if inner_instruction["index"] == index:
                        for ii in inner_instruction["instructions"]:
                            if "program" in ii:
                                process_tx(ii)
        else: 
            for inner_instruction in meta["innerInstructions"]:
                if inner_instruction["index"] == index:
                        for ii in inner_instruction["instructions"]:
                            if "program" in ii:
                                process_tx(ii)
    _check_chain_(transfers,pump_address)




def send_request(ws):
    request = {
        "jsonrpc": "2.0",
        "id": "1",
        "method": "blockSubscribe",
        "params": [
            {
            "mentionsAccountOrProgram": pump_address
            },
            {
            "commitment": "confirmed",
            "encoding": "jsonParsed",
            "showRewards": True,
            "transactionDetails": "full",
            "maxSupportedTransactionVersion": 0
            }
        ]
        }
    ws.send(json.dumps(request))

# Function to send a ping to the WebSocket server
def start_ping(ws):
    def ping():
        while True:
            if ws.sock and ws.sock.connected:
                ws.send('{"jsonrpc": "2.0", "method": "ping"}')
                #print('Ping sent')
            time.sleep(30)  # Ping every 30 seconds
    threading.Thread(target=ping).start()

# Define WebSocket event handlers
def on_open(ws):
    print('WebSocket is open')
    send_request(ws)  # Send a request once the WebSocket is open
    start_ping(ws)    # Start sending pings

def on_message(ws, message):
    if True:
        message_obj = json.loads(message)
        if "params" in message_obj:
            for row in message_obj["params"]["result"]["value"]["block"]["transactions"]:
                if row["meta"]["err"]==None:
                    print(f"========================= {row["transaction"]["signatures"][0]}  ================= { row['transaction']['message']['accountKeys'][0]['pubkey']} =====================")
                    process_solana_transaction(row)
         
                  

def on_error(ws, error):
    print('WebSocket error:', error)

def on_close(ws, close_status_code, close_msg):
    print('WebSocket is closed')

# Create a WebSocket connection


ws_url = 'wss://go.getblock.io/7fd33c9eaa0e45bfa986ee41b9d216e2'
ws_url ='wss://little-sleek-mansion.solana-mainnet.quiknode.pro/4b69dc2b49ac07ae787d9c65b8a33ccee7a904d2'
ws = websocket.WebSocketApp(ws_url,
                            on_open=on_open,
                            on_message=on_message,
                            on_error=on_error,
                            on_close=on_close)

# Run the WebSocket in a separate thread
ws_thread = threading.Thread(target=ws.run_forever)
ws_thread.start()

