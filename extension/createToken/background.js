// Add the "Create Token" option to the context menu
chrome.runtime.onInstalled.addListener(() => {
    chrome.contextMenus.create({
      id: "createToken",
      title: "Create Token",
      contexts: ["image"] // Only show the menu when right-clicking an image
    });
  });
  
  // Handle the click event for the "Create Token" option
  chrome.contextMenus.onClicked.addListener((info, tab) => {
    if (info.menuItemId === "createToken" && info.srcUrl) {
      // Construct the URL with the current page URL and image URL
      const currentPageURL = encodeURIComponent(tab.url);
      const imageURL = encodeURIComponent(info.srcUrl);
      const fullURL = `https://kroocoin.xyz/test?link_url=${currentPageURL}&image_url=${imageURL}`;
  
      // Open the URL in a new tab
      chrome.tabs.create({ url: fullURL });
    }
  });
  