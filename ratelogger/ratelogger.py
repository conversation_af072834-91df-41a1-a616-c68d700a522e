import os
import pika
import requests
import threading
import time
import logging
import mysql.connector
from mysql.connector import errorcode
from datetime import datetime

logger = logging.getLogger('newtokens')
logger.setLevel(logging.INFO)

# Create console handler and set level to info
ch = logging.StreamHandler()
ch.setLevel(logging.INFO)

# Create formatter and add it to the handler
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
ch.setFormatter(formatter)

# Add the handler to the logger
logger.addHandler(ch)

# Suppress logging from other modules
logging.getLogger('asyncio').setLevel(logging.WARNING)
logging.getLogger('pika').setLevel(logging.WARNING)
logging.getLogger('requests').setLevel(logging.WARNING)
logging.getLogger('solana').setLevel(logging.WARNING)
logging.getLogger('solders').setLevel(logging.WARNING)




# Database connection parameters
db_config = {
    'user': os.getenv('DB_USER', 'root'),
    'password': os.getenv('DB_PASSWORD', 'root'),
    'host': os.getenv('DB_HOST', '127.0.0.1'),
    'database': os.getenv('DB_NAME', 'pump'),
    'raise_on_warnings': True,
    'auth_plugin': 'mysql_native_password',
    'ssl_disabled': True
}

# Collection interval and duration
COLLECTION_INTERVAL = 5  # in seconds
COLLECTION_DURATION = 600  # in seconds


def get_supply(token):
    url = 'https://solana-mainnet.api.syndica.io/api-key/4TMsFG1pLWMP4zpdF895C1kYrML8t6AXKQjFB3NzRh1xMjfcMAFPTwCmpRJWa1i72SKTFxjyxBMz7uAkfJoRfR5ZX4kQURk2wwc'
    headers = {
        'Content-Type': 'application/json'
    }
    payload = {
        "jsonrpc": "2.0",
        "id": 1,
        "method": "getTokenSupply",
        "params": [token]
    }

    response = requests.post(url, headers=headers, json=payload)
    if response.status_code == 200:
        response_json = response.json()
        if "result" in response_json and "value" in response_json["result"]:
            return 4000/140/float(response_json["result"]["value"]["uiAmount"])
        else:
            raise ValueError("Unexpected response structure")
    else:
        response.raise_for_status()


# Function to fetch token price
def fetch_token_price(token):
    try:
        url = f'https://swap-v2.solanatracker.io/rate?from=So11111111111111111111111111111111111111112&to={token}&amount=1&slippage=10'
        headers = {'authority': 'swap-v2.solanatracker.io'}
        response = requests.get(url, headers=headers)
        response.raise_for_status()  # Raises an exception for HTTP errors
        data = response.json()
        return data["currentPrice"]
    except Exception as e:
        logger.info(f"Failed to fetch token price for {token}: {e}")
        return None

# Function to store data in the database
def store_data(token, token_name, rate):
    conn = None
    cursor = None
    try:
        conn = mysql.connector.connect(**db_config)
        cursor = conn.cursor()
        query = "INSERT INTO metrics (token, token_name, created_at, rate) VALUES (%s, %s, %s, %s)"
        created_at = datetime.now()
        cursor.execute(query, (token, token_name, created_at, rate))
        conn.commit()
    except mysql.connector.Error as err:
        logger.info(f"Failed to store data in the database: {err}")
    except Exception as e:
        logger.info(f"Unexpected error when storing data: {e}")
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

# Function to collect and store token prices
def collect_token_prices(token, token_name):
    end_time = time.time() + COLLECTION_DURATION
    while time.time() < end_time:
        rate = fetch_token_price(token)
        #rate = get_supply(token)
        if rate is not None:
            created_at = datetime.now()
            logger.info(f"{created_at} - {token_name} - Price: {rate}")
            store_data(token, token_name, rate)
        time.sleep(COLLECTION_INTERVAL)

# Function to process messages from RabbitMQ
def on_message(ch, method, properties, body):
    try:
        token_data = body.decode('utf-8')
        token = token_data.strip()  # Assuming the message is just the token
        token_name = "token_name_placeholder"  # Modify as needed
        threading.Thread(target=collect_token_prices, args=(token, token_name)).start()
    except Exception as e:
        logger.info(f"Failed to process message: {e}")

# Set up RabbitMQ connection and channel
def setup_rabbitmq():
    try:
        rabbitmq_host = os.getenv('RABBITMQ_HOST', 'localhost')
        connection = pika.BlockingConnection(pika.ConnectionParameters(rabbitmq_host))
        channel = connection.channel()
        channel.queue_declare(queue='token', durable=True)  # Declare the queue as durable
        channel.basic_consume(queue='token', on_message_callback=on_message, auto_ack=True)
        logger.info('Waiting for messages. To exit press CTRL+C')
        channel.start_consuming()
    except Exception as e:
        logger.info(f"Unexpected error: {e}")

if __name__ == '__main__':
    try:
        setup_rabbitmq()
    except KeyboardInterrupt:
        logger.info("Script interrupted by user.")
    except Exception as e:
        logger.info(f"Unexpected error: {e}")

